<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsStudentRankMaster.php');
include('../setRequest.php');

$schoolId = 0;
$loggedUserId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;
$transchooldisplayName = '';
if (isset($_GET['schoolId'])) //Edit Mode
{
    $schoolId = $_GET['schoolId'];
    $schoolId = DecodeQueryData($schoolId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

$title = "Student Rank| " . $transchooldisplayName;

//For Rank List
$objStudentRankMaster = new clsStudentRankMaster();
$totalStudentRank = 0;
$rowsStudentRank = $objStudentRankMaster->GetStudentRankMasterDetails($schoolId);
if ($rowsStudentRank != '') {
    $totalStudentRank = mysqli_num_rows($rowsStudentRank);
}
unset($objStudentRankMaster);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">Rank</li>
                </ol>
            </div>

            <div class="pull-right">
                <a class="btn btn-link" href="addranking.html">Add</a>
            </div>


        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rank added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rank updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rank deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Ranking</th>

                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalStudentRank > 0) {
                    while ($row = mysqli_fetch_array($rowsStudentRank)) {

                        $rankId = $row['rankId'];
                        $schoolId = $row['schoolId'];
                        $title = stripslashes($row['title']);
                        //$isPrimaryRole = stripslashes($row['isPrimaryRole']);
                        $objStudentRankMaster = new clsStudentRankMaster();
                        $assigneCount = $objStudentRankMaster->CheckRankIsAssignedToStudet($rankId, $schoolId);
                        unset($objStudentRankMaster);


                        $totalUserCount = 0;
                ?>
                        <tr>
                            <td>
                                <?php echo ($title); ?>
                            </td>

                            <td style="text-align: center">
                                <a href="addranking.html?id=<?php echo (EncodeQueryData($rankId)); ?>">Edit</a>
                                | <a href="javascript:void(0);" class="deleteAjaxRow" rankId="<?php echo EncodeQueryData($rankId); ?>" studentRankName="<?php echo ($title); ?>" assigneCount="<?php echo ($assigneCount); ?>">Delete</a>
                            </td>
                        </tr>
                <?php
                    }
                }
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "aoColumns": [{
                "sWidth": "30%"
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter",
                "bSortable": false
            }, ]
        });

        // ajax call for deleteAjaxRow


        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var rankId = $(this).attr('rankId');
            var title = $(this).attr('studentRankName');
            var assigneCount = $(this).attr('assigneCount');
            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';

            if (assigneCount > 0) {
                alertify.error("Students are assigned to this Rank, You can't delete it");
            } else {
                alertify.confirm('Student Rank: ' + title, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: rankId,
                            userId: userId,
                            type: 'studentRank'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});
            }

        });
    </script>


</body>

</html>