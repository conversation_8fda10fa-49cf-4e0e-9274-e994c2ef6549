<?php
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');  
    include('../class/clsStudent.php');  
	include('../setRequest.php');	 
	
	    
	$rankId=0;
    $title ="Add Student Promotions";
    if(isset($_GET['schoolId'])) //Edit Mode
    {
        $schoolId = $_GET['schoolId'];
        $schoolId = DecodeQueryData($schoolId);
    }
    else
    {
        $schoolId= $currentSchoolId;
        //$tranSchoolDisplayname = $currenschoolDisplayname;
    }
    
    $bedCrumTitle = 'Add';
  
    $title .= " | ".$currenschoolDisplayname;
    
    //For Rank 
	if(isset($_GET['rankId']))
	{
		$rankId = $_GET['rankId'];
        $rankId = DecodeQueryData($rankId);
    }


	$objStudent = new clsStudent();
    $totalstudent = 0;

    //For Student List
    $rowsstudent = $objStudent->GetAllSchoolStudentsForPromotion($schoolId,$rankId);
	if($rowsstudent !='')
	{       
		$totalstudent =mysqli_num_rows($rowsstudent);        
    } 
    
    //For Dropdown Current Rank
        $studentrank = $objStudent->GetAllRank($schoolId);
        
	//For Dropdown New rank
   	 $studentnewrank = $objStudent->GetAllRankForNewRank($schoolId,$rankId);
     unset($objStudent);
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($title); ?></title>
    <?php include('includes/headercss.php');?>
    <link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
     <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>
<body>
    <?php include('includes/header.php');?>
    <?php include("includes/datatablecss.php") ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
					<li><a href="settings.html">Settings</a></li>
					<li>Student Promotions</li>
                    <li class="active"><?php  echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

    <div class="pageheading"></div>

         <form id="frmstudentpromotion" data-parsley-validate class="form-horizontal" method="POST" action="studentpromotionSubmit.html" >
            <div class="row">
			  <div class="col-md-6">	
                   <!-- Text input-->
						<div class="form-group">
                        <label class="col-md-12 control-label" for="cboImmunization">Current Ranking:</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">                       
                            <select  id="cboCurrentRank" name="cboCurrentRank" class="form-control step1 input-md  select2_single"  >
                            <option value="" selected>Select</option>
                            <?php
                                if($studentrank!="")
                                {
                                    while($row = mysqli_fetch_assoc($studentrank))
                                    {
                                         $selrankId  = $row['rankId'];
                                         $title  = stripslashes($row['title']);
                                         
                                         ?>										 
                                          <option value="<?php echo(EncodeQueryData($selrankId)); ?>" <?php if($rankId==$selrankId){ ?>  selected="true" <?php } ?>><?php echo($title); ?></option>
									
									<?php

                                    }
									
                                } 
						      
                            ?>
                            </select> 		
					   </div>
                    </div>
              </div>
              
					<div class="col-md-6">
                   	<div class="form-group">
                        <label class="col-md-12 control-label" for="cboImmunization">New Ranking:</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">                       
                            <select  id="cboNewRanking" name="cboNewRanking" class="form-control step2 input-md select2_single"  required >
                            <option value="" selected>Select</option>
                            <?php
                                 if($studentnewrank!="")
                                {
                                    while($row = mysqli_fetch_assoc($studentnewrank))
                                    {
                                         $selrankId  = $row['rankId'];
                                         $title  = stripslashes($row['title']);

                                         ?>										 
                                          <option value="<?php echo($selrankId); ?>" ><?php echo($title); ?></option>
									
									<?php

                                    }
									
                                } 
								
                            ?>
                            </select>
					   </div>
                    </div>
                                
                           

		         </div>

				</div>
                <div class="row"> 
				<div class="col-md-12">
					<div class="form-group">
					<label class="col-md-12 control-label" for="Student">Student</label>
                        <div class="col-md-12">
					 <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                     <thead>
                        <tr>
						
							<th style="text-align: center;vertical-align: middle;white-space: inherit;"><input name="select_all" value="" id="selectall" class="checked_all" type="checkbox"   data-parsley-errors-container="#error-selectall" ></th>
                            <th style="text-align: center;vertical-align: middle;">First Name</th>
                            <th style="text-align: center;vertical-align: middle;">Last Name</th>
                            <th style="text-align: center; vertical-align: middle;">Rank</th>
                            <th style="text-align: center;white-space: inherit;">Account Created Date</th>
                            <div id="error-selectall"></div>
                        </tr>
                    </thead>
					<tbody>
					<?php
					if($totalstudent>0)
                        {
                            
                            while($row = mysqli_fetch_array($rowsstudent))
                            {
								 $studentId = $row['studentId'];
                                 $firstName = $row['firstName'];
                                 $lastName = $row['lastName'];
                                 // $fullName = $row['Fullname'];
                      $createdDate = $row['createdDate'];
								 $rank = $row['rank'];
								 
								 ?>
					<tr class="dataTableRowSelected">
					    <td style="text-align: center">
						<?php if($rankId) ?>
						<input type="checkbox" id="chk_attendance_approved" name="checkbox[]"  class="checkbox" value="<?php echo($studentId); ?> "
						   " value="<?php echo($studentId); ?>" checked ">
						</td>
                        <td style=" text-align: left"><?php echo ($firstName); ?>
					    <td style=" text-align: left"><?php echo ($lastName); ?>
					      <td style="text-align: center"><?php echo ($rank) ;?>  </td>
                          <td style="text-align: left"><?php echo date('m/d/Y', strtotime($createdDate));?></td>
					 </tr>
                            <?php
							
                            }   
                        }
						
                    ?>					
					</tbody>
					 </table>   
					</div>
					</div>	
		            </div>		
                </div>		

                <div class="row">
                <!-- <label class="col-md-5 control-label"></label> -->
                            <div class="col-md-12" style="display: flex; justify-content: center;margin: 20px 0;gap: 15px;">
                                <button id="btnSubmit" name="btnSubmit" href="javascript:void(0);" class="btn btn-success">Submit</button>
                                <a type="button" href="settings.html" class="btn btn-default">Cancel</a>
                            
                     </div>
                </div>
               
        </form>


    </div>

    <?php include('includes/footer.php');?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
     <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>	 
    <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>

        <script type="text/javascript">

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function(){

            $(".select2_single").select2();
            $('#select2-cboCountry-container').addClass('required-select2');
            $('#form-control step2 input-md select2_single').addClass('required-select2');

			$('#StartDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY',
						 defaultDate: new Date()
						
           			});
					
					$("#cboCurrentRank").change(function(){
					var selrankId = $(this).val();
					
					if(selrankId)
					{
						
						window.location.href = "studentpromotion.html?rankId="+selrankId;
					}
					else{
						window.location.href = "studentpromotion.html";
					}
				});

        });
        $("#selectall").change(function () {
            $("input:checkbox").prop('checked', $(this).prop("checked"));
            })	

		$(document).on('click', '#btnSubmit', function(e) {
                  e.preventDefault(); //STOP default action
                alertify.confirm('Student Promotion Confirmation', 'Continue with Promotion ', function() {
             
				  $("#frmstudentpromotion").submit();
                }, function() {});

            });
            var current_datatable = $("#datatable-responsive").DataTable({
                searching: false, 
                paging: false, 
                info: false,
                "aoColumns": [{
                    "sWidth": "20%",
                    "bSortable": false
                }, {
                    "sWidth": "20%"
                },
                {
                    "sWidth": "20%"
                },
                {
                    "sWidth": "20%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
                {
                    "sWidth": "20%"
                } ]
            });
	
	
	 </script>
	
</body>
</html>