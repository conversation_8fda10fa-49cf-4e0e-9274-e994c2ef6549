<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
    include('../includes/commonfun.php');
    include('../setRequest.php');
    include('../class/clsSystemUserHistoryId.php');
    include('../class/clsClinician.php');
    include('../class/clsStudent.php');
    include('../class/clsSystemUser.php');
    include('../class/clsSystemUserRoleMaster.php');
        
    $totalSchoolUser='';

	$loggedUserId = $_SESSION["loggedUserId"];   
	$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
    $loggedUserFirstName = $_SESSION["loggedUserFirstName"];

   //For User Logs
	$currentSchoolId;
    $objuserlogs  = new clsSystemUserHistoryid();
	$objClinician  = new clsClinician();
	$objStudent = new clsStudent();
	$objSystemUser = new clsSystemUser();
	$objSystemUserRoleMaster = new clsSystemUserRoleMaster();
	 	 
    $rowdisplayUserLogs = $objuserlogs->GetAllLogusers($currentSchoolId);
    unset($objuserlogs);

    
	if($rowdisplayUserLogs != '')
	{
		$totalSchoolUser = mysqli_num_rows($rowdisplayUserLogs);
	}
    else {
        header('location:settings.html?error=1');
        exit;
    }
    
   

?>
    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>User Logs </title>
        <?php include('includes/headercss.php');?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
        <?php include("includes/datatablecss.php") ?>

        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">User Logs</li>
                    </ol>
                </div>
                <div class="pull-right mt-2">
                <?php                
                if($totalSchoolUser > 0) { ?>
                    <a class="loginAsSchoolUser" href="javascript:void(0);"  >Clear All</a>
                <?php } ?>
                </div>
            </div>
        </div>

        <div class="container">      

            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="ClearAll")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Clear All Logs
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> User updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="StatusUpdated")
					{
				?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> User status updated successfully.
                    </div>
                <?php
					}
                    else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th style="text-align: Left">User</th>
                            <th style="text-align: Left">Type</th>
                            <th style="text-align: center">Ip Address</th>
                            <th style="text-align: center">Login Date</th>
                            <th style="text-align: center">Logout Date</th>
                            <th style="text-align: Left">Browser History</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
				        $rowDisplayStudent='';
				        $rowDisplayClinician ='';
				        $rowDisplaySystemUser='';
                        if($totalSchoolUser>0)
                        {
                            while($row = mysqli_fetch_array($rowdisplayUserLogs))
                            {
                                $type =stripslashes($row['type']);
                                $typeUserId =stripslashes($row['typeUserId']);
                                $ip = stripslashes($row['ip']);
                                $typeUser='';
                                $loginDate = stripslashes($row['loginDate']);
                               
								// $loginDate = converFromServerTimeZone($loginDate,$TimeZone);
						    	//$loginDate=	date('m/d/Y h:i A', strtotime($loginDate));
						    	
								/* 30042021 */
                                $displayDate = stripslashes($row['loginDate']);
                                // $loginDate = converFromServerTimeZone($loginDate, $TimeZone);
                                $datetime = new DateTime($displayDate);
                                $la_time = new DateTimeZone($TimeZone);
                                $datetime->setTimezone($la_time);
                                $loginDate = $datetime->format('m/d/Y h:i A');
                                /* 30042021 */
                                
                                // $logoutDate = stripslashes($row['logoutDate']);
                                // $logoutDate = converFromServerTimeZone($logoutDate,$TimeZone);
                                // $logoutDate =	date('m/d/Y h:i A', strtotime($logoutDate));
						  
						  
								/* 30042021 */
                                $logoutDate1 = stripslashes($row['logoutDate']);
                                // $loginDate = converFromServerTimeZone($loginDate, $TimeZone);
                                $datetime = new DateTime($logoutDate1);
                                $la_time = new DateTimeZone($TimeZone);
                                $datetime->setTimezone($la_time);
                                $logoutDate = $datetime->format('m/d/Y h:i A');
                                /* 30042021 */
						    	
                                if($logoutDate1 !='0000-00-00 00:00:00' && $logoutDate1 !='' && $logoutDate1 !='12/31/1969 06:00 PM')
                                {
                                    // $logoutDate = converFromServerTimeZone($logoutDate,$TimeZone);
                                    $logoutDate = $logoutDate;
                                }
                                else{
                                    $logoutDate = "-";
                                }
                                
                                if($type =='C'){
									$typeUser ='Clinician';
									$rowDisplayClinician = $objClinician->GetClinicianDetails($typeUserId);
									$firstName = isset($rowDisplayClinician['firstName']) ? $rowDisplayClinician['firstName'] : '';
									$lastName  = isset($rowDisplayClinician['lastName']) ? $rowDisplayClinician['lastName'] : '';
									$fullName = $firstName.' '.$lastName; 
								}elseif($type=='A'){
									
									$rowDisplaySystemUser = $objSystemUser->GetSchoolSystemUsersName($typeUserId);
									$firstName = isset($rowDisplaySystemUser['firstName']) ? $rowDisplaySystemUser['firstName'] : '';
									$lastName  = isset( $rowDisplaySystemUser['lastName']) ? $rowDisplaySystemUser['lastName'] : '';
									$fullName = $firstName.' '.$lastName; 
									$systemUserRoleMasterId  = $rowDisplaySystemUser['systemUserRoleMasterId'];
								
									$typeUser = $objSystemUserRoleMaster->GetSystemUserRoleTitle($systemUserRoleMasterId);
								
									
								}elseif($type=='S'){
									$typeUser ='Student';
									$rowDisplayStudent = $objStudent->GetStudentDetails($typeUserId);
									$firstName = $rowDisplayStudent['firstName'];
									$lastName  = $rowDisplayStudent['lastName'];
									$fullName = $firstName.' '.$lastName; 
								}
                                
                                $browserHistory = stripslashes($row['browserHistory']);
                                ?>
                            <tr>
                                <td style="text-align: Left">
                                    <?php echo ($fullName); ?>
                                </td>
                                <td style="text-align: Left">
                                    <?php echo $typeUser; ?>
                                </td>
                               <td style="text-align: center">
                                     <?php echo($ip); ?>
                                </td>
                                <td style="text-align: center">
                                    <?php echo($loginDate); ?><br>
                                </td>
                                <td style="text-align: center">
                                    <?php echo ($logoutDate);?><br>
                                </td>
                                <td style="text-align: Left">
                                     <?php echo( $browserHistory); ?>
                                </td>
                            </tr>
                            <?php
                            }
                        }
                    ?>
                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            });
			
			// Datatable
            var current_datatable = $("#datatable-responsive").DataTable({
                "ordering": true,
            "aaSorting": [],
                "aoColumns": [{
                    "sWidth": "10%"
                   
                }, {
                    "sWidth": "10%"
                },{
                    "sWidth": "10%",
					"bSortable": false
                },{
                    "sWidth": "10%"
					
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "10%"
                }]
            });
			
			// Clear All
            $(document).on('click', '.loginAsSchoolUser', function() {
                var currentSchoolId ='<?php echo $currentSchoolId; ?>';
                var loggedUserId ='<?php echo $loggedUserId; ?>';

                alertify.confirm('Clear All Confirmation', 'Continue with Clear All ', function() {
                    window.location.href='clearalllog.html?currentSchoolId='+currentSchoolId+'&loggedUserId='+loggedUserId;
                }, function() {});
            });
        </script>
    </body>
    </html>