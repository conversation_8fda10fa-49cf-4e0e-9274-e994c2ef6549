<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsStudent.php');
include('../class/clsEquipment.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsSectionStudentName.php');
include('../class/clsClinician.php');
include('../class/clsLocations.php');

// echo '<pre>';
// print_r($_GET); 
// exit;

$schoolId = 0;
$studentId = 0;
$rotationId = 0;
$studentEquipmentMasterId = 0;
$clinicianId = 0;
$currentstudentId = 0;
$equipmentrotationid = 0;
$schoolId = $currentSchoolId;
$equipmentUsageDate = null;
$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

$objRotation = new clsRotation();

if (isset($_GET['equipmentrotationid'])) {
	$equipmentrotationid = DecodeQueryData($_GET['equipmentrotationid']);
}
if (isset($_GET['studentId'])) {
	$currentstudentId = DecodeQueryData($_GET['studentId']);
}
if (isset($_GET['rotationId'])) {
	$rotationId = DecodeQueryData($_GET['rotationId']);
	$equipmentrotationid = $rotationId;
}
//For Edit Equipment


if (isset($_GET['studentEquipmentMasterId']) && ($_GET['equipmentrotationid'])) {

	$schoolId = $currentSchoolId;
	$page_title = "Edit Equipment ";
	$bedCrumTitle = 'Edit';

	//For Equipment Details
	$objEquipment = new clsEquipment();
	$studentEquipmentMasterId = DecodeQueryData($_GET['studentEquipmentMasterId']);
	$rowEquipment = $objEquipment->GetStudentEquipmentDetails($studentEquipmentMasterId);
	unset($objEquipment);
	if ($rowEquipment == '') {
		header('location:equipmentlist.html');
		exit;
	}

	$equipmentrotationid = ($rowEquipment['rotationId']);
	$clinicianId = ($rowEquipment['clinicanId']);
	$equipmentUsageDate = ($rowEquipment['equipmentUsageDate']);

	$parentRotationId = $rowEquipment['parentRotationId'];
	$rotationLocationId  = ($rowEquipment['rotationLocationId']);
	$courselocationId = $rowEquipment['locationId'];

	$locationId = 0;
	if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
		if (!$rotationLocationId)
			$locationId = $objRotation->GetLocationByRotation($equipmentrotationid);
		else
			$locationId  = $rotationLocationId;
	} else {
		$locationId  = $courselocationId;
	}

	//Get Time Zone By Rotation 
	$objLocation = new clsLocations();
	$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
	unset($objLocation);

	if ($TimeZone == '')
		$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

	$equipmentUsageDate = converFromServerTimeZone($equipmentUsageDate, $TimeZone);
	$equipmentUsageDate = (date('m/d/Y', strtotime($equipmentUsageDate)));
	$studentId = ($rowEquipment['studentId']);

	//For Rotation List
	$rowstudentrotation = $objRotation->GetRotationByStudent($schoolId, $currentstudentId);
} else {

	$page_title = "Add Equipment";
	$bedCrumTitle = 'Add';

	//For Rotation List
	$rowstudentrotation = $objRotation->GetCurrentRotationByStudent($schoolId, $currentstudentId);
}

//For Clinician Name		
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByRotation($schoolId, $equipmentrotationid);
unset($objClinician);

//For Student Name
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($schoolId, $equipmentrotationid);
//For Student Full Name		
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname = $Rowstudent ? ($Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']) : '';

unset($objStudent);

//For Section
$totalSection = 0;
$objEquipment = new clsEquipment();
$EquipmentSection = $objEquipment->GetSections($schoolId);
if ($EquipmentSection != '') {
	$totalSection = mysqli_num_rows($EquipmentSection);
}

//For Rotation Title

$RotationName = $objRotation->GetrotationDetails($equipmentrotationid, $schoolId);
$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] : '';
unset($objRotation);

$bedCrumTitle = (isset($_GET['view']) && $_GET['view'] == 'V') ? 'View' : $bedCrumTitle;

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($page_title); ?></title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />


	<style>
		/* Style for the collapsible button */
		.collapsible {
			/* background-color: #3498db;
            color: white;
            padding: 10px;
            border: none; */
			width: 100%;
			cursor: pointer;
			display: flex;
			justify-content: space-between;
			/* Align content horizontally */
		}

		.panel-heading {
			width: 100%;
		}

		/* Style for the arrow icons */
		.arrow-icon {
			transition: transform 0.3s;
		}

		.collapsible.expanded .arrow-icon i {
			transform: rotate(180deg);
		}
	</style>
</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
					<?php if ($currentstudentId > 0) { ?>
						<li><a href="clinical.html">Clinical</a></li>
						<li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
						<li><a href="equipmentlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Equipment Evaluation</a></li>
					<?php } else { ?>
						<li><a href="rotations.html">Rotations</a></li>
						<li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
						<li><a href="equipmentlist.html?equipmentrotationid=<?php echo EncodeQueryData($equipmentrotationid); ?>">Equipment Evaluation</a></li>
					<?php } ?>
					<li class="active"><?php echo ($bedCrumTitle); ?></li>
				</ol>
			</div>

		</div>
	</div>

	<div class="container">

		<form id="frmequipment" data-parsley-validate class="form-horizontal" method="POST" <?php if ($currentstudentId > 0) { ?>action="equipmentsubmit.html?studentId=<?php echo (EncodeQueryData($currentstudentId)); ?>&studentEquipmentMasterId=<?php echo (EncodeQueryData($studentEquipmentMasterId)); ?>" <?php } else { ?> action="equipmentsubmit.html?studentEquipmentMasterId=<?php echo (EncodeQueryData($studentEquipmentMasterId)); ?>
																									&equipmentrotationid=<?php echo (EncodeQueryData($equipmentrotationid)); ?>" <?php } ?>>

			<div class="row">
				<div class="col-md-6">

					<div class="form-group">
						<label class="col-md-12 control-label" for="cboclinician">Evaluator</label>
						<div class="col-md-12 flex-col-reverse">
							<select id="cboclinician" name="cboclinician" class="form-control input-md required-input select2_single" required>
								<option value="" selected>Select</option>
								<?php
								if ($Clinician != "") {
									while ($row = mysqli_fetch_assoc($Clinician)) {
										$selClinicianId  = $row['clinicianId'];
										$name  = stripslashes($row['firstName']);
										//echo 'NAME->'.$name;
								?>
										<option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

								<?php

									}
								}
								?>
							</select>
						</div>
					</div>
					<!-- ROTATION DD END -->
				</div>


				<div class="col-md-6">

					<div class="form-group">
						<label class="col-md-12 control-label" for="cbostudent">Student</label>
						<div class="col-md-12 flex-col-reverse">
							<select id="cbostudent" name="cbostudent" class="form-control input-md required-input select2_single" required>
								<option value="" selected>Select</option>
								<?php
								if ($Student != "") {
									while ($row = mysqli_fetch_assoc($Student)) {
										$selstudentId  = $row['studentId'];
										$firstName  = stripslashes($row['firstName']);
										$lastName  = stripslashes($row['lastName']);
										$name =	$firstName . ' ' . $lastName;
										if ($currentstudentId > 0) { ?>
											<option value="<?php echo ($selstudentId); ?>" <?php if ($currentstudentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
										<?php } else { ?>
											<option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
										<?php }  ?>
								<?php

									}
								}
								?>
							</select>

						</div>
					</div>
					<!-- ROTATION DD END -->
				</div>


			</div>



			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="equipmentUsageDate">Equipment Usage Date</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full relative' id='equipmentUsageDate'>

								<input type='text' name="equipmentUsageDate" id="equipmentUsageDate" class="form-control input-md required-input rotation_date" value="<?php echo ($equipmentUsageDate); ?>" required data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>

				<?php if ($currentstudentId > 0) { ?>
					<div class="col-md-6">
						<div class="form-group">
							<label class="col-md-12 control-label" for="cborotation">Rotation</label>
							<div class="col-md-12 flex-col-reverse">
								<select id="cborotation" name="cborotation" class="form-control input-md required-input select2_single" required>
									<option value="" selected>Select</option>
									<?php
									if ($rowstudentrotation != "") {
										while ($row = mysqli_fetch_assoc($rowstudentrotation)) {
											$selrotationId  = $row['rotationId'];
											$title  = stripslashes($row['title']);
									?>
											<option value="<?php echo ($selrotationId); ?>" <?php if ($equipmentrotationid == $selrotationId) { ?> selected="true" <?php } ?>><?php echo ($title); ?></option>
									<?php }
									}
									?>
								</select>
								<input type="hidden" value="<?php echo ($rotationId); ?>" name="rotation">
							</div>
						</div>
					</div>
				<?php } ?>
			</div>

			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="panel panel-default border-14">
								<div class="panel-body">

									<b>ONLY check items in the sections below if the student has worked with equipment, and please COMMENT on their interaction with equipment, if appropriate.</b>


								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 1st SECTION div start -->
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
						<div class="col-md-12 col-sm-12 col-xs-12">

							<div class="panel-group" id="posts">
								<?php


								while ($row = mysqli_fetch_array($EquipmentSection)) {
									$sectionMasterId = $row['sectionMasterId'];
									$title = $row['title'];
									//$firstName = $row['firstName'];
								?>

									<div class="panel panel-default">
										<a class="collapsible" style="color: #000; text-decoration: none;" href="#<?php echo $sectionMasterId; ?>" data-toggle="collapse" data-parent="#posts" id="collapse-link">
											<div class="panel-heading" style="display: flex; justify-content: space-between; align-items: center;">
												<h4 class="panel-title">
													<b><?php echo  $title; ?></b>
												</h4>
												<span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
											</div>
										</a>
										<div id="<?php echo $sectionMasterId; ?>" class="panel-collapse panel-body collapse">
											<?php
											// for question
											$totalEquipment = 0;
											$Equipmentquestion = $objEquipment->GetAllEquipmentQuestionMaster($schoolId, $sectionMasterId);

											if ($Equipmentquestion != '') {
												$totalEquipment = mysqli_num_rows($Equipmentquestion);
											}

											if ($totalEquipment > 0) {
												while ($row = mysqli_fetch_array($Equipmentquestion)) {
													if (isset($_GET['studentEquipmentMasterId'])) {
														$studentEquipmentMasterId = DecodeQueryData($_GET['studentEquipmentMasterId']);
													} else {
														$studentEquipmentMasterId = 0;
													}

													$schoolEquipmentQuestionId = $row['schoolEquipmentQuestionId'];
													$schoolEquipmentQuestionTitle = $row['questionText'];
													$schoolEquipmentQuestionType = $row['schoolEquipmentQuestionType'];
													$qhtml = GetEquipmentQuestionHtml($schoolEquipmentQuestionId, $schoolEquipmentQuestionType, $studentEquipmentMasterId, $currentSchoolId);

											?>
													<div class="panel-body">
														<b><?php echo ($schoolEquipmentQuestionTitle); ?></b> <br /><br />
														<?php echo $qhtml; ?>
													</div>
											<?php
												}
											}
											?>
										</div>
									</div>
								<?php
								}

								?>
							</div>


						</div>

					</div>
				</div>
			</div>

			<div class="row">
				<div class="form-group mx-0">
					<!-- <label class="col-md-2 control-label"></label> -->
					<div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px">
						<?php
						$rotationStatus = checkRotationStatus($equipmentrotationid);
						if ($rotationStatus == 0) {
						?>
							<button style="margin: 0 0 0 11px;" id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
						<?php } ?>

						<?php if ($currentstudentId > 0) { ?>
							<a type="button" href="equipmentlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>" class="btn btn-default">Cancel</a>
						<?php } else { ?>
							<a type="button" href="equipmentlist.html?equipmentrotationid=<?php echo EncodeQueryData($equipmentrotationid); ?>" class="btn btn-default">Cancel</a>
						<?php } ?>
					</div>
				</div>
			</div>

		</form>


	</div>

	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>




	<script type="text/javascript">
		$(window).load(function() {


			$('#frmequipment').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {
					ShowProgressAnimation();
					return true; // Don't submit form for this demo
				});

			$('#equipmentUsageDate').datetimepicker({
				format: 'MM/DD/YYYY'

			});


			//for searching dropdown
			$(".select2_single").select2();
			$('#select2-cbostudent-container').addClass('required-select2');
			$('#select2-cboclinician-container').addClass('required-select2');

			<?php if (isset($_GET['studentEquipmentMasterId']) && ($_GET['equipmentrotationid'])) { ?>
				$('#cbostudent').prop('disabled', true);
			<?php } ?>
		});

		// <?php // if (isset($_GET['studentEquipmentMasterId']) && ($_GET['equipmentrotationid'])) { 
			?>
		// 	$('#cbostudent').prop('disabled', true);
		// <?php //} 
			?>

		<?php if ($currentstudentId > 0) { ?>
			$('#cbostudent').prop('disabled', true);
		<?php }
		if ($equipmentrotationid > 0) { ?>
			document.getElementById("cborotation").required = false;
		<?php } ?>
	</script>
	<script>
		// Get all collapsible button elements
		var buttons = document.querySelectorAll(".collapsible");
		var contents = document.querySelectorAll(".panel-collapse");

		// Add click event listeners to all buttons
		buttons.forEach(function(button, index) {
			button.addEventListener("click", function() {
				// Check if the content is currently expanded
				var isExpanded = contents[index].style.display === "block";

				// Close all sections
				contents.forEach(function(content) {
					content.style.display = "none";
				});

				// Reset the "expanded" class for all buttons
				buttons.forEach(function(btn) {
					btn.classList.remove("expanded");
				});

				// Toggle the content for the clicked section
				if (!isExpanded) {
					contents[index].style.display = "block";
					button.classList.add("expanded");
				}
			});
		});
	</script>
</body>

</html>