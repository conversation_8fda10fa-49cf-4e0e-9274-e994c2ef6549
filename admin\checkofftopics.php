<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsCheckoffTopicMaster.php');
include('../setRequest.php');

$currentSchoolId;
$schoolTopicId;
$isActiveCheckoff = $isActiveCheckoffForStudent;
$loggedUserId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;
$isCurrentSchoolSuperAdmin = isset($_SESSION['isCurrentSchoolSuperAdmin']) ? ($_SESSION['isCurrentSchoolSuperAdmin']) : 0;
if (isset($_GET['schoolTopicId'])) {
    $schoolTopicId = DecodeQueryData($_GET['schoolTopicId']);
}

//For checkoff Type
$objDB = new clsDB();
$checkoffType = $objDB->GetSingleColumnValueFromTable('schools', 'checkoffType', 'schoolId', $currentSchoolId);
unset($objDB);

//For CheckOff Topic
$objCheckoffTopicMaster = new clsCheckoffTopicMaster();
$rowsCheckoffTopics = $objCheckoffTopicMaster->GetAllCheckoffTopic($currentSchoolId, $checkoffType);
$totalCount = 0;
if ($rowsCheckoffTopics != '') {
    $totalCount = mysqli_num_rows($rowsCheckoffTopics);
}
$loggedAsBackUserId = isset($_SESSION["loggedAsBackUserId"]) ? $_SESSION["loggedAsBackUserId"] : 0;
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Comps Steps</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">Comps</li>
                    <!-- <li class="active"><?php //echo $checkoffText; ?></li> -->
                </ol>
            </div>
            <?php
            if ($loggedAsBackUserId) {
            ?>
                <div class="pull-right">
                    <ol class="breadcrumb">
                        <li><a href="addcheckofftopics.html">Add</a></li>
                        <?php if ($currentSchoolId == 75 || $currentSchoolId == 123) { ?> |
                            <!-- <a class="impostStudentList" href="importstudent.html?type=4">Import Section</a>  -->
                            | <a class="impostStudentList" href="importstudent.html?type=5">Import Checkoffs</a>
                        <?php } ?>

                    </ol>
                </div>
            <?php
            }
            ?>
        </div>
    </div>

    <div class="container">


        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Comps Steps added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Comps Steps updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
            <?php
            } else if ($_GET["status"] == "Imported") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Comps Section added successfully.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>

                    <th style="text-align: center">PEF ID</th>
                    <th>Comps Title</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalCount > 0) {
                    while ($row = mysqli_fetch_array($rowsCheckoffTopics)) {
                        $schoolTopicId = ($row[0]);
                        $checkoffTitleId = ($row['checkoffTitleId']);
                        $topicTitle = ($row['schooltitle']);
                        $sectionCount = ($row['sectionCount']);
                        $currentstatus = ($row['status']);

                        if ($currentstatus == "1") {
                            $displayStatus = "Active";
                            $updateStatus = "0";
                            $buttoncss = "text-primary";
                        } else {
                            $displayStatus = "Inactive";
                            $updateStatus = "1";
                            $buttoncss = "text-warning";
                        }
                ?>
                        <tr>

                            <td style="text-align:center"><?php echo ($checkoffTitleId); ?></td>
                            <td style="white-space: normal; word-wrap: break-word; background:white"><?php echo ($topicTitle); ?></td>
                            <td style="text-align: center">
                                <a href="checkoffsection.html?topicid=<?php echo EncodeQueryData($schoolTopicId); ?>">Section</a>
                                <span class="badge"><?php echo ($sectionCount); ?></span>
                                |
                                <!-- <a class="<?php echo ($buttoncss); ?>"  href="checkoffstatus.html?schoolTopicId=<?php echo ($schoolTopicId); ?>&newStatus=<?php echo ($updateStatus); ?>&type=status">
										<?php echo ($displayStatus); ?>
                                </a>  -->

                                <a class="updateCheckoffStatus <?php echo ($buttoncss); ?>" schoolTopicId="<?php echo EncodeQueryData($schoolTopicId); ?>" newStatus="<?php echo ($updateStatus); ?>" userId="<?php echo EncodeQueryData($loggedUserId); ?>" isCurrentSchoolSuperAdmin="<?php echo ($isCurrentSchoolSuperAdmin); ?>" href="javascript:void(0);">
                                    <?php echo ($displayStatus); ?>
                                </a>
                                <?php if ($loggedAsBackUserId) { ?>
                                    |
                                    <a href="addcheckofftopics.html?editid=<?php echo EncodeQueryData($schoolTopicId); ?>">Edit</a>

                                    | <a href="javascript:void(0);" class="deleteAjaxRow" schoolTopicId="<?php echo EncodeQueryData($schoolTopicId); ?>" topicTitle="<?php echo ($topicTitle); ?>">Delete</a>

                                <?php } ?>
                            </td>
                        </tr>
                <?php
                    }
                }
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <!-- sort with alphanumeric -->
    <!-- <script src="https://cdn.datatables.net/plug-ins/1.10.24/sorting/natural.js"></script> -->

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $('.cliniciandetails').magnificPopup({
            'type': 'ajax',
        });

        $('.impostStudentList').magnificPopup({
            type: 'ajax',
            'closeOnBgClick': false
        });

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();
        });

        var current_datatable = $("#datatable-responsive").DataTable({
            'iDisplayLength': 250,
            "columnDefs": [{
                "targets": 0, // Index of the first column
                "type": "natural" // Use the natural sorting type
            }],
            "order": [
                [0, 'asc']
            ], // Order by the first column (index 0) in ascending order
            // Other DataTable options...

        });

        //Delete Checkoff
        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var schoolTopicId = $(this).attr('schoolTopicId');
            var topicTitle = $(this).attr('topicTitle');

            var userId = '<?php echo EncodeQueryData($loggedUserId ); ?>';
            var isCurrentSchoolSuperAdmin = '<?php echo $isCurrentSchoolSuperAdmin; ?>';

            alertify.confirm('Comps Topic: ' + topicTitle, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: schoolTopicId,
                        type: 'PEF_Checkoff_Topic',
                        userId: userId,
                        isCurrentSchoolSuperAdmin: isCurrentSchoolSuperAdmin

                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        //Update Checkoff Status
        $(document).on('click', '.updateCheckoffStatus', function() {

            // var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var schoolTopicId = $(this).attr('schoolTopicId');
            var newStatus = $(this).attr('newStatus');
            var statusClass = $(this).attr('class');
            // console.log(statusClass); return false;
            const myArray = statusClass.split(" ");
            // console.log(myArray); return false;
            var userId = $(this).attr('userId');
            var isCurrentSchoolSuperAdmin = $(this).attr('isCurrentSchoolSuperAdmin');

            var e = $(this);
            $.ajax({
                type: "GET",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_Update_checkoffstatus.html",
                data: {
                    schoolTopicId: schoolTopicId,
                    newStatus: newStatus,
                    userId: userId,
                    isCurrentSchoolSuperAdmin: isCurrentSchoolSuperAdmin
                },
                success: function(data) {
                    // console.log(data)
                    if (data == 'success') {
                        // console.log('hi' + this)
                        $(e).removeClass(myArray[1]);
                        var status = (newStatus == 1) ? 0 : 1;
                        var statusLabel = (newStatus == 1) ? 'Active' : 'Inactive';
                        var activeClass = (myArray[1] == 'text-primary') ? 'text-warning' : 'text-primary';
                        // current_datatable.row(current_datatable_row).remove().draw(false);
                        // console.log('status'+status)
                        // console.log('activeClass'+activeClass)
                        // console.log('statusLabel'+statusLabel)
                        $(e).attr('newStatus', status);
                        $(e).addClass(activeClass);
                        $(e).text(statusLabel);
                        alertify.success('Updated');
                    } else {
                        alertify.error('Not Updated');
                    }
                }
            });

            // alertify.confirm('Comps Topic: '+topicTitle, 'Continue with delete?', function() {

            // }, function() {});

        });
    </script>
</body>

</html>