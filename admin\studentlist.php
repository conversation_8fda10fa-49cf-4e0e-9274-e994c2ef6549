<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');       
    include('../class/clsCoarc.php');       
    include('../setRequest.php'); 	
include('../class/clsEmployerCoarcRequestMaster.php');
	
	$selrotationId=0;
	$rotationId=0;
	$from_date='';
	$to_date = '';	
	$display_from_date= date('m/d/Y', strtotime('-15 days'));
	$display_to_date= date('m/d/Y');
	$currentSchoolId;
	$coarcSurveyMasterId =0;
	
	if(isset($_GET['coarcSurveyMasterId']))
	{
		$coarcSurveyMasterId=DecodeQueryData($_GET['coarcSurveyMasterId']);
	}
	$objCoarc = new clsCoarc();			
	$rowsData = $objCoarc->GetStudentListByCoarcSurvey($coarcSurveyMasterId,$currentSchoolId);
	$totalCount = 0;
	if($rowsData !='')
	{
		$totalCount = mysqli_num_rows($rowsData);
	}
	$GetTitle = $objCoarc->GetCoarcTitle($coarcSurveyMasterId);
	$CoarcSurveyTitle=$GetTitle['title'];
    
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>CoARC Survey</title>       
    </head>
 <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
    <body>
       

        <div class="container">
			<div class="" tabindex="-1" role="dialog">
				<div class="modal-dialog" role="document">
					<div class="modal-content">
						<div class="modal-header">
							<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
								<h4 class="modal-title">CoARC Survey <?php echo ($CoarcSurveyTitle); ?> :Student</h4>
						</div>
						<div class="modal-body">
							<div class="modal-body">
                
									<table id="clinicianlist_table" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
										<thead>
											<tr>										                             
												<th>First Name</th>
												<th>Last Name</th>
												<th>Rank</th>
												<th>Status</th>
												
											</tr>
										</thead>
										<tbody>
											<?php
											if($totalCount > 0)
											{
												while($row = mysqli_fetch_array($rowsData))
												{								
													$coarcSurveyMasterId = ($row['coarcSurveyMasterId']);    
													$studentId=stripslashes($row['studentId']);
													$firstName=stripslashes($row['firstName']);
													$lastName=stripslashes($row['lastName']);	
													$Ranktitle=stripslashes($row['Ranktitle']);	
													$coarctype=($row['coarctype']);
													
													if($coarctype == 3)
													{
														$status = $objCoarc->GetGraduateStudentCoarcSurveyStatus($coarcSurveyMasterId,$studentId);
													}
													else 
													{
														 $status = $objCoarc->GetStudentCoarcSurveyStatus($coarcSurveyMasterId,$studentId);	
														// exit;
													}

													if($coarctype == 4)
													{
														$objEmployerCoarcRequestMaster = new clsEmployerCoarcRequestMaster();
														$surveyDetail = $objEmployerCoarcRequestMaster->GetEmployerCoarcSurveyByStudent($studentId, $coarcSurveyMasterId);
														$employercoarcId = stripslashes($surveyDetail['employercoarcId'] ?? '');
														$status = stripslashes($surveyDetail['status'] ?? '');
							
														// if ($employercoarcId) {
														// 	if ($status == 1) {
														// 		$status = 'Completed';
														// 	} elseif ($status == 0) {
														// 		$status = 'Pending';
														// 	}
													}
													
													//Display Status
													if($status == 1)
														$status = 'Completed';
													else
														$status = 'Pending';
													
												   ?>
												<tr>																				
													<td style="background:white"><?php echo ($firstName); ?></td>							
													<td style="background:white"><?php echo ($lastName); ?></td>							
													<td style="background:white"><?php echo ($Ranktitle); ?></td> 
													<td style="background:white"><?php echo ($status); ?></td>
												</tr>
												<?php
												}
											}
											unset($objCoarc);
										?>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div> 
	
         <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
		<script>
			var current_clinicianlist_table = $("#clinicianlist_table").DataTable({
					
                "aoColumns": [{
                    "sWidth": "10%"
                },{
                    "sWidth": "10%"
                },{
                    "sWidth": "10%"
                }]
            });
		</script>
    </body>
    </html>