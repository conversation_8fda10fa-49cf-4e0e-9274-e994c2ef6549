<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
    include('../class/clsSchool.php');
    include('../class/clsSystemUser.php');
    include('../includes/commonfun.php');
    include('../setRequest.php');   
   $schoolId='';
   if(isset($_GET['schoolId'])) //Edit Mode
	{
		$schoolId = $_GET['schoolId'];
		$schoolId = DecodeQueryData($schoolId);
	}
	else
	{
        header('location:schools.html?Error=1');
        exit;
    }
	$activebtn = 1;
	
    if(isset($_GET['active']))
       $activebtn = DecodeQueryData($_GET['active']);

    
    if($activebtn == 1)
		$clsBtnActive = "active";
    elseif($activebtn == 0 )
        $clsBtnInActive = "active";
    elseif($activebtn == 2 )
    {
        $clsBtnActiveAll = "active";
        $activebtn ='';
    }
    else
		$clsBtnActive = "active";

    //CREATE OBJECT
	$totalSchoolUser = '';
	$objSystemUsers = new clsSystemUser();  
    $rowsSchoolUsers = $objSystemUsers->GetSchoolSystemUsersForAdmin($schoolId,0,$activebtn);
    unset($objSystemUsers);
	if($rowsSchoolUsers != '')
	{
		$totalSchoolUser = mysqli_num_rows($rowsSchoolUsers);
	}
    else 
    {
        header('location:schools.html?Error=1');
        exit;
    }
    
    //Get School Name
    $objSchool  = new clsSchool();
    $displaySchoolName = $objSchool->GetSchoolName($schoolId);
    unset($objSchool);
?>
    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Users </title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>

        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li>
						<?php if(isset($_GET['typeNotification'])) {$notificationId = $_GET['typeNotification'];?>
						<a href="superadminnotification.html">Notification</a><?php }else{?>
						<a href="schools.html">Schools</a><?php }?> </li>
                        <li class="active">Users</li>
						
                    </ol>
                </div>
                
            </div>
        </div>

        <div class="container">

        <div class="formSubHeading"><?php echo($displaySchoolName); ?></div>

            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="Added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>User added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> User updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="StatusUpdated")
					{
				?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> User status updated successfully.
                    </div>
                <?php
					}
					else if($_GET["status"] =="blockStatusUpdated")
					{
				?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> User lock status updated successfully.
                    </div>
                <?php
					}
                    else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
                <div class="row margin_bottom_ten">
                <div class="btn-group pull-right" role="group" aria-label="First group">
                <a role="button" class="btn btn-primary <?php echo $clsBtnActive; ?>" href="displayschoolsystemuser.html?schoolId=<?php echo EncodeQueryData($schoolId); ?>&active=<?php echo EncodeQueryData(1); ?>">Active</a>
                <a  role="button" class="btn btn-primary  <?php echo $clsBtnInActive; ?>" href="displayschoolsystemuser.html?schoolId=<?php echo EncodeQueryData($schoolId); ?>&active=<?php echo EncodeQueryData(0); ?>">Inactive</a>
                <a role="button" class="btn btn-primary <?php echo $clsBtnActiveAll; ?>" href="displayschoolsystemuser.html?schoolId=<?php echo EncodeQueryData($schoolId); ?>&active=<?php echo EncodeQueryData(2); ?>" >All</a>
                </div>
            </div>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Name</th>
                            <th>Login Info</th>
                            <th>Contact Info</th>
                           
                            <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalSchoolUser>0)
                        {
                            while($row = mysqli_fetch_array($rowsSchoolUsers))
                            {

                                $systemUserMasterId = $row['systemUserMasterId'];					
                                $firstName = stripslashes($row['firstName']);
                                $lastName = stripslashes($row['lastName']);
                                $fullName = $firstName.' '.$lastName;
                                $email = stripslashes($row['email']);
                                $phone = stripslashes($row['phone']);
                                $cellPhone = stripslashes($row['cellPhone']);
                                $isPrimaryUser = stripslashes($row['isPrimaryUser']);
                                $userName = stripslashes($row['username']);
                                $schoolRoleTitle = stripslashes($row['roleTitle']);

                                $IsPublished= $row['isActive'];
								$isBlocked= $row['isBlocked'];
                                $displayStatus ="";
                                $updateStatus = "0";
                                $buttoncss = "btn-primary";
								$displayBlockStatus ="";
                                if($IsPublished =="1")
                                {
                                    $displayStatus="Active";
                                    $updateStatus = "0";
                                    $buttoncss = "text-primary";
									$isShowemail = 1;
                                }
                                else
                                {
                                    $displayStatus="Inactive";
                                    $updateStatus = "1";
                                    $buttoncss = "text-warning";
									$isShowemail = 0;
                                }
								if($isBlocked =="0")
								{
									$displayBlockStatus="Unlocked";
									$updateBlockStatus = "1";
									$buttoncss = "text-primary";								
								}
								else
								{
									$displayBlockStatus="Locked";
									$updateBlockStatus = "0";
									$buttoncss = "text-warning";										
								}
                                ?>
                            <tr>
                                <td>
                                    <?php echo($fullName); ?>
                                </td>
                                <td>
                                    Username: <?php echo ($userName); ?><br>
                                    Role: <?php echo ($schoolRoleTitle); ?>
                                    </a>
                                    <br>
                                    <br>
                                    
                                </td>
                                <td>
									Email: <a href="mailto:<?php echo ($email); ?>">
									 <?php echo ($email); ?></a><br>
                                    Phone: <a href="tel:<?php echo ($phone); ?>">
                                        <?php echo($phone); ?>
                                    </a>
                                    <br>Cell Phone: <?php echo ($cellPhone); ?>
                                </td>
                               
                                <td style="text-align: center">
                                    <a class="<?php echo($buttoncss);?>" href="displayschoolsystemusertranssubmit.html?schoolId=<?php echo(EncodeQueryData($schoolId)); ?>&id=<?php echo(EncodeQueryData($systemUserMasterId)); ?>&newStatus=<?php echo($updateStatus); ?>&type=status">
                                        <?php echo($displayStatus); ?>
                                    </a>
									| 
									 <a class="<?php echo($buttoncss); ?>" href="displayschoolsystemusertranssubmit.html?schoolId=<?php echo(EncodeQueryData($schoolId)); ?>&id=<?php echo EncodeQueryData($systemUserMasterId); ?>&newblockStatus=<?php echo ($updateBlockStatus); ?>&type=block">
                                        <?php echo($displayBlockStatus); ?>
                                    </a>
									<?php if($isShowemail==1){?>
                                    | 
                                    <a href="javascript:void(0);" systemUserMasterId="<?php echo($systemUserMasterId); ?>" currentSchoolId="<?php echo($schoolId); ?>" class="loginEmailAjaxRow" schoolUserFullName="<?php echo($fullName); ?>">Send email</a>
                                    <?php } ?>
									| <a href="javascript:void(0)" class="loginAsSchoolUser" systemUserMasterId="<?php echo EncodeQueryData($systemUserMasterId); ?>"   currentSchoolId="<?php echo EncodeQueryData($schoolId); ?>"  schoolUserFullName="<?php echo($fullName); ?>" >Login as school user</a> 
                                    
                                </td>
                            </tr>
                            <?php


                            }
                        }
                    ?>



                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            });


            var current_datatable = $("#datatable-responsive").DataTable({
                "aoColumns": [{
                    "sWidth": "20%"
                }, {
                    "sWidth": "25%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "10%"
                }, 
                {
                    "sWidth": "40%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, ]
            });
            
            function ShowDeleteMessage()
            {
                alertify.alert('Warning','This is the Primary User. You can\'t delete this.');
            }

            $(document).on('click', '.loginAsSchoolUser', function() {
                var systemUserMasterId = $(this).attr('systemUserMasterId');
                var schoolUserFullName = $(this).attr('schoolUserFullName');
                var schoolId = $(this).attr('currentSchoolId');
				
                alertify.confirm('Login Confirmation', 'Continue with login as '+schoolUserFullName+'?', function() {
                    window.location.href='loginasschooluser.html?userId='+systemUserMasterId+' & schoolId='+schoolId;
                }, function() {});

            });

            $(document).on('click', '.loginEmailAjaxRow', function() {

                var systemUserMasterId = $(this).attr('systemUserMasterId');
                var schoolUserFullName = $(this).attr('schoolUserFullName');
                var currentSchoolId = $(this).attr('currentSchoolId');
                

                alertify.confirm('User: '+ schoolUserFullName, 'Continue with send login detail\'s email?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_login_email_to_school_user.html",
                        data: {
                            id: systemUserMasterId,
                            schoolId: currentSchoolId,
                            type:'systemuser'
                        },
                        success: function() {
                            alertify.success('Sent');
                        }
                    });
                }, function() {});

            });


             $(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var systemUserMasterId = $(this).attr('systemUserMasterId');
                var schoolUserFullName = $(this).attr('schoolUserFullName');
                
                alertify.confirm('User: '+schoolUserFullName, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: systemUserMasterId,
                            type: 'systemuser'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });

        </script>


    </body>

    </html>