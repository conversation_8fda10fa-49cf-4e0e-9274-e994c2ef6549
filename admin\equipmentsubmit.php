<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsEquipment.php'); 
    include('../class/clsQuestionOption.php'); 
	include('../setRequest.php');
	
	$userId = $_SESSION["loggedUserId"];
	//print_r($_POST);exit;
	 $studentEquipmentMasterId=0;	 
	 if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		$rotationId=0;	
		$equipmentrotationid=0;	
			
		if(isset($_GET['studentEquipmentMasterId'])) 
		{
			$studentEquipmentMasterId = $_GET['studentEquipmentMasterId'];
			$studentEquipmentMasterId = DecodeQueryData($studentEquipmentMasterId);
		}
		if(isset($_GET['equipmentrotationid'])) 
		{
			$equipmentrotationid = $_GET['equipmentrotationid'];
			$equipmentrotationid = DecodeQueryData($equipmentrotationid);
			
		}
				
			$studentEquipmentMasterId = isset($_GET['studentEquipmentMasterId']) ? DecodeQueryData($_GET['studentEquipmentMasterId']) : 0;
			$status = ($studentEquipmentMasterId > 0) ? 'updated' : 'added';
			
			if(isset($_GET['studentId'])) 
				{
					$student = $_GET['studentId'];
					$student = DecodeQueryData($student);
				
				}
				else
				{ 
					$student=($_POST['cbostudent']);
				}
				if(isset($_POST['cborotation']))
				{
					$equipmentrotationid=($_POST['cborotation']);
				}
				else if(isset($_POST['rotation']))
				{
					$equipmentrotationid=($_POST['rotation']);
				}
				else
				{
					$equipmentrotationid;
				}
			$cboclinician  = ($_POST['cboclinician']);	
			$equipmentUsageDate=GetDateStringInServerFormat($_POST['equipmentUsageDate']);
				
			$objEquipment = new clsEquipment();
			$objEquipment->rotationId =$equipmentrotationid;
			$objEquipment->schoolId =$currentSchoolId;
			$objEquipment->clinicanId =$cboclinician;				
			$objEquipment->studentId =$student;
			$objEquipment->equipmentUsageDate = $equipmentUsageDate;	
			$objEquipment->createdBy =$_SESSION["loggedUserId"];		
			$retEquipmentId = $objEquipment->SaveAdminEquipment($studentEquipmentMasterId);		
			$objEquipment->DeleteStudentEquipmentDetails($retEquipmentId);
			
			foreach($_POST as $id=>$value)
		{
			
			if (strpos($id, 'questionoptions_') === 0) 
			{			
			$id = explode("_", $id)[1];
			
			$objEquipment->studentEquipmentMasterId = $retEquipmentId;
			$objEquipment->schoolEquipmentQuestionId = $id;		 		
			$objEquipment->schoolEquipmentOptionValue = $value[0];
			$objEquipment->schoolEquipmentOptionAnswerText ='';	
			$studentEquipmentDetailId=$objEquipment->SaveStudentEquipmentDetail($retEquipmentId);
			}
		}
		foreach($_POST as $id=>$value)
		{
			
			if (strpos($id, 'questionoptionst_') === 0) 
			{			
				$id = explode("_", $id)[1];
				
				$objEquipment->studentEquipmentMasterId = $retEquipmentId;
				$objEquipment->schoolEquipmentQuestionId = $id;		 		
				$objEquipment->schoolEquipmentOptionValue ='';
				$objEquipment->schoolEquipmentOptionAnswerText =$value[0];	
				$studentEquipmentDetailId=$objEquipment->SaveStudentEquipmentDetail($retEquipmentId);
			}
		}
			
			unset($objEquipment);
			
			if($retEquipmentId > 0)
			{
				//Audit Log Start
				// Instantiate the Logger class
				$objLog = new clsLogger();

				// Determine the action type (EDIT or ADD) based on the presence of a ID
				$action =  ($studentEquipmentMasterId > 0) ? $objLog::EDIT : $objLog::ADD;
				$userType = $objLog::ADMIN; // User type is set to ADMIN
				

				$objEquipment = new clsEquipment();
				$objEquipment->saveEquipmentAuditLog($retEquipmentId,  $userId, $userType, $action);
				unset($objEquipment);

				unset($objLog);

				if(isset($_GET['studentId'])) 
				{
				header('location:equipmentlist.html?studentId='.EncodeQueryData($student).'&status='.$status);
				exit();
				}
				else
				{
				header('location:equipmentlist.html?studentEquipmentMasterId='.EncodeQueryData($studentEquipmentMasterId).'&equipmentrotationid='.EncodeQueryData($equipmentrotationid).'&status='.$status);	
				exit();
				}
			}
			else
			{
				header('location:equipment.html?status=error');
			}
		 
	}
	{
		header('location:equipmentlist.html');
		exit();
	}	
?>