<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');   
    include('../class/clsStudentMinCharacterEntry.php');
	include('../setRequest.php'); 

    
    $page_title ="Add Dr.Interaction Count";
   
    //For Journal Details
    $objJournal = new clsStudentMinCharacterEntry();
    $rowsjournal = $objJournal->GetStudentJournalEntryCount($currentSchoolId);
    $id = $rowsjournal['id']; 
    $interctionCount = $rowsjournal['interactionCharacterCount']; 
    unset($objJournal);
      
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">Dr.Interaction Response Count</li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

         <form id="frmRank" data-parsley-validate class="form-horizontal" method="POST" action="studentInteractionEntryCountSubmit.html?id=<?php echo $id;?>" >

            <div class="row">
                <div class="col-md-6">


					<div class="form-group">
                        <label class="col-md-4 padding_zero text-right" for="txtIntercationCount">Student Dr.Intercation <br>Minimum Character Count</label>
                        <div class="col-md-3 padding_right_zero">
                            <input id="txtIntercationCount"  name="txtIntercationCount" value="<?php echo($interctionCount); ?>" required type="text" placeholder="" class="form-control input-md required-input" 
                            data-parsley-validation-threshold="1" data-parsley-trigger="keyup" data-parsley-type="digits">
                        </div>
                    </div>
					
                     
                </div>
            </div>
				<div class="form-group">
                        <label class="col-md-2 control-label"></label>
						<div class="col-md-10">                       
                            <button id="btnSubmit" name="btnSubmit" class="btn btn-success margin_right_five">Save</button>
                            <a type="button" href="settings.html" class="btn btn-default">Cancel</a>
                     </div>
                </div>
        </form>


    </div>

    <?php include('includes/footer.php');?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>



    <script type="text/javascript">
 
        $(window).load(function(){

             $('#frmRank').parsley().on('field:validated', function() {
			    var ok = $('.parsley-error').length === 0;
            })
            .on('form:submit', function() {
                ShowProgressAnimation();									  
                return true; // Don't submit form for this demo
            });


        });
		
		

    </script>
    



</body>

</html>