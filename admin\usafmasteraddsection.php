<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');  
    include('../class/clsUsafMasterCheckoffSection.php');
    include('../class/clsUsafMasterCheckoffTopic.php');
    include('../class/clsusafEquiment.php');
	include('../setRequest.php'); 

    $schoolId = 0;  
	$schoolId= $currentSchoolId;
	$title ="Add Comps Section - ".$currenschoolDisplayname;   
    $page_title ="Add Comps Section";
    $sectionId = 0;
    $title = '';
    $sortOrder  = '';
	$sordOrder ='';
    $bedCrumTitle = 'Add';
    $arrSectionEquipmentList=array();
    $objusafEquiment  = new clsusafEquiment();
	if(isset($_GET['topicid']))
	{
		$topicid=DecodeQueryData($_GET['topicid']);
	}
    if(isset($_GET['editid'])) //Edit Mode
	{
        $sectionId = DecodeQueryData($_GET['editid']);
	    $page_title ="Edit Comps Section";
        $bedCrumTitle = 'Edit';

        $objCheckoffSectionMaster = new clsUsafMasterCheckoffSection();
		$row = $objCheckoffSectionMaster->GetUsafCheckoffTopicByCheckoffSectionId($sectionId);
        unset($objCheckoffSectionMaster);
        if($row =='')
        {
            
            header('location:usafmasteraddsection.html');
            exit;
        }

        $title  = stripslashes($row['title']); 
        $sortOrder  = stripslashes($row['sortOrder']);      
        $description  = stripslashes($row['description']);
        $description  = strip_tags($row['description']);
        
        //For Equipment List
        $sectionEquipmentList = $objusafEquiment->GetSectionEquipmentList($sectionId);
        $arrSectionEquipmentList = GetSingleFieldArrayFromResultSet($sectionEquipmentList, 'equipmentId');
	       
	}
	$objCheckoffTopicMaster=new clsUsafMasterCheckoffTopic();
    $GetSingleTopicId=$objCheckoffTopicMaster->GetSingleUsafCheckoffTopicId($topicid,$sectionId);
	$TopicTitleId=$GetSingleTopicId['title'] ?? '';
    unset($objCheckoffTopicMaster);
    
    //For Equiment List
   
    $equipmeentList = $objusafEquiment->GetAlldefaultusafequipmentlist();
    unset($objusafEquiment);
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

<style>
     .select2-container--default .select2-selection--multiple {
            min-height: 45px;
            background-color: #f6f6ff !important;
            border-radius: 12px !important;
            box-shadow: none !important;
            border: none !important;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__rendered li {
            text-wrap: wrap;
        }

        .select2-hidden-accessible~.select2-container--disabled {
            min-height: 45px !important;
            height: fit-content !important;
        }
</style>
    

</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
					 <li><a href="dashboard.html">Setting</a></li>
                    <?php if($topicid > 0) { ?> 
                    <li><a href="usafmastercheckofftopic.html">Military Comps Topics</a></li>
                    <li><a href="usafmastercheckoffsection.html?topicid=<?php echo EncodeQueryData($topicid); ?>"><?php echo ($TopicTitleId); ?>-Comps Section</a></li>
                    <?php } else {?>
                    <li><a href="usafmasterviewsection.html">Military Comps Section</a></li>
                    <?php } ?>
                    <li class="active"><?php echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

         <form id="frmcheckoff" data-parsley-validate class="form-horizontal" method="POST" <?php if(isset($_GET['editid'])){?> action="usafmastercheckoffsectionsubmit.html?editid=<?php echo(EncodeQueryData($sectionId)); ?>&topicid=<?php echo EncodeQueryData($topicid); ?>" <?php } else { ?> action="usafmastercheckoffsectionsubmit.html?topicid=<?php echo EncodeQueryData($topicid); ?>" <?php } ?> >

            <div class="row">
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtcheckoffsection">Comps Section</label>
                        <div class="col-md-12">
                            <input  id="txtcheckoffsection"  name="txtcheckoffsection" value="<?php echo($title); ?>"  type="text"  class="form-control input-md required-input" required>

                        </div>
                    </div>
                </div>
                <div class="col-md-6">					
					<div class="form-group">
                        <label class="col-md-12 control-label" for="txtsortorder">Section Number</label>
                        <div class="col-md-12">
                            <input  id="txtsortorder"  name="txtsortorder" value="<?php echo($sortOrder); ?>"  type="text"  class="form-control input-md required-input" required>

                        </div>
                    </div>
                </div>
            </div>
                <div class="col-md-6">
            <div class="form-group">
                <label for="recipient-name" class="col-md-12 px-0 control-label alignmentDropdown" >Equipment </label>
                <div class="col-md-12 input-md required-input p-0" style="padding: 0 3px;border-radius: 12px !important;">
                    <select id="equipmentListId" name="equipmentList[]" multiple="multiple" class="form-control required-input input-md select2_tags" style="width:100%">
                        <?php
                            if($equipmeentList !="")
                            {
                                while($row = mysqli_fetch_assoc($equipmeentList))
                                {
                                    $equipmentId  = $row['equipmentId'];
                                    $equipmenttitle  = stripslashes($row['title']);

                                    ?>
                                    <option  value="<?php echo($equipmentId); ?>" <?php if( in_array( $equipmentId,$arrSectionEquipmentList)){ ?>  selected="true" <?php } ?>><?php echo($equipmenttitle); ?></option>
                                    <?php
                                }
                            }
                        ?>
                    </select>
                </div>
            </div> 
                </div>
                <div class="col-md-6">

            <div class="form-group">
                <label class="col-md-12 control-label" for="txtdescription">Description</label>
                <div class="col-md-12">
                    <textarea  id="txtdescription"  name="txtdescription"  type="text"  class="form-control input-md " rows="4" cols="100" ><?php if(isset($_GET['editid'])){ echo($description); } ?></textarea>

                </div>
            </div>
                </div>
                        
				
				<div class="form-group">
                        <!-- <label class="col-md-2 control-label"></label> -->
                        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                        <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                            <?php if($topicid > 0) { ?>
                            <a type="button" href="usafmastercheckoffsection.html?topicid=<?php echo EncodeQueryData($topicid); ?>" class="btn btn-default">Cancel</a>
                            <?php } else { ?>
                            <a type="button" href="usafmasterviewsection.html?topicid=<?php echo EncodeQueryData($topicid); ?>" class="btn btn-default">Cancel</a>
                            <?php } ?>
						</div>
                </div>
        </form>


    </div>

    <?php include('includes/footer.php');?>
   
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>

	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <!-- <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/ckeditor.js"></script> -->
    <script type="text/javascript">
    

        $(window).load(function(){

            $(".select2_tags").select2({'placeholder':'Select'}); //for multiple selection
           // CKEDITOR.replace('txtdescription');

            // CKEDITOR.replace('txtdescription',{
            //     enterMode: CKEDITOR.ENTER_BR,
            //     shiftEnterMode:CKEDITOR.ENTER_P
            // });
            // ClassicEditor
 			// .create(document.querySelector('#txtdescription'))
 			// .catch(error => {
 			// 	console.error(error);
 			// });	

             $('#frmcheckoff').parsley().on('field:validated', function() {
			    var ok = $('.parsley-error').length === 0;
            })
            .on('form:submit', function() {
                ShowProgressAnimation();									  
                return true; // Don't submit form for this demo
            });
		
        });
		
	
       
    </script>

 
    



</body>

</html>