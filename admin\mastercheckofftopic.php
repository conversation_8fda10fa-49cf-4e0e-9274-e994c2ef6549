<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');       
    include('../class/clsMasterCheckoffTopic.php');       
    include('../setRequest.php'); 	
	
	$type = isset($_GET['type']) ? $_GET['type'] : ''; 
	$currentSchoolId;
	$defaultTopicId =0;
	if(isset($_GET['defaultTopicId']))
	{
		$defaultTopicId=DecodeQueryData($_GET['defaultTopicId']);
	}
	$objCheckoffTopicMaster = new clsMasterCheckoffTopic();			
	$rowsCheckoffTopics = $objCheckoffTopicMaster->GetAllCheckoffTopic();
	$totalCount = 0;
	if($rowsCheckoffTopics !='')
	{
		$totalCount = mysqli_num_rows($rowsCheckoffTopics);
	}
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Comps Steps</title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css"> 
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

        <style>
            .center{
                text-align:center;
            }

        </style>
    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Setting</a></li>						
						<li class="active">Comps</li>						
                    </ol>
                </div>
				
               <div class="pull-right margin_top_seven"> 
                    <a href="masteraddcheckofftopic.html">Add</a>
                    | <a class="importCheckoff" href="importSuperadminCheckoff.html?type=1">Import Topic</a>
                    | <a class="importCheckoff" href="importSuperadminCheckoff.html?type=2">Import Section</a>
                    | <a class="importCheckoff" href="importSuperadminCheckoff.html?type=3">Import Steps</a>
               </div>
			   
            </div>
        </div>

        <div class="container">


            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Comps Topic added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Comps Topic updated successfully.
                </div>
                <?php 
					}                   
                    else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Imported")
					{
                        if($type == 1)
                            $importTitle = 'Topics';
                        elseif($type == 2)
                            $importTitle = 'Sections';
                        elseif($type == 3)
                            $importTitle = 'Steps';
						?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                                </button> Comps <?php echo  $importTitle; ?> added successfully.
                    </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
				<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>							
                           <th>Comps ID</th>		   
                           <th>Comps Title</th>		   
                           <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalCount > 0)
                        {
                            while($row = mysqli_fetch_array($rowsCheckoffTopics))
                            {								
                                $defaultTopicId = ($row[0]);					
                                $checkoffTitleId = ($row['checkoffTitleId']);					
                                $topicTitle = ($row['title']); 
								$sectionCount = ($row['sectionCount']); 
                               
                               ?>
                            <tr>
								
								<td style="text-align: center"><?php echo ($checkoffTitleId); ?></td>
								<td><?php echo ($topicTitle); ?></td>
								<td class="center">	
								<a  href="mastercheckoffsection.html?topicid=<?php echo EncodeQueryData($defaultTopicId); ?>">Section</a>
									<span class="badge"><?php echo($sectionCount); ?></span>
								|
								<a  href="masteraddcheckofftopic.html?editid=<?php echo EncodeQueryData($defaultTopicId); ?>">Edit</a>								
								|  <a href="javascript:void(0);" class="deleteAjaxRow"
								 defaultTopicId="<?php echo EncodeQueryData($defaultTopicId); ?>" topicTitle="<?php echo($topicTitle); ?>">Delete</a>
                               
                                
								</td>
                            </tr>
                            <?php
                            }
                        }
                    ?>
					</tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
		<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
		<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
		
        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";
				
            $('.importCheckoff').magnificPopup({
                type:'ajax',
                'closeOnBgClick': false
            });
			
            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
				$(".select2_single").select2();
				 });
			
			
                 var current_datatable = $("#datatable-responsive").DataTable({
                    'iDisplayLength': 250
					
				 });    

		//delete student
				$(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var defaultTopicId = $(this).attr('defaultTopicId');
                var topicTitle = $(this).attr('topicTitle');
                
                alertify.confirm('Comps Topic: '+topicTitle, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: defaultTopicId,
                            type: 'Master_PEF_Checkoff_Topic'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });
			
			

        </script>
    </body>
    </html>