<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../includes/commonfun.php');
include('../class/clsRotation.php');
include('../class/clsImmunization.php');
include('../class/clsClinicianImmunization.php');
include('../class/clsStudent.php');
include('../class/clsClinician.php');

include('../setRequest.php');
include('../class/clsRotationDetails.php');

//Get School Name
$objSchool  = new clsSchool();
$objimmunization = new clsImmunization();
$displaySchoolName = $objSchool->GetSchoolName($currentSchoolId);
unset($objSchool);
$immunizationDate = '';
$studentname = '';
$totalimmunization = 0;
$clinicianImmunizationId = 0;
$Immunization = 0;
$clinicianId = 0;
$rankId = 0;
$Type = 'I';
$clinicianId = 0;
$objStudent = new clsStudent();
if (isset($_GET['rankId'])) {
    $rankId = $_GET['rankId'];
    $rankId = DecodeQueryData($rankId);
}

if (isset($_GET['clinicianId'])) //Edit Mode
{
    $clinicianId = DecodeQueryData($_GET['clinicianId']);

    $objClinician = new clsClinician();
    $clinician = $objClinician->GetClinicianDetails($clinicianId);
    $firstName = isset($clinician['firstName']) ? $clinician['firstName'] : '';
    $lastName = isset($clinician['lastName']) ? $clinician['lastName'] : '';
    $fullName = $firstName . ' ' . $lastName;
    unset($objClinician);
}

$objClinicianImmmulization = new clsClinicianImmunization();
$rowsimmunization = $objClinicianImmmulization->GetAllSingleClinicianImumunizationDetails($clinicianImmunizationId, $clinicianId);

if ($rowsimmunization != '') {
    $totalimmunization = mysqli_num_rows($rowsimmunization);
}

unset($objClinicianImmmulization);
$reportType = "ClinicianImmulizationReport";

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Evaluator Immunization</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">


    <style>
        input:read-only {
            background-color: #EEF0F0 !important;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="schoolclinicians.html">Evaluator</a></li>
                    <li><a href="schoolclinicians.html?clinicianId=<?php echo EncodeQueryData($clinicianId); ?>"><?php echo $fullName; ?></a></li>
                    <li class="active">Immunization</li>
                </ol>
            </div>

            <div class="pull-right">

                <form id="frmexportreport" data-parsley-validate class="form-horizontal" method="POST" action="exportClinicianImmuList.html?Type=<?php echo ($reportType); ?>&currentSchoolId=<?php echo (EncodeQueryData($currentSchoolId)); ?>&clinicianId=<?php echo (EncodeQueryData($clinicianId)); ?>" enctype="multipart/form-data">
                    <input type="hidden" name="cboreporttype" value="<?php echo ($reportType); ?>">
                    <input type="submit" name="btnStudentExport" id="btnStudentExport" class="btn btn-link" style="background-color: #EEF0F0 !important;" value="Export to Excel">|
                    <a class="btn btn-link" href="addClinicianImmunization.html?clinicianId=<?php echo EncodeQueryData($clinicianId);?>">Add</a>
                </form>

            </div>
        </div>
    </div>

    <div class="container">
        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Evaluator Immunization Added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Evaluator Immunization updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Evaluator Immunization deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>

        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <?php $currentDate = date("m/d/Y");
                if (date("m/d/Y", strtotime($currentDate))) { ?>
                    <tr style="background:red;">
                    <?php } else { ?>
                    <tr>
                    <?php } ?>



                    <th style="text-align:left">Immunization</th>
                    <th style="text-align:center">Date</th>
                    <th style="text-align:center">Notification Date</th>
                    <th style="text-align:center">Expiration Date</th>
                    <th style="text-align:center">Action</th>
                    </tr>
            </thead>
            <tbody>
                <?php
                if ($totalimmunization > 0) {


                    while ($row = mysqli_fetch_array($rowsimmunization)) {
                        $immunizationMId = $row['immunizationMId'];
                        $expiryDays  = $row['expiryDays'];
                        $ImmunizationId = $row['clinicianImmunizationId'];
                        $shortName = $row['shortName'];
                        $firstName = stripslashes($row['firstName']);
                        $lastName = stripslashes($row['lastName']);
                        $immunizationDate = $row['immunizationDate'];
                        $immunizationDate = date("m/d/Y", strtotime($immunizationDate));
                        $immunizationNotificationDate = $row['immunizationNotificationDate'];
                        $immunizationNotificationDate = date("m/d/Y", strtotime($immunizationNotificationDate));
                        $ExpiryDate = $row['expiryDate'];
                        $ExpiryDate = date("m/d/Y", strtotime($ExpiryDate));

                        $clinicianId = $row['clinicianId'];
                        $fullName = $firstName . ' ' . $lastName;

                        $TotaldocumentCount = $objStudent->getDocumentsCount($clinicianId, $Type, $ImmunizationId);
                        $countDocument = $TotaldocumentCount['countDocument'];
                        //for validte ExpiryDate days

                        if ($expiryDays > 0) {
                            $currentDate = date("m/d/Y");
                            $validateDays = strtotime('+' . $expiryDays . 'days', strtotime($immunizationDate));
                            $validateDays = date('m/d/Y', $validateDays);
                            $validateDays = date("m/d/Y", strtotime($validateDays));
                        } else {
                            $validateDays = '-';
                        }

                ?>
                        <?php
                        if ((strtotime($currentDate)) >= (strtotime($ExpiryDate)) && $expiryDays != 0) { ?>
                            <tr class="updaterow">
                            <?php } else { ?>
                            <tr>
                            <?php } ?>

                            <td>
                                <?php echo ($shortName); ?>
                            </td>

                            <td style="text-align:center">
                                <?php
                                if ($immunizationDate != '' && $immunizationDate != '0000-00-00' &&  $immunizationDate != '01/01/1970') {
                                    echo $immunizationDate;
                                } else {
                                    echo "-";
                                }
                                ?>
                            </td>

                            <td style="text-align:center">
                                <?php

                                if ($expiryDays == 0) {
                                    echo "N/A";
                                } else if ($immunizationNotificationDate != '' && $immunizationNotificationDate != '0000-00-00' &&  $immunizationNotificationDate != '01/01/1970' && $immunizationNotificationDate != '11/30/-0001') {
                                    echo $immunizationNotificationDate;
                                } else {
                                    echo "-";
                                }

                                ?>

                            <td style="text-align:center">

                                <?php

                                if ($ExpiryDate != '-') {
                                    if ($expiryDays != 0) {
                                        if ((strtotime($currentDate)) >= (strtotime($ExpiryDate))) {

                                ?>
                                            <?php
                                            if ($ExpiryDate != '' && $ExpiryDate != '0000-00-00' &&  $ExpiryDate != '01/01/1970' && $ExpiryDate != '11/30/-0001') {
                                                echo ($ExpiryDate);
                                            } else {
                                                echo "-";
                                            }

                                            ?><br>
                                            <small style="text-align:center;color: red">Expired</small>
                                    <?php                 } else {
                                            echo ($ExpiryDate);
                                        }
                                    } else {
                                        echo "N/A";
                                    }
                                } else { ?>
                                    <?php echo ($ExpiryDate); ?> <br>
                                    <small style="text-align:center;color:green">No Expiry Date</small>
                                <?php } ?><br>
                            </td>

                            <td style="text-align: center">
                                <a href="clinicianDocuments.html?clinicianIdImmunizationId=<?php echo (EncodeQueryData($ImmunizationId)); ?>&clinicianId=<?php echo (EncodeQueryData($clinicianId)); ?>&Type=I">Documents</a>
                                <?php if ($countDocument > 0) { ?><span class="badge"><?php echo ($countDocument); ?></span> <?php } ?>|
                                    <a class="addCommentpopup " title="Preview Immunization Details"
                                        href="clinicianImmunizationDetails.html?clinicianIdImmunizationId=<?php echo EncodeQueryData($ImmunizationId); ?>&clinicianId=<?php echo (EncodeQueryData($clinicianId)); ?>">View </a>|
                                    <a href="editclinicianImmunization.html?clinicianIdImmunizationId=<?php echo (EncodeQueryData($ImmunizationId)); ?>&clinicianId=<?php echo (EncodeQueryData($clinicianId)); ?>">Edit</a>
                                    |<a href="javascript:void(0);" class="deleteAjaxRow" ImmunizationId="<?php echo EncodeQueryData($ImmunizationId); ?>"> Delete</a>
                            </td>

                            </tr>
                    <?php


                    }
                }
                unset($objStudent);
                    ?>

            </tbody>
        </table>
    </div>
    </div>


    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>

    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>


    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $('.addCommentpopup').magnificPopup({
            'type': 'ajax',
        });

        $(window).load(function() {

            $("#divTopLoading").addClass('hide');




        });


        var current_datatable = $("#datatable-responsive").DataTable({
            "ordering": true,
            "order": [
                [1, "desc"]
            ],

            "aoColumns": [{
                "sWidth": "10%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "10%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "10%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "10%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "20%",
                "sClass": "alignCenter",
                "bSortable": false
            }]
        });

        //delete student
        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var ClinicianImmunizationId = $(this).attr('ImmunizationId');

            var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';
            var isUser = 1; //for Administrators    

            alertify.confirm('Immunization ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: ClinicianImmunizationId,
                        userId: userId,
                        isUser: isUser,
                        type: 'ClinicianImmunization'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });
    </script>


</body>

</html>