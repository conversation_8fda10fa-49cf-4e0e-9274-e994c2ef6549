<?php
	ini_set('display_errors', 1);
	ini_set('display_startup_errors', 1);
	error_reporting(E_ALL);
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSummative.php');
    include('../class/clsQuestionOption.php'); 
	include('../setRequest.php');
	

	 $studentSummativeMasterId=0;
	 $summativerotationid=0;
		//	PRINT_R($_POST);EXIT;
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
			$rotationId=0;
			$schoolIncidentQuestionId='';
			$schoolIncidentQuestionType='';
			
		if(isset($_GET['studentSummativeMasterId'])) 
		{
			$studentSummativeMasterId = $_GET['studentSummativeMasterId'];
			$studentSummativeMasterId = DecodeQueryData($studentSummativeMasterId);
		}
		if(isset($_GET['summativerotationid'])) 
		{
			$summativerotationid = $_GET['summativerotationid'];
			$summativerotationid = DecodeQueryData($summativerotationid);
			
		}
			$studentSummativeMasterId = isset($_GET['studentSummativeMasterId']) ? DecodeQueryData($_GET['studentSummativeMasterId']) : 0;
			$status = ($studentSummativeMasterId > 0) ? 'updated' : 'added';	
			
			$objsummative = new clsSummative();
			$questionIds=$objsummative->GetQuestionId(); 
			
			
			
			if(isset($_GET['studentId'])) 
				{
					$student = $_GET['studentId'];
					$student = DecodeQueryData($student);
				
				}
				else
				{ 
					//$student=isset(($_POST['cbostudent'])?($_POST['cbostudent']):0);
					$student=isset($_POST['cbostudent']) ? $_POST['cbostudent'] : 0;
				}
				
			if(isset($_GET['summativerotationid']))
				{
					$summativerotationid = $_GET['summativerotationid'];
					$summativerotationid = DecodeQueryData($summativerotationid);
				}
			elseif  (isset($_POST['cborotation']))
				{ 
					$summativerotationid  = ($_POST['cborotation']);
				}
			else
			{
			    $summativerotationid=($_POST['rotationhidden']);
			}
				
			//$cboclinician  = ($_POST['cboclinician']);
			$cboclinician  = isset($_POST['cboclinician']) ? $_POST['cboclinician'] : 0;
			
			$evaluationDate=GetDateStringInServerformat($_POST['evaluationDate']);		
			$evaluationDate = str_replace('00:00:00','12:00 PM',$evaluationDate);
			$evaluationDate = date('Y-m-d H:i',strtotime($evaluationDate));
			
			$objsummative->schoolId =$currentSchoolId;
			$objsummative->rotationId =$summativerotationid;
			$objsummative->clinicanId =$cboclinician;				
			$objsummative->studentId =$student;	
			$objsummative->createdBy =$_SESSION["loggedUserId"];
			$objsummative->evaluationDate = $evaluationDate;	   
			$retsummativeId = $objsummative->SaveAdminSummative($studentSummativeMasterId);	
			
			$objsummative->DeleteStudentSummativeDetails($retsummativeId);
				
		foreach($_POST as $id=>$value)
		{
			
			if (strpos($id, 'questionoptions_') === 0) 
			{			
			$id = explode("_", $id)[1];

			$objsummative->studentSummativeMasterId = $retsummativeId;
			$objsummative->schoolSummativeQuestionId = $id; 
			$objsummative->schoolSummativeOptionValue = $value[0]; 		
			$objsummative->schoolSummativeOptionAnswerText = ''; 		
			$studentsummativeDetailId=$objsummative->SaveStudentSummativeDetail($retsummativeId); 
			}
			
			if (strpos($id, 'questionoptionst_') === 0) 
			{			
			$id = explode("_", $id)[1];
	
			$objsummative->studentSummativeMasterId = $retsummativeId;
			$objsummative->schoolSummativeQuestionId = $id; 
			$objsummative->schoolSummativeOptionValue = ''; 		
			$objsummative->schoolSummativeOptionAnswerText = $value[0]; 		
			$studentsummativeDetailId=$objsummative->SaveStudentSummativeDetail($retsummativeId); 
			}
		} 
			
			unset($objsummative);
			
			if($retsummativeId > 0)
			{
				
				if(isset($_GET['studentId'])) 
				{
					header('location:summativelist.html?studentId='.EncodeQueryData($student).'&status='.$status);
					exit();
				}
				else
				{
					header('location:summativelist.html?studentSummativeMasterId='.EncodeQueryData($studentSummativeMasterId).'&summativerotationid='.EncodeQueryData($summativerotationid).'&status='.$status);	
					exit();	
				}
			}
			else
			{
				header('location:summative.html?status=error');
			}
			
	}
	{
		header('location:summativelist.html?studentSummativeMasterId='.EncodeQueryData($studentSummativeMasterId).'&summativerotationid='.EncodeQueryData($summativerotationid).'&status='.$status);	
		exit();
	}	
?>