<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');   
    include('../class/clsStudent.php');
    include('../class/clsPEvaluation.php');
	include('../class/clsRotation.php');
	include('../setRequest.php');
	
	$loggedUserId = $_SESSION["loggedUserId"];   
    $TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
    $isActiveCheckoff='';
    $isDefaultCiEval = '';
    $isActiveCheckoff =$_SESSION["isActiveCheckoff"];
    $isDefaultCiEval = $_SESSION["isDefaultCiEval"];
	$loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0; 
    
    $canvasStatus= isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';
    $type = isset($_GET['type']) ? $_GET['type'] : '';
	if($type == 'canvas')
		$canvasStatus =1;

    $pevaluationrotationid = 0;
    $schoolId = 0;
    $currentstudentId = 0;
    $pEvaluationMasterId = 0;
	$schoolId = $currentSchoolId;
    $transchooldisplayName = '';
    $encodedRotationId = '';
    if(isset($_GET['pevaluationrotationid'])) //Edit Mode
	{
		$encodedRotationId = $_GET['pevaluationrotationid'];
		$pevaluationrotationid = $_GET['pevaluationrotationid'];
        $pevaluationrotationid = DecodeQueryData($pevaluationrotationid);
    }
    $transchooldisplayName=$currenschoolDisplayname;
    $encodedStudentId = '';
	if(isset($_GET['studentId']))
	{
		$encodedStudentId = $_GET['studentId'];
		$currentstudentId = $_GET['studentId'];
        $currentstudentId = DecodeQueryData($currentstudentId);
    }

    $title ="T Evaluation| ".$transchooldisplayName;  
        
    //For P Evalution List
    $objPevaluation=new clsPEvaluation();
    $getPevaluationdetails=$objPevaluation->GetAllEvaluation($pevaluationrotationid,$currentstudentId,$canvasStatus,$currentSchoolId);
    $totalCIevaluationCount = ($getPevaluationdetails !='') ? mysqli_num_rows($getPevaluationdetails) : 0;
    
    //For Rotation Title
    $objRotation=new clsRotation();
    $RotationName=$objRotation->GetrotationDetails($pevaluationrotationid,$schoolId);
    $rotationtitle= isset($RotationName['title']) ? $RotationName['title'] : '';
    unset($objRotation);
    $objStudent=new clsStudent();
    $Rowstudent=$objStudent->GetSingleStudent($schoolId,$currentstudentId);
    $studentfullname= $Rowstudent ? ($Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']) : '';
    unset($objStudent);
    
?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo($title); ?></title>
        <?php include('includes/headercss.php');?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
        <?php include("includes/datatablecss.php") ?>
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css"> 

		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <?php if($type == 'canvas') { ?>
							<li><a href="dashboard.html">Home</a></li>
							<li><a href="settings.html">Settings</a></li>
							<li class="active">T Evaluation</li>
						<?php } else { ?>
                            <li><a href="dashboard.html">Home</a></li>
                            <?php if(isset($_GET['studentId'])) 
                                { ?> <li><a href="clinical.html">Clinical</a></li>
                                <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>				
                                    
                                <?php } else { ?>
                            <li><a href="rotations.html">Rotations</a></li>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                            <?php } ?>
                            <li class="active">T Evaluation</li>
                        <?php } ?>
                       
                    </ol>
                </div>
         
            </div>
        </div>

        <div class="container">

            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> T Evaluation added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> T Evaluation updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Deleted")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> T Evaluation deleted successfully.
                </div>
                <?php 
					}
                     else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>
			
                <div id="divTopLoading" >Loading...</div>
                <?php if($isActiveCanvas && $type !='canvas') { ?>
                <!-- <div class="row margin_bottom_ten">
                    <div class="col-md-8"></div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-6 control-label text-right" for="" style="margin-top:8px">Canvas Status</label>
                            <div class="col-md-6 padding_right_zero padding_left_zero">
                                <select id="canvasStatus" name="canvasStatus" class="form-control input-md required-input select2_single"  >
                                    <option value="" selected>All</option>
                                    <option value="1" <?php if($canvasStatus==1) { ?> selected="true" <?php } ?>>Sent</option>
                                    <option value="0" <?php if($canvasStatus==0 && $canvasStatus!='' ) { ?> selected="true" <?php } ?>>Not Sent</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div> -->
                <?php } ?>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th class="text-left">First Name</th>
                            <th class="text-left">Last Name</th>                           
                            <th class="text-left">Rotation</th> 
                            <th class="text-left">Hospital Site</th>                           
                            <th style="text-align: center">Evaluation<br>Date</th>                           
                            <th style="text-align: center">Average<br>Rating</th>
                            
                            <?php if($type !='canvas') { ?>
                            <th style="text-align: center">Action</th>
                            <?php } ?>
                            <?php if($isActiveCanvas && $type !='canvas') { ?>
                                <!-- <th class="text-center">Canvas Status</th> -->
                            <?php } ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalCIevaluationCount > 0)
                        {
                            while($row = mysqli_fetch_array($getPevaluationdetails))
                            {
								
                                $pEvaluationMasterId = $row['StudentPEvaluationMasterId'];
                                $hospitalSiteTitle = $row['hospitalSiteTitle'];
								$pevaluationrotationid = $row['rotationId'];
								$DBstudentId = $row['studentId'];
								$rotationtitle = $row['title'];
								$firstName = $row['firstName'];
								$lastName = $row['lastName'];
                                $studentName = $firstName . ' ' . $lastName;
                                
                                $evaluationDate = stripslashes($row['evaluationDate']);					
                                $evaluationDate = converFromServerTimeZone($evaluationDate,$TimeZone);
                                $evaluationDate =date("m/d/Y",strtotime($evaluationDate));

                                //For Average Rating
								$GetCIevaluationScore=$objPevaluation->GetEvaluationScore($pEvaluationMasterId);
                                $EvaluationScore=$GetCIevaluationScore['EvaluationScore'];
                                $EvaluationScore = number_format((float)$EvaluationScore, 2, '.', '');


                                $isApproved = $row['isApproved'];    
                                if($isApproved > 0)
                                    $actiontype="false";
                                else
                                    $actiontype="true";
                                 

                                // For Canvas
								$isSendToCanvas  = isset($row['isSendToCanvas']) ? $row['isSendToCanvas'] : 0;

                                $isSendToCanvasClass ='isSendRecordToCanvas';
                                $isSentToCanvasClass ='hide';
                                if($isSendToCanvas)
                                {
                                    $isSendToCanvasClass ='hide';
                                    $isSentToCanvasClass ='';
                                }

								$isUserCanSendCompletedRecordToCanvas = 0;
								if($evaluationDate)
									$isUserCanSendCompletedRecordToCanvas = 1;
                                
                                /// End Canvas ///
                                
                                
                                ?>
                            <tr>
								<td><?php echo($firstName); ?></td>
								<td><?php echo($lastName); ?></td>                               
								<td><?php echo($rotationtitle); ?></td>  
                                <td><?php echo($hospitalSiteTitle); ?></td>                              
                                <td style="text-align: center"><?php echo ($evaluationDate); ?></td>
                                 <td style="text-align: center"><?php echo $EvaluationScore; ?>
								</td>	
                               
                                	
                                <?php if($type !='canvas') { ?>				 
                                <td style="text-align: center">
                                <?php
                                $rotationStatus = checkRotationStatus($pevaluationrotationid);
                               
                                if ($rotationStatus) { ?>
                                  <a href="addPEvaluation.html?pEvaluationMasterId=<?php echo(EncodeQueryData($pEvaluationMasterId)); ?>
									&pevaluationrotationid=<?php echo(EncodeQueryData($pevaluationrotationid));?>&view=1">View</a> |
							
								<?php } elseif(isset($_GET['studentId']))
								{?>
                                    <a href="addPEvaluation.html?pEvaluationMasterId=<?php echo(EncodeQueryData($pEvaluationMasterId)); ?>
									&pevaluationrotationid=<?php echo(EncodeQueryData($pevaluationrotationid));?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Edit</a> |
								<?php } else { ?>	
									<a href="addPEvaluation.html?pEvaluationMasterId=<?php echo(EncodeQueryData($pEvaluationMasterId)); ?>
									&pevaluationrotationid=<?php echo(EncodeQueryData($pevaluationrotationid));?>">Edit</a> |
							
								<?php } ?>								
									<a href="javascript:void(0);" class="deleteAjaxRow"
									pEvaluationMasterId="<?php echo EncodeQueryData($pEvaluationMasterId); ?>" studentName="<?php echo($studentName); ?>" >Delete</a>
                                </td>
                                <?php } ?>
                                  
                            </tr>
                            <?php
                            }
                        }
                        unset($objPevaluation);
                    ?>
                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script> 

        <script type="text/javascript">

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
                $(".select2_single").select2();
            });
			
            var current_datatable = $("#datatable-responsive").DataTable({
                "ordering": true,
                "order": [[4, "desc" ]],
                "aoColumns": [
                {"sWidth": "10%"},
                {"sWidth": "10%"},
                {"sWidth": "15%"},
                {"sWidth": "15%"},
                {"sWidth": "15%"},
                {"sWidth": "10%"},
                {"sWidth": "10%"},
                ]
            });

            // ajax call for deleteAjaxRow
             $(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var pEvaluationMasterId = $(this).attr('pEvaluationMasterId');
                var title = $(this).attr('studentName');
                var UserId = '<?php echo EncodeQueryData($loggedUserId); ?>';

               
                alertify.confirm('Student T Evaluation: '+ title, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: pEvaluationMasterId,
                            userId: UserId,
                            type: 'PEvaluation'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });

            $("#datatable-responsive").on("click", ".ciEvalstatus", function() {	

                var action;	
                var pEvaluationMasterId = $(this).attr('pEvaluationMasterId');
                var loggedUserId = $(this).attr('loggedUserId');
                var Status = $(this).val();
                var thischeck= $(this);
                var actiontype = $(this).attr('actiontype');
                $.ajax({
                    type: "POST",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_ciEval_approve.html",
                    data: {	
                    
                        id: pEvaluationMasterId, 
                        action : actiontype,
                        Status: Status,
                        loggedUserId: loggedUserId,
                        type: 'Status'	
                        
                    },
                    success: function() {                         
                        
                        if(actiontype == 'true'){  //Assigned 
                            alertify.success('Approved');
                            thischeck.attr('actiontype','false'); }
                            
                        else if(actiontype == 'false') {//Removed
                            alertify.error('Disapprove');
                            thischeck.attr('actiontype','true'); }
                    }
                });
                
            });

            //Send Records To Canvas
			$(document).on('click', '.isSendRecordToCanvas', function() {

                var that = this;
                var evaluationDate = $(this).attr('evaluationDate');
                var clinicianName = $(this).attr('clinicianName');
                var pEvaluationMasterId = $(this).attr('pEvaluationMasterId');
                var avgRating = $(this).attr('avgRating');
                var rotation = $(this).attr('rotation');
                var schoolId = "<?php echo $currentSchoolId; ?>";
                var studentfullname = $(this).attr('studentfullname');
                var studentId = $(this).attr('studentId');

                alertify.confirm('CI Evalution ', 'Continue with send record to Canvas?', function() {
                    $(that).text('Loading..');
                    $(that).prop('disabled',true);

                    $.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_record_to_canvas.html",
                        data: {
                            
                            evaluationDate: evaluationDate,
                            clinicianName: clinicianName,
                            pEvaluationMasterId: pEvaluationMasterId,
                            avgRating: avgRating,
                            rotation: rotation,
                            studentFullName: studentfullname,
                            studentId: studentId,
                            schoolId: schoolId,
                            type: 'CIEval'
                        },
                        success: function(response) {
                            if(response == 'Success')
                            {
                                $(that).addClass('hide');
                                $('.isSentToCanvas_'+pEvaluationMasterId).removeClass('hide')
                                alertify.success('Record Successfully Sent to Canvas.');
                            }
                            else
                            {
                                alertify.success(response);
                            }
                            
                        }
                    });
                }, function() {});

            });

            $("#canvasStatus").change(function(){
                var canvasStatus=$(this).val();
                var studentId='<?php echo $encodedStudentId; ?>';
                var rotationId='<?php echo $encodedRotationId; ?>';

                if(studentId !='')
                {
                    if(canvasStatus !='')
                        window.location.href = "cievaluationlist.html?studentId="+studentId+"&canvasStatus="+canvasStatus;
                    else
                        window.location.href = "cievaluationlist.html?studentId="+studentId;
                }
                else if(rotationId !='')
                {
                    if(canvasStatus !='')
                        window.location.href = "cievaluationlist.html?pevaluationrotationid="+rotationId+"&canvasStatus="+canvasStatus;
                    else
                        window.location.href = "cievaluationlist.html?pevaluationrotationid="+rotationId;
                }
               
                
            });
        </script>


    </body>

    </html>