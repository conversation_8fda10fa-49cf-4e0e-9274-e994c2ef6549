<?php
$ServerRootPath    = '/www/wwwroot/rad.clinicaltrac.net';
$ServerRootPath    = 'C:/xampp/htdocs/clinicaltrac';
// echo 'hi';
// exit;
include($ServerRootPath . '/includes/config.php');
include($ServerRootPath . '/includes/commonfun.php');
include($ServerRootPath . '/class/clsDB.php');
// include($ServerRootPath . '/setRequest.php');
include($ServerRootPath . '/class/clsCheckoffSectionMaster.php');
include($ServerRootPath . '/class/clsCheckoffQuestionMaster.php');
include($ServerRootPath . '/class/clsCheckoffTopicMaster.php');
include($ServerRootPath . '/class/clsAdvanceMasterCheckoffTopic.php');
include($ServerRootPath . '/class/clsAdvanceMasterCheckoffSection.php');
include($ServerRootPath . '/class/clsAdvanceMasterCheckoffQuestion.php');

$objCheckoffTopic = new clsAdvanceMasterCheckoffTopic();
$rowsCheckoffTopics = $objCheckoffTopic->GetAllAdvanceCheckoffTopicImport();
$rowsCheckoffTopicsCount = ($rowsCheckoffTopics != '') ? mysqli_num_rows($rowsCheckoffTopics) : 0;
$currentSchoolId = 1;
$id = 0;
// echo '$rowsCheckoffTopicsCount'.$rowsCheckoffTopicsCount;exit;
$objDB = new clsDB();
if ($rowsCheckoffTopicsCount) {
    while ($row = mysqli_fetch_array($rowsCheckoffTopics)) {
        $id = trim($row['id']);
        $topicTitle = trim($row['topicName']);
        $sectionTitle = trim($row['sectionName']);
        $stepTitle = trim($row['stepTitle']);
        $optionType = trim($row['optionType']);
        $optionValues = trim($row['optionValues']);
        $sortOrder = trim($row['stepSortOrder']);
        $sectionSortOrder = trim($row['sectionSortOrder']);
        // $schoolOptionText = trim($row[7]);

        $titleId = explode(" - ", $topicTitle);
        $checkofftitleId = current($titleId);
        // $title = str_replace($checkofftitleId, "", $topicTitle);exit;
        $title = $topicTitle;
        // print_r($titleId);
        // $title = trim($titleId[1]);
        //Check Topic is Exist Or not 
        if ($currentSchoolId == 1) {
            $isExistTopicId = $objDB->GetSingleColumnValueFromTable('defaultadvancetopicmaster', 'defaultTopicId', 'title', $title);
            // echo '</br>TopicId -> ' . $isExistTopicId;
            // exit;
            if ($title != '') {
                if (!$isExistTopicId) {
                    // $objCheckoffTopic = new clsCheckoffTopicMaster();
                    $checkofftitleId = rtrim($checkofftitleId, '.');

                    $objCheckoffTopic->checkoffTitleId = $checkofftitleId;
                    $objCheckoffTopic->title = $title;
                    $objCheckoffTopic->procedureCategoryId = 0;
                    $objCheckoffTopic->proceduteCountId = 0;
                    $objCheckoffTopic->isTopicAdded = '0';
                    $isExistTopicId = $objCheckoffTopic->SaveCheckoffTopic(0);
                }

                // $sections = explode(" ", $sectionTitle);
                // $checkofftitleId = current($sections);
                // $sectionTitle = str_replace($checkofftitleId, "", $sectionTitle);

                //Check Section is Exist Or Not
                $isExistSectionId = $objDB->GetSingleColumnValueFromTable('defaultadvancesectionmaster', 'sectionId', 'title', $sectionTitle);
                // echo '</br>SectionId -> ' . $isExistSectionId;
                // exit;
                if (!$isExistSectionId) {
                    //Save data
                    $description = '*Student must perform the modality proficiently five times prior to final check off';
                    $objCheckoffSection = new clsAdvanceMasterCheckoffSection();
                    $objCheckoffSection->title = $sectionTitle;
                    $objCheckoffSection->sortOrder = $sectionSortOrder;
                    $objCheckoffSection->description = $description;
                    $isExistSectionId = $objCheckoffSection->SaveCheckoffSection(0);

                    $objCheckoffSection->defaultTopicId = $isExistTopicId;
                    $objCheckoffSection->sectionId = $isExistSectionId;
                    $objCheckoffSection->SaveCheckoffSectionInTopicDetails($isExistSectionId);
                }
                // exit;
                if ($stepTitle && $sortOrder) {
                    // echo 'hi';exit;

                    //Check Topic is Exist Or not 
                    $isExistStepId = $objDB->GetSingleColumnValueFromTable('defaultadvancequestionmaster', 'questionId', 'questionTitle', $stepTitle, 'sortOrder', $sortOrder);
                    // //Check sortOrder is Exist Or not 
                    // $isExistSortOrder = $objDB->GetSingleColumnValueFromTable('schooldefaultquestionmaster', 'sortOrder','schoolQuestionId',$isExistStepId,'schoolId',$currentSchoolId);
                    // $objQuestionMaster = new clsCheckoffQuestionMaster();
                    $objQuestionMaster = new clsAdvanceMasterCheckoffQuestion();

                    // $isExistStepId = $objQuestionMaster->getStepId($stepTitle, $currentSchoolId, $sortOrder);
                    // echo '</br> $isExistStepId -> ' . $isExistStepId;
                    // exit;
                    if ($isExistStepId == 0 || $isExistStepId == '') {
                        // continue;


                        if ($optionType == 1)
                            $optionValues = array('Yes', 'No', 'N/A');
                        elseif ($optionType == 2)
                            $optionValues = explode(",", $optionValues);
                        elseif ($optionType == 3)
                            $optionValues = explode(",", $optionValues);

                        // print_r($optionValues);exit;

                        

                        $objQuestionMaster = new clsAdvanceMasterCheckoffQuestion();
                        $objQuestionMaster->questionTitle = $stepTitle;
                        $objQuestionMaster->questionType = $optionType;
                        $objQuestionMaster->schoolId = $currentSchoolId;
                        $objQuestionMaster->sortOrder = $sortOrder;
                        $objQuestionMaster->marks = 0;
                        $objQuestionMaster->proceduralSteps = '';

                        $RetQuestionId = $objQuestionMaster->SaveNewQuestionsMaster(0);
                        if ($RetQuestionId) {
                            if ($optionType == 2) {
                                foreach ($optionValues as $keys => $value) {
                                    $schoolOptionValue = ($value == 'Yes') ? 1 : 0;
                                    // $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                    // $objQuestionMaster->schoolOptionText = $value;
                                    // $objQuestionMaster->schoolOptionValue = $schoolOptionValue;
                                    // $objQuestionMaster->choiceAnswer = 0;
                                    // $QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);

                                    $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                    $objQuestionMaster->schoolOptionText = $value;
                                    $objQuestionMaster->schoolOptionValue = $schoolOptionValue;
                                    $objQuestionMaster->choiceAnswer = 0;
                                    $QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
                                }
                            } elseif ($optionType == 3) {
                                foreach ($optionValues as $keys => $value) {
                                    $schoolOptionValue = ($value == 'Clinical') ? 1 : 2;
                                    // $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                    // $objQuestionMaster->schoolOptionText = $value;
                                    // $objQuestionMaster->schoolOptionValue = $schoolOptionValue;
                                    // $objQuestionMaster->choiceAnswer = 0;

                                    $objQuestionMaster->questionId = $RetQuestionId;
                                    $objQuestionMaster->optionText = $value;
                                    $objQuestionMaster->optionValue = $schoolOptionValue;
                                    $objQuestionMaster->isCorrectAnswer = 0;
                                    $QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
                                }
                            } elseif ($optionType == 4 || $optionType == 6) {
                                $schoolOptionValue = ($optionType == 4) ? 1 : 0;
                                // $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                // $objQuestionMaster->schoolOptionText = '';
                                // $objQuestionMaster->schoolOptionValue = $schoolOptionValue;
                                // $objQuestionMaster->choiceAnswer = 0;
                                // $QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);

                                $objQuestionMaster->questionId = $RetQuestionId;
                                $objQuestionMaster->optionText = '';
                                $objQuestionMaster->optionValue = $schoolOptionValue;
                                $objQuestionMaster->isCorrectAnswer = 0;
                                $QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
                            }
                            $objQuestionMaster->defaultTopicId = $isExistTopicId;
                            $objQuestionMaster->sectionId = $isExistSectionId;
                            $objQuestionMaster->questionId = $RetQuestionId;
                            $result = $objQuestionMaster->SaveQuestion(0);
                        }
                    }
                }
            }
            // exit;
        } else {
            $isExistTopicId = $objDB->GetSingleColumnValueFromTable('schooltopicmaster', 'schoolTopicId', 'schooltitle', $title, 'schoolId', $currentSchoolId);
            // echo $isExistTopicId ;exit;
            if ($title != '') {
                if (!$isExistTopicId) {
                    $objCheckoffTopic = new clsCheckoffTopicMaster();
                    $checkofftitleId = rtrim($checkofftitleId, '.');

                    $objCheckoffTopic->checkoffTitleId = $checkofftitleId;
                    $objCheckoffTopic->schooltitle = $title;
                    $objCheckoffTopic->schoolId = $currentSchoolId;
                    $objCheckoffTopic->procedureCategoryId = 0;
                    $objCheckoffTopic->proceduteCountId = 0;
                    $objCheckoffTopic->isAdvanceCheckoff = 0;

                    $isExistTopicId = $objCheckoffTopic->SaveCheckoffTopic(0);
                }

                // $sections = explode(" ", $sectionTitle);
                // $checkofftitleId = current($sections);
                // $sectionTitle = str_replace($checkofftitleId, "", $sectionTitle);

                //Check Section is Exist Or Not
                $isExistSectionId = $objDB->GetSingleColumnValueFromTable('schoolsectionmaster', 'schoolSectionId', 'schoolSectionTitle', $sectionTitle, 'schoolId', $currentSchoolId);
                // echo $isExistSectionId;exit;
                if (!$isExistSectionId) {
                    //Save data
                    $objCheckoffSection = new clsCheckoffSectionMaster();
                    $objCheckoffSection->schoolSectionTitle = $sectionTitle;
                    $objCheckoffSection->sortOrder = $sectionSortOrder;
                    $objCheckoffSection->description = '*Student must perform the modality proficiently five times prior to final check off';
                    $objCheckoffSection->schoolId = $currentSchoolId;
                    $isExistSectionId = $objCheckoffSection->SaveAdvanceCheckoffSection(0);

                    $objCheckoffSection->schoolTopicId = $isExistTopicId;
                    $objCheckoffSection->schoolSectionId = $isExistSectionId;
                    $objCheckoffSection->SaveCheckoffSectionInTopicDetails($isExistSectionId);
                }
                // exit;
                if ($stepTitle && $sortOrder) {
                    // echo 'hi';exit;

                    //Check Topic is Exist Or not 
                    // $isExistStepId = $objDB->GetSingleColumnValueFromTable('schooldefaultquestionmaster', 'schoolQuestionId','schoolQuestionTitle',$stepTitle,'schoolId',$currentSchoolId);
                    // //Check sortOrder is Exist Or not 
                    // $isExistSortOrder = $objDB->GetSingleColumnValueFromTable('schooldefaultquestionmaster', 'sortOrder','schoolQuestionId',$isExistStepId,'schoolId',$currentSchoolId);
                    $objQuestionMaster = new clsCheckoffQuestionMaster();

                    $isExistStepId = $objQuestionMaster->getStepId($stepTitle, $currentSchoolId, $sortOrder);
                    // echo '$isExistStepId'.$isExistStepId;
                    // exit;
                    if ($isExistStepId == 0 || $isExistStepId == '') {
                        // continue;


                        if ($optionType == 1)
                            $optionValues = array('Yes', 'No', 'N/A');
                        elseif ($optionType == 2)
                            $optionValues = explode(",", $optionValues);
                        elseif ($optionType == 3)
                            $optionValues = explode(",", $optionValues);

                        // print_r($optionValues);exit;

                        $objQuestionMaster->schoolQuestionTitle = $stepTitle;
                        $objQuestionMaster->schoolQuestionType = $optionType;
                        $objQuestionMaster->schoolId = $currentSchoolId;
                        $objQuestionMaster->sortOrder = $sortOrder;
                        $objQuestionMaster->marks = 0;

                        $RetQuestionId = $objQuestionMaster->SaveNewQuestionsMasterForAdvance(0);
                        if ($RetQuestionId) {
                            if ($optionType == 2) {
                                foreach ($optionValues as $keys => $value) {
                                    $schoolOptionValue = ($value == 'Yes') ? 1 : 0;
                                    $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                    $objQuestionMaster->schoolOptionText = $value;
                                    $objQuestionMaster->schoolOptionValue = $schoolOptionValue;
                                    $objQuestionMaster->choiceAnswer = 0;
                                    $QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
                                }
                            } elseif ($optionType == 3) {
                                foreach ($optionValues as $keys => $value) {
                                    $schoolOptionValue = ($value == 'Clinical') ? 1 : 2;
                                    $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                    $objQuestionMaster->schoolOptionText = $value;
                                    $objQuestionMaster->schoolOptionValue = $schoolOptionValue;
                                    $objQuestionMaster->choiceAnswer = 0;
                                    $QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
                                }
                            } elseif ($optionType == 4 || $optionType == 6) {
                                $schoolOptionValue = ($optionType == 4) ? 1 : 0;
                                $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                $objQuestionMaster->schoolOptionText = '';
                                $objQuestionMaster->schoolOptionValue = $schoolOptionValue;
                                $objQuestionMaster->choiceAnswer = 0;
                                $QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
                            }
                            $objQuestionMaster->schoolTopicId = $isExistTopicId;
                            $objQuestionMaster->schoolSectionId = $isExistSectionId;
                            $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                            $result = $objQuestionMaster->SaveQuestion(0);
                        }
                    }
                }
            }
        }
        // exit;
    }
    echo '</br> ID: ' . $id;
    if ($id == $rowsCheckoffTopicsCount) {
        $phone = '+919545928770';
        $msg = 'Cron executed and Id is ' . $id;
        $result = sendSMS($phone, $msg);

        $phone = '+918275449870';
        $result = sendSMS($phone, $msg);
    }
}
