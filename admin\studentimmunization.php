<?php

    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
    include('../class/clsSchool.php');
    include('../includes/commonfun.php');
	include('../class/clsRotation.php');
	include('../class/clsImmunization.php');
    include('../setRequest.php');
	include('../class/clsRotationDetails.php');

    //Get School Name
    $objSchool  = new clsSchool();
	$objimmunization = new clsImmunization();
    $displaySchoolName = $objSchool->GetSchoolName($currentSchoolId);
    unset($objSchool);
	$immunizationDate='';
	$studentname='';
	$totalimmunization = 0;
	$studentImmunizationId=0;
	$immunizationName=0;
	
	 
	if(isset($_GET['studentImmunizationId'])) //Edit Mode
	{	  
		$studentImmunizationId = DecodeQueryData($_GET['studentImmunizationId']);	
		$immunizationName = $objimmunization->GetImmulizationName($studentImmunizationId);
	}
	if((isset($_GET['studentImmunizationId']))&& isset($_POST['btnSubmit']))
	{
      $immunizationDate=($_POST['StartDate']);
	  $studentname=($_POST['txtStudentname']);	
      $studentImmunizationId = DecodeQueryData($_GET['studentImmunizationId']);	
    }
	 $rowsimmunization = $objimmunization->GetAllStudent($studentImmunizationId,$immunizationDate,$studentname);
	if($rowsimmunization !='')
	{
		$totalimmunization =mysqli_num_rows($rowsimmunization);
	}
	unset($objimmunization);
	
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Students Immunization</title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
   </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
						<li><a href="immunization.html">Immunization</a></li>
                        <li class="active">Students</li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="container">
		<?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="Added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Immunization Added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Immunization updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Deleted")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Immunization deleted successfully.
                </div>
                <?php 
					}
                     else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>
	
				  
				  
				  
        <div class="formSubHeading">Immunization: <?php echo $immunizationName; ?></div>
                
                <div id="divTopLoading" >Loading...</div>

				<form name="immunization" id="immunization" method="POST" action="studentimmunization.html?studentImmunizationId=<?php echo (EncodeQueryData($studentImmunizationId));?>">
				<div class="row">
					<div class="col-md-4 pull-left margin_bottom_ten">
					<div class="form-group">
                        <div class="col-md-8 pull-right padding_right_zero">
                           <input type='text' name="txtStudentname"  id="txtStudentname" class="form-control input-md " value="" />
                        </div>
						<label class="col-md-1 control-label  pull-left" for="Student">Student:</label>
                        
						</div>
                    </div>
					<div class="col-md-4 pull-left margin_bottom_ten">
					<div class="form-group">
                        <div class="col-md-8 pull-right padding_right_zero">
                          <div class='input-group date' id='StartDate'>
	
									<input type='text' name="StartDate"  id="StartDate" class="form-control input-md required-input rotation_date" value="" />
										<span class="input-group-addon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-txtDate"></div>
                        </div>
						<label class="col-md-1 control-label  pull-left" for="Student"> Date:</label>
                        
						</div>
                    </div>
					<div class="col-md-4 pull-left margin_bottom_ten">
					<div class="form-group">
                    <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Search</button>
                  
                </div>
                </div>
				</form>  
				</div>	
				
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
							
                            <th  style="text-align:left">Student</th>
                            <th style="text-align:left">Rank</th>
                            <th style="text-align:center">Date</th>
                            <th style="text-align:center">Notification Date</th>
                            <th style="text-align:center">Expire Date</th>
                            <th style="text-align:center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalimmunization>0)
                        {
                           
							
							while($row = mysqli_fetch_array($rowsimmunization))
                            {
                                $immunizationMId = $row['immunizationMId'];
                                $ImmunizationId = $row['studentImmunizationId'];
                                $expiryDays  = $row['expiryDays'];
                                $shortName = $row['shortName'];
                                $firstName = stripslashes($row['firstName']);
                                $lastName = stripslashes($row['lastName']);
                                $title = stripslashes($row['title']);
                                $immunizationDate = $row['immunizationDate'];
                                $studentId = $row['studentId'];
                                $fullName = $firstName.' '.$lastName;
								//for validte 60 days
                                 $currentDate=date("Y/m/d");
                                $ExpiryDate  = date("Y/m/d", strtotime($row['expiryDate']));	
                                $ExpiryDate=date("m/d/Y", strtotime($ExpiryDate));	
                                
                                $immunizationNotificationDate = $row['immunizationNotificationDate'];
                                $immunizationNotificationDate=date("m/d/Y", strtotime($immunizationNotificationDate));
								 
									
                            ?>
                            <?php  		
					        if((strtotime($currentDate)) >= (strtotime($ExpiryDate)) && $expiryDays !=0){ ?> 
                            <tr class="updaterow">
							<?php }else{?> 
                               <tr>	
                                <?php }?>
                                <td>
                                    <?php echo($fullName); ?>
                                </td>
                                <td>
                                    <?php echo($title); ?>
                                </td>
                                <td style="text-align:center">
								<?php echo date("m/d/Y", strtotime($immunizationDate));?>
								</td>
								
                                <td style="text-align:center">
                                <?php
                                
                                if($expiryDays ==0)
                                {
                                    echo "N/A";
                                }
                                else if($immunizationNotificationDate !='' && $immunizationNotificationDate !='0000-00-00' &&  $immunizationNotificationDate !='01/01/1970' && $immunizationNotificationDate !='11/30/-0001')
                                {
                                 echo $immunizationNotificationDate;
                                }
                                else{
                                    echo "-";
                                } 
                            
                                 ?>

								<td style="text-align:center">
                                <?php 
                                if($expiryDays == 0){
                                    echo "N/A";
                                }

								else if((strtotime($currentDate)) >= (strtotime($ExpiryDate))){ ?>
                                    <?php
                                    if($ExpiryDate !='' && $ExpiryDate !='0000-00-00' &&  $ExpiryDate !='01/01/1970' && $ExpiryDate !='11/30/-0001')
                                    {
                                        echo ($ExpiryDate);
                                    }
                                    else{
                                        echo "-";
                                    }
                                    ?> <br>
								    <small style="text-align:center;color: red">Expired</small>
								<?php } else{?>
								<?php echo date("m/d/Y", strtotime($ExpiryDate)); }?><br>
								</td>
								
								<td style="text-align: center">
                                    <a href="editstudentimmunization.html?studentImmunizationId=<?php echo(EncodeQueryData($ImmunizationId)); ?> & studentId=<?php echo(EncodeQueryData($studentId)); ?> ">Edit</a> 
									|<a  href="javascript:void(0);" class="deleteAjaxRow" ImmunizationId="<?php echo EncodeQueryData($ImmunizationId); ?>" >Delete</a> 
                                </td>
                                
                            </tr>
                            <?php


                            }
							
                        }
                    ?>

                    </tbody>
                </table>
        </div>
        </div>
		

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
		 <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
		<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
		<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>    
		<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
		<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>	
		
		<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

        <script type="text/javascript">
				
				
				alertify.defaults.transition = "slide";
				alertify.defaults.theme.ok = "btn btn-success";
				alertify.defaults.theme.cancel = "btn btn-danger";
				alertify.defaults.theme.input = "form-control";
				
				

				$(window).load(function(){
					 $(".select2_single").select2();
            $('#select2-cboCountry-container').addClass('required-select2');
            $('#select2-cboState-container').addClass('required-select2');
					$("#divTopLoading").addClass('hide');
					
					$('#StartDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY',
					    defaultDate: new Date()
						
           			});
				
					
				});


				var current_datatable = $("#datatable-responsive").DataTable({
					"aoColumns": [{
						"sWidth": "20%",
						"sClass": "alignCenter"
					}, 
					{
						"sWidth": "10%",
						"sClass": "alignCenter"
					}, {
						"sWidth": "15%",
						"sClass": "alignCenter"
					}, {
						"sWidth": "15%",
						"sClass": "alignCenter"
					},{
						"sWidth": "15%",
						"sClass": "alignCenter"
					},{
						"sWidth": "20%",
                       "sClass": "alignCenter",
                       "bSortable": false
					}]
				});
			
			//delete student
				$(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var ImmunizationId = $(this).attr('ImmunizationId');
               
                
                alertify.confirm('Immunization: ','Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: ImmunizationId,
                            type: 'Immunization'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });
        </script>


    </body>

    </html>