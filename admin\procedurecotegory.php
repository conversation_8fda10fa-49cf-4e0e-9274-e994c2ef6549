<?php 
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');    
	include('../class/clsProcedureCategory.php');
	include('../setRequest.php'); 

    $schoolId = 0;
    $transchooldisplayName = '';
    if(isset($_GET['schoolId']))
	{
		$schoolId = $_GET['schoolId'];
        $schoolId = DecodeQueryData($schoolId);
    }
    else
    {
        $schoolId = $currentSchoolId;
        $transchooldisplayName=$currenschoolDisplayname;
    }

    $title ="Procedure Category | ".$transchooldisplayName;
     
    //CREATE OBJECT
	$objProcedureCategory = new clsProcedureCategory();
    $totalProcedureCategory = 0;
	$rowsProcedureCategory = $objProcedureCategory->GetProcedureDetails();
	
	if($rowsProcedureCategory !='')
	{
		$totalProcedureCategory =mysqli_num_rows($rowsProcedureCategory);
	}
	unset($objProcedureCategory);
?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo($title); ?></title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>

		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Procedure Count Category </li>
                    </ol>
                </div>
         
               <div class="pull-right">
                     <a class="btn btn-link" href="addcotegory.html">Add</a>
               </div>
         

            </div>
        </div>

        <div class="container">

            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Procedure Category added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Procedure Category updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Deleted")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Procedure Category deleted successfully.
                </div>
                <?php 
					}
                     else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Procedure Count Category</th>
                            
                            <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalProcedureCategory>0)
                        {
                            while($row = mysqli_fetch_array($rowsProcedureCategory))
                            {
								
								$procedureCategoryId = $row['procedureCategoryId'];
                              					
                                $title = stripslashes($row['categoryName']);                    
                               ?>
                            <tr>
                                <td>
                                    <?php echo($title); ?>
                                </td>
                                                                
                                <td style="text-align: center">
                                    <a href="addcotegory.html?id=<?php echo(EncodeQueryData($procedureCategoryId)); ?>">Edit</a> 
									| <a href="javascript:void(0);" class="deleteAjaxRow"
									procedureCategoryId="<?php echo EncodeQueryData($procedureCategoryId); ?>" ProcedureCategoryName="<?php echo($title); ?>" >Delete</a>
                                </td>
                            </tr>
                            <?php
                            }
                        }
                    ?>
                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

        <script type="text/javascript">

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            });
			
            var current_datatable = $("#datatable-responsive").DataTable({
                "aoColumns": [{
                    "sWidth": "95%"
                },{
                    "sWidth": "5%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, ]
            });

            // ajax call for deleteAjaxRow
			
			// ajax call for delete
		  function ShowDeleteMessage()
            {
                alertify.alert('Warning','This is the Primary User. You can\'t delete this.');
            }    

		  function ShowCountMessage()
            {
                alertify.alert('Warning','This role is already in used. You can\'t delete this.');
            } 
			
			
			
                 
             $(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var procedureCategoryId = $(this).attr('procedureCategoryId');
                var title = $(this).attr('ProcedureCategoryName');
               
                alertify.confirm('Procedure Category: '+ title, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: procedureCategoryId,
                            type: 'Procedure_Category'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });
        </script>


    </body>

    </html>