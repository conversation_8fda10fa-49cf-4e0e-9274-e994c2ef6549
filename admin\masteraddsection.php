<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');  
    include('../class/clsMasterCheckoffSection.php');
    include('../class/clsMasterCheckoffTopic.php');
	include('../setRequest.php'); 

    $schoolId = 0;  
	$schoolId= $currentSchoolId;
	$title ="Add Comps Section - ".$currenschoolDisplayname;   
    $page_title ="Add Comps Section";
    $sectionId = 0;
    $title = '';
    $sortOrder  = '';
	$sordOrder ='';
    $bedCrumTitle = 'Add';
	
	if(isset($_GET['topicid']))
	{
		$topicid=DecodeQueryData($_GET['topicid']);
	}
    if(isset($_GET['editid'])) //Edit Mode
	{
        $sectionId = DecodeQueryData($_GET['editid']);
	    $page_title ="Edit Comps Section";
        $bedCrumTitle = 'Edit';

        $objCheckoffSectionMaster = new clsMasterCheckoffSection();
		$row = $objCheckoffSectionMaster->GetCheckoffTopicByCheckoffSectionId($sectionId);
        unset($objCheckoffSectionMaster);
        if($row=='')
        {
            header('location:masteraddsection.html');
            exit;
        }

        $title  = stripslashes($row['title']);      
        $sortOrder  = stripslashes($row['sortOrder']);      
        $description  = stripslashes($row['description']);      
	       
	}
	$objCheckoffTopicMaster=new clsMasterCheckoffTopic();
    $GetSingleTopicId=$objCheckoffTopicMaster->GetSingleCheckoffTopicId($topicid,$sectionId);
	$TopicTitleId=$GetSingleTopicId[0] ?? '';
	unset($objCheckoffTopicMaster);
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">


    

</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
					 <li><a href="dashboard.html">Setting</a></li>
                    <li><a href="mastercheckofftopic.html">Comps Topics</a></li>
                    <li><a href="mastercheckoffsection.html?topicid=<?php echo EncodeQueryData($topicid); ?>"><?php echo ($TopicTitleId); ?>-Comps Section</a></li>
                    <li class="active"><?php echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

         <form id="frmcheckoff" data-parsley-validate class="form-horizontal" method="POST" <?php if(isset($_GET['editid'])){?> action="mastercheckoffsectionsubmit.html?editid=<?php echo(EncodeQueryData($sectionId)); ?>&topicid=<?php echo EncodeQueryData($topicid); ?>" <?php } else { ?> action="mastercheckoffsectionsubmit.html?topicid=<?php echo EncodeQueryData($topicid); ?>" <?php } ?> >

            <div class="row">
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtcheckoffsection">Comps Section</label>
                        <div class="col-md-12">
                            <input  id="txtcheckoffsection"  name="txtcheckoffsection" value="<?php echo($title); ?>"  type="text"  class="form-control input-md required-input" required>

                        </div>
                    </div>
                </div>
                <div class="col-md-6">
					
					<div class="form-group">
                        <label class="col-md-12 control-label" for="txtsortorder">Section Number</label>
                        <div class="col-md-12">
                            <input  id="txtsortorder"  name="txtsortorder" value="<?php echo($sortOrder); ?>"  type="text"  class="form-control input-md required-input" required>

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
					<div class="form-group">
                        <label class="col-md-12 control-label" for="txtdescription">Description</label>
                        <div class="col-md-12">
                            <textarea  id="txtdescription"  name="txtdescription"  type="text"  class="form-control input-md" rows="4" cols="100" ><?php if(isset($_GET['editid'])){ echo($description); } ?></textarea>

                        </div>
                    </div>
					
				</div>
            </div>
            
				<div class="form-group">
                        <!-- <label class="col-md-2 control-label"></label> -->
                        <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                            <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                            <a type="button" href="mastercheckoffsection.html?topicid=<?php echo EncodeQueryData($topicid); ?>" class="btn btn-default">Cancel</a>
						</div>
                </div>
        </form>


    </div>

    <?php include('includes/footer.php');?>
   
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>

	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

    <script type="text/javascript">
 
        $(window).load(function(){

             $('#frmcheckoff').parsley().on('field:validated', function() {
			    var ok = $('.parsley-error').length === 0;
            })
            .on('form:submit', function() {
                ShowProgressAnimation();									  
                return true; // Don't submit form for this demo
            });
		
        });
		
	
       
    </script>

 
    



</body>

</html>