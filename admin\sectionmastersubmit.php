<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsCheckoffSectionMaster.php');
include('../class/clsusafEquiment.php');
include('../class/clscheckoff.php');
include('../setRequest.php');

//print_r($_POST);
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {

	$checkoffSectionId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;

	$status = ($checkoffSectionId > 0) ? 'updated' : 'added';

	if (isset($_GET['topicid'])) {
		$topicid = DecodeQueryData($_GET['topicid']);
	}
	$title = $_POST['txtcheckoffsection'];
	$sortOrder = $_POST['txtsortorder'];
	$description = $_POST['txtdescription'];
	$equipmentList  = $_POST['equipmentList'];

	//Save data
	$objCheckoffSection = new clsCheckoffSectionMaster();
	$objCheckoffSection->schoolSectionTitle = $title;
	$objCheckoffSection->sortOrder = $sortOrder;
	$objCheckoffSection->description = $description;
	$objCheckoffSection->schoolId = $currentSchoolId;
	$retcheckoffsectionmasterId = $objCheckoffSection->SaveCheckoffSection($checkoffSectionId);

	/*if(!isset($_GET['editid']))
		{
			$objCheckoffSection->schoolTopicId=$topicid;
			$objCheckoffSection->schoolSectionId=$retcheckoffsectionmasterId;
			$objCheckoffSection->SaveCheckoffSectionInTopicDetails($retcheckoffsectionmasterId);
		}*/

	//Add Equipment List 
	$objusafEquiment = new clsusafEquiment();
	//Delete all clinician hospitalsites
	$objusafEquiment->DeleteAllSchoolSectionEquipmentList($checkoffSectionId);
	if (count($equipmentList)) {
		//Now save new Equipment
		foreach ($equipmentList as $equipmentId) {
			$studentEquipmentId = $objusafEquiment->SaveSchoolSectionusafequipmentlist($retcheckoffsectionmasterId, $equipmentId);
		}
	}
	unset($objusafEquiment);

	unset($objCheckoffSection);
	if ($retcheckoffsectionmasterId > 0) {
		
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "section";
		$action = ($checkoffSectionId > 0) ? $objLog::EDIT : $objLog::ADD;
		$isSuperAdmin = isset($_SESSION['isCurrentSchoolSuperAdmin']) ? ($_SESSION['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;

		$objCheckoff = new clscheckoff();
		$objCheckoff->saveCompEvaluationAuditLog($retcheckoffsectionmasterId,$_SESSION['loggedUserId'], $userType, $action, 0, $type, $isSuperAdmin);
		unset($objCheckoff);
		unset($objLog);

		header('location:viewsectionmaster.html?status=' . $status . '&topicid=' . EncodeQueryData($topicid));
	} else {
		header('location:addsectionmaster.html?status=error');
	}
} else {
	header('location:viewsectionmaster.html?status=' . $status . '&topicid=' . EncodeQueryData($topicid));
	//header('location:checkoffsection.html');
	exit();
}
