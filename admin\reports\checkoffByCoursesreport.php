<?php
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$objcheckoff = new clscheckoff();

$individual_student = unserialize($individual_student);
$rowscheckoff = $objcheckoff->GetCheckOffByCoursesForreport($currentSchoolId, $rotationId, $subcborotation, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $checkoffTopic, $AscDesc, $sordorder, $cbosemester, $courseTopicCompletion,$courseId);
$objPHPExcel = new \PHPExcel();

// Create a new Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('Checkoff by Courses Reports');

// Document properties
$spreadsheet->getProperties()
    ->setCreator('Jacson Community College')
    ->setLastModifiedBy('JCC')
    ->setTitle('Reports')
    ->setSubject('School Report')
    ->setDescription('Checkoff by Courses Reports');

// Header styles
$headerStyle = [
    'font' => ['bold' => true, 'size' => 16],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'E0E0E0']
    ],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
];

// Report title
$sheet->mergeCells("B2:I2");
$sheet->setCellValue('B2', $schoolname);
$sheet->getStyle('B2:I2')->applyFromArray($headerStyle);

$sheet->mergeCells("B4:I4");
$sheet->setCellValue('B4', 'Checkoff by Courses Reports');
$sheet->getStyle('B4:I4')->applyFromArray($headerStyle);

// Table headings
$headingStyle = [
    'font' => ['bold' => true, 'size' => 12],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'E0E0E0']
    ],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
];

$headers = ['B6' => 'First Name', 'C6' => 'Last Name', 'D6' => 'Course', 
            'E6' => 'Evaluator', 'F6' => 'Course Topics', 'G6' => 'Date', 
            'H6' => 'Score', 'I6' => 'Status'];

foreach ($headers as $cell => $text) {
    $sheet->setCellValue($cell, $text);
    $sheet->getStyle($cell)->applyFromArray($headingStyle);
}

// Populate data
$rowCounter = 7;
if ($rowscheckoff) {
    $dataStyle = [
        'font' => ['size' => 10],
        'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]
    ];

    while ($row = mysqli_fetch_array($rowscheckoff)) {
        $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
        $checkoffDateTime = converFromServerTimeZone($row['checkoffDateTime'], $TimeZone);
        $checkoffDateTime = date('m/d/Y', strtotime($checkoffDateTime));
        $score = $isActiveCheckoff == 2 ? $row['calculatedUsafScore'] : $row['calculatedScore'];

        $sheet->setCellValue('B' . $rowCounter, $row['firstName']);
        $sheet->setCellValue('C' . $rowCounter, $row['lastName']);
        $sheet->setCellValue('D' . $rowCounter, $row['courseTitle']);
        $sheet->setCellValue('E' . $rowCounter, $row['clinicianfname'] . ' ' . $row['clinicianlname']);
        $sheet->setCellValue('F' . $rowCounter, stripslashes($row['schooltitle']));
        $sheet->setCellValue('G' . $rowCounter, $checkoffDateTime);
        $sheet->setCellValue('H' . $rowCounter, $score);
        $sheet->setCellValue('I' . $rowCounter, stripslashes($row['Status']));

        $sheet->getStyle('B' . $rowCounter . ':I' . $rowCounter)->applyFromArray($dataStyle);
        $rowCounter++;
    }
}

// Add border to table
$sheet->getStyle('B6:I' . ($rowCounter - 1))->getBorders()
    ->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

// Auto-size columns
foreach (range('B', 'I') as $columnID) {
    $sheet->getColumnDimension($columnID)->setAutoSize(true);
}

// Save the spreadsheet
$writer = new Xlsx($spreadsheet);
$reportname = 'CheckoffByCoursesReport_';
?>