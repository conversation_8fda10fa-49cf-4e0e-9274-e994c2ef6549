<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');  
    include('../class/clsRotation.php');
    include('../class/clsSchoolHoliday.php');
    include('../class/clsHospitalSite.php');
	include('../setRequest.php');
	
	$schoolId = 0;
    $rotationId = 0;
    $AppopinementCalender="";
    $totalRotations = 0;
	$courseId=0;
    $hospitalSiteTitle='';
	$loopCounter=0;

    $transchooldisplayName = '';
  
    $title ="Calender  ";
    $courseTitle='';
	
	$defualtView = 'basicWeek';

    //For Rotation
    $objRotation = new clsRotation();
    $objSchoolHoliday = new clsSchoolHoliday();
    $objHospitalSite = new clsHospitalSite();

    $hospitalSiteData = $objHospitalSite->GetAllHospitalSiteId($currentSchoolId);

    if($hospitalSiteData !='')
    {
        $totalHospitalSite =mysqli_num_rows($hospitalSiteData);
    }
    $hospitalSiteArr = array();
   
    if($totalHospitalSite>0)
    {
    
        while($row = mysqli_fetch_array($hospitalSiteData))
            {	
                
                $hospitalSiteId =$row['hospitalSiteId'];
                $hospitalSiteArr[] = $hospitalSiteId;
            }
        foreach($hospitalSiteArr as $val){
            
            $schoolHolidays = $objSchoolHoliday->GetSchoolHolidayDetails($currentSchoolId,$val,$site='H');
            if($schoolHolidays !='')
            {
                $holidayArr =array();
                $typeArr =array();
        
                while($row = mysqli_fetch_array($schoolHolidays))
                {	
                    array_push($holidayArr,$row['holidayDate']);
                    array_push($typeArr,$row['type']);
                }   
                $holidayData = json_encode($holidayArr);
                $typeData = json_encode($typeArr);
            } 
            
        } 
    }
        
 unset($objRotation);
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo($title); ?></title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>

		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

<style>
    @import url('https://fonts.googleapis.com/css2?family=Open+Sans&display=swap');


.modal-container{
    display: none;
    align-items: center;
    justify-content: center;
    width:100vw;
    height:100vh;
    background-color: #00000055;
    position: fixed;
    top:0;
    left:0;
    z-index: 2000;

}

.box{
    color:#242424;
    padding:40px;
    width:60%;
    min-width: 300px;
    background-color:#cecece ;
    border: 2px solid #cecece;
    text-align: center;
    position: relative;
    box-shadow: 0px 0px 0px 10px #707070;

}
h3{
    font-size: 2em;
}
#close{
    position: absolute;
    top:-10px;
    right: -10px;
    width: 25px;
    height: 25px;
    border: 2px solid #cecece;
    border-radius: 50%;
    color:#cecece;
    cursor: pointer;
    background-color:black ;
}
.button{
    margin-top: 10px;
    width: 100px;
    height: 30px;
    color:#242424;
    border: 1px solid #707070;
    font-size: 1em;
    font-weight: bold;
    border-radius: 5px;
}
.button:hover{
    color:#cecece;
    background-color: #242424;
}
.input{
    font-size: 1.2em;
    width: 90%;
    height: 30px;
    border: 1px solid #707070;
    border-radius: 5px;
}

.modal-container.display{
    display: flex;
}

@keyframes modal {
    from{
        opacity: 0;
        transform: translate3d(0, -60px, 0);
    }
    to{
        opacity: 1;
        transform: translate3d(0, 0, 0);
    }
}
.display .box{
    animation: modal .3s;
}
#active-modal{
    font-size: 1.2em;
    width: 200px;
    height: 50px;
    border: 1px solid #707070;
    border-radius: 5px;
    color:#cecece;
    background-color: #242424;
}

#made{
    font-size: .7em;
    margin-top: 80vh;
    position: absolute;
    color: #707070;
}
#made a{
    text-decoration: none;
    color: #707070;
}
#made a:hover{
    color:#8B46E2;
}

body {
  margin-top: 40px;
  text-align: center;
  font-size: 14px;
  font-family: "Lucida Grande",Helvetica,Arial,Verdana,sans-serif;
}

#wrap {
  width: 1500px;
  margin: 0 auto;
}

#external-events {
  float: left;
  width: 150px;
  padding: 0 10px;
  border: 1px solid #ccc;
  background: #eee;
  text-align: left;
}

#external-events h4 {
  font-size: 16px;
  margin-top: 0;
  padding-top: 1em;
}

#external-events .fc-event {
  margin: 10px 0;
  cursor: pointer;
}

#external-events p {
  margin: 1.5em 0;
  font-size: 11px;
  color: #666;
}

#external-events p input {
  margin: 0;
  vertical-align: middle;
}

#calendar {
  float: right;
  width: 100%;
}
.mb-20{
  margin-bottom:20px;
}

#holiday {
  margin-bottom: 10px;
  background: red;
  font-size: 16px;
  font-weight: 700;
  text-align: center;
  color: black;
}
#closed {
  margin-bottom: 10px;
  background: gray;
  color: black;
  font-size: 16px;
  font-weight: 700;
  text-align: center;
}
.fc-row .fc-content-skeleton {
    position: relative;
    z-index: 0 !important;
    padding-bottom: 2px;
}
.fc-row .fc-bg {
    z-index: none !important;
}
a{
    z-index: 4554854875 !important;
}
.fc-color{
    background-color: red;
}
.holidayEvent{
    padding: 59px 40px 56px 62px;position: absolute;color: black;font-weight: 700;
}
.fc-prev-button{
    z-index: 999999;
}
    </style>
    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Setting</a></li>
                        <li class="active">Calendar</li>
                    </ol>
                </div>
            </div>
        </div>
        <div id='wrap'>

  <div id='external-events'>
    <div id='external-events-listing'>
      <h4><strong>Events</strong></h4>
      <div class='fc-event' id= "holiday" data-duration='01:00'>Holiday</div>
      <div class='fc-event' id="closed" data-duration='01:00'>Closed</div>
    </div>
  </div>
  <div class="container">
    <div class="col-md-12">

      <div class="container">
           <div id='calendar'> </div>         
        </div>

    </div>
  </div>

  <div style='clear:both'></div>

</div>
        
    <div id="modal" class="modal-container"> 
        <div class="box">
            <button id="close">X</button>
                <h3> Cadastre seu e-mail </h3>
                <form>
                    <input type="text" class="input" placeholder="E-mail">
                    <input type="button" class="button" value="cadastrar">
                </form>
        </div>
        </div>
        <div id="made">Made by <b><a href="https://ddparkas.github.io/site/ " target="_blank">Daniel Dormin</a></b> 13-05-2021
        </div>
        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
	    <?php include("includes/fullclaender.php") ?>
     <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>	
     <script type="text/javascript">


$(document).ready(function() {

   // $("#calendar").trigger("eventRender");
    var date = new Date();
    var d = date.getDate();
    var m = date.getMonth();
    var y = date.getFullYear();
	/*****************************************************************
	* Dropped and copied external event
	******************************************************************/
	

    function external_event_dropped(date, external_event,cDate,title,holidayDate,schoolId,type,Arr) {
	
    var event_object,
        copied_event_object,
        tempDate = new Date(date);
    
    event_object = $(external_event).data('eventObject');
    copied_event_object = $.extend({}, event_object);
    copied_event_object.start =  date;
    copied_event_object.end = new Date(tempDate.setHours(tempDate.getHours()+1)) - 2;
    copied_event_object.allDay =  false;
    copied_event_object.title =  title;
   
    $.ajax({
            url: '<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_save_schoolHoliday.html',
            data: 'title='+title+'&holidayDate='+ holidayDate +'&schoolId='+ schoolId+'&type='+type+'&hospitalArr='+Arr+'&site=A',
            type: "POST",
            success: function(json) {
                console.log("Saved Successfully");
                location.reload();
        }
    });
    //external_event.remove();
    
    $('#calendar').fullCalendar('renderEvent', copied_event_object, true);
    
}

	/*****************************************************************
	* Initialize external events
	******************************************************************/

	$('#external-events .fc-event').each(function() {

		$(this).draggable({
			zIndex: 999,
			revert: true,
			revertDuration: 0
		});

	});

	/*****************************************************************
	* Initialize the calendar
	******************************************************************/
  
	$('#calendar').fullCalendar({
        header: {
                left: 'prev,next today',
                center: 'title',
                right: 'month,agendaWeek,agendaDay'
            },
		editable: true,
		droppable: true,
		dragRevertDuration: 0,
		eventLimit: true,
        axisFormat: 'HH:mm',
        events: [<?php echo $AppopinementCalender; ?>],
        dropable:true,
       
        drop: function (date , allDay, jsEvent) {

                    var fixed = new Date(date);
            
                    var cDate = moment(fixed, "MM/DD/YYYY");
                    var convertedDate = cDate.toISOString();
                    var holidayDate = convertedDate.substring(0, 10);
                    var title = jsEvent.helper[0].innerText;
                   
                    var schools = <?php echo json_encode($hospitalSiteArr); ?>;
                    var hospitalSiteArr = JSON.stringify(schools); 

                    var schoolId = '<?php echo $currentSchoolId; ?>';
                    if(title == 'Holiday'){
                        var type ='h';
                    }else if(title == 'Closed'){
                        var type ='c';
                    }
                    var $this = $(this);
                        external_event_dropped(date, $this,cDate,title,holidayDate,schoolId,type,hospitalSiteArr);
                    
                },

            eventRender: function (event, element) {

                var eventDay = $(".fc-day");
               
                var holidayData = '<?php echo json_encode($holidayData); ?>';
                var typeData = '<?php echo json_encode($typeData); ?>';
                var schoolId = '<?php echo $currentSchoolId; ?>';
                var str_holiday = holidayData.split(',');
                var str_type = typeData.split(',');
                for(var i = 1; i < str_holiday.length; i++){
                var holidatStr = str_holiday[i].replace(/[\[\]']+/g,'');
                var typeStr = str_type[i].replace(/[\[\]']+/g,'');
                
                var trimedHoliday = holidatStr.replace(/"/g, "");
                var trimedType = typeStr.replace(/"/g, "");
                
                $.each( eventDay, function( key, value ) {
      
                    if ( value.dataset["date"] == trimedHoliday) {
                        if(trimedType =='c'){
                            value.style.backgroundColor = "gray";
                            $(this).attr("id","holiday_"+trimedHoliday);
                            $(this).html("<a href='#' holidayDate="+trimedHoliday+" schoolId="+schoolId+" class='holidayEvent'>Closed</a>");
                        }else{
                            value.style.backgroundColor = "red";
                            $(this).attr("id","holiday_"+trimedHoliday);
                            $(this).html("<a href='#' class='holidayEvent' holidayDate="+trimedHoliday+" schoolId="+schoolId+">Holiday</a>");
                        }
                    }
                });
                
            }
            $(this).fullCalendar('renderEvent', true);
            },
            selectable: true,
            selectHelper: true,
        
            droppable: true, // this allows things to be dropped onto the calendar

        eventClick: function(calEvent, jsEvent, view) {

                var deletedRecord = calEvent.start.format();
                var hDate = deletedRecord.substring(0, 10);
                var schoolId = '<?php echo $currentSchoolId; ?>';

                var deleteMsg = confirm("Do you really want to delete?");
                if (deleteMsg) {
                    $.ajax({
                        type: "POST",
                        url: '<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete_schoolHoliday.html',
                        data: 'holidayDate='+ hDate +'&schoolId='+ schoolId,
                        type: "POST",
                        success: function (response) {
                            location.reload();
                        }
                    });
                }
                
            },
            dayRender: function(date, cell) {
                
                var holidayData = '<?php echo ($holidayData); ?>';
                    var typeData = '<?php echo ($typeData); ?>';
                    var schoolId = '<?php echo $currentSchoolId; ?>';
                    var str_holiday = holidayData.split(',');
                    var str_type = typeData.split(',');
                
                    var holidatStr = str_holiday[0].replace(/[\[\]']+/g,'');
                    var typeStr = str_type[0].replace(/[\[\]']+/g,'');
                
                    var trimedHoliday = holidatStr.replace(/"/g, "");
                    var trimedType = typeStr.replace(/"/g, "");
                
                    var row = $(".fc-day[data-date='"+trimedHoliday+"']").css("background-color","transparent");
            },
           
            viewRender: function (view, element) {
                cur = view.intervalStart;
                //console.log(hD);
                d = moment(cur).add(1, "months");
                var cD = d.toISOString();
                var hD = cD.substring(0, 10);
                var event={start: hD, backgroundColor: 'transparent',borderColor: 'transparent', zIndex:'0000'};
                $('#calendar').fullCalendar( 'renderEvent', event, true);
            }
         });
         // batch every modification into one re-render
    
            $('.holidayEvent').click(function(){
                var HDate =  $(this).attr('holidayDate');
                var schoolId =  $(this).attr('schoolId');
                var deleteMsg = confirm("Do you really want to delete?");
                if (deleteMsg) {
                    $.ajax({
                        type: "POST",
                        url: '<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete_schoolHoliday.html',
                        data: 'holidayDate='+ HDate +'&schoolId='+ schoolId,
                        type: "POST",
                        success: function (response) {
                            location.reload();
                        }
                    });
                }
            }); 
        });
</script>
 </body>
</html>

