<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsPersonnelCoarc.php');
include('../class/clsCoarc.php');
include('../class/clsClinician.php');
include('../setRequest.php');
require '../vendor/autoload.php';  // Include PhpSpreadsheet's autoloader

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;
$studentImmunizationId = 0;
$studentId = 0;
$totalSection = 0;
$schoolId = $currentSchoolId;
$objPersonnelCoarc = new clsPersonnelCoarc();
$objCoarc = new clsCoarc();
$CoarcSection = $objPersonnelCoarc->GetPersonnelSections($schoolId);
if ($CoarcSection != '') {
	$totalSection = mysqli_num_rows($CoarcSection);
}

if (isset($_GET['coarcSurveyMasterId'])) {
	$coarcSurveyMasterId = DecodeQueryData($_GET['coarcSurveyMasterId']);
}

if (isset($_GET['AssignedcoarcSurveyMasterId'])) {
	$AssignedcoarcSurveyMasterId = DecodeQueryData($_GET['AssignedcoarcSurveyMasterId']);
}

if (isset($_GET['clinicianId'])) {
	$clinicianId = DecodeQueryData($_GET['clinicianId']);
}


$objClinician = new clsClinician();
$rowStudent = $objClinician->GetClinicianDetails($clinicianId);
$firstName = $rowStudent ? $rowStudent['firstName'] : '';
$lastName = $rowStudent ? $rowStudent['lastName'] :'';
$fullName = $firstName . ' ' . $lastName;

$title = 'Clinician Personnel Report';
date_default_timezone_set('Asia/Kolkata');
$today = (date('m/d/Y, H:i A'));
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnStudentExport']))
	$reportType = $_POST['cboreporttype'];


switch ($reportType) {
	case "PersonnelServey":

		$spreadsheet = new Spreadsheet();


		//Active Sheet
		$spreadsheet->setActiveSheetIndex(0);
		$spreadsheet->getActiveSheet()->setTitle('Personnel Survey');

		//Print Heading	
		$headerstyleArray = array('font'  => array('bold'  => true, 'size'  => 16));

		$spreadsheet->getActiveSheet()->mergeCells("B2:H2");
		$spreadsheet->getActiveSheet()->setCellValue('B2', $title);
		$spreadsheet->getActiveSheet()->getStyle('B2')->applyFromArray($headerstyleArray);
		$spreadsheet->getActiveSheet()->getStyle('B2')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

		$spreadsheet->getActiveSheet()
			->getStyle('B2')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setRGB('E0E0E0');

		$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];	
		$spreadsheet->getActiveSheet()->getStyle('B2:H2')->applyFromArray($styleBorderArray);


		$styleArray = array('font'  => array('bold'  => true, 'size'  => 12));

		$spreadsheet->getActiveSheet()->mergeCells("B4:H4");
		$spreadsheet->getActiveSheet()->setCellValue('B4', $fullName);
		$spreadsheet->getActiveSheet()->getStyle('B4')->applyFromArray($styleArray);
		$spreadsheet->getActiveSheet()->getStyle('B4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

		$spreadsheet->getActiveSheet()
			->getStyle('B4')
			->getFill()
			->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			->getStartColor()
			->setRGB('E0E0E0');

		$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];	
		$spreadsheet->getActiveSheet()->getStyle('B4:H4')->applyFromArray($styleBorderArray);
		$comment1 = '';
		$comment2 = '';
		$comment3 = '';
		$comment4 = '';
		$comment5 = '';
		$comment6 = '';
		$comment7 = '';
		// echo 'isActiveCheckoffForStudent'.$isActiveCheckoffForStudent;
		// 						if($isActiveCheckoffForStudent == 1)
		// {							$schoolCoarcQuestionIds = '1035,1045,1052,1056,1063,1071,1074';
		// }						else if($isActiveCheckoffForStudent == 2)
		// {							$schoolCoarcQuestionIds = '2175,2185,2192,2196,2203,2211,2214';
		// }						else
		// {							$schoolCoarcQuestionIds = '5180,5190,5197,5201,5208,5216,5219';
		// echo '<pre>';
		// }
		$schoolCoarcQuestionIds = $objPersonnelCoarc->GetStudentCoarcCommentQuestionId($AssignedcoarcSurveyMasterId);
		$rsClinitionComments = $objPersonnelCoarc->GetAllStudentCoarcSurveyComments($AssignedcoarcSurveyMasterId, $schoolCoarcQuestionIds, $clinicianId);

		$results = array();
		$rsStudentCommentsCount = 0;
		if ($rsClinitionComments != '')
			$rsStudentCommentsCount = mysqli_num_rows($rsClinitionComments);
		// exit;
		// echo '<pre>';
		if ($rsStudentCommentsCount) {
			while ($rowStudentComments = mysqli_fetch_array($rsClinitionComments)) {

				// print_r($rowStudentComments);
				$StudentComment = $rowStudentComments['schoolCoarcOptionAnswerText'];
				$StudentCommentschoolCoarcQuestionId = $rowStudentComments['schoolCoarcQuestionId'];
				$StudentfirstName = $rowStudentComments['firstName'];
				$StudentlastName = $rowStudentComments['lastName'];
				$studentFullName = $StudentfirstName . " " . $StudentlastName;
				// 3276,3286,3293,3298,3307,3310
				// 879,889,896,901,910,913
				// 1848,1858,1865,1870,1879,1882
				// if ($StudentComment != '') {
				$results[] = array(
					"commentQuestionId" => $StudentCommentschoolCoarcQuestionId,
					"studentFullName" => $studentFullName,
					"studentComment" => $StudentComment

				);
				// }


				// if ($StudentCommentschoolCoarcQuestionId == 3276 || $StudentCommentschoolCoarcQuestionId == 879 || $StudentCommentschoolCoarcQuestionId == 1848)
				// 	$comment1 .= $studentFullName . ' - ' . $StudentComment . PHP_EOL;
				// else if ($StudentCommentschoolCoarcQuestionId == 3286 || $StudentCommentschoolCoarcQuestionId == 889 || $StudentCommentschoolCoarcQuestionId == 1858)
				// 	$comment2 .= $studentFullName . ' - ' . $StudentComment . PHP_EOL;
				// else if ($StudentCommentschoolCoarcQuestionId == 3293 || $StudentCommentschoolCoarcQuestionId == 896 || $StudentCommentschoolCoarcQuestionId == 1865)
				// 	$comment3 .= $studentFullName . ' - ' . $StudentComment . PHP_EOL;
				// else if ($StudentCommentschoolCoarcQuestionId == 3298 || $StudentCommentschoolCoarcQuestionId == 901 || $StudentCommentschoolCoarcQuestionId == 1870)
				// 	$comment4 .= $studentFullName . ' - ' . $StudentComment . PHP_EOL;
				// else if ($StudentCommentschoolCoarcQuestionId == 3307 || $StudentCommentschoolCoarcQuestionId == 910 || $StudentCommentschoolCoarcQuestionId == 1879)
				// 	$comment5 .= $studentFullName . ' - ' . $StudentComment . PHP_EOL;
				// else if ($StudentCommentschoolCoarcQuestionId == 3310 || $StudentCommentschoolCoarcQuestionId == 913 || $StudentCommentschoolCoarcQuestionId == 1882)
				// 	$comment6 .= $studentFullName . ' - ' . $StudentComment . PHP_EOL;
			}
		}

		// exit;
		// print_r($results);
		$resultCount = ($results != '') ? count($results) : 0;


		if ($resultCount) {
			// Group answers by question ID
			foreach ($results as $result) {
				$questionId = $result['commentQuestionId'];
				$name = $result['studentFullName'];
				$comment = $result['studentComment'];

				if (!isset($groupedResults[$questionId])) {
					$groupedResults[$questionId] = '';
				}

				if ($comment != '') {
					$groupedResults[$questionId] .= $name . ' - ' . $comment . PHP_EOL . PHP_EOL;
				}
			}

			$keys = array_keys($groupedResults);
			$commentCount = count($groupedResults);

			for ($i = 0; $i < $commentCount; $i++) {
				$commentVariable = 'comment' . ($i + 1);
				${$commentVariable} = $groupedResults[$keys[$i]];
			}
		}
		// echo 'comment1 '.$comment1.'</br>';
		// echo 'comment2 '.$comment2.'</br>';
		// echo 'comment3 '.$comment3.'</br>';
		// echo 'comment4 '.$comment4.'</br>';
		// echo 'comment5 '.$comment5.'</br>';
		// echo 'comment6 '.$comment6.'</br>';
		// exit;
		$printStartRowCounter = 7;
		if ($totalSection > 0) {
			while ($row = mysqli_fetch_array($CoarcSection)) {
				// print_r($row);
				$sectionMasterId = $row['sectionMasterId'];
				$title = $row['title'];

				// Check if studentId is 0 and title is '7. ADDITIONAL COMMENTS'
				if ($clinicianId == 0 && $title == '8. ADDITIONAL COMMENTS') {
					continue; // Skip this iteration of the loop
				}
				//Make Table Heading
				$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

				$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $title);
				$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
				$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B' . $printStartRowCounter)->setWidth('20');

				$printStartRowCounter++;


				//Make Table Heading
				$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

				$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Student Program Resource Survey');
				$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
				$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B' . $printStartRowCounter)->setWidth('20');


				$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, 'Strongly Agree');
				$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, 'Generally Agree');
				$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, 'Neutral');
				$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, 'Generally Disagree');
				$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, 'Strongly Disagree');
				$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, 'Comments');
				$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleBorderArray);
				$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':H' . $printStartRowCounter)->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');

				$printStartRowCounter++;
				$issurveyReport = 1;
				$Coarcquestion = $objPersonnelCoarc->GetAllPersonnelCoarcSurveyQuestionMaster($schoolId, $sectionMasterId, $issurveyReport);

				if ($Coarcquestion != '') {
					$totalCoarcquestion = mysqli_num_rows($Coarcquestion);
				}

				if ($totalCoarcquestion > 0) {
					while ($row = mysqli_fetch_array($Coarcquestion)) {
						// print_r($row);
						$coarcQuestionTitle = stripslashes($row['questionText']);
						$schoolPersonnelCoarcQuestionId = $row['schoolPersonnelCoarcQuestionId'];
						$schoolPersonnelCoarcQuestionType = $row['schoolPersonnelCoarcQuestionType'];

						$GetCalrows = $objPersonnelCoarc->GetCalculationsforPersonnelReport($schoolPersonnelCoarcQuestionId, $clinicianId, $coarcSurveyMasterId, $AssignedcoarcSurveyMasterId);

						$CountStronglyAgree = $GetCalrows ? $GetCalrows['CountStronglyAgree'] : 0;
						$CountAgree = $GetCalrows ? $GetCalrows['CountAgree'] : 0;
						$CountNeutral = $GetCalrows ? $GetCalrows['CountNeutral'] : 0;
						$CountDisagree = $GetCalrows ? $GetCalrows['CountDisagree'] : 0;
						$CountStronglyDisagree = $GetCalrows ? $GetCalrows['CountStronglyDisagree'] : 0;
						$CoarcOptionAnswerText = $GetCalrows ? $GetCalrows['schoolCoarcOptionAnswerText'] : 0;

						$TotalCount = $CountStronglyAgree + $CountAgree + $CountNeutral + $CountDisagree + $CountStronglyDisagree;

						// if($studentId > 0)
						// {
						// 	$CoarcOptionAnswerText = $GetCalrows['schoolCoarcOptionAnswerText'];
						// }
						// else{
						// 	$CoarcOptionAnswerText = $GetCalrows['commentCount'];
						// }

						if ($schoolPersonnelCoarcQuestionType > 0) {
							if ($TotalCount > 0) {
								$StronglyAgreePer = $CountStronglyAgree * 100 / $TotalCount;
								$AgreePer = $CountAgree * 100 / $TotalCount;
								$NeutralPer = $CountNeutral * 100 / $TotalCount;
								$DisagreePer = $CountDisagree * 100 / $TotalCount;
								$StronglyDisagreePer = $CountStronglyDisagree * 100 / $TotalCount;

								$StronglyAgreePer = number_format((float)$StronglyAgreePer, 2, '.', '') . '%';
								$AgreePer = number_format((float)$AgreePer, 2, '.', '') . '%';
								$NeutralPer = number_format((float)$NeutralPer, 2, '.', '') . '%';
								$DisagreePer = number_format((float)$DisagreePer, 2, '.', '') . '%';
								$StronglyDisagreePer = number_format((float)$StronglyDisagreePer, 2, '.', '') . '%';

								if ($StronglyAgreePer > 0)
									$StronglyAgreePer = number_format((float)$StronglyAgreePer, 2, '.', '') . '%';
								else
									$StronglyAgreePer = '';

								if ($AgreePer > 0)
									$AgreePer = number_format((float)$AgreePer, 2, '.', '') . '%';
								else
									$AgreePer = '';

								if ($NeutralPer > 0)
									$NeutralPer = number_format((float)$NeutralPer, 2, '.', '') . '%';
								else
									$NeutralPer = '';

								if ($DisagreePer > 0)
									$DisagreePer = number_format((float)$DisagreePer, 2, '.', '') . '%';
								else
									$DisagreePer = '';

								if ($StronglyDisagreePer > 0)
									$StronglyDisagreePer = number_format((float)$StronglyDisagreePer, 2, '.', '') . '%';
								else
									$StronglyDisagreePer = '';
							}
						} else {
							$StronglyAgreePer = "";
							$AgreePer = "";
							$NeutralPer = "";
							$DisagreePer = "";
							$StronglyDisagreePer = "";
						}
						if ($schoolPersonnelCoarcQuestionType == 5) {

							$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $coarcQuestionTitle);
							$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
							$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);

							$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, $CoarcOptionAnswerText);
							$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
							$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);


							$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, '');
							$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
							$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);

							$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, '');
							$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
							$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);

							$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, '');
							$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
							$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);

							$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, '');
							$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
							$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);
						} else {
							$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $coarcQuestionTitle);
							$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
							$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);

							$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, $StronglyAgreePer);
							$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
							$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);


							$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, $AgreePer);
							$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
							$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);

							$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, $NeutralPer);
							$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
							$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);

							$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, $DisagreePer);
							$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
							$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);

							$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, $StronglyDisagreePer);
							$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
							$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);
						}
						// 5180,5190,5197,5201,5208,5216,5219
						$rsComment = "";
						if ($clinicianId) {
							if ($schoolPersonnelCoarcQuestionId == 5180) {
								// if($comment1 != '')
								// {
								$rsComment = $comment1;
								// $spreadsheet->getActiveSheet()->mergeCells("H10:H20");
								// }

							} else if ($schoolPersonnelCoarcQuestionId == 5190) {
								$rsComment = $comment2;
							} else if ($schoolPersonnelCoarcQuestionId == 5197) {
								$rsComment = $comment3;
							} else if ($schoolPersonnelCoarcQuestionId == 5201) {
								$rsComment = $comment4;
							} else if ($schoolPersonnelCoarcQuestionId == 5208) {
								$rsComment = $comment5;
							} else if ($schoolPersonnelCoarcQuestionId == 5216) {
								$rsComment = $comment6;
							} else if ($schoolPersonnelCoarcQuestionId == 5219) {
								$rsComment = $comment7;
							}

							$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, $rsComment);
							$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
							$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);
						}

						$printStartRowCounter++;
					}
					// comment 1
					$comment_value1 = $spreadsheet->getActiveSheet()->getCell('H22')->getValue();
					$prev_value = $spreadsheet->getActiveSheet()->getCell('H21')->getValue();
					$spreadsheet->getActiveSheet()->setCellValue('H9', $comment1 . "\n");
					$spreadsheet->getActiveSheet()->setCellValue('H22', $prev_value . "\n");
					$spreadsheet->getActiveSheet()->mergeCells("H9:H22");

					// comment 2
					$comment_value2 = $spreadsheet->getActiveSheet()->getCell('H33')->getValue();
					$spreadsheet->getActiveSheet()->setCellValue('H25', $comment2 . "\n");
					$spreadsheet->getActiveSheet()->setCellValue('H33', $prev_value . "\n");
					$spreadsheet->getActiveSheet()->mergeCells("H25:H33");

					// comment 3
					$comment_value3 = $spreadsheet->getActiveSheet()->getCell('H41')->getValue();
					$spreadsheet->getActiveSheet()->setCellValue('H36', $comment3 . "\n");
					$spreadsheet->getActiveSheet()->setCellValue('H41', $prev_value . "\n");
					$spreadsheet->getActiveSheet()->mergeCells("H36:H41");

					// comment 4
					$comment_value4 = $spreadsheet->getActiveSheet()->getCell('H46')->getValue();
					$spreadsheet->getActiveSheet()->setCellValue('H44', $comment4 . "\n");
					$spreadsheet->getActiveSheet()->setCellValue('H46', $prev_value . "\n");
					$spreadsheet->getActiveSheet()->mergeCells("H44:H46");

					// comment 5
					$comment_value5 = $spreadsheet->getActiveSheet()->getCell('H54')->getValue();
					$spreadsheet->getActiveSheet()->setCellValue('H49', $comment5 . "\n");
					$spreadsheet->getActiveSheet()->setCellValue('H54', $prev_value . "\n");
					$spreadsheet->getActiveSheet()->mergeCells("H49:H54");

					// comment 6
					$comment_value6 = $spreadsheet->getActiveSheet()->getCell('H63')->getValue();
					$spreadsheet->getActiveSheet()->setCellValue('H57', $comment6 . "\n");
					$spreadsheet->getActiveSheet()->setCellValue('H63', $prev_value . "\n");
					$spreadsheet->getActiveSheet()->mergeCells("H57:H63");

					// comment 7
					$comment_value7 = $spreadsheet->getActiveSheet()->getCell('H67')->getValue();
					$spreadsheet->getActiveSheet()->setCellValue('H66', $comment7 . "\n");
					$spreadsheet->getActiveSheet()->setCellValue('H67', $prev_value . "\n");
					$spreadsheet->getActiveSheet()->mergeCells("H66:H67");
				}
			}

			if ($clinicianId == 0) {
				$title = '9. Clinical Trac CoARC Survey Results';

				//Make Table Heading
				$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

				$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $title);
				$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
				$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B' . $printStartRowCounter)->setWidth('20');

				$printStartRowCounter++;


				//Make Table Heading
				$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

				$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, '');
				$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
				$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B' . $printStartRowCounter)->setWidth('20');


				$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, 'Survey Start Date');
				$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, 'Survey End Date');
				$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, 'Number of Surveys Sent');
				$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, 'Number of Surveys Completed');
				$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getColumnDimension('F')->setWidth(30);

				$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, 'Number of Surveys Pending');
				$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getColumnDimension('G')->setWidth(30);

				$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, 'Return Rate');
				$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);
				$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getColumnDimension('H')->setWidth(30);

				$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':H' . $printStartRowCounter)->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');

				$printStartRowCounter++;

				$rowsClinicianData = $objCoarc->GetClinicianDetailsByCoarcSurvey($coarcSurveyMasterId, $currentSchoolId);

				$totalClinicianCount = 0;
				if ($rowsClinicianData != '') {
					$totalClinicianCount = mysqli_num_rows($rowsClinicianData);
				}
				$pendingStatus = 0;
				$completedStatus = 0;
				if ($totalClinicianCount > 0) {

					while ($row = mysqli_fetch_array($rowsClinicianData)) {

						$clinicianId = $row['clinicianId'];
						$startDate = $row['startDate'];
						$endDate = $row['endDate'];
						$startDateTimestamp = strtotime($startDate);
						$endDateTimestamp = strtotime($endDate);
						$status = $objCoarc->GetPersonnelCoarcSurveyStatus($coarcSurveyMasterId, $clinicianId);


						if ($status > 0) {
							$completedStatus += $status;
						}
					}
					// echo $status ; exit;
				}
				$pendingStatus = $totalClinicianCount - $completedStatus;
				//For Return Rate
				$returnRate = ($completedStatus / $totalClinicianCount) * 100;
				$returnRate = number_format((float)$returnRate, 2, '.', '') . '%';
				$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, '');
				$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
				$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);

				$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, (date('m/d/Y', $startDateTimestamp)));
				$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);


				$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, (date('m/d/Y', $endDateTimestamp)));
				$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);

				$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, $totalClinicianCount);
				$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);

				$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, $completedStatus);
				$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);

				$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, $pendingStatus);
				$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);

				$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, $returnRate);
				$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);

				//new  sheet
				$sheet2 = $spreadsheet->createSheet();
				$sheet2->setTitle('Additional comments');

				// $sheet2->setCellValue('A1', 'Hello from Sheet 2');

				//Print Heading	
				$headerstyleArray = array('font'  => array('bold'  => true, 'size'  => 16));

				$sheet2->mergeCells("B2:F2");
				$sheet2->setCellValue('B2', 'Additional Comments');
				$sheet2->getStyle('B2')->applyFromArray($headerstyleArray);
				$sheet2->getStyle('B2')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$sheet2->getStyle('B2')
					->getFill()
					->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
					->getStartColor()
					->setRGB('E0E0E0');
				$styleBorderArrays = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];	
				$sheet2->getStyle('B2:F2')->applyFromArray($styleBorderArrays);

				$styleArrays = array('font'  => array('bold'  => true, 'size'  => 12));

				// $sheet2->mergeCells("B4:F4");
				// $sheet2->setCellValue('B4', $fullName);
				$sheet2->getStyle('B4')->applyFromArray($styleArrays);
				$sheet2->getStyle('B4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$sheet2->getStyle('B4')
					->getFill()
					->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
					->getStartColor()
					->setRGB('E0E0E0');
				$styleBorderArrays = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];	
				$sheet2->getStyle('B4:F4')->applyFromArray($styleBorderArrays);

				// echo $sectionMasterId;
				$rowsClinicianIds = $objPersonnelCoarc->GetPersonnelCoarcSurveyStudentIds($sectionMasterId, $coarcSurveyMasterId);
				$totalClinicianCount = 0;
				if ($rowsClinicianIds != '') {
					$totalClinicianCount = mysqli_num_rows($rowsClinicianIds);
				}
				$printStartRowCounters = 4;

				//Make Table Heading
				$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

				$sheet2->setCellValue('B' . $printStartRowCounters, 'Clinician');
				$sheet2->getStyle('B' . $printStartRowCounters)->applyFromArray($styleArray);
				$sheet2->getStyle('B' . $printStartRowCounters)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
				$sheet2->getDefaultColumnDimension('B' . $printStartRowCounters)->setWidth('20');


				$sheet2->setCellValue('C' . $printStartRowCounters, 'Comment');
				$sheet2->getStyle('C' . $printStartRowCounters)->applyFromArray($styleArray);
				$sheet2->getStyle('C' . $printStartRowCounters)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$sheet2->setCellValue('D' . $printStartRowCounters, 'Year');
				$sheet2->getStyle('D' . $printStartRowCounters)->applyFromArray($styleArray);
				$sheet2->getStyle('D' . $printStartRowCounters)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$sheet2->setCellValue('E' . $printStartRowCounters, 'Month');
				$sheet2->getStyle('E' . $printStartRowCounters)->applyFromArray($styleArray);
				$sheet2->getStyle('E' . $printStartRowCounters)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$sheet2->setCellValue('F' . $printStartRowCounters, 'Overall Ratings');
				$sheet2->getStyle('F' . $printStartRowCounters)->applyFromArray($styleArray);
				$sheet2->getStyle('F' . $printStartRowCounters)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

				$sheet2->getStyle('B' . $printStartRowCounters . ':F' . $printStartRowCounters)->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
				$printStartRowCounters++;
				$comment = '';
				$year = 0;
				$month = 0;
				$overall = 0;
				// Declare dataArray outside the loop
				$dataArray = array();

				if ($totalClinicianCount > 0) {
					// Initialize dataArray for all students
					$dataArray = array();

					while ($row = mysqli_fetch_array($rowsClinicianIds)) {

						$clinicianId = stripslashes($row['clinicianId']);
						$schoolCoarcQuestionId = $row['schoolPersonnelCoarcQuestionId'];
						$rowsClinicianDetails = $objPersonnelCoarc->GetPersonnelCoarcSurveyQustionDetails($sectionMasterId, $clinicianId, $coarcSurveyMasterId);

						// Initialize associative array to store answers for each question
						$answers = array();

						if ($rowsClinicianDetails) {
							while ($rowDetail = mysqli_fetch_array($rowsClinicianDetails)) {
								$clinicianFirstName = stripslashes($rowDetail['clinicianFirstName']);
								$clinicianLastName = stripslashes($rowDetail['clinicianLastName']);
								$clinicianName = $clinicianFirstName . ' ' . $clinicianLastName;
								$question = stripslashes($rowDetail['questionText']);
								$answer = stripslashes($rowDetail['schoolCoarcOptionAnswerText']);

								// Store answers in the associative array based on the question
								$answers[$question] = $answer;
							}
						}

						// Append data for the current student to dataArray
						$dataArray[] = array(
							'clinicianName' => $clinicianName, // Make sure to set $clinicianName appropriately
							'answers' => $answers
						);
					}
				}



				// Populate Excel sheet using dataArray
				$printStartRowCounter = 5; // You can set the starting row as needed
				$styleArray = array('font' => array('size' => 10));

				foreach ($dataArray as $data) {
					// Assuming $data['clinicianName'] is set appropriately
					$sheet2->setCellValue('B' . $printStartRowCounter, $data['clinicianName']);
					$sheet2->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left', 'vertical' => 'top'));
					$sheet2->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);

					// Loop through the answers and display them in the corresponding columns
					foreach ($data['answers'] as $question => $answer) {
						switch ($question) {
							case 'Please provide any additional comments or recommendations for improvement:':
								$sheet2->setCellValue('C' . $printStartRowCounter, $answer);
								$sheet2->getStyle('C' . $printStartRowCounter)->getAlignment()->setWrapText(true)->applyFromArray(array('vertical' => 'top'));
								$sheet2->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);
								break;
							case 'Years:':
								$sheet2->setCellValue('D' . $printStartRowCounter, $answer);
								$sheet2->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center', 'vertical' => 'top'));
								$sheet2->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);
								break;
							case 'Months:':
								$sheet2->setCellValue('E' . $printStartRowCounter, $answer);
								$sheet2->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center', 'vertical' => 'top'));
								$sheet2->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);
								break;
							case 'OVERALL RESOURCE RATING:':
								$sheet2->setCellValue('F' . $printStartRowCounter, $answer);
								$sheet2->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center', 'vertical' => 'top'));
								$sheet2->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);
								break;
								// Add more cases for other questions if needed
						}
					}

					// Increment the row counter for the next student
					$printStartRowCounter++;
				}

				$spreadsheet->setActiveSheetIndex(0);
			}
		}
		//Make Border
		$printStartRowCounter--;
		$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];	
		$spreadsheet->getActiveSheet()->getStyle('B6:H7' . $printStartRowCounter)->applyFromArray($styleBorderArray);

		// Auto size columns for each worksheet
		foreach (range('B', 'E') as $columnID) {
			$spreadsheet->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
		}
		$reportname = 'ClinicianPersonnelReport_';
		break;

	default:
		echo "<b>Please Select Valid Type.</b>";
		break;
}
// echo '<pre>';

// print_r($spreadsheet);
// exit;
$spreadsheet->setActiveSheetIndex(0);

$coarcSurveyTitle = $objCoarc->GetCoarcSurveyTitle($coarcSurveyMasterId);
$currentDate = date('m_d_Y_h_i');

header('Content-type: application/vnd.ms-excel; charset=UTF-8');
header('Content-Disposition: attachment;filename="' . $coarcSurveyTitle . ' ' . $today . '.xls"');
header("Pragma: no-cache");
header("Expires: 0");

$writer = IOFactory::createWriter($spreadsheet, 'Xls');
$writer->save('php://output');
?>