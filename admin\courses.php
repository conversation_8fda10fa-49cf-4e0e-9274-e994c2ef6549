<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include('../class/clsCourses.php');
include('../class/clsSemester.php');

$isActiveHospital = 0;
$schoolId = 0;
$semesterId = 0;
$transchooldisplayName = '';
$loggedUserLocationId = '';

$loggedUserLocationId = $_SESSION["loggedUserLocation"];
if (isset($_GET['schoolId'])) //Edit Mode
{
    $schoolId = $_GET['schoolId'];
    $schoolId = DecodeQueryData($schoolId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}
$title = "Courses  ";
if (isset($_GET['semesterId'])) {
    $semesterId = $_GET['semesterId'];
    $semesterId = DecodeQueryData($semesterId);
}

if (isset($_GET['active']))
    $isActiveHospital = DecodeQueryData($_GET['active']);


if ($isActiveHospital == 1)
    $clsBtnActiveAll = "active";
elseif ($isActiveHospital == 0)
    $clsBtnActive = "active";
elseif ($isActiveHospital == 2)
    $clsBtnInActive = "active";
else
    $clsBtnActive = "active";

//CREATE OBJECT
$objCourses = new clsCourses();
$totalCourses = 0;
$rowsCourses = $objCourses->GetAllSchoolCourses($schoolId, $semesterId, $isActiveHospital, $loggedUserLocationId);
if ($rowsCourses != '') {
    $totalCourses = mysqli_num_rows($rowsCourses);
}
unset($objCourses);
$objSemester = new clsSemester();
$Semester = $objSemester->GetAllSemester($currentSchoolId);
unset($objSemester);


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    <style>
        .mt1 {
            margin-top: 7px;
        }
    </style>

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active">Courses</li>
                </ol>
            </div>

            <div class="pull-right">
                <a class="btn btn-link" href="addcourse.html">Add</a>
            </div>


        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Course added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Course updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Course deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>

        <div class="row margin_bottom_ten">
            <div class="btn-group pull-right" style="margin-right: 14px;" role="group" aria-label="First group">
                <a role="button" class="btn btn-primary <?php echo $clsBtnActive; ?>" href="courses.html?schoolId=<?php echo EncodeQueryData($currentSchoolId); ?>&active=<?php echo EncodeQueryData(0); ?>">Active</a>
                <a role="button" class="btn btn-primary  <?php echo $clsBtnInActive; ?>" href="courses.html?schoolId=<?php echo EncodeQueryData($currentSchoolId); ?>&active=<?php echo EncodeQueryData(2); ?>">Inactive</a>
                <a role="button" class="btn btn-primary <?php echo $clsBtnActiveAll; ?>" href="courses.html?schoolId=<?php echo EncodeQueryData($currentSchoolId); ?>&active=<?php echo EncodeQueryData(1); ?>">All</a>
            </div>
        </div>
        <div class="row">
            <div class="col-md-4 pull-right margin_bottom_ten">
                <div class="form-group">

                    <div class="col-md-6 pull-right padding_right_zero">
                        <select id="cboSemester" name="cboSemester" class="form-control input-md select2_single">
                            <option value="" selected>Select</option>

                            <?php
                            if ($Semester != "") {
                                while ($row = mysqli_fetch_assoc($Semester)) {
                                    $selsemesterId  = $row['semesterId'];
                                    $name  = stripslashes($row['title']);

                            ?>
                                    <option value="<?php echo (EncodeQueryData($selsemesterId)); ?>" <?php if ($semesterId == $selsemesterId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                            <?php

                                }
                            }
                            ?>
                        </select>
                    </div>
                    <label class="col-md-3 control-label  pull-right mt1" for="cboSemester">Semester:</label>

                </div>
            </div>
        </div>

        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Title </th>
                    <th>Semester </th>
                    <th style="text-align:center">Rotations</th>
                    <th style="text-align:center">Required Hours</th>
                    <th style="text-align:center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalCourses > 0) {
                    while ($row = mysqli_fetch_array($rowsCourses)) {
                        $courseId = $row['courseId'];
                        $schoolId = $row['schoolId'];
                        $rotationCount = $row['rotationCount'];
                        $studentCount = $row['studentCount'];
                        $courseStartDate = $row['courseStartDate'];
                        $courseEndDate = $row['courseEndDate'];
                        $title = stripslashes($row['title']);
                        $semestertitle = stripslashes($row['semestertitle']);
                        $totalRotationCount = 0;
                        $totalUserCount = 0;
                        $isActive = $row['isActive'];
                        $isActiveLabel = ($isActive == 0) ? 'Active' : 'Inactive';
                        $courseHours = $row['courseHours'];
                        $objCourses = new clsCourses();
                        $isActiveRotations = $objCourses->GetRotationsCountByCourses($currentSchoolId, $courseId);
                        unset($objCourses);
                ?>
                        <tr>
                            <td>
                                <?php echo ($title); ?>
                            </td>
                            <td>
                                <?php echo ($semestertitle); ?>
                            </td>
                            <td style="text-align:center">
                                <?php
                                $displayText = 'Add';
                                if ($rotationCount > 0) {
                                    $displayText = $rotationCount;

                                ?>
                                    <a title='Add rotation to course' href="rotations.html?courseId=<?php echo (EncodeQueryData($courseId)); ?>"><?php echo $displayText; ?></a>
                                <?php } else { ?>
                                    <a title='Add rotation to course' href="addrotations.html?courseId=<?php echo (EncodeQueryData($courseId)); ?>"><?php echo $displayText; ?></a>
                                <?php } ?>
                            </td>

                            <td style="text-align:center"><?php echo ($courseHours); ?></td>
                            <td style="text-align: center">
                             
                                <a href="addcourse.html?id=<?php echo (EncodeQueryData($courseId)); ?>&type=copy">Copy</a>

                                | <a href="addcourse.html?id=<?php echo (EncodeQueryData($courseId)); ?>">Edit</a>


                                <?php

                                if ($rotationCount > 0) {
                                ?>
                                    | <a onclick="javascript:ShowCountMessage();" href="javascript:void(0);">Delete</a>
                                <?php
                                } else { ?>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow" courseId="<?php echo EncodeQueryData($courseId); ?>" courseName="<?php echo ($title); ?>">Delete</a>
                                <?php
                                }

                                ?>
                                | <a href="javascript:void(0);" onclick="updateCourseStatus(this)" courseId="<?php echo $courseId; ?>" isActiveRotations="<?php echo $isActiveRotations; ?>" type="A" isCourseStatus="<?php echo $isActive; ?>"><?php echo $isActiveLabel; ?></a>

                            </td>
                        </tr>
                <?php


                    }
                }
                ?>



            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_tags").select2({
                'placeholder': 'Select'
            }); //for multiple selection
            $(".select2_single").select2();

        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "aoColumns": [{
                "sWidth": "10%"
            }, {
                "sWidth": "10%"
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "10%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "10%",
                "sClass": "alignCenter",
                "bSortable": false
            }, ]
        });

        // ajax call for deleteAjaxRow

        // ajax call for delete
        function ShowDeleteMessage() {
            alertify.alert('Warning', 'This is the Primary User. You can\'t delete this.');
        }

        function ShowCountMessage() {
            alertify.alert('Warning', 'This course is already in used. You can\'t delete this.');
        }

        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var courseId = $(this).attr('courseId');
            var title = $(this).attr('courseName');
            var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';

            alertify.confirm('Course: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: courseId,
                        type: 'course',
                        userId: userId
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $("#cboSemester").change(function() {
            var semesterId = $(this).val();

            if (semesterId) {
                window.location.href = "courses.html?semesterId=" + semesterId;
            } else {
                window.location.href = "courses.html";
            }
        });


        function updateCourseStatus(eleObj) {
            var courseId = $(eleObj).attr('courseId');
            var type = $(eleObj).attr('type');
            var isCourseStatus = $(eleObj).attr('isCourseStatus');

            if (type == 'A') {
                if (isCourseStatus == 0)
                    isStatus = 1;
                else
                    isStatus = 0;

                var isActiveRotations = $(eleObj).attr('isActiveRotations');
                if (isActiveRotations > 0) {
                    alertify.alert('Warning!', 'The course has assigned for active rotations and cannot be made inactive.');
                    return false;
                }

            }

            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_update_course_status.html",
                data: {
                    id: courseId,
                    isStatus: isStatus,
                    type: type
                },
                success: function() {

                    if (type == 'A') {
                        $(eleObj).attr('isCourseStatus', isStatus);
                        var isCourseStatusLabel = (isStatus == 0) ? 'Active' : 'Inactive';

                        $(eleObj).text(isCourseStatusLabel);
                    }
                    alertify.success('Updated');
                }
            });
        }
    </script>


</body>

</html>