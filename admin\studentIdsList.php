<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include('../class/clsStudent.php');
include('../class/clsStudentRankMaster.php');

$display_from_date = '';
$display_to_date = '';

if (isset($_GET['fromDate']) && isset($_GET['toDate'])) {
    $display_from_date = $_GET['fromDate'];
    $display_from_date = date("m/d/Y", strtotime($display_from_date));
    $display_to_date = $_GET['toDate'];
    $display_to_date = date("m/d/Y", strtotime($display_to_date));
}
$rankId = 0;
if (isset($_GET['rankId'])) {
    $rankId = $_GET['rankId'];
    $rankId = DecodeQueryData($rankId);
}

$status = isset($_GET['status']) ? $_GET['status'] : '';
$objStudent = new clsStudent();
//Get All Student List
$rowsStudentData = $objStudent->GetAllSchoolStudentList($currentSchoolId, $rankId, 0, $display_from_date, $display_to_date);
$totalStudentCount = 0;
if ($rowsStudentData != '') {
    $totalStudentCount = mysqli_num_rows($rowsStudentData);
}
unset($objStudent);
//StudentRank
$objStudentRankMaster = new clsStudentRankMaster();
$ranks = $objStudentRankMaster->GetAllStudentRankBySchool($currentSchoolId);
unset($objStudentRankMaster);

$isRow = isset($_GET['isRow']) ? $_GET['isRow'] : 0;

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Student List</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <style>
        .mt-1 {
            margin-top: 10px;
            padding-left: 55px;
        }
    </style>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">Student Id's List</li>
                </ol>
            </div>
            <div class="pull-right">
                <a class="btn btn-link impostStudentList" href="importstudent.html?type=1">Import Student Id</a><?php if ($isActiveCanvas) { ?>|<a class="btn btn-link impostStudentList" href="importstudent.html?type=3">Import Access Token</a><?php } ?>|<a class="btn btn-link" href="exportstudentIds.html">Export</a>
            </div>
        </div>
    </div>

    <div class="container">
        <?php
        if (isset($_GET["status"])) {
            $alertType = 'alert-danger';
            $message = 'Error occurred.';

            switch ($status) {
                case 'Imported':
                    $alertType = 'alert-success';
                    $message = "Student Id's Imported";
                    break;
                case 'Importerror':
                    $message = 'Please Import .csv file';
                    break;
                case 'Error':
                    $message = 'Error occurred while importing data from the CSV file at Row No. '.$isRow. '.  The Student Id number '.$_GET["value"].' is already exist.';
                    break;
            }
        ?>
            <div class="alert <?php echo $alertType; ?> alert-dismissible fade in" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                <?php echo $message; ?>
            </div>
        <?php
        }
        ?>

        <?php if ($isActiveCanvas) { ?>
            <div class="row">
                <div class="col-md-12 text-right">
                    <button id="isSendCanvasRequestToStudent" class="btn btn-primary text-right disabled">Send canvas request to student </button>
                </div>
            </div>
        <?php } ?>
        <div class="row margin_top_twenty">
            <form name="studentIdslist" id="studentIdslist" method="GET" action="studentIdsList.html">
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="col-md-4 control-label text-right margin_top_seven" for="fromDate">From Date</label>
                        <input type='hidden' name="rankId" id="rankId" value="<?php echo EncodeQueryData($rankId); ?>">

                        <div class='input-group date col-md-7' name="fromDate" id='fromDate'>
                            <input type='text' name="fromDate" id="fromDate" placeholder="MM-DD-YYYY" value="<?php echo $display_from_date; ?>" class="dateInputFormat form-control input-md r rotation_date fromDate" data-parsley-errors-container="#error-txtDate" required/>
                            <span class="input-group-addon calender-icon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        <div id="error-txtDate"></div>
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="col-md-4 control-label text-right margin_top_seven" for="toDate">To Date</label>
                        <div class='input-group date col-md-7' id='toDate'>
                            <input type='text' name="toDate" id="toDate" class="dateInputFormat form-control input-md  rotation_date toDate" placeholder="MM-DD-YYYY" value="<?php echo $display_to_date; ?>" data-parsley-errors-container="#error-txtDate" required/>
                            <span class="input-group-addon calender-icon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        <div id="error-txtDate"></div>
                    </div>
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <button id="btnSearch" name="btnSearch" class="btn btn-success">Search</button>
                    </div>
                </div>
            </form>
        </div>
        <div id="divTopLoading">Loading...</div>
        <div class="row">
            <div class="col-md-9"></div>
            <div class="col-md-3">
                <!-- <div class="form-group"> -->
                <label class="col-md-4 control-label mt-1" for="cborank">Rank</label>
                <div class="col-md-8">
                    <select id="cborank" name="cborank" class="form-control select2_single">
                        <option value="" selected>Select All</option>

                        <?php
                        if ($ranks != "") {
                            while ($row = mysqli_fetch_assoc($ranks)) {
                                $selrankId  = $row['rankId'];
                                $name  = stripslashes($row['title']);

                        ?>
                                <option value="<?php echo EncodeQueryData($selrankId); ?>" <?php if ($rankId == $selrankId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                        <?php
                            }
                        }
                        ?>
                    </select>
                </div>
                <!-- </div> -->
            </div>
        </div>
        <br>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <?php if ($isActiveCanvas) { ?>
                        <th class="text-center"> <input value='' type="checkbox" name="selectAll" id="selectAll" class="hide"></th>
                    <?php } ?>
                    <th class="text-center">Student Id</th>
                    <?php if ($isActiveCanvas) { ?>
                        <th class="text-center">Canvas Access Token</th>
                    <?php } ?>
                    <th>Student Info</th>
                    <th class="text-center">Account Created</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalStudentCount > 0) {
                    while ($row = mysqli_fetch_array($rowsStudentData)) {
                        $rank = $row['rank'];
                        $email = $row['email'];
                        $recordIdNumber = $row['recordIdNumber'] ? $row['recordIdNumber'] : '';
                        $canvasAcessToken = $row['canvasAcessToken'] ? $row['canvasAcessToken'] : '';
                        $canvasUserId = $row['canvasUserId'] ? $row['canvasUserId'] : 0;

                        $studentId = $row['studentId'];
                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $fullName = $firstName . ' ' . $lastName;
                        $createdDate = ($row['createdDate']);
                        $createdDate = date("m/d/Y", strtotime($createdDate));
                        $rank = stripslashes($row['rank']);
                ?>
                        <tr>
                            <?php if ($isActiveCanvas) { ?>
                                <td class="text-center">
                                    <?php if ($canvasUserId == 0) { ?>
                                        <input value='<?php echo $studentId; ?>' type="checkbox" name="" id="" class="isCheckbox">
                                    <?php } else { ?>
                                        -
                                    <?php } ?>
                                </td>
                            <?php } ?>
                            </td>
                            <?php //} 
                            ?>
                            <td class="text-center">
                                <span class="hide" id="isHideRecordId_<?php echo $studentId; ?>"><?php echo ($recordIdNumber); ?></span>
                                <input type="text" id="recordIdNumber" name="recordIdNumber" value="<?php echo ($recordIdNumber); ?>" style="width:100%;" class="updateAjaxRow text-center validateOnlynumbers " maxlength="18" updateTtype="R" studentId="<?php echo EncodeQueryData($studentId); ?>" studentIdWithoutEncoded="<?php echo ($studentId); ?>">
                            </td>
                            <?php if ($isActiveCanvas) { ?>
                                <td>
                                    <textarea name="canvasAcessToken" id="canvasAcessToken" cols="10" rows="3" class="updateAjaxRow" updateTtype="A" studentId="<?php echo EncodeQueryData($studentId); ?>"><?php echo ($canvasAcessToken); ?></textarea>
                                </td>
                            <?php } ?>
                            <td><?php echo $fullName; ?>
                                <br>
                                Rank: <?php echo $rank; ?> <br>
                                Email: <a href="addstudent.html?id=<?php echo (EncodeQueryData($studentId)); ?>&type=<?php echo (EncodeQueryData(1)); ?>"><?php echo $email; ?></a> <br>
                            </td>
                            <td class="text-center"><?php echo $createdDate; ?></td>
                        </tr>
                <?php
                    }
                }  ?>
            </tbody>
        </table>
    </div>


    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>

    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <!-- <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/common.js"></script> -->

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            var status = '<?php echo $status ?>';
            if (status == 'updated')
                alertify.success('Updated');
            var newUrl = 'studentIdsList.html';
            history.pushState({}, null, newUrl);
            $("#divTopLoading").addClass('hide');

            $(function() {
                $("#fromDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            $(function() {
                $("#toDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            var checkboxCount = $('.isCheckbox').length;
            if (checkboxCount > 0)
                $('#selectAll').removeClass('hide');



        });

        $('.impostStudentList').magnificPopup({
            type: 'ajax',
            'closeOnBgClick': false
        });

        $(".select2_single").select2();
        $('#select2-copySchool-container').addClass('required-select2');
        $('#form-control step2 input-md select2_single').addClass('required-select2');

        var current_datatable = $("#datatable-responsive").DataTable({
            "aaSorting": [],
            'iDisplayLength': 100,
            "aoColumns": [
                <?php if ($isActiveCanvas) { ?> {
                        "sWidth": "5%",
                        "bSortable": false
                    },
                <?php } ?> {
                    "sWidth": "10%"
                },
                <?php if ($isActiveCanvas) { ?> {
                        "sWidth": "30%"
                    },
                <?php } ?> {
                    "sWidth": "20%"
                },
                {
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                }
            ],

            // columnDefs: [{
            //     type: 'date-uk',
            //     targets: 0
            // }]
        });


        $("#cborank").change(function() {
            var rankId = $(this).val();
            var fromDate = $('.fromDate').val();
            var toDate = $('.toDate').val();

            if (rankId && fromDate && toDate) {
                window.location.href = "studentIdsList.html?rankId=" + rankId + "&fromDate=" + fromDate + "&toDate=" + toDate;

            } else if (rankId)
                window.location.href = "studentIdsList.html?rankId=" + rankId;
            else
                window.location.href = "studentIdsList.html";

        });

        // ajax call for update Title      
        $(document).on('change', '.updateAjaxRow', function() {
            var studentId = $(this).attr('studentId');
            var studentIdWithoutEncoded = $(this).attr('studentIdWithoutEncoded');
            var type = $(this).attr('updateTtype');
            var schoolId = '<?php echo $currentSchoolId; ?>';
            var selectedRecordId = $(this);
            var value = $(this).val();

            $.ajax({
                type: "GET",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_studetRecordId.html",
                data: {
                    id: studentId,
                    value: value,
                    type: type,
                    schoolId: schoolId
                },
                success: function(data) {
                    // console.log(data);
                    if (data == 'success') {
                        alertify.success('Updated');
                    } else if (data == 'already exist') {
                        alertify.error('Student Id is already exist');
                        selectedRecordId.val('');
                    } else {
                        alertify.error('Error Occur');
                    }


                }
            });
        });

        $(document).on('click', '#isSendCanvasRequestToStudent', function() {

            var isDisabled = $(this).hasClass('disabled');
            if (isDisabled == true)
                return false;

            // var studentIds = $('.isCheckbox:checked').attr('studentId');;
            var studentIds = [];
            $('.isCheckbox:checked').each(function(i) {
                studentIds[i] = $(this).val();
            });

            $.ajax({
                method: 'get',
                data: {
                    'studentIds': studentIds
                },
                url: "sendCanvasRequestToStudent.html",
                success: function(result) {
                    alertify.success(result);
                    if (result == 'Request Sent')
                        location.reload();

                }
            });
        });

        $(document).on('click', '.isCheckbox', function() {

            if (!$(this).prop("checked")) {
                $("#selectAll").prop("checked", false);
            }

            var checkedCount = $('.isCheckbox:checked').length;
            $('#isSendCanvasRequestToStudent').addClass('disabled');
            if (checkedCount > 0)
                $('#isSendCanvasRequestToStudent').removeClass('disabled');

        });

        $("#selectAll").click(function() {
            $(".isCheckbox").prop('checked', $(this).prop('checked'));
            var checkedCount = $('.isCheckbox:checked').length;
            $('#isSendCanvasRequestToStudent').addClass('disabled');
            if (checkedCount > 0)
                $('#isSendCanvasRequestToStudent').removeClass('disabled');
            // $(".isCheckbox").trigger('click');
        });
    </script>
</body>

</html>