<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsStudentRankMaster.php');
include('../setRequest.php');


if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {

	$title  = isset($_POST['txtRank']) ? trim($_POST['txtRank']) : "";
	$sordOrder = $_POST['txtRankOrder'];
	$rankId = isset($_GET['id']) ? DecodeQueryData($_GET['id']) : 0;
	$status = ($rankId > 0) ? 'updated' : 'added';


	if ($title == "") {
		header('location:addranking.html?status=mandatory&id=' . encryptValue($rankId));
		exit();
	}

	//Save data
	$objStudentRankMaster = new clsStudentRankMaster();
	$objStudentRankMaster->title = $title;
	$objStudentRankMaster->sordOrder = $sordOrder;
	$objStudentRankMaster->schoolId = $currentSchoolId;
	$retrankId = $objStudentRankMaster->SaveStudentRank($rankId);
	unset($objStudentRankMaster);

	if ($retrankId > 0) {
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = ($rankId > 0) ? $objLog::EDIT : $objLog::ADD;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objStudentRankMaster = new clsStudentRankMaster();
		$objStudentRankMaster->saveRankAuditLog($retrankId, $_SESSION["loggedUserId"], $userType, $action, $IsMobile = 0);
		unset($objStudentRankMaster);
		unset($objLog);
		//Audit Log End

		header('location:viewranking.html?status=' . $status);
	} else {
		header('location:addranking.html?status=error');
	}
} else {
	header('location:viewranking.html');
	exit();
}
