<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');       
    include('../class/clsCheckoffSectionMaster.php');       
    include('../class/clsCheckoffTopicMaster.php');       
    include('../setRequest.php'); 	
	
	
	$currentSchoolId;
	$schoolSectionId =0;
	$encodedSchoolTopicId=0;
	$encodedSchoolSectionId=0;
	$topicid =0;
    $loggedAsBackUserId = isset($_SESSION["loggedAsBackUserId"]) ? isset($_SESSION["loggedAsBackUserId"]) : 0;

	if(isset($_GET['schoolSectionId']))
	{
		$encodedSchoolSectionId=$_GET['schoolSectionId'];
		$schoolSectionId=DecodeQueryData($_GET['schoolSectionId']);
	}
	if(isset($_GET['topicid']))
	{
		$encodedSchoolTopicId=$_GET['topicid'];
		$topicid=DecodeQueryData($_GET['topicid']);
    }
    
    //For Checkoff Section
	$objCheckoffSectionMaster = new clsCheckoffSectionMaster();			
	$rowsCheckoffSection = $objCheckoffSectionMaster->GetCheckoffSectionByCheckoffTopicMasterId($currentSchoolId,$topicid,$schoolSectionId);
	
	$totalCount = 0;
	if($rowsCheckoffSection !='')
	{
		$totalCount = mysqli_num_rows($rowsCheckoffSection);
    }
    
    //For Checkoff Topic Title
	$objCheckoffTopicMaster=new clsCheckoffTopicMaster();
    $GetSingleTopicId=$objCheckoffTopicMaster->GetSingleCheckoffTopicId($currentSchoolId,$topicid,$schoolSectionId);
	$TopicTitleId= $GetSingleTopicId ? $GetSingleTopicId['schooltitle'] : '';
	unset($objCheckoffTopicMaster);
	
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Comps Steps</title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
		
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

		
    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>
						<li><a href="settings.html">Setting	</a></li>                        						
						<li class="active">Comps Section</li>						
                    </ol>
                </div>
                <?php if(isset($_SESSION["loggedAsBackUserId"])) {?>   
                    <div class="pull-right">  
                        <ol class="breadcrumb">
                        <li> <a href="addsectionmaster.html?topicid=<?php echo EncodeQueryData($topicid); ?>">Add</a> </li>
                        </ol>   
                    </div>
                <?php } ?>    
            </div>
        </div>

        <div class="container">


            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Comps Section added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Comps Section updated successfully.
                </div>
                <?php 
					}                   
                    else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
				<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
						
                           <th>Section Number</th>		   
                           <th>Section Description</th>		   
                            <?php if(isset($_SESSION["loggedAsBackUserId"])) {?>   
                                <th style="text-align: center">Action</th>                                                   
                            <?php }?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalCount > 0)
                        {
                            while($row = mysqli_fetch_array($rowsCheckoffSection))
                            {								
                                $schoolSectionId = ($row['SectionId']);					
                                //$schoolTopicId = ($row['schoolTopicId']);					
                                $sectionTitle = ($row['schoolSectionTitle']);                             
                                $sortOrder = ($row['sortOrder']);                             
                                $questionCount = ($row['questionCount']);                             
                              $CheckdSectionId = ($row['CheckdSectionId']);  
								// echo 'CheckdSectionId->'.$CheckdSectionId;
                                     if($CheckdSectionId > 0)
								{
									$actiontype="false";
								}
								else
								{
									$actiontype="true";
								}                    
                               ?>
                            <tr>
								
								<td align="center"><?php echo ($sortOrder); ?></td>
								<td><?php echo ($sectionTitle); ?></td>
                                <?php if(isset($_SESSION["loggedAsBackUserId"])) {?>       
                                    <td style="text-align: center">									
                                    <a  href="addsectionmaster.html?editid=<?php echo EncodeQueryData($schoolSectionId); ?>&topicid=<?php echo  EncodeQueryData($topicid); ?>">Edit</a>
                                        <?php if($CheckdSectionId > 0) { ?>
                                        |<a id="warningAjax" class="text-muted" href="javascript:void(0);" SectionId="<?php echo EncodeQueryData($schoolSectionId); ?>" sectionTitle="<?php echo($sectionTitle); ?>"  >Delete</a>
                                        <?php } else { ?>
                                    |  <a href="javascript:void(0);" class="deleteAjaxRow"
                                    schoolSectionId="<?php echo EncodeQueryData($schoolSectionId); ?>" sectionTitle="<?php echo($sectionTitle); ?>">Delete</a>
                                    </td>
                                <?php }  }?>
                            </tr>
                            <?php
                            }
                        }
                    ?>
					</tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
		
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
		
        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

			
            $(window).load(function(){
				
				 var TotalCheckboxCount = $('input[name="checkoffsection[]"]').length;                       
                                    var CheckedCheckboxCount = $('input[name="checkoffsection[]"]:checked').length;
                                    if(TotalCheckboxCount==CheckedCheckboxCount)
                                    {
                                        $('.selectall').prop('checked', true);
                                    }else{
                                        $('.selectall').prop('checked', false);
                                    }
									
                $("#divTopLoading").addClass('hide');
				 });
			
                 var loggedAsBackUserId = '<?php echo $loggedAsBackUserId; ?>';			
                 var current_datatable = $("#datatable-responsive").DataTable({
                    'iDisplayLength': 250,
					"aoColumns": [{
                    "sWidth": "1%"
                },{
                    "sWidth": "68%"
				  },<?php if($loggedAsBackUserId) { ?>

                    {
                    "sWidth": "30%",
                    "bSortable": false					
                    }
                    <?php } ?>

                    ]
                });  

				 
				 $('#selectall').click(function() {				
				if ($(this).is(':checked')) {					
					$('input:checkbox').prop('checked', true);
					var ischeckall=1;
				} else {					
					//$('input:checkbox').prop('checked', false);
					$('input').filter(':checkbox').removeAttr('checked');
					var ischeckall=0;
				}
				
				var schoolTopicId = '<?php echo ($encodedSchoolTopicId); ?>';
				//var schoolSectionId = $(this).attr('schoolSectionId');;
				var SchoolId = '<?php echo ($currentSchoolId); ?>';
					
				$.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/assign_sections.html",
                        data: {							     
							  ischeckall: ischeckall,
							  SchoolId: SchoolId,
							  schoolTopicId: schoolTopicId,							  
							  type: 'assign_all_sections'								  
                        },
						 success: function() {                           
                            
							if(ischeckall == 1){ //Assigned 
                            	alertify.success('Assigned');
							 }
								
							else if(ischeckall == 0) {//Removed
                            	alertify.error('Removed');
							 }
                        }
                    });
					
					
				});
				
			
		//delete student
		
		$(document).on('click', '#warningAjax', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                
                
                alertify.confirm('Warning!', 'This section already assigned, you cant delete it!', function(){
				 }, function() {});
            });
			
				$(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var schoolSectionId = $(this).attr('schoolSectionId');
                var sectionTitle = $(this).attr('sectionTitle');
                var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']) ?>';

                alertify.confirm('Comps Section: '+sectionTitle, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: schoolSectionId,
                            type: 'PEF_Checkoff_Section',
                            userId: userId
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });
			
		$("#datatable-responsive").on("click", ".sendrequest", function() {		
                    var action;	
                    var thischeck= $(this);
                    var actiontype = $(this).attr('actiontype');
                    var schoolTopicId = $(this).attr('schoolTopicId');                 
                    var schoolSectionId = $(this).attr('schoolSectionId'); 
			        
					$.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/assign_sections.html",
                        data: {							
						      id: schoolSectionId,
							  action : actiontype,							  
							  schoolTopicId: schoolTopicId,							  
							  type: 'assign_sections'								  
                        },
						 success: function() {                           
                            //alertify.success('Assigned Question');
							//alert(actiontype);
							if(actiontype == 'true'){ //Assigned 
                            	alertify.success('Assigned');
							thischeck.attr('actiontype','false'); }
								
							else if(actiontype == 'false') {//Removed
                            	alertify.error('Removed');
							thischeck.attr('actiontype','true'); }
                        }
                    });
			        
				});
				
				
			

        </script>
    </body>
    </html>