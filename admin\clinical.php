<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');
	include('../setRequest.php'); 
    include('../class/clsStudent.php');    
    include('../class/clsRotation.php');
    include('../class/clsAttendance.php');
    include('../class/clsInteraction.php');
	include('../class/clsStudentRankMaster.php');    
    
    //For Check Checkoff 
    $isActiveCheckoff='';
    $isActiveCheckoff =$_SESSION["isActiveCheckoff"];
	
	$selrotationId=0;
	$rotationId=0;
	$rankId=0;
	$inse=0;
    $insetting=0;	
    $TotalHoursSpent=0;	
    $TotalpointsAwarded=0;
    $TotalInteractionHoursSpent=0;
    $TotalApprovedTotalHours =0;
    $totalHoursTominutes=0;
    $totalOriginalHoursTominutes=0;
    $totalMinutes = 0;
    $totalOriginalHoursForAllStudent=0;
    $originalTotalHours=0;
    $totalOriginalMinutes=0;
    $approvedhours=0;
	if(isset($_GET['ins']))
	{
		$insetting = $_GET['ins'];
		$inse = DecodeQueryData($insetting);
	}
	if(isset($_GET['rankId']))
	{
		$rankId = $_GET['rankId'];
        $rankId = DecodeQueryData($rankId);
    }

    //For All Student List
	$objStudent = new clsStudent();
	$rowsStudentData=$objStudent->GetAllSchoolStudentsForClinical($currentSchoolId,$rankId,0,0,0,0,0,0);
	$totalStudentCount = 0;
	if($rowsStudentData !='')
	{
		$totalStudentCount = mysqli_num_rows($rowsStudentData);
	}
		
	//For Rotation
	$objrotation = new clsRotation();
	$rotation = $objrotation->GetAllrotation($currentSchoolId, $locationId=0, $courseId=0, $selrotationId);
    unset($objrotation);
    
    //For Rank List
	$objStudentRankMaster = new clsStudentRankMaster();
	$ranks = $objStudentRankMaster->GetAllStudentRankBySchoolClinician($currentSchoolId);
	unset($objStudentRankMaster);
    
    $ApprovedTotalHours = '';
    $objAttendance=new clsAttendance();
    // $GetAttendance = $objAttendance->GetAttendanceTotalBySchool($currentSchoolId);
    
    // $ApprovedTotalHours=$GetAttendance['ApprovedTotalHours'];

    // if($ApprovedTotalHours !='')
    // {
    //     $ApprovedHours = explode(':', $ApprovedTotalHours);
    //     $Hours = $ApprovedHours[0];
    //     $Minutes = $ApprovedHours[1];
    //     $totalHoursTominutes += $Hours;
    //     $totalMinutes += $Minutes;
    //     $ApprovedTotalHours= $Hours.':'.$Minutes;
    // }
    // else 
    // {
    //     $ApprovedTotalHours="-";
    // }
     
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Clinical</title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">

    <style>
        .mt-1 {
            margin-top: 9px;
            padding-left: 58px;
        }

        thead tr th {
            text-align: center;
        }
    </style>

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if ($inse == 1) { ?>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Clinical</li>
                    <?php } else { ?>
                        <li class="active">Clinical</li>
                    <?php } ?>
                </ol>
            </div>

        </div>
    </div>

    <div class="custom-container">


        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Journal added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Journal updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <div class="row">
            <div class="col-md-3 pull-right">
                <div class="form-group ">
                    <label class="col-md-4 control-label mt-1" for="cborank">Rank</label>
                    <div class="col-md-8">
                        <select id="cborank" name="cborank" class="form-control select2_single">
                            <option value="" selected>Select All</option>
                            <?php
                            if ($ranks != "") {
                                while ($row = mysqli_fetch_assoc($ranks)) {
                                    //if($row['title'] == 'Freshman') continue;
                                    $selrankId  = $row['rankId'];
                                    $name  = stripslashes($row['title']);

                            ?>
                                    <option value="<?php echo EncodeQueryData($selrankId); ?>" <?php if ($rankId == $selrankId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                            <?php

                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
            </div>
        </div> <br>
        <div class="col-md-12" style="overflow-x: auto;">
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Rank</th>
                        <th style="text-align:center">Attendance<br>Approved Hours</th>
                        <th style="text-align:center;width:115px">Attendance<br>Total Hours</th>
                        <th style="text-align: center">Action</th>
                    </tr>
                </thead>

            </table>
        </div>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>



    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();
            $('.addCommentpopup').magnificPopup({
                'type': 'ajax',
            });

        });

        $("#cborank").change(function() {
            var rankId = $(this).val();
            var inse = '<?php echo ($insetting); ?>';

            if (rankId) {
                window.location.href = "clinical.html?rankId=" + rankId + "&ins=" + inse;
            } else {
                window.location.href = "clinical.html?ins=" + inse;
            }
        });

        var rankId = '<?php echo ($rankId); ?>';
        var inse = '<?php echo ($inse); ?>';
        var currentSchoolId = '<?php echo ($currentSchoolId); ?>';
        var isActiveCheckoff = '<?php echo ($isActiveCheckoff); ?>';

        var current_datatable = $("#datatable-responsive").DataTable({
            "footerCallback": function(row, data, start, end, display) {
                var api = this.api(),
                    data;
                // converting to interger to find total
                var intVal = function(i) {
                    return typeof i === 'string' ?
                        i.replace(/[\$,]/g, '') * 1 :
                        typeof i === 'number' ?
                        i : 0;
                };


                //Total Approved Hour
                var approvedHourTotal = api
                    .column(3)
                    .data()
                    .reduce(function(a, b) {
                        return intVal(a) + intVal(b);
                    }, 0);

                //Total Hours
                var hoursTotal = api
                    .column(4)
                    .data()
                    .reduce(function(a, b) {
                        return intVal(a) + intVal(b);
                    }, 0);

                var ApprovedTotalHours = '<?php echo $ApprovedTotalHours; ?>';

                // Update footer
                // $( api.column( 2 ).footer() ).html('Total');
                // $( api.column( 3 ).footer() ).html(timeSpentTotal); 
                // $( api.column( 4 ).footer() ).html(pointsTotal); 
                // $( api.column( 5 ).footer() ).html(ApprovedTotalHours); 
                // $( api.column( 6 ).footer() ).html(hoursTotal); 

            },
            "responsive": true,
            "processing": true,
            "bServerSide": true,
            "aoColumns": [{
                "sWidth": "15%"
            }, {
                "sWidth": "15%"
            }, {
                "sWidth": "15%"
            }, {
                "sWidth": "15%",
                "sClass": "text-center",
                "bSortable": false
            }, {
                "sWidth": "15%",
                "sClass": "text-center",
                "bSortable": false
            }, {
                "sWidth": "20%",
                "bSortable": false
            }],
            "ajax": {
                url: "../ajax/ajax_get_student_list_to_admin.html", // json datasource
                type: "POST", // method  , by default get
                data: {
                    'isActiveCheckoff': isActiveCheckoff,
                    'currentSchoolId': currentSchoolId,
                    'rankId': rankId,
                    'inse': inse
                },
                error: function() { // error handling
                    $(".employee-grid-error").html("");
                    $("#datatable-responsive").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
                    $("#employee-grid_processing").css("display", "none");
                }
            }
        });
    </script>
</body>

</html>