<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsCheckoffSectionMaster.php');
include('../class/clsCheckoffQuestionMaster.php');
include('../class/clsCheckoffTopicMaster.php');

if ($_SERVER['REQUEST_METHOD'] == "POST") {

    $objDB = new clsDB();

    $type = isset($_POST['type']) ? $_POST['type'] : '';


    ini_set('upload_max_filesize', '50M');
    ini_set('post_max_size', '50M');
    ini_set('max_input_time', 300000);
    ini_set('max_execution_time', 300000);

    $notifyMessage = 'Imported';
    $row = 1;
    if (isset($_FILES['file'])) {
        $filename = $_FILES["file"]["tmp_name"];
        $ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));

        if ($ext != "csv") {
            $error = "Upload only .csv file.";
            header('location:checkofftopics.html?status=Importerror');
            exit();
        }

        if ($_FILES["file"]["size"] > 0) {

            $file = fopen($filename, "r");
            $counter = 0;

            $retStudentId = 0;
            while (($getData = fgetcsv($file, 10000, ",")) !== FALSE) {
                // echo $type;exit; 
                if ($row == 1) {
                    $row++;
                    continue;
                }

                if ($type == 'step') {
                    echo '<pre>';
                    print_r($getData);
                    $isActiveCheckoffForStudent = $objDB->GetSingleColumnValueFromTable('schools', 'isActiveCheckoffForStudent', 'schoolId', $currentSchoolId);
                    $isAdvanceCheckoff = ($isActiveCheckoffForStudent == 0) ? 1 : 0;
                    if ($currentSchoolId == 123) {
                        $topicTitle = trim($getData[0]);
                        $sectionTitle = trim($getData[1]);
                        $stepTitle = trim($getData[2]);
                        $optionType = trim($getData[3]);
                        $optionValues = trim($getData[4]);
                        $sortOrder = trim($getData[5]);
                        $sectionSortOrder = trim($getData[6]);
                        $schoolOptionText = trim($getData[7]);
                        $isMandatory = trim($getData[8]);
                        $isComp = trim($getData[9]);

                        $titleId = explode(" ", $topicTitle);
                        echo 'titleId '.$checkofftitleId = current($titleId);
                        $title = str_replace($checkofftitleId, "", $topicTitle);
                        // $title = $topicTitle;
                        $title = trim($title);
                        echo 'title '. trim($title);
                        //Check Topic is Exist Or not 
                        $isExistTopicId = $objDB->GetSingleColumnValueFromTable('schooltopicmaster', 'schoolTopicId', 'schooltitle', $title, 'schoolId', $currentSchoolId);
                        // echo 'isExistTopicId '. $isExistTopicId ;exit;
                        if ($title != '') {
                            if (!$isExistTopicId) {
                                $objCheckoffTopic = new clsCheckoffTopicMaster();
                                $checkofftitleId = rtrim($checkofftitleId, '.');

                                $objCheckoffTopic->checkoffTitleId = $checkofftitleId;
                                $objCheckoffTopic->schooltitle = $title;
                                $objCheckoffTopic->schoolId = $currentSchoolId;
                                $objCheckoffTopic->procedureCategoryId = 0;
                                $objCheckoffTopic->proceduteCountId = 0;
                                $objCheckoffTopic->isAdvanceCheckoff = $isAdvanceCheckoff;

                                $isExistTopicId = $objCheckoffTopic->SaveCheckoffTopic(0);
                            }

                            // $sections = explode(" ", $sectionTitle);
                            // $checkofftitleId = current($sections);
                            // $sectionTitle = str_replace($checkofftitleId, "", $sectionTitle);

                            //Check Section is Exist Or Not
                            $isExistSectionId = $objDB->GetSingleColumnValueFromTable('schoolsectionmaster', 'schoolSectionId', 'schoolSectionTitle', $sectionTitle, 'schoolId', $currentSchoolId);
                            echo '</br>isExistSectionId '.$isExistSectionId;
                            // exit;
                            if (!$isExistSectionId) {
                                //Save data
                                $objCheckoffSection = new clsCheckoffSectionMaster();
                                $objCheckoffSection->schoolSectionTitle = $sectionTitle;
                                $objCheckoffSection->sortOrder = $sectionSortOrder;
                                // $objCheckoffSection->description = '*Student must perform the modality proficiently five times prior to final check off';
                                $objCheckoffSection->description = '';
                                $objCheckoffSection->schoolId = $currentSchoolId;
                                $isExistSectionId = $objCheckoffSection->SaveAdvanceCheckoffSection(0);

                                $objCheckoffSection->schoolTopicId = $isExistTopicId;
                                $objCheckoffSection->schoolSectionId = $isExistSectionId;
                                $objCheckoffSection->SaveCheckoffSectionInTopicDetails($isExistSectionId);
                            }
                            // exit;
                            if ($stepTitle && $sortOrder) {
                                // echo 'hi';exit;

                                //Check Topic is Exist Or not 
                                // $isExistStepId = $objDB->GetSingleColumnValueFromTable('schooldefaultquestionmaster', 'schoolQuestionId','schoolQuestionTitle',$stepTitle,'schoolId',$currentSchoolId);
                                // //Check sortOrder is Exist Or not 
                                // $isExistSortOrder = $objDB->GetSingleColumnValueFromTable('schooldefaultquestionmaster', 'sortOrder','schoolQuestionId',$isExistStepId,'schoolId',$currentSchoolId);
                                $objQuestionMaster = new clsCheckoffQuestionMaster();

                                $isExistStepId = $objQuestionMaster->getStepId($stepTitle, $currentSchoolId, $sortOrder);
                                echo '</br>isExistStepId '.$isExistStepId;
                                // exit;

                                if ($isExistStepId)
                                    continue;


                                if ($optionType == 1)
                                    $optionValues = array('Yes', 'No', 'N/A');
                                elseif ($optionType == 2)
                                    $optionValues = explode(",", $optionValues);
                                elseif ($optionType == 3)
                                    $optionValues = explode(",", $optionValues);

                                // print_r($optionValues);exit;

                                $objQuestionMaster->schoolQuestionTitle = $stepTitle;
                                $objQuestionMaster->schoolQuestionType = $optionType;
                                $objQuestionMaster->schoolId = $currentSchoolId;
                                $objQuestionMaster->sortOrder = $sortOrder;
                                $objQuestionMaster->marks = 0;
                                $objQuestionMaster->isMandatory = $isMandatory;

                                $RetQuestionId = $objQuestionMaster->SaveNewQuestionsMasterForAdvance(0);
                                if ($RetQuestionId) {
                                    if ($optionType == 2) {
                                        foreach ($optionValues as $keys => $value) {
                                            $schoolOptionValue = ($value == 'Yes') ? 1 : 0;
                                            $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                            $objQuestionMaster->schoolOptionText = $value;
                                            $objQuestionMaster->schoolOptionValue = $schoolOptionValue;
                                            $objQuestionMaster->choiceAnswer = 0;
                                            $QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
                                        }
                                    } elseif ($optionType == 3) {
                                        foreach ($optionValues as $keys => $value) {
                                            $schoolOptionValue = ($value == 'Clinical') ? 1 : 2;
                                            $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                            $objQuestionMaster->schoolOptionText = $value;
                                            $objQuestionMaster->schoolOptionValue = $schoolOptionValue;
                                            $objQuestionMaster->choiceAnswer = 0;
                                            $QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
                                        }
                                    } elseif ($optionType == 4 || $optionType == 6) {
                                        $schoolOptionValue = ($optionType == 4) ? 1 : 0;
                                        $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                        $objQuestionMaster->schoolOptionText = '';
                                        $objQuestionMaster->schoolOptionValue = $schoolOptionValue;
                                        $objQuestionMaster->choiceAnswer = 0;
                                        $QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
                                    }
                                    $objQuestionMaster->schoolTopicId = $isExistTopicId;
                                    $objQuestionMaster->schoolSectionId = $isExistSectionId;
                                    $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                    $result = $objQuestionMaster->SaveQuestion(0);
                                }
                            }
                        }
                    } else {

                        $topicTitle = trim($getData[0]);
                        $sectionTitle = trim($getData[1]);
                        $stepTitle = trim($getData[2]);
                        $optionType = trim($getData[3]);
                        $optionValues = trim($getData[4]);
                        $sortOrder = trim($getData[5]);

                        $titleId = explode(" ", $topicTitle);
                        $checkofftitleId = current($titleId);
                        $title = str_replace($checkofftitleId, "", $topicTitle);
                        // $title = $topicTitle;
                        //Check Topic is Exist Or not 
                        $isExistTopicId = $objDB->GetSingleColumnValueFromTable('schooltopicmaster', 'schoolTopicId', 'schooltitle', $title, 'schoolId', $currentSchoolId);
                        // echo $isExistTopicId ;exit;
                        if (!$isExistTopicId) {
                            $objCheckoffTopic = new clsCheckoffTopicMaster();
                            $checkofftitleId = rtrim($checkofftitleId, '.');

                            $objCheckoffTopic->checkoffTitleId = $checkofftitleId;
                            $objCheckoffTopic->schooltitle = $title;
                            $objCheckoffTopic->schoolId = $currentSchoolId;
                            $objCheckoffTopic->procedureCategoryId = 0;
                            $objCheckoffTopic->proceduteCountId = 0;
                            $objCheckoffTopic->isAdvanceCheckoff = 0;

                            $isExistTopicId = $objCheckoffTopic->SaveCheckoffTopic(0);
                        }

                        $sections = explode(" ", $sectionTitle);
                        $checkofftitleId = current($sections);
                        $sectionTitle = str_replace($checkofftitleId, "", $sectionTitle);

                        //Check Section is Exist Or Not
                        $isExistSectionId = $objDB->GetSingleColumnValueFromTable('schoolsectionmaster', 'schoolSectionId', 'schoolSectionTitle', $sectionTitle, 'schoolId', $currentSchoolId);
                        if (!$isExistSectionId) {
                            //Save data
                            $objCheckoffSection = new clsCheckoffSectionMaster();
                            $objCheckoffSection->schoolSectionTitle = $sectionTitle;
                            $objCheckoffSection->sortOrder = 1;
                            $objCheckoffSection->description = '';
                            $objCheckoffSection->schoolId = $currentSchoolId;
                            $isExistSectionId = $objCheckoffSection->SaveCheckoffSection(0);

                            $objCheckoffSection->schoolTopicId = $isExistTopicId;
                            $objCheckoffSection->schoolSectionId = $isExistSectionId;
                            $objCheckoffSection->SaveCheckoffSectionInTopicDetails($isExistSectionId);
                        }

                        if ($stepTitle && $sortOrder) {
                            // echo 'hi';exit;

                            //Check Topic is Exist Or not 
                            // $isExistStepId = $objDB->GetSingleColumnValueFromTable('schooldefaultquestionmaster', 'schoolQuestionId','schoolQuestionTitle',$stepTitle,'schoolId',$currentSchoolId);
                            // //Check sortOrder is Exist Or not 
                            // $isExistSortOrder = $objDB->GetSingleColumnValueFromTable('schooldefaultquestionmaster', 'sortOrder','schoolQuestionId',$isExistStepId,'schoolId',$currentSchoolId);
                            $objQuestionMaster = new clsCheckoffQuestionMaster();

                            $isExistStepId = $objQuestionMaster->getStepId($stepTitle, $currentSchoolId, $sortOrder);
                            // echo $isExistStepId;
                            // exit;
                            if ($isExistStepId)
                                continue;


                            if ($optionType == 1)
                                $optionValues = array('Yes', 'No', 'N/A');
                            elseif ($optionType == 2)
                                $optionValues = explode(",", $optionValues);

                            $objQuestionMaster->schoolQuestionTitle = $stepTitle;
                            $objQuestionMaster->schoolQuestionType = $optionType;
                            $objQuestionMaster->schoolId = $currentSchoolId;
                            $objQuestionMaster->sortOrder = $sortOrder;
                            $objQuestionMaster->marks = 0;

                            $RetQuestionId = $objQuestionMaster->SaveNewQuestionsMaster(0);
                            if ($RetQuestionId) {
                                if ($optionType == 2) {
                                    foreach ($optionValues as $keys => $value) {
                                        $schoolOptionValue = ($value == 'Yes') ? 1 : 0;
                                        $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                        $objQuestionMaster->schoolOptionText = $value;
                                        $objQuestionMaster->schoolOptionValue = $schoolOptionValue;
                                        $objQuestionMaster->choiceAnswer = 0;
                                        $QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
                                    }
                                }

                                $objQuestionMaster->schoolTopicId = $isExistTopicId;
                                $objQuestionMaster->schoolSectionId = $isExistSectionId;
                                $objQuestionMaster->schoolQuestionId = $RetQuestionId;
                                $result = $objQuestionMaster->SaveQuestion(0);
                            }
                        }
                    }
                } else {
                    $topicName = trim($getData[0]);
                    $sectionName = trim($getData[1]);
                    $sectionNum = trim($getData[2]);
                    $description = trim($getData[3]);

                    //Check Topic is Exist Or not 
                    $isExistTopicId = $objDB->GetSingleColumnValueFromTable('schooltopicmaster', 'schoolTopicId', 'schooltitle', $topicName, 'schoolId', $currentSchoolId);
                    if (!$isExistTopicId)
                        continue;

                    //Check Record is Exist Or Not
                    $isExistSection = $objDB->GetSingleColumnValueFromTable('schoolsectionmaster', 'schoolSectionId', 'schoolSectionTitle', $sectionName, 'schoolId', $currentSchoolId);
                    if ($isExistSection)
                        continue;


                    if ($topicName && $sectionName && !$isExistSection) {
                        //Save data
                        $objCheckoffSection = new clsCheckoffSectionMaster();
                        $objCheckoffSection->schoolSectionTitle = $sectionName;
                        $objCheckoffSection->sortOrder = $sectionNum;
                        $objCheckoffSection->description = $description;
                        $objCheckoffSection->schoolId = $currentSchoolId;
                        $retcheckoffsectionmasterId = $objCheckoffSection->SaveCheckoffSection(0);

                        $objCheckoffSection->schoolTopicId = $isExistTopicId;
                        $objCheckoffSection->schoolSectionId = $retcheckoffsectionmasterId;
                        $result = $objCheckoffSection->SaveCheckoffSectionInTopicDetails($retcheckoffsectionmasterId);
                    }
                }
            }

            fclose($file);

            $messageText = $result ? 'Imported' : 'Error';
            header('location:checkofftopics.html?status=' . $messageText);

            exit();
        }
    }
}
