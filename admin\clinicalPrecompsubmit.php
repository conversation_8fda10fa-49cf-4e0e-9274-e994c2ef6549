<?php
$IsMobile  = isset($_POST['IsMobile']) ? $_POST['IsMobile'] : 0;

include('../includes/config.php');
include('includes/validateUserLogin.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsClinicalPreComp.php');

// echo '<pre>';
// print_r($_POST);
// print_r($_GET);
// exit;

@session_start();
// print_r($_SESSION);
$clinicalPrecompId = 0;
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
    $rotationId = 0;


    if (isset($_GET['clinicalPrecompId'])) {
        $clinicalPrecompId = $_GET['clinicalPrecompId'];
        $clinicalPrecompId = DecodeQueryData($clinicalPrecompId);
    }
    if (isset($_GET['clinicalPrecompRotationId'])) {
        $clinicalPrecompRotationId = $_GET['clinicalPrecompRotationId'];
        $clinicalPrecompRotationId = DecodeQueryData($clinicalPrecompRotationId);
    }
    //$status =  'Added';	
    $clinicalPrecompId = isset($_GET['clinicalPrecompId']) ? DecodeQueryData($_GET['clinicalPrecompId']) : 0;
    $status = ($clinicalPrecompId > 0) ? 'updated' : 'added';

    //echo $status.$formativeId;   

    $cbostudent = $_POST['cboStudent'];
    $cboTerm = $_POST['cboTerm'];
    $cboclinician  = ($_POST['cboclinician']);
    $exam  = ($_POST['exam']);
    $accession  = ($_POST['accession']);
    $radioResult  = ($_POST['radioResult']);
    $reason  = ($_POST['reason']);
    $comment  = ($_POST['comment']);


    $evaluationDate = GetDateStringInServerFormat($_POST['evaluationDate']);
    $evaluationDate = str_replace('00:00:00', '12:00 PM', $evaluationDate);
    $evaluationDate = date('Y-m-d H:i', strtotime($evaluationDate));

    // $studentDate = $_POST['studentDate'];
    // if ($studentDate != '') {
    //     $studentDate = GetDateStringInServerFormat($_POST['studentDate']);
    //     $studentDate = str_replace('00:00:00', '12:00 PM', $studentDate);
    //     $studentDate = date('Y-m-d H:i', strtotime($studentDate));
    // }

    // $evaluatorDate = $_POST['evaluatorDate'];
    // if ($evaluatorDate != '') {
    //     $evaluatorDate = GetDateStringInServerFormat($_POST['evaluatorDate']);
    //     $evaluatorDate = str_replace('00:00:00', '12:00 PM', $evaluatorDate);
    //     $evaluatorDate = date('Y-m-d H:i', strtotime($evaluatorDate));
    // }

    $technologiestsignature = $_POST['technologiestsignature'];

    $dateOftechnologiestSignature = $_POST['dateOftechnologiestSignature'];
    if ($dateOftechnologiestSignature != '') {
        $dateOftechnologiestSignature = GetDateStringInServerFormat($_POST['dateOftechnologiestSignature']);
        $dateOftechnologiestSignature = str_replace('00:00:00', '12:00 PM', $dateOftechnologiestSignature);
        $dateOftechnologiestSignature = date('Y-m-d H:i', strtotime($dateOftechnologiestSignature));
    }

   
    
    $objClinicalPreComp = new clsClinicalPreComp();
    $objClinicalPreComp->rotationId = $clinicalPrecompRotationId;
    $objClinicalPreComp->clinicianId = $cboclinician;
    $objClinicalPreComp->studentId = $cbostudent;
    $objClinicalPreComp->schoolId = $currentSchoolId;
    $objClinicalPreComp->termId = $cboTerm;
    $objClinicalPreComp->technologiestsignature = $technologiestsignature;
    $objClinicalPreComp->evaluationDate = $evaluationDate;
    $objClinicalPreComp->dateOftechnologiestSignature = $dateOftechnologiestSignature;
    $objClinicalPreComp->exam = $exam;
    $objClinicalPreComp->accession = $accession;
    $objClinicalPreComp->result = $radioResult;
    $objClinicalPreComp->reason = $reason;
    $objClinicalPreComp->comment = $comment;    
    $objClinicalPreComp->createdBy = $_SESSION["loggedClinicianId"];
    $retclinicalPrecompId = $objClinicalPreComp->SaveClinicalPreComp($clinicalPrecompId);

    unset($objClinicalPreComp);

    if ($retclinicalPrecompId > 0) {
        if ($IsMobile) {
            header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=clinicalPrecomp');
            exit;
        } else {
            header('location:clinicalPrecompList.html?clinicalPrecompId=' . EncodeQueryData($clinicalPrecompId) . '&clinicalPrecompRotationId=' . EncodeQueryData($clinicalPrecompRotationId) . '&status=' . $status);
            exit();
        }
    } else {
        if ($IsMobile) {
            header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=clinicalPrecomp');
            exit();
        } else {
            header('location:clinicalPrecomp.html?status=error');
        }
    }
} {
    if ($IsMobile) {
        header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=clinicalPrecomp');
        exit();
    } else {
        header('location:clinicalPrecompList.html');
        exit();
    }
}
