<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php');
	include('../includes/commonfun.php');
    include('../class/clsDB.php');
	include('../setRequest.php');  
    include('../class/clsStudent.php');
    include('../class/clsStudentRankMaster.php');	
// print_r($_GET);exit;
    $type = isset($_GET['type']) ? $_GET['type'] : '';
    $actionType = '';
    $samplefilePath = '';
    $topicId = 0;
    $sectionId = 0;

    if($type == 1) //For Import Record Id's
    {
        $pageTitle = "Import Student Record Id's";

        $redirectUrl = 'studentIdsList.html';
        $actionUrl = 'importStudentIdslist.html';

        if(DEVELOPMENT==1)
        $samplefilePath = ROOT_PATH."/upload/sample_student_import_ids_file.csv";
        else
            $samplefilePath = BASE_PATH."/upload/sample_student_import_ids_file.csv";

    }
    else if($type == 2) //For Import Program Dates List
    {
        $pageTitle = "Import Student Program Dates";

        $redirectUrl = 'programDatesList.html';
        $actionUrl = 'importProgramDatesList.html';

        if(DEVELOPMENT==1)
        $samplefilePath = ROOT_PATH."/upload/sample_student_import_program_dates_file.csv";
        else
            $samplefilePath = BASE_PATH."/upload/sample_student_import_program_dates_file.csv";

    }
    elseif($type == 3) //For Import Canvas Access Token
    {
        $pageTitle = "Import Student Canvas Access Token";
        $actionType = 'accessToken';
        $redirectUrl = 'studentIdsList.html';
        $actionUrl = 'importStudentIdslist.html';

        if(DEVELOPMENT==1)
            $samplefilePath = ROOT_PATH."/upload/sample_student_import_canvas_access_token_file.csv";
        else
            $samplefilePath = BASE_PATH."/upload/sample_student_import_canvas_access_token_file.csv";

    }
    elseif($type == 4) //For Import Checkoff Sections
    {
        $pageTitle = "Import Section";
        $redirectUrl = 'checkofftopics.html';
        $actionUrl = 'importCheckoffSection.html';

    }
    elseif($type == 5) //For Import Evaluation Section
    {
        $topicId = isset($_GET['topicid']) ? $_GET['topicid'] : '';
        $sectionId = isset($_GET['sectionId']) ? $_GET['sectionId'] : '';

        $pageTitle = "Import Steps";
        $actionType = 'step';
        $redirectUrl = 'importEvaluationList.html';
        $actionUrl = 'importEvaluationListSection.html';

    }
    else //For Stuedent Import
    {
        $pageTitle = 'Import Student';
        if(DEVELOPMENT==1)
        $samplefilePath = ROOT_PATH."/upload/sample_EvaluationList_file.csv";
        else
            $samplefilePath = BASE_PATH."/upload/sample_EvaluationList_file.csv";
        
        $redirectUrl = 'schoolstudents.html';
        $actionUrl = 'importstudentlist.html';
    }
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo $pageTitle; ?></title>  
        

    </head>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
    <body>
       
        <div class="container">
			<div class="studentdetails" tabindex="-1" role="dialog">
                <form id="frmexportreport" data-parsley-validate class="form-horizontal" method="POST" action="<?php echo $actionUrl; ?>" enctype="multipart/form-data">
                    <input type="hidden" name="type" value="<?php echo $actionType; ?>">
                    <input type="hidden" name="topicId" value="<?php echo $topicId; ?>">
                    <input type="hidden" name="sectionId" value="<?php echo $sectionId; ?>">

                    <div class="modal-dialog" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><a href="<?php echo $redirectUrl; ?>" class="btn btn-light btn-form ml-3">X</a></button>
                                <h4 class="modal-title"><?php echo $pageTitle; ?></h4>
                            </div>
                            <div class="modal-body">
                                <input type="file" name="file"  style="margin-top: 22px;margin-bottom: 22px;"  accept=".csv" class="form-control" id="validationDefaultUsername9" required>
                                <a href="<?php echo $samplefilePath; ?>" style="color:blue;">Download sample file</a>
                                <div class="modal-footer">
                                    <button type="submit" style="margin-right: -16px;" class="btn btn-primary ml-3" data-dismiss="modal">Import</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </form>
			</div> 
		</div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
		<script>
            $('#close').on( "click", function() {
                $.magnificPopup.close();
            });
		</script>
    </body>
    </html>