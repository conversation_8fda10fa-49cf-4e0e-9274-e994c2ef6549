<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsStudent.php');
include('../class/clsMidterm.php');
include('../class/clsQuestionOption.php');
include('../class/clsSectionStudentName.php');
include('../class/clsClinician.php');
include('../class/clsExternalPreceptors.php');

$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

$schoolId = 0;
$rotationId = 0;
$studentId = 0;
$currentstudentId = 0;
$studentMidtermMasterId = 0;
$clinicianId = 0;
$midtermrotationid = 0;
$dateOfStudentSignature = '';
$evaluationDate = '';
$display_to_date = date('m/d/Y');
$schoolId = $currentSchoolId;
$InstructorSignature = null;
$dateOfInstructorSignature = null;
$studentSignature = '';
$studentfullname = '';
$absence = 0;
$daysTardy = 0;
$OverAllRating = '';
$clinicianComment = '';

//object
$objRotation = new clsRotation();

//For Rotation  
if (isset($_GET['midtermrotationid']))
	$midtermrotationid = DecodeQueryData($_GET['midtermrotationid']);

//For Student	
if (isset($_GET['studentId'])) {
	$currentstudentId = DecodeQueryData($_GET['studentId']);
	$studentId = $currentstudentId;
}

if (isset($_GET['rotationId'])) {
	$midtermrotationid = DecodeQueryData($_GET['rotationId']);
}
$objStudent = new clsStudent();
//For Edit Formative
if (isset($_GET['studentMidtermMasterId']) && ($_GET['midtermrotationid'])) {
	$schoolId = $currentSchoolId;
	$page_title = "Edit Midterm ";
	$bedCrumTitle = 'Edit';

	//For Formative Details
	$objMidterm = new clsMidterm();
	$studentMidtermMasterId = DecodeQueryData($_GET['studentMidtermMasterId']);
	$rowMidterm = $objMidterm->GetStudentMidtermDetails($studentMidtermMasterId);
	unset($objMidterm);
	if ($rowMidterm == '') {
		header('location:midterm.html');
		exit;
	}
	$midtermrotationid = ($rowMidterm['rotationId']);
	$clinicianId = ($rowMidterm['clinicianId']);
	$evaluationDate = ($rowMidterm['evaluationDate']);
	$evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
	$evaluationDate = (date('m/d/Y', strtotime($evaluationDate)));
	$studentId = ($rowMidterm['studentId']);
	$OverAllRating = ($rowMidterm['OverAllRating']);
	$absence = ($rowMidterm['absence']);
	$daysTardy = ($rowMidterm['daysTardy']);
	$dateOfStudentSignature = ($rowMidterm['dateOfStudentSignature']);

	$courselocationId = $rowMidterm['locationId'];
	$parentRotationId = stripslashes($rowMidterm['parentRotationId']);
	$rotationLocationId = stripslashes($rowMidterm['rotationLocationId']);

	$locationId = 0;
	if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
		if ($parentRotationId > 0) {
			if (!$rotationLocationId)
				$locationId = $objRotation->GetLocationByRotation($midtermrotationid);
			else
				$locationId  = $rotationLocationId;
		}
	} else {
		$locationId  = $courselocationId;
	}

	//Get Time Zone By Rotation 
	$objLocation = new clsLocations();
	$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
	unset($objLocation);
	if ($TimeZone == '')
		$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
	$dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
	$dateOfStudentSignature = (date('m/d/Y', strtotime($dateOfStudentSignature)));
	$dateOfInstructorSignature = ($rowMidterm['dateOfInstructorSignature']);
	$dateOfInstructorSignature = converFromServerTimeZone($dateOfInstructorSignature, $TimeZone);
	$dateOfInstructorSignature = (date('m/d/Y', strtotime($dateOfInstructorSignature)));

	if ($dateOfInstructorSignature != '' && $dateOfInstructorSignature != '0000-00-00 00:00:00' && $dateOfInstructorSignature != '01/01/1970' && $dateOfInstructorSignature != '12/31/1969' && $dateOfInstructorSignature != '11/30/-0001 12:00 AM') {
		$dateOfInstructorSignature;
	} else {
		$dateOfInstructorSignature = null;
	}
	$studentComment = strip_tags($rowMidterm['studentComment']);
	$clinicianComment = strip_tags($rowMidterm['clinicianComment']);

	//For Student Full Name

	$Rowstudent = $objStudent->GetSingleStudent($schoolId, $studentId);
	$studentfullname = $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'];
} else {

	$page_title = "Add Midterm";
	$bedCrumTitle = 'Add';
	$Rowstudent = $objStudent->GetSingleStudent($schoolId, $studentId);
	$studentfullname = $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'];
}
unset($objStudent);

//For Clinician Name
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, $midtermrotationid);
unset($objClinician);

//For Student Name
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($currentSchoolId, $midtermrotationid);
unset($objStudent);

$objSectionStudentName = new clsSectionStudentName();
$getStudentName = $objSectionStudentName->GetSectionStudentName($schoolId, $midtermrotationid);
unset($objSectionStudentName);


$totalSection = 0;
$objMidterm = new clsMidterm();
$MidtermSection = $objMidterm->GetSections($schoolId);
if ($MidtermSection != '') {
	$totalSection = mysqli_num_rows($MidtermSection);
}

//For Rotation Name

$rowstudentrotation = $objRotation->GetRotationByStudent($schoolId, $currentstudentId);
$RotationName = $objRotation->GetrotationDetails($midtermrotationid, $schoolId);
$rotationtitle = $RotationName['title'];
unset($objRotation);

$preceptorId = 0;
$isPreceptor = isset($_GET['isPreceptor']) ? DecodeQueryData($_GET['isPreceptor']) : '';
$preceptorFullName = '';
if ($isPreceptor > 0) {
	$preceptorId = $isPreceptor;
	$objExternalPreceptors = new clsExternalPreceptors();
	$externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
	$preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
	$preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
	$preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
}
$bedCrumTitle = (isset($_GET['view']) && $_GET['view'] == 'V') ? 'View' : $bedCrumTitle;

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($page_title); ?></title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
	<style type="text/css">
		.some-class {
			float: left;
			clear: none;
		}
	</style>
</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
					<?php if ($currentstudentId > 0) { ?>
						<li><a href="clinical.html">Clinical</a></li>
						<li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
						<li><a href="midtermlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Midterm Evaluation</a></li>
					<?php } else { ?>
						<li><a href="rotations.html">Rotations</a></li>
						<li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
						<li><a href="midtermlist.html?midtermrotationid=<?php echo EncodeQueryData($midtermrotationid); ?>">Midterm Evaluation</a></li>
					<?php } ?>
					<li class="active"><?php echo ($bedCrumTitle); ?></li>
				</ol>
			</div>

		</div>
	</div>

	<div class="container">

		<form id="frmnidterm" data-parsley-validate class="form-horizontal" method="POST" <?php if ($currentstudentId > 0) { ?>action="midtermsubmit.html?studentId=<?php echo (EncodeQueryData($currentstudentId)); ?>&studentMidtermMasterId=<?php echo (EncodeQueryData($studentMidtermMasterId)); ?>&midtermrotationid=<?php echo (EncodeQueryData($midtermrotationid)); ?>" <?php } else { ?>action="midtermsubmit.html?studentMidtermMasterId=<?php echo (EncodeQueryData($studentMidtermMasterId)); ?>
		 &midtermrotationid=<?php echo (EncodeQueryData($midtermrotationid)); ?>" <?php } ?>>

			<div class="row">
				<?php if ($isPreceptor || $preceptorId) { ?>
					<div class="col-md-6">
						<div class="form-group">
							<label class="col-md-4 control-label" for="cboclinician">Preceptor</label>
							<div class="col-md-8">
								<input type="text" name="" id="" value="<?php echo $preceptorFullName; ?>" class="form-control" disabled>
							</div>
						</div>
					</div>
				<?php } else { ?>
					<div class="col-md-6">

						<div class="form-group">
							<label class="col-md-4 control-label" for="cboclinician">Clinician</label>
							<div class="col-md-8">
								<select id="cboclinician" name="cboclinician" class="form-control input-md required-input select2_single" required>
									<option value="" selected>Select</option>
									<?php
									if ($Clinician != "") {
										while ($row = mysqli_fetch_assoc($Clinician)) {
											$selClinicianId  = $row['clinicianId'];
											$name  = stripslashes($row['firstName']);
											$lastName  = stripslashes($row['lastName']);
											$fullName = $name . ' ' . $lastName;
									?>
											<option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?> selected="true" <?php } ?>><?php echo ($fullName); ?></option>

									<?php

										}
									}
									?>
								</select>
							</div>
						</div>
						<!-- ROTATION DD END -->
					</div>
				<?php } ?>


				<div class="col-md-6">

					<div class="form-group">
						<label class="col-md-4 control-label" for="cbostudent">Student</label>
						<div class="col-md-8">
							<select id="cbostudent" name="cbostudent" class="form-control input-md required-input select2_single" required>
								<option value="" selected>Select</option>
								<?php
								if ($Student != "") {
									while ($row = mysqli_fetch_assoc($Student)) {
										$selstudentId  = $row['studentId'];
										$firstName  = stripslashes($row['firstName']);
										$lastName  = stripslashes($row['lastName']);
										$name =	$firstName . ' ' . $lastName;
										if ($currentstudentId > 0) { ?>
											<option value="<?php echo ($selstudentId); ?>" <?php if ($currentstudentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
										<?php } else { ?>
											<option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
										<?php }  ?>
								<?php

									}
								}
								?>
							</select>

						</div>
					</div>

				</div>


			</div>



			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="evaluationDate">Evaluation Date</label>
						<div class="col-md-8">
							<div class='input-group date' id='evaluationDate'>

								<input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date" value="<?php echo ($evaluationDate); ?>" required data-parsley-errors-container="#error-txtDate" />
								<span class="input-group-addon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<?php if ($isPreceptor || $preceptorId) { ?>
							<label class="col-md-4 control-label" for="InstructorDate">Date of Preceptor Signature</label>
						<?php } else { ?>
							<label class="col-md-4 control-label" for="InstructorDate">Date of Instructor Signature</label>
						<?php } ?> <div class="col-md-8">
							<div class='input-group date' id='InstructorDate'>

								<input type='text' name="InstructorDate" readonly id="InstructorDate" class="form-control input-md required-input rotation_date" value="<?php echo ($dateOfInstructorSignature); ?>" data-parsley-errors-container="#error-txtDate" />
								<span class="input-group-addon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="studentsigniture">Student Signature</label>
						<div class="col-md-8">
							<input type='text' name="studentsigniture" readonly id="studentsigniture" class="form-control input-md required-input " value="<?php echo ($studentfullname); ?>" />
							<div id="error-txtDate"></div>
						</div>
					</div>

				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="studentsignitureDate">Date Of Student Signature</label>
						<div class="col-md-8 col-sm-4 col-xs-12">
							<div class='input-group date' id='studentsignitureDate'>

								<input type='text' name="studentsignitureDate" readonly id="studentsignitureDate" class="form-control input-md required-input rotation_date" value="<?php if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
																																														echo ($dateOfStudentSignature);
																																													} ?>" data-parsley-errors-container="#error-txtDate" />
								<span class="input-group-addon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="studentcomment">Student Comments</label>
						<div class="col-md-10 col-sm-6 col-xs-12">
							<textarea name="studentcomment" id="studentcomment" readonly class="form-control input-md " rows="4" cols="100"><?php if (isset($_GET['studentMidtermMasterId']))  echo ($studentComment); ?></textarea>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="cliniciancomment"><?php if ($isPreceptor || $preceptorId) {
																							echo 'Preceptor Comments:';
																						} else {
																							echo 'Clinician Comments:';
																						} ?></label>
						<div class="col-md-10 col-sm-6 col-xs-12">
							<textarea name="cliniciancomment" id="cliniciancomment" readonly class="form-control input-md " rows="4" cols="100"><?php if (isset($_GET['studentMidtermMasterId']))  echo ($clinicianComment); ?></textarea>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="instructions:"><b>Overall Midterm Evaluation: (check one):</b></label>
						<div class="col-md-10 col-sm-6 col-xs-12">
							<div class="panel panel-default">
								<div class="panel-body">
									<input type="radio" name="MidtermEval" value="SUCCEEDING" <?php echo ($OverAllRating == 'SUCCEEDING') ? "checked" : ""; ?>> SUCCEEDING
									<input type="radio" name="MidtermEval" value="PROGRESSING" <?php echo ($OverAllRating == 'PROGRESSING') ? "checked" : ""; ?>> PROGRESSING
									<input type="radio" name="MidtermEval" value="UNSATISFACTORY" <?php echo ($OverAllRating == 'UNSATISFACTORY') ? "checked" : ""; ?>> UNSATISFACTORY
									<p><b>The mid-term evaluation is formative in nature and serves to guide the student and the clinical instructor
											in planning the student's clinical activities. It will be utilized in conjunction with the course syllabus. A
											summative evaluation of the student's overall clinical performance will be conducted at the end of the
											semester. Successfully completing the clinical course will depend on these evaluations.
											Performance criteria for the mid-term evaluation are based on the following</b></p>
									<dl>
										<dt>Succeeding</dt>
										<dd>- Student is successful in all job-related competencies and expectations and may excel in some areas. Student is on track to pass the clinical course.</dd>
										<dt>Progressing</dt>
										<dd>- Student is consistently meeting criteria at the minimum achievement level. Continuous improvement is expected in some areas. Student is on track to pass the clinical course.</dd>
										<dt>Unsatisfactory</dt>
										<dd>- Student is not consistently meeting criteria at expected level of achievement. The student must demonstrate consistent performance at the expected achievement level by the end of the term to pass clinical course.</dd>
									</dl>
									<p>The clinical instructor should discuss areas of strength and weakness and, if necessary, develop a plan to
										guide the student for the remainder of the rotation. If the student receives an <span><b>UNSATISFACTORY on
												one or more criteria,</b></span> the student's overall midterm evaluation will be <span><b>unsatisfactory and a plan for
												improvement should be developed.</b></span></p>
									<p><b>CLINICAL ATTENDANCE:</b></p>
									<p><b>Absence(s):</b> <input style="width:200px" value="<?php echo ($absence); ?>" type="text" id="Absence" name="Absence">
										<b>Days Tardy:</b> <input style="width:200px" value="<?php echo ($daysTardy); ?>" type="text" id="DaysTardy" name="DaysTardy">
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="instructions:"></label>
						<div class="col-md-10 col-sm-6 col-xs-12">
							<div class="panel panel-default">
								<div class="panel-body">
									<p>The clinical site ratings are based on the following categories. The rating scale for each category is<br>
										<b>1-Succeeding, 2-Progressing, 3-Unsatisfactory.</b>
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 1st SECTION div start -->
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="instructions:"></label>
						<div class="col-md-10 col-sm-6 col-xs-12">

							<div class="panel-group" id="posts">





								<div class="panel panel-default">
									<div class="panel-heading">
										<h4 class="panel-title">
											<a href="#sectionMasterId" data-toggle="collapse" data-parent="#posts"><b><?php echo  'Midterm Respiratory Care Clinical Evaluation'; ?></b></a>
										</h4>
									</div>

									<div id="sectionMasterId" class="panel-collapse collapse">
										<?php
										while ($row = mysqli_fetch_array($MidtermSection)) {
											$sectionMasterId = $row['sectionMasterId'];
											$title = $row['title'];

											// for question
											$totalFormative = 0;
											$midtermquestion = $objMidterm->GetAllMidtermQuestionMaster($schoolId, $sectionMasterId);
											if ($midtermquestion != '') {
												$totalFormative = mysqli_num_rows($midtermquestion);
											}

											if ($totalFormative > 0) {
												while ($row = mysqli_fetch_array($midtermquestion)) {
													if (isset($_GET['studentMidtermMasterId'])) {
														$studentMidtermMasterId = DecodeQueryData($_GET['studentMidtermMasterId']);
													} else {
														$studentMidtermMasterId = 0;
													}

													$midtermQuestionId = $row['midtermQuestionId'];
													$schoolMidtermQuestionTitle = $row['optionText'];
													$midtermQuestionType = $row['midtermQuestionType'];
													$qhtml = GetMidtermQuestionHtml($midtermQuestionId, $midtermQuestionType, $studentMidtermMasterId, $currentSchoolId);

										?>
													<div id="mydiv" class="panel-body">
														<b><?php echo ($schoolMidtermQuestionTitle); ?> </b><br /><br />
														<?php echo $qhtml; ?>
													</div>
										<?php
												}
											}
										}
										?>
									</div>
								</div>
								<?php
								//} 

								?>
							</div>



						</div>
					</div>
				</div>

				<div class="row">
					<div class="form-group">
						<label class="col-md-2 control-label"></label>
						<div class="col-md-10">
							<?php
							$rotationStatus = checkRotationStatus($midtermrotationid);
							if ($rotationStatus == 0) {
							?>
								<button style="margin: 0 0 0 11px;" id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
							<?php } ?>
							<?php if ($currentstudentId > 0) { ?>
								<a type="button" href="midtermlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>" class="btn btn-default">Cancel</a>
							<?php } else { ?>
								<a type="button" href="midtermlist.html?midtermrotationid=<?php echo EncodeQueryData($midtermrotationid); ?>" class="btn btn-default">Cancel</a>
							<?php } ?>
						</div>
					</div>
				</div>
			</div>
		</form>
	</div>
	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
	<!-- <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/ckeditor.js"></script> -->
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>




	<script type="text/javascript">
		// ClassicEditor
		// 	.create(document.querySelector('#studentcomment'))
		// 	.catch(error => {
		// 		console.error(error);
		// 	});
		// ClassicEditor
		// 	.create(document.querySelector('#cliniciancomment'))
		// 	.catch(error => {
		// 		console.error(error);
		// 	});
		$(window).load(function() {

			$(".select2_single").select2();
			//$('#select2-cborotation-container').addClass('required-select2');			
			$('#select2-cboclinician-container').addClass('required-select2');
			$('#select2-cbostudent-container').addClass('required-select2');

			$('#frmnidterm').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {
					ShowProgressAnimation();
					return true; // Don't submit form for this demo
				});

			$('#evaluationDate').datetimepicker({
				format: 'MM/DD/YYYY'
			});
			$('#studentsignitureDate').datetimepicker({
				format: 'MM/DD/YYYY'
			});
			$('#InstructorDate').datetimepicker({
				format: 'MM/DD/YYYY'
			});

			//for searching dropdown


			<?php if (isset($_GET['studentMidtermMasterId']) && ($_GET['midtermrotationid'])) { ?>
				$('#cbostudent').prop('disabled', true);
			<?php } ?>

			<?php if ($currentstudentId > 0) { ?>
				$('#cbostudent').prop('disabled', true);
			<?php }
			if ($midtermrotationid > 0) { ?>
				//document.getElementById("cborotation").required = false;	
			<?php } ?>

			$('#frmnidterm').parsley().on('div:validate', function(formInstance) {
				var ok = formInstance.isValid();
				if (!ok) {
					formInstance.validationResult = false;
				} else {
					$('#frmEditSection [type=submit]');
					formInstance.validationResult = true;
				}
			});


		});
	</script>
</body>

</html>