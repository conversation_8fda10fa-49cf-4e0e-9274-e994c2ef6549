<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php');
	include('../includes/commonfun.php');
    include('../class/clsDB.php');
	include('../setRequest.php');  
    include('../class/clsSchoolHoliday.php');
    
	$loggedUserId = $_SESSION["loggedUserId"];   
	$TimeZone='';
	$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
    //Get School Name
   
	//school Holidays
	$objSchoolHoliday = new clsSchoolHoliday();
	$holidayData = $objSchoolHoliday->GetAll($currentSchoolId);

    if($holidayData !='')
	{
		$totalHolidays =mysqli_num_rows($holidayData);

	}
	
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>School Holiday List  </title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
 <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">  
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

        <style>
           .mt-1 {
                    margin-top: 10px;
                    padding-left: 55px;
                }
        </style>

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">School Holidays</li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="container"> 

            <?php
		
				if (isset($_GET["status"]))
				{
					
					if($_GET["status"] =="added")
					{
						
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Student added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Student updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="StatusUpdated")
					{
				?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Student status updated successfully.
                    </div>
                <?php
					}
                    else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}
					 else if($_GET["status"] =="datanotfound")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Certification log not available.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
				

                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Type</th>
                            <th>Date</th>                    
                            <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                         if($totalHolidays>0)
                        {
                            while($row = mysqli_fetch_array($holidayData))
                            {					
                                $schoolHolidayId = stripslashes($row['schoolHolidayId']);
                                $schoolId = stripslashes($row['schoolId']);
								$type = stripslashes($row['type']); 
                                if($type=='c'){
                                    $holidayType = 'Closed';
                                }else{
                                    $holidayType = 'Holiday';
                                }
                                $holidayDate = stripslashes($row['holidayDate']);   
                                $holidayDate = date('m/d/Y', strtotime($holidayDate));                             
								
                                ?>
                            <tr>
                                <td> <?php echo($holidayType);?></td>
								<td><?php echo($holidayDate);?></td>
                                <td style="text-align: center">		
                                        <a href='addschoolholiday.html?schoolHolidayId=<?php echo EncodeQueryData($schoolHolidayId); ?>''>Edit</a>
                               </td>
                            </tr>
                            <?php


                            }
                        }
                        unset($objSchoolHoliday);
                    ?>



                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
         <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>    
        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            	$(".select2_tags").select2({'placeholder':'Select'}); //for multiple selection
				$(".select2_single").select2();
			});


            var current_datatable = $("#datatable-responsive").DataTable({
                "aoColumns": [{
                    "sWidth": "20%"
                }, {
                    "sWidth": "20%"
                }, {
                    "sWidth": "20%",
                    "bSortable": false
                    
                } ]
            });

            $("#cborank").change(function(){
                var rankId = $(this).val();
                
                if(rankId)
                {
                    window.location.href = "studentCheckoffList.html?rankId="+rankId;
                }
                else{
                    window.location.href = "studentCheckoffList.html";
                }
            });
				
        </script>
    </body>
    </html>