<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
	include('../setRequest.php');
    include('../class/clsDaily.php');
    include('../class/clsQuestionOption.php');
	// echo '<pre>';
	// print_r($_POST);
	// print_r($_GET);
	// exit;
	 $Type='';
	 $studentDailyMasterId=0;	 
	 $evaluationDate= date('Y-m-d');
	 if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
			$rotationId=0;
			
			$midtermrotationid=0;
			$schoolIncidentQuestionId='';
			$schoolIncidentQuestionType='';
			
			//Get Rotation
			$studentId = isset($_GET['studentId']) ? DecodeQueryData($_GET['studentId']) : 0;

			//Get Rotation
			$rotationId = isset($_GET['rotationId']) ? DecodeQueryData($_GET['rotationId']) : 0;

			//Get Type
			$Type = isset($_GET['Type']) ? ($_GET['Type']) : '';

			$studentDailyMasterId = isset($_GET['studentDailyMasterId']) ? DecodeQueryData($_GET['studentDailyMasterId']) : 0;

			$status = ($studentDailyMasterId > 0) ? 'updated' : 'added';
				
			$cboclinician  = isset($_POST['cboclinician']) ? $_POST['cboclinician'] : 0;
			$cbohospitalsites  = isset($_POST['cbohospitalsites']) ? $_POST['cbohospitalsites'] : 0;
			$cbostudent  = isset($_POST['cbostudent']) ? $_POST['cbostudent'] : 0;	
			$preceptorId  = isset($_POST['preceptorId']) ? $_POST['preceptorId'] : 0;
			$evaluationDate=GetDateStringInServerFormat($_POST['evaluationDate']);			
			//$rotationId  =isset($_POST['cborotation']) ? $_POST['cborotation'] : 0;

			$firstSectionAvg  = $_POST['firstSectionAvg'];
			$secondSectionAvg  = $_POST['secondSectionAvg'];
			$thirdSectionAvg  = $_POST['thirdSectionAvg'];
			$fourthSectionAvg  = $_POST['fourthSectionAvg'];
			$fiveSectionAvg  = $_POST['fiveSectionAvg'];
			$sixSectionAvg  = $_POST['sixSectionAvg'];
			$totalAvg  = $_POST['totalAvg'];

			
			$objDailyEval = new clsDaily();
			$objDailyEval->rotationId =$rotationId;
			$objDailyEval->clinicianId =$cboclinician;
			$objDailyEval->hospitalSiteId =$cbohospitalsites;	
			$objDailyEval->studentId =$cbostudent;			
			$objDailyEval->schoolId =$currentSchoolId;
			$objDailyEval->evaluationDate = $evaluationDate;

			$objDailyEval->firstSectionAvg = $firstSectionAvg;	
			$objDailyEval->secondSectionAvg = $secondSectionAvg;	
			$objDailyEval->thirdSectionAvg = $thirdSectionAvg;	
			$objDailyEval->fourthSectionAvg = $fourthSectionAvg;	
			$objDailyEval->fiveSectionAvg = $fiveSectionAvg;	
			$objDailyEval->sixSectionAvg = $sixSectionAvg;	
			$objDailyEval->totalAvg = $totalAvg;	

						
			$objDailyEval->createdBy =$_SESSION['loggedUserId'];;		
			$retDailyEvalId = $objDailyEval->SaveAdminDailyEval($studentDailyMasterId);	
			
			
			$objDailyEval->DeleteStudentDailyDetails($retDailyEvalId);
			
				foreach($_POST as $id=>$value)
			{
			
				if (strpos($id, 'questionoptions_') === 0) 
				{			
				//$id = explode("_", $id)[1];
				$id =	str_replace('questionoptions_','',$id);
				$objDailyEval->studentDailyMasterId = $retDailyEvalId;
				$objDailyEval->studentQuestionId = $id;		 		
				$objDailyEval->studentoptionvalue = $value[0];
				$objDailyEval->studentOptionAnswerText ='';	
				$studentDailyDetailId=$objDailyEval->SaveDailyDetails($retDailyEvalId);
				}
			}
			foreach($_POST as $id=>$value)
			{
			
				if (strpos($id, 'questionoptionst_') === 0) 
				{			
				//$id = explode("_", $id)[1];
				$id =	str_replace('questionoptionst_','',$id);
				$objDailyEval->studentDailyMasterId = $retDailyEvalId;
				$objDailyEval->studentQuestionId = $id;		 		
				$objDailyEval->studentoptionvalue ='';
				$objDailyEval->studentOptionAnswerText =$value[0];	
				$studentDailyDetailId=$objDailyEval->SaveDailyDetails($retDailyEvalId);
				}
			}
			
			unset($objDailyEval);
			
			if($retDailyEvalId > 0)
			{
				
				
				if(isset($_GET['studentDailyMasterId']) && $Type == '')
					header('location:dailyEvalList.html?studentDailyMasterId='.EncodeQueryData($studentDailyMasterId).'&status='.$status);
				elseif(isset($_GET['studentId']) && $Type == 'C')
					header('location:dailyEvalList.html?studentId='.EncodeQueryData($studentId).'&Type='.$Type.'&status='.$status);
				else
					header('location:dailyEvalList.html?rotationId='.EncodeQueryData($rotationId).'&Type='.$Type.'&status='.$status);	
					

				exit();
				
			}
			else
			{
				header('location:dailyEval.html?status=error');
			}
		 
	}
	{
		header('location:dailyEvalList.html');
		exit();
	}	
?>