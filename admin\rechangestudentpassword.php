<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');  
	include('../includes/commonfun.php');          
    include('../setRequest.php'); 	
	
	
	$currentSchoolId;
	if(isset($_GET['id']))
	{
		$studentId=$_GET['id'];
		$studentId = DecodeQueryData($studentId);
	}
	
	
	
?>
	<div class="container">
			<div class="" tabindex="-1" role="dialog">
				<div class="modal-dialog" role="document">
					<div class="modal-content">
							<div class="modal-header">
								<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>									
								<h4 class="modal-title">Change Password</h4>
							</div>
							<div class="modal-body">
								<div class="modal-body">
									<form name="rechnagepassword" action="rechangestudentpasswordsubmit.html?SchoolId=<?php echo EncodeQueryData($currentSchoolId); ?>&studentId=<?php echo  EncodeQueryData($studentId); ?>" data-parsley-validate class="form-horizontal" method="POST">
										 <div>
											<label  for="txtpassword">Password</label>
											<div >
												<input id="txtpassword"  class="form-control" name="txtpassword"    required type="password" placeholder="" >
											</div>
										</div>
										
										<div>
											<label for="txtconfirmpassword">Confirm Password</label>
											<div >
												<input id="txtconfirmpassword" class="form-control" name="txtconfirmpassword"   data-parsley-equalto="#txtpassword"  required type="password" placeholder="" >
											</div>
										</div><br>
										<div class="form-group">
											<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
											<a type="button" href="addstudent.html?id=<?php echo EncodeQueryData($studentId); ?>" class="btn btn-default">Cancel</a>
										</div>
									</form>
								</div>
							</div>
					</div>
				</div>
			</div>
		</div>
		<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>