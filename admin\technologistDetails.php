<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include('../class/clsExternalPreceptors.php');

$NumberSearch = '';

$NumberSearch = isset($_GET['NumberSearch']) ? $_GET['NumberSearch'] : '';
$totalStudentCount = 0;

$objPreceptor = new clsExternalPreceptors();
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSearch'])) {
  $NumberSearch = $_POST['NumberSearch'];

  //Get All Student List

  // $rowsStudentData = $objPreceptor->GetAllSchoolUserByMobileNumber($NumberSearch);
  // // print_r($rowsStudentData );exit;
  // if ($rowsStudentData != '') {
  //   $totalStudentCount = mysqli_num_rows($rowsStudentData);
  // }
}

unset($objStudent);


?>
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
  <title>Student List</title>
  <?php include('includes/headercss.php'); ?>
  <?php include("includes/datatablecss.php") ?>

  <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
  <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
  <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
  <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
  <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

  <style>
    .warpper {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .tabs {
      background: #ececf1;
      padding: 5px;
      width: 350px;
      display: flex;
      height: 55px;
      border-radius: 15px;
    }

    .tab {
      font-size: 14px;
      font-weight: 800;
      cursor: pointer;
      padding: 10px 20px;
      margin-bottom: 0;
      /* margin: 0px 2px; */
      /* background: #000; */
      display: inline-block;
      color: #8e8ea0;
      /* border-radius: 3px 3px 0px 0px; */
      /* box-shadow: 0 0.5rem 0.8rem #00000080; */
      width: 50%;
      text-align: center;
      border-radius: 15px;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .panels {
      background: #fffffff6;
      /* box-shadow: 0 2rem 2rem #00000080; */
      min-height: 200px;
      width: 100%;
      /* max-width:500px; */
      border-radius: 3px;
      /* overflow: hidden; */
      padding: 20px 0;
      overflow-x: auto;
      /* margin-top: 50px; */
    }

    .panel {
      display: none;
      animation: fadein .8s;
    }

    .note-text {
      font-size: 15px;
      font-weight: 600;
      font-style: italic;
      margin-bottom: 0;
    }

    .search-parent-section {
      max-width: 300px;
      width: 100%;
      margin-right: 10px;
    }

    .preceptor-form-section {
      display: flex;
      justify-content: start;
      align-items: end;
      margin: 20px 0 0;
      max-width: 400px;
      width: 100%;
    }

    .preceptor-form-parent {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
    }

    .mr-7 {
      margin-right: 7px;
    }

    .w-full {
      width: 100%;
    }

    @keyframes fadein {
      from {
        opacity: 0;
      }

      to {
        opacity: 1;
      }
    }

    .panel-title {
      font-size: 1.5em;
      font-weight: bold
    }

    .radio {
      display: none;
    }

    #one:checked~.panels #one-panel,
    #two:checked~.panels #two-panel,
    #three:checked~.panels #three-panel {
      display: block
    }

    #one:checked~.tabs #one-tab,
    #two:checked~.tabs #two-tab,
    #three:checked~.tabs #three-tab {
      background: #fffffff6;
      color: #000;
      /* border-top: 3px solid #000; */
    }

    #one:checked~.tabs #one-tab i,
    #two:checked~.tabs #two-tab i {
      color: #8688cb;
    }

    .table-bordered {
      border: 1px solid #ddd !important;
    }

    /* table.dataTable thead .sorting_asc:after {
      content: "";
    } */
  </style>
</head>

<body>
  <?php include('includes/header.php'); ?>
  <div class="row margin_zero breadcrumb-bg">
    <div class="container">
      <div class="pull-left">
        <ol class="breadcrumb">
          <li><a href="dashboard.html">Home</a></li>
          <li class="active">Technologist</li>
        </ol>
      </div>
    </div>
  </div>

  <div class="container">
    <div class="warpper">
      <input class="radio" id="one" name="group" type="radio" value="1" checked>
      <input class="radio" id="two" name="group" type="radio" value="0">
      <!-- <input class="radio" id="three" name="group" type="radio"> -->
      <div class="tabs">
        <label class="tab" id="one-tab" for="one"><i class="fa fa-user mr-7" aria-hidden="true"></i>School Users </label>
        <label class="tab" id="two-tab" for="two"><i class="fa fa-phone mr-7" aria-hidden="true"></i>Technologist</label>
        <!-- <label class="tab" id="three-tab" for="three">Prerequisites</label> -->
      </div>

      <div id="processing-indicator" style="display: none;">
        <div class="spinner"></div>
        <!-- <p>Loading data...</p> -->
      </div>

      <div class="w-full">
        <div class="preceptor-form-parent">
          <form class="preceptor-form-section" name="studentList" id="studentList" method="POST" action="technologistDetails.html">
            <!-- <div style="width: 100%; max-width: 260px; margin-right: 10px;"> -->
            <!-- <div style="margin-bottom: 0;" class="form-group control-label">
              <label style="font-weight: 600;">Enter the Phone Number to Search</label>
            </div> -->
            <div class="search-parent-section">
              <input type="text" class="form-control" placeholder="Search by Phone Number" name="NumberSearch" id="NumberSearch" value="<?php echo $NumberSearch; ?>">
            </div>
            <!-- </div> -->
            <div class="">
              <div class="form-group" style="margin-bottom: 0;">
                <button type="button" id="btnSearch" name="btnSearch" class="btn btn-success">Search</button>
              </div>
            </div>
          </form>
          <div>
            <!-- <p class="note-text">Note: We can show the top 50 Records here.</p> -->
          </div>
        </div>
      </div>
      <div class="panels">
        <div class="panel" id="one-panel">
          <table id="datatable-schooluser" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
              <tr>
                <th style="text-align:center">School</th>
                <th style="text-align:center">Name</th>
                <th style="text-align:center">Email ID</th>
                <th style="text-align:center">Phone Number</th>
                <th style="text-align:center">Role</th>
              </tr>
            </thead>
            <tbody>
            </tbody>
          </table>
        </div>
        <div class="panel" id="two-panel">
          <table id="datatable-preceptor" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
              <tr>
                <th style="text-align:center">School</th>
                <th style="text-align:center">Student</th>
                <th style="text-align:center">Phone Number</th>
                <th style="text-align:center">Rotation </th>
                <th style="text-align:center">Date</th>
                <th style="text-align:center">Type</th>
              </tr>
            </thead>
            <tbody>

            </tbody>
          </table>
        </div>
        <!-- <div class="panel" id="three-panel">
                <div class="panel-title">Note on Prerequisites</div>
                <p>We recommend that you complete Learn HTML before learning CSS.</p>
            </div> -->
      </div>
    </div>
  </div>


  <?php include('includes/footer.php'); ?>
  <?php include("includes/datatablejs.php") ?>
  <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
  <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
  <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
  <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

  <script type="text/javascript">
    alertify.defaults.transition = "slide";
    alertify.defaults.theme.ok = "btn btn-primary";
    alertify.defaults.theme.cancel = "btn btn-danger";
    alertify.defaults.theme.input = "form-control";

    $(window).load(function() {
      $("#divTopLoading").addClass('hide');
    });

    $(document).ready(function() {
      var current_datatable_schooluser = $("#datatable-schooluser").DataTable({

        // "autoWidth": false,
        // 'iDisplayLength': 250,
        "aoColumns": [{
            "sWidth": "20%",
            "bSortable": true 
          },
          {
            "sWidth": "20%",
            "bSortable": true
          },
          {
            "sWidth": "20%",
            "bSortable": true
          },
          {
            "sWidth": "20%",
            "bSortable": false
          },
          {
            "sWidth": "20%",
            "bSortable": false

          }
        ]

      });

      var current_datatable_preceptor = $("#datatable-preceptor").DataTable({
        // "autoWidth": false,
        // 'iDisplayLength': 250,
        "aoColumns": [{
            "sWidth": "20%",
            "bSortable": true
          },
          {
            "sWidth": "20%",
            "bSortable": true
          },
          {
            "sWidth": "10%",
            "bSortable": false
          },
          {
            "sWidth": "20%",
            "bSortable": false
          },
          {
            "sWidth": "10%",
            "bSortable": false

          },
          {
            "sWidth": "20%",
            "bSortable": false

          }
        ]

      });

      $('#btnSearch').on('click', function() {

        var phoneNumber = $("#NumberSearch").val();
        if (one.checked == 1) {
          table = current_datatable_schooluser
          type = 'schooluser';
        } else {
          table = current_datatable_preceptor
          type = 'preceptor';
        }

        // $("#processing-indicator").show();
        ShowProgressAnimation();


        $.ajax({
          type: "POST",
          url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_get_user_details.html",
          data: {
            phoneNumber: phoneNumber,
            type: type
          },
          success: function(data) {
            // console.log(data);
            // $("#processing-indicator").hide();
            HideProgressAnimation();
            if (type == 'schooluser') {

              table.clear().draw();

              data = JSON.parse(data);

              $.each(data, function(index, row) {

                // Add a row to the DataTable
                table.row.add([
                  row.schoolName,
                  row.userName,
                  row.email,
                  row.phone,
                  row.userRole
                ]).draw();
              });
            } else {
              table.clear().draw();

              data = JSON.parse(data);

              $.each(data, function(index, row) {

                // Add a row to the DataTable
                table.row.add([
                  row.schoolName,
                  row.userName,
                  row.phone,
                  row.rotationName,
                  row.evaluationDate,
                  row.type
                ]).draw();
              });
            }
          }
        });
      });
    });

    // $(".select2_single").select2();
    //       $('#select2-copySchool-container').addClass('required-select2');
    //       $('#form-control step2 input-md select2_single').addClass('required-select2');

    // $('.addCopyPopup').magnificPopup({
    //   'type': 'ajax',

    //   'closeOnBgClick': false
    // });


    $.magnificPopup.instance._onFocusIn = function(e) {
      // Do nothing if target element is select2 input
      if ($(e.target).hasClass('select2-input')) {
        return true;
      }
      // Else call parent method
      $.magnificPopup.proto._onFocusIn.call(this, e);
    }

    $(document).ready(function() {
      // Initialize DataTables for both tabs
      var table1 = $('#datatable-schooluser').DataTable();
      var table2 = $('#datatable-preceptor').DataTable();

      // Handle radio button change event
      $('input[name="group"]').on('change', function() {
        var selectedTab = $(this).val();

        // Clear the rows from the DataTable for the other tab
        if (selectedTab === '1') {
          table2.clear().draw();
          $('#datatable-preceptor_info').text('0 to 0 of 0');
          // Reinitialize DataTable for the School User tab
          table1 = $('#datatable-schooluser').DataTable();
        } else {
          table1.clear().draw();
          $('#datatable-schooluser_info').text('0 to 0 of 0');
          // Reinitialize DataTable for the Preceptor tab
          table2 = $('#datatable-preceptor').DataTable();
        }
      });
    });
  </script>


</body>

</html>