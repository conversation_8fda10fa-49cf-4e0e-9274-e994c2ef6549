<?php

	include('includes/validateUserLogin.php');
    include('../includes/config.php');
    include('../class/clsDB.php');
    include('../class/clsClinician.php'); 
	include('../class/clsSchool.php'); 
	include('../includes/commonfun.php');
	include('../setRequest.php');

	if( isset($_GET['id'])) //Edit Mode
	{
		$clinicianId = $_GET['id'];
		$type = $_GET['type'];
		if($type == 'status')
		{
			//Create object
			$objClinician = new clsClinician();
			$newStatus = $_GET['newStatus'];
			$objClinician->SetClinicianStatus($clinicianId,$newStatus,$currentSchoolId);

			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

			// $isUser 1 for Admin and 2 for Clinician and 3 for Student
			// Instantiate the Logger class
			$objLog = new clsLogger();
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			if ($isUser == 1) {
				$userType = $objLog::ADMIN;
			} else if ($isUser == 3) {
				$userType = $objLog::STUDENT;
			} else if ($isUser == 2) {
				$userType = $objLog::EVALUATOR;
			}

			$objClinician->saveClinicianAuditLog($clinicianId, $userId, $userType, $logAction);
				

			unset($objClinician);
			header('location:schoolclinicians.html?status=StatusUpdated');

		}
		else
		{
			$objClinician = new clsClinician();
			$newblockStatus = $_GET['newblockStatus'];
			$objClinician->SetClinicianBlockUnblock($clinicianId,$newblockStatus,$currentSchoolId);

			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

			// $isUser 1 for Admin and 2 for Clinician and 3 for Student
			// Instantiate the Logger class
			$objLog = new clsLogger();
			$logAction = ($newblockStatus == 0) ? $objLog::UNLOCK : $objLog::LOCK;
			if ($isUser == 1) {
				$userType = $objLog::ADMIN;
			} else if ($isUser == 3) {
				$userType = $objLog::STUDENT;
			} else if ($isUser == 2) {
				$userType = $objLog::EVALUATOR;
			}

			$objClinician->saveClinicianAuditLog($clinicianId, $userId, $userType, $logAction);

			unset($objClinician);
			header('location:schoolclinicians.html?status=blockStatusUpdated');

		}
	}
	else
	{
		exit();
	}
?>