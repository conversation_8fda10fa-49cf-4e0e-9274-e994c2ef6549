<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsRotation.php');
include('../class/clsStudent.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsSectionStudentName.php');
include('../class/clsClinician.php');
include('../class/clsPersonnelCoarc.php');

$schoolId = 0;
$rotationId = 0;
$clinicianId = 0;
$personnelCoarcMasterId = 0;
$clinicianId = 0;
$studentId = 0;

$display_to_date = date('m/d/Y');



//FOR EDIT JRCERT Survey
if (isset($_GET['personnelCoarcMasterId']) && ($_GET['clinicianId']) && ($_GET['personnelCoarcId'])) {
	$personnelCoarcMasterId = DecodeQueryData($_GET['personnelCoarcMasterId']);
	$personnelCoarcId = DecodeQueryData($_GET['personnelCoarcId']);

	$clinicianId = DecodeQueryData($_GET['clinicianId']);

	$schoolId = $currentSchoolId;
	$page_title = "View Personnel JRCERT Survey ";
	$bedCrumTitle = 'View';
	$objPersonnelCoarc = new clsPersonnelCoarc();
	$rowCoarc = $objPersonnelCoarc->GetPersonnelStudentCoarcSurveyDetails($personnelCoarcMasterId, $clinicianId);
	//unset($objPersonnelCoarc);
	if ($rowCoarc == '') {
		header('location:personnelrequest.html');
		exit;
	}

	$evaluationDate = ($rowCoarc['evaluationDate']);
	$evaluationDate = date('m/d/Y', strtotime($evaluationDate));
	$clinicianId = ($rowCoarc['clinicianId']);
	$sponsoringInstitution = ($rowCoarc['sponsoringInstitution']);
	$coARCSatelliteOptionProgramId = ($rowCoarc['coARCSatelliteOptionProgramId']);
	$coARCEntryBaseProgramId = ($rowCoarc['coARCEntryBaseProgramId']);
} else {
	$schoolId = $currentSchoolId;
	$page_title = "Add Personnel JRCERT Survey";
	$bedCrumTitle = 'Add';

	if (isset($_GET['personnelCoarcId'])) {

		$personnelCoarcId = $_GET['personnelCoarcId'];
		$personnelCoarcId = DecodeQueryData($personnelCoarcId);
	}
	if (isset($_GET['studentId'])) {

		$clinicianId = $_GET['studentId'];
		$clinicianId = DecodeQueryData($clinicianId);
	}
}


//----------------------------//
$objClinician = new clsClinician();
$Clinician = $objClinician->GetAllSchoolClinicians($schoolId);
unset($objClinician);
//--------------------------//
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($clinicianId, $rotationId);
unset($objStudent);
//--------------------------//
$objSectionStudentName = new clsSectionStudentName();
$getStudentName = $objSectionStudentName->GetSectionStudentName($schoolId, $clinicianId);
unset($objSectionStudentName);
///----------------------//

$objPersonnelCoarc = new clsPersonnelCoarc();
$totalSection = 0;
$CoarcSection = $objPersonnelCoarc->GetSections($schoolId);
if ($CoarcSection != '') {
	$totalSection = mysqli_num_rows($CoarcSection);
}

//Get Personal JRCERT Survey Detail
$personalCoarcSurveyDetail = $objPersonnelCoarc->GetPersonnelStudentCoarcSurveyDetails($personnelCoarcMasterId, $clinicianId);
$personnelCoarcMasterId = isset($personalCoarcSurveyDetail['personnelCoarcMasterId']) ? $personalCoarcSurveyDetail['personnelCoarcMasterId'] : 0;

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($page_title); ?></title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<style type="text/css">
		.some-class {
			float: left;
			clear: none;
		}

		.redColourToOptions {
			color: red;
			font-weight: bold;
		}

		/* .required-input {
			border-left: 1px solid #ccc !important;
		} */


		.collapsible {
			cursor: pointer;
			/* padding: 15px; */
			/* border: 1px solid #181818; */
			/* background-color: #f9f9f9; */
			display: flex;
			justify-content: space-between;
			align-items: center;
			/* border-radius: 14px; */
		}

		.collapsible p {
			margin: 0;
		}

		.collapsible-arrow {
			font-size: 18px;
			transition: transform 0.3s ease;
		}

		.content {
			display: none;
			padding: 10px 0;
			/* border-top: 1px solid #ececec; */
		}

		.content.active {
			display: block;
		}

		.active.collapsible-arrow {
			transform: rotate(180deg);
		}

		.row-delete-icon {
			position: absolute;
			top: -82px;
			right: 20px;
		}

		.mobile-block {
			display: block;
		}
	</style>

</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
					<li><a href="settings.html">Setting</a></li>
					<li><a href="personnelrequest.html">Personnel JRCERT Survey</a></li>
					<li class="active"><?php echo ($bedCrumTitle); ?></li>
				</ol>
			</div>

		</div>
	</div>

	<div class="container">
		<?php
		if (isset($_GET["status"])) {
			if ($_GET["status"] == "updated") {
		?>
				<div class="alert alert-success alert-dismissible fade in" role="alert">
					<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
					</button> JRCERT Survey updated successfully.
				</div>
		<?php
			}
		}  ?>
		<form id="frmemployercoarcsurvey" data-parsley-validate class="form-horizontal" method="POST"
			action="addpersonnelsurveysubmit.html?personnelCoarcMasterId=<?php echo (EncodeQueryData($personnelCoarcMasterId)); ?>
		&studentId=<?php echo (EncodeQueryData($clinicianId)); ?>&personnelCoarcId=<?php echo (EncodeQueryData($personnelCoarcId)); ?>">

			<div class="row">
				<div class="col-md-6">

					<div class="form-group">
						<label class="col-md-12 control-label" for="cboclinician">Clinician</label>
						<div class="col-md-12">
							<select id="cboclinician" name="cboclinician"
								class="form-control input-md required-input select2_single" required>
								<option value="" selected>Select</option>
								<?php
								if ($Clinician != "") {
									while ($row = mysqli_fetch_assoc($Clinician)) {
										$selClinicianId  = $row['clinicianId'];
										$name  = stripslashes($row['firstName']);
								?>
										<option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

								<?php

									}
								}
								?>
							</select>
						</div>
					</div>

				</div>


				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="evaluationDate">Evaluation Date</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full relative' id='evaluationDate'>

								<input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date" value="<?php echo ($evaluationDate); ?>" required data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>

						</div>
					</div>

				</div>


			</div>



			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="spomsoringInstitudeName">Sponsoring Institution/
							Consortium Name:</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full' id='spomsoringInstitudeName'>

								<input type='text' name="spomsoringInstitudeName" id="spomsoringInstitudeName" class="form-control input-md required-input" <?php if (isset($_GET['personnelCoarcMasterId'])) { ?>value="<?php echo ($sponsoringInstitution); ?>" <?php } else { ?> value="" <?php } ?> required />

							</div>

						</div>
					</div>
				</div>
				<div class="col-md-6">

					<div class="form-group">
						<label class="col-md-12 control-label" for="coarcSatellite">JRCERT Satellite Option
							Program ID# (if applicable):</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full' id='coarcSatellite'>

								<input type='text' name="coarcSatellite" id="coarcSatellite" class="form-control input-md required-input" <?php if (isset($_GET['personnelCoarcMasterId'])) { ?>value="<?php echo ($coARCSatelliteOptionProgramId); ?>" <?php } else { ?> value="" <?php } ?> required />

							</div>

						</div>
					</div>

				</div>
			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="coarcEntryBaseProgramId">JRCERT Entry Base Program ID#:</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full' id='coarcEntryBaseProgramId'>

								<input type='text' name="coarcEntryBaseProgramId" id="coarcEntryBaseProgramId" class="form-control input-md required-input" <?php if (isset($_GET['personnelCoarcMasterId'])) { ?>value="<?php echo ($coARCEntryBaseProgramId); ?>" <?php } else { ?> value="" <?php } ?> required />

							</div>

						</div>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-12 control-label" for="instructions:"></label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="panel panel-default border-14">
								<div class="panel-body">
									<b><u>NOTE: Completion of this survey is required as part of outcomes assessment by the
											program's accreditation body (JRCERT).</u></b><br>
									<li><b><i>The purpose of this survey is to help faculty evaluate the Program's success in preparing graduates to function as competent respiratory therapists. Compiled data from all returned surveys will be used to evaluate program quality; data from individual surveys will be held in strict confidence. The JRCERT requests that this survey be completed by the graduate's immediate supervisor.</i></b></li>


								</div>
							</div>
						</div>
					</div>
				</div>
			</div>


			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-12 control-label" for="instructions:">Instructions</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="panel panel-default border-14">
								<div class="panel-body">

									<li>Consider each item separately and rate each item independently of all others. Check the rating that indicates the extent to which you agree with each statement. Please do not skip any rating. If you do not know about a particular area, please check N/A.</li>
									<p><b>5 = Strongly Agree 4 = Generally Agree 3 = Neutral (acceptable)<span style="color:red"> 2 = Generally Disagree 1 = Strongly Disagree</span>
											<br>N/A = Not Applicable
											<span style="color:red">NOTE: Please provide detailed comments for any item rated below 3.</span>
											<center><i>(Relevant Standard is in parentheses)</i></center>
										</b></p>

								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!---- 1st SECTION div start --------->
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-12 control-label" for="instructions:"></label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="panel-group" id="posts">
								<div class="panel panel-default">
									<a class="collapsible" style="color: #000; text-decoration: none;" href="#CoarcSection" data-toggle="collapse" data-parent="#posts" id="collapse-link">
										<div class="panel-heading" style="width : 100%; display: flex; justify-content: space-between; align-items: center;">
											<h4 class="panel-title">
												Clinical Trac JRCERT Personnel Program Survey
											</h4>
											<span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
										</div>
									</a>

									<div id="CoarcSection" class="panel-collapse panel-body collapse">

										<?php
										while ($row = mysqli_fetch_array($CoarcSection)) {
											$sectionMasterId = $row['sectionMasterId'];
											$title = $row['title'];

										?>
											<div class="panel-body"><?php echo '<b>' . $title . '</b>'; ?></div>
											<?php 		//$objPersonnelCoarc='';
											$totalSection = 0;
											$objPersonnelCoarc = new clsPersonnelCoarc();
											$CoarcSubSection = $objPersonnelCoarc->GetAllSurveyQuestionBySchool($sectionMasterId);
											if ($CoarcSubSection != '') {
												$totalSection = mysqli_num_rows($CoarcSubSection);
											}


											// for question
											$totalCoarc = 0;
											$Coarcquestion = $objPersonnelCoarc->GetAllPersonnelCoarcSurveyQuestionMaster($schoolId, $sectionMasterId);

											if ($Coarcquestion != '') {
												$totalCoarc = mysqli_num_rows($Coarcquestion);
											}

											if ($totalCoarc > 0) {
												while ($row = mysqli_fetch_array($Coarcquestion)) {
													// $personnelCoarcMasterId =  isset($_GET['personnelCoarcMasterId']) ? DecodeQueryData($_GET['personnelCoarcMasterId']) : 0;
													$schoolPersonnelCoarcQuestionId = $row['schoolPersonnelCoarcQuestionId'];

													$questionText = $row['questionText'];
													$schoolPersonnelCoarcQuestionType = $row['schoolPersonnelCoarcQuestionType'];
													$qhtml = GetPersonnelCoarcQuestionHtml($schoolPersonnelCoarcQuestionId, $schoolPersonnelCoarcQuestionType, $personnelCoarcMasterId, $currentSchoolId, 0);

											?>
													<div class="panel-body">
														<?php echo ($questionText); ?> <br /><br />
														<?php echo $qhtml; ?>
													</div>
										<?php
												}
											}
										}
										?>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-md-6"></div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label"></label>
						<div class="col-md-6">

							<!--button id="btncompletecoarc" name="btncompletecoarc" class="btn btn-success">Submit Survey</button---->
							<?php if (isset($_GET['personnelCoarcMasterId']) && ($_GET['clinicianId']) && ($_GET['personnelCoarcId'])) { ?>
								<a type="button" class="btn btn-success" href="personnelrequest.html?clinicianId=<?php echo EncodeQueryData($clinicianId); ?>" class="btn btn-default">Cancel</a>
							<?php } else { ?>
								<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Send Request</button>
							<?php } ?>
						</div>
					</div>
				</div>
			</div>
		</form>


	</div>

	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>




	<script type="text/javascript">
		$(window).load(function() {


			$('#frmemployercoarcsurvey').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {
					ShowProgressAnimation();
					return true; // Don't submit form for this demo
				});

			$('#evaluationDate').datetimepicker({
				format: 'MM/DD/YYYY'
			});


			//for searching dropdown
			$(".select2_single").select2();
			$('#select2-cborotation-container').addClass('required-select2');
			$('#select2-cboclinician-container').addClass('required-select2');

			<?php if (isset($_GET['personnelCoarcMasterId']) && ($_GET['studentId'])) { ?>
				$('#cbostudent').prop('disabled', true);
			<?php } ?>
		});
	</script>
		<script>
		// Get all collapsible button elements
		var buttons = document.querySelectorAll(".collapsible");
		var contents = document.querySelectorAll(".panel-collapse");

		// Add click event listeners to all buttons
		buttons.forEach(function(button, index) {
			button.addEventListener("click", function() {
				// Check if the content is currently expanded
				var isExpanded = contents[index].style.display === "block";

				// Close all sections
				contents.forEach(function(content) {
					content.style.display = "none";
				});

				// Reset the "expanded" class for all buttons
				buttons.forEach(function(btn) {
					btn.classList.remove("expanded");
				});

				// Toggle the content for the clicked section
				if (!isExpanded) {
					contents[index].style.display = "block";
					button.classList.add("expanded");
				}
			});
		});
	</script>
</body>

</html>