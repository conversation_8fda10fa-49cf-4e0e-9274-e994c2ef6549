<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php');
	include('../includes/commonfun.php');
    include('../class/clsDB.php');
	include('../setRequest.php');  
    include('../class/clsStudent.php');
    include('../class/clsStudentRankMaster.php');
    
	$loggedUserId = $_SESSION["loggedUserId"];   
	$TimeZone='';
	$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
    //Get School Name
	$rankId = 0;
    //CREATE OBJECT
	$objStudents = new clsStudent();
    $totalSchoolStudents = 0;
    $accreditationId = 0;
    $currentstudentId  = 0;
    $currentSchoolId;
    $studentImmunizationId=0;

    //For Check checkoff 
    $isActiveCheckoff='';
    $isActiveCheckoff =$_SESSION["isActiveCheckoff"];
    
    if(isset($_GET['rankId']))
	{
		$rankId = $_GET['rankId'];
        $rankId = DecodeQueryData($rankId);
    }
	if(isset($_GET['studentId']))
	{
		$currentstudentId = $_GET['studentId'];
        $currentstudentId = DecodeQueryData($currentstudentId);
    }	
	//SchoolStudents
	$rowsSchoolStudents = $objStudents->GetAllSchoolStudents($currentSchoolId,$rankId,$currentstudentId);
	if($rowsSchoolStudents !='')
	{
		$totalSchoolStudents = mysqli_num_rows($rowsSchoolStudents);
	}
    else
    {
        header('location:dashboard.html?status=error');
        exit;
    }
	 if(isset($_GET['studentId'])) 
	{
		$studentId = $_GET['studentId'];
        $studentId = ($studentId);
    }
	 if(isset($_GET['reaccreditationId'])) 
	{
		$reaccreditationId = $_GET['reaccreditationId'];
        $reaccreditationId = ($reaccreditationId);
    }	
 
   
	//StudentRank
	$objStudentRankMaster = new clsStudentRankMaster();
	$ranks = $objStudentRankMaster->GetAllStudentRankBySchoolClinician($currentSchoolId);
	unset($objStudentRankMaster);
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Checkoff Students List  </title>
        <?php include('includes/headercss.php');?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
        <?php include("includes/datatablecss.php") ?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">  
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />


        <style>
           .mt-1 {
                    margin-top: 10px;
                    padding-left: 55px;
                }
        </style>

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li class="active">Student List</li>
                    </ol>
                </div>
            </div>
        </div>

        <div class="container"> 

            <?php
		
				if (isset($_GET["status"]))
				{
					
					if($_GET["status"] =="added")
					{
						
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Student added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Student updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="StatusUpdated")
					{
				?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Student status updated successfully.
                    </div>
                <?php
					}
                    else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}
					 else if($_GET["status"] =="datanotfound")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Certification log not available.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
				
				<div class="row">
						<div class="col-md-3 pull-right">
						<div class="form-group">
                            <label class="col-md-4 control-label mt-1" for="cborank">Rank</label>
                            <div class="col-md-8 pr-0">
                                <select id="cborank" name="cborank" class="form-control select2_single"   >
                                <option value="" selected>Select All</option>
                                
                                    <?php
                                    if($ranks!="")
                                    {
                                        while($row = mysqli_fetch_assoc($ranks))                                    {
                                            
                                            $selrankId  = $row['rankId'];
                                            $name  = stripslashes($row['title']);

                                            ?>
                                            <option value="<?php echo EncodeQueryData($selrankId); ?>" <?php if($rankId==$selrankId){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
                                            <?php

                                        }
                                    }
                                ?>
                                </select>
                            </div>
                        </div>
						</div>
				</div>	<br>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>First Name</th>
                            <th>Last Name</th>
                            <th>Rank</th>                         
                            <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                         if($totalSchoolStudents>0)
                        {
                            while($row = mysqli_fetch_array($rowsSchoolStudents))
                            {
								$studentId='';
                                $studentId = $row[0];					
                                $firstName = stripslashes($row['firstName']);
                                $lastName = stripslashes($row['lastName']);
                                $fullName = $firstName.' '.$lastName;
								$rank = stripslashes($row['rank']);                                
								
                                ?>
                            <tr>
                                <td> <?php echo($firstName);?></td>
								<td><?php echo($lastName);?></td>
								<td><?php echo($rank); ?></td>
								
                                <td style="text-align: center">	
                                    <?php if($isActiveCheckoff == 1) {?>		
                                        <a href='checkoff.html?studentId=<?php echo EncodeQueryData($studentId); ?>&Type=C' >View</a>
                                    <?php } else if($isActiveCheckoff == 2) {?>		
                                        <a href='checkoffusaf.html?studentId=<?php echo EncodeQueryData($studentId); ?>&Type=C' >View</a>
                                    <?php } else { ?>
                                        <a href='checkoffs.html?studentId=<?php echo EncodeQueryData($studentId); ?>&Type=C' >View</a>
                                    <?php } ?>   
                                </td>
                            </tr>
                            <?php


                            }
                        }
                        unset($objStudents);
                    ?>



                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
         <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>    
        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            	$(".select2_tags").select2({'placeholder':'Select'}); //for multiple selection
				$(".select2_single").select2();
			});


            var current_datatable = $("#datatable-responsive").DataTable({
                "aoColumns": [{
                    "sWidth": "20%"
                }, {
                    "sWidth": "20%"
                }, {
                    "sWidth": "20%"
                },{
                    "sWidth": "40%",
                    "sClass": "alignCenter",
                    "bSortable": false
                } ]
            });

            $("#cborank").change(function(){
                var rankId = $(this).val();
                
                if(rankId)
                {
                    window.location.href = "studentCheckoffList.html?rankId="+rankId;
                }
                else{
                    window.location.href = "studentCheckoffList.html";
                }
            });
				
        </script>
    </body>
    </html>