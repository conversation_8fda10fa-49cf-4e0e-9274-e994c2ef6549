<?php
// Redirect from mobile
$IsMobile = isset($_GET['IsMobile']) ? ($_GET['IsMobile']) : 0;

include('../includes/config.php');
include('includes/validateUserLogin.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsCIevaluation.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsClinician.php');
include('../class/clsQuestionOption.php');
include('../class/clsPerformance.php');
include('../class/clsClinicalEval.php');
include('../class/clscheckoff.php');
include('../class/clsCheckoffTopicMaster.php');
include('../class/clsExternalPreceptors.php');
include('../class/clsProjection.php');
// echo '<pre>';
@session_start();
// print_r($_SESSION);
// exit;
$schoolId = 0;
$rotationId = 0;
$studentId = 0;
$clinicalEvaluationMasterId = 0;
$loggedClinicianId = $clinicianId = isset($_SESSION['loggedClinicianId']) ? $_SESSION['loggedClinicianId'] : 0;
$clinicalRotationId = 0;
$patientCareAdultAreaId = 0;
$patientCarePediatricAreaId = 0;
$patientCareNeonatalaAreaId = 0;
$hospitalSiteId = 0;
$courselocationId = 0;
$totalSection = 0;
$parentRotationId = 0;
$rotationLocationId = 0;
$display_to_date = date('m/d/Y');
$evaluationDate = '';
$evaluatorDate = $studentDate = '';
$currentDate = date('m/d/Y');
$view = '';
$exam = '';
$preceptorsignature = '';
$preceptorSignatureDate = '';
$accession = $comment = $schoolComment = $reason = '';
$radioType = '0';
$radioRepeatReason = $radioRepeat = $radioResult = $schoolTopicId = '0';
//Technologist Details
$preceptorId = isset($_GET['preceptorId']) ? DecodeQueryData($_GET['preceptorId']) : 0;
$preceptorNum = isset($_GET['preceptorNum']) ? DecodeQueryData($_GET['preceptorNum']) : '';
$projectionAId = $projectionBId = $projectionCId = 0;

$weeks = 0;
$objDB = new clsDB();
$getTimezoneId = $objDB->GetSingleColumnValueFromTable('schools', 'timeZoneId', 'schoolId', $currentSchoolId);
$TimeZone = $objDB->GetSingleColumnValueFromTable('timezonemaster', 'timezone', 'timeZoneId', $getTimezoneId);
unset($objDB);

//object
$objRotation = new clsRotation();
$objDB = new clsDB();
$objPerformance = new clsPerformance();
$objClinicalEval = new clsClinicalEval();
if (isset($_GET['clinicalRotationId']))
    $clinicalRotationId = DecodeQueryData($_GET['clinicalRotationId']);

$checkoffId = isset($_GET['checkoffId']) ? DecodeQueryData(($_GET['checkoffId'])) : 0;
if ($checkoffId) {
    $objcheckoff = new clscheckoff();
    $row = $objcheckoff->GetCheckOff($checkoffId);
    if ($row != '') {
        $accession = $row['accessionNo'];
        $schoolTopicId = stripslashes($row['schoolTopicId']);
        $studentId = stripslashes($row['studentId']);
        $evaluationDate  = stripslashes($row['evaluationDate']);

        if ($evaluationDate != '0000-00-00 00:00:00') {
            $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
            $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
        } else {
            $evaluationDate = '';
        }
    }
}
//For Edit CI Evaluation
if (isset($_GET['clinicalEvaluationMasterId'])) {
    $clinicalEvaluationMasterId = DecodeQueryData($_GET['clinicalEvaluationMasterId']);

    if ($clinicalEvaluationMasterId) {
        // $schoolId = $currentSchoolId;
        $page_title = "Edit Clinical Evaluation ";
        $bedCrumTitle = 'Edit';

        //Get CI Evalution Details

        $rowEvaluation = $objClinicalEval->GetClinicalEvaluation($currentSchoolId, $clinicalEvaluationMasterId);

        if ($rowEvaluation == '') {
            header('location:clinicalEvaluationList.html');
            exit;
        }
        // echo '<pre>';
        // print_r($rowEvaluation);
        // $clinicalEvaluationMasterId = $rowEvaluation['clinicalEvaluationMasterId'];
        $clinicianId = isset($rowEvaluation['clinicianId']) ? $rowEvaluation['clinicianId'] : 0;
        $studentId = isset($rowEvaluation['studentId']) ? $rowEvaluation['studentId'] : 0;
        $clinicianName = isset($rowEvaluation['clinicianName']) ? $rowEvaluation['clinicianName'] : '';
        $studentName = isset($rowEvaluation['studentName']) ? $rowEvaluation['studentName'] : '';
        $rotationId = isset($rowEvaluation['rotationId']) ? $rowEvaluation['rotationId'] : 0;
        $rotationName = isset($rowEvaluation['rotationName']) ? $rowEvaluation['rotationName'] : '';
        $schoolTopicId = $exam = isset($rowEvaluation['exam']) ? $rowEvaluation['exam'] : '';
        $accession = isset($rowEvaluation['accession']) ? $rowEvaluation['accession'] : '';
        $weeks = isset($rowEvaluation['weeks']) ? $rowEvaluation['weeks'] : '';
        $preceptorId = ($preceptorNum == '') ? $rowEvaluation['preceptorId'] : $preceptorId;
        $clinicianId = ($preceptorNum == '' && $clinicianId == 0) ? $loggedClinicianId : $clinicianId;
        $checkoffId = isset($rowEvaluation['checkoffId']) ? $rowEvaluation['checkoffId'] : 0;

        $evaluationDate = isset($rowEvaluation['evaluationDate']) ? stripslashes($rowEvaluation['evaluationDate']) : '';
        if ($evaluationDate != '' && $evaluationDate != '0000-00-00 00:00:00') {
            $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
            $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
        } else
            $evaluationDate = '';

        $evaluatorSignatureDate = isset($rowEvaluation['evaluatorSignatureDate']) ? stripslashes($rowEvaluation['evaluatorSignatureDate']) : '';
        if ($evaluatorSignatureDate != '' && $evaluatorSignatureDate != '0000-00-00 00:00:00') {
            $evaluatorSignatureDate = converFromServerTimeZone($evaluatorSignatureDate, $TimeZone);
            $evaluatorDate = $evaluatorSignatureDate = date("m/d/Y", strtotime($evaluatorSignatureDate));
        } else
            $evaluatorDate = "";

        $studentSignatureDate = isset($rowEvaluation['studentSignatureDate']) ? stripslashes($rowEvaluation['studentSignatureDate']) : '';
        if ($studentSignatureDate != '' && $studentSignatureDate != '0000-00-00 00:00:00') {
            $studentSignatureDate = converFromServerTimeZone($studentSignatureDate, $TimeZone);
            $studentDate = $studentSignatureDate = date("m/d/Y", strtotime($studentSignatureDate));
        } else
            $studentDate = "";

        $preceptorSignatureDate = isset($rowEvaluation['preceptorSignatureDate']) ? stripslashes($rowEvaluation['preceptorSignatureDate']) : '';
        if ($preceptorSignatureDate != '' && $preceptorSignatureDate != '0000-00-00 00:00:00') {
            $preceptorSignatureDate = converFromServerTimeZone($preceptorSignatureDate, $TimeZone);
            $preceptorSignatureDate = date("m/d/Y", strtotime($preceptorSignatureDate));
        } else
            $preceptorSignatureDate = "";

        $radioType = isset($rowEvaluation['type']) ? stripslashes($rowEvaluation['type']) : '';
        $radioRepeat = isset($rowEvaluation['repeat']) ? stripslashes($rowEvaluation['repeat']) : '';
        $radioRepeatReason = isset($rowEvaluation['repeatReason']) ? stripslashes($rowEvaluation['repeatReason']) : '';
        $totalScore = isset($rowEvaluation['totalScore']) ? stripslashes($rowEvaluation['totalScore']) : '';
        $radioResult = isset($rowEvaluation['result']) ? stripslashes($rowEvaluation['result']) : '';
        $reason = isset($rowEvaluation['reason']) ? stripslashes($rowEvaluation['reason']) : '';
        $comment = isset($rowEvaluation['comment']) ? stripslashes($rowEvaluation['comment']) : '';
        $schoolComment = isset($rowEvaluation['schoolComment']) ? stripslashes($rowEvaluation['schoolComment']) : '';
    }
} else {
    $schoolId = $currentSchoolId;
    $page_title = "Add Clinical Evaluation";
    $bedCrumTitle = 'Add';
}

//----------------------------//
//Get Clinician Names
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, $clinicalRotationId);
unset($objClinician);

///----------------------//

$clinicianSection = $objClinicalEval->GetSectionsForFinalComp($currentSchoolId,  $schoolTopicId);
if ($clinicianSection != '') {
    $totalSection = mysqli_num_rows($clinicianSection);
}

//Get Hospital Site
// $objHospitalSite = new clsHospitalSite();
// $hospitalSite = $objHospitalSite->GetAllHospitalSite($currentSchoolId);
// unset($objHospitalSite);

//Get Rotation Name

$RotationName = $objRotation->GetrotationDetails($clinicalRotationId, $currentSchoolId);

$rotationtitle = $RotationName['title'];
$endDate = $RotationName['endDate'];

//For Schedule
$isSchedule = $RotationName['isSchedule'];
$parentsRotationId = $RotationName['parentRotationId'];
// if ($isSchedule)
//     $endDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentsRotationId);
// //-----------------
// $endDate = date('m/d/Y', strtotime($endDate));

$view = isset($_GET['view']) ? $_GET['view'] : '';
$bedCrumTitle = ($view) ? 'View' : $bedCrumTitle;
$Type = isset($_GET['type']) ? ($_GET['type']) : '';

//For Student
$objStudent = new clsStudent();
$totalstudent = 0;
$rowsstudent = $objStudent->GetStudentsByRotation($currentSchoolId, $clinicalRotationId);
if ($rowsstudent != '') {
    $totalstudent = mysqli_num_rows($rowsstudent);
}
// unset($objStudent);
unset($objDB);

//rotation
$objrotation = new clsRotation();
$totalRotation = 0;
$rotation = $objrotation->GetRotationForRad($currentSchoolId);


if ($rotation != '') {
    $totalRotation = mysqli_num_rows($rotation);
}

//For checkoff Type
$objDB = new clsDB();
$checkoffType = $objDB->GetSingleColumnValueFromTable('schools', 'checkoffType', 'schoolId', $currentSchoolId);
unset($objDB);

//For CheckOff Topic
$objCheckoffTopicMaster = new clsCheckoffTopicMaster();
$rowsCheckoffTopics = $objCheckoffTopicMaster->GetAllCheckoffTopic($currentSchoolId, $checkoffType);
$totalCount = 0;
if ($rowsCheckoffTopics != '') {
    $totalCount = mysqli_num_rows($rowsCheckoffTopics);
}
$preceptorFullName = '';
$preceptorFirstName = '';
if ($preceptorId > 0) {
    $objExternalPreceptors = new clsExternalPreceptors();
    $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
    $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
    $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
    $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
}
$preceptorsignature = $preceptorFullName;

$studentfullname = $objStudent ->GetStudentNameById($studentId);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($page_title); ?></title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    <style type="text/css">
        .some-class {
            float: left;
            clear: none;
        }

        .dropdown-width>.select2-container--default {
            width: 200px !important;
            margin-top: 5px;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #2e3192; */
            /* color: #fff; */
            color: #555;
            background: #f6f6ff;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f6ff !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        /* Style for the collapsible content */
        .panel-collapse {
            display: none;
            /* Hidden by default */
            /* padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9; */
        }

        /* Style for the collapsible button */
        .collapsible {
            /* background-color: #3498db;
            color: white;
            padding: 10px;
            border: none; */
            width: 100%;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            /* Align content horizontally */
        }

        .panel-heading {
            width: 100%;
        }

        /* Style for the arrow icons */
        .arrow-icon {
            transition: transform 0.3s;
        }

        .collapsible.expanded .arrow-icon i {
            transform: rotate(180deg);
        }

        .panel-collapse .panel-body {
            padding: 0 15px;
        }

        .border-right {
            border-right: 1px solid #dcdcdc;
            padding: 15px;
            margin: 0 !important;
        }

        .last-col {
            padding: 15px;
            margin: 0 !important;
        }

        .desktop-d-flex {
            display: flex;
        }

        .border-right b {
            line-height: 1.8;
        }

        .some-class p {
            margin: 0 !important;
        }


        /* .border-right {
            border-right: 1px solid #d9d9d9;
        } */

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            .border-right {
                border-right: none
            }

            .desktop-d-flex {
                display: block;
            }
        }

        @media screen and (max-width: 500px) {}
    </style>

</head>

<body>
    <?php if ($IsMobile == 0) { ?>

        <?php include('includes/header.php');
        ?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <!-- <li><a href="rotations.html">Rotation</a></li>
                        <li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                        <li><a href="clinicalEvaluationList.html?clinicalRotationId=<?php echo EncodeQueryData($clinicalRotationId); ?>"><?php if ($checkoffId) {
                                                                                                                                                echo 'Final Comp';
                                                                                                                                            } else {
                                                                                                                                                echo 'Clinical Evaluation';
                                                                                                                                            } ?></a></li> -->
                        <?php if ($isActiveCheckoff == 1) {
                            if ($rotationId > 0) { ?>
                                <li><a href="rotations.html">Rotation</a></li>
                                <li><a href="checkoff.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&Type=R">Final Comp</a></li>
                            <?php } else { ?>
                                <li><a href="clinical.html">Clinical</a></li>
                                <li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                                <li><a href="checkoff.html?studentId=<?php echo EncodeQueryData($studentId); ?>">Final Comp</a></li>
                            <?php }
                        } else {
                            if ($rotationId > 0 && $Type == 'R') { ?>
                                <li><a href="rotations.html">Rotation</a></li>
                                <li><a href="checkoffs.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&Type=R">Final Comp</a></li>
                            <?php } elseif ($rotationId > 0 && $Type == 'C') { ?>
                                <li><a href="studentCheckoffList.html">Student List</a></li>
                                <li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                                <li><a href="checkoffs.html?studentId=<?php echo EncodeQueryData($studentId); ?>&Type=C">Final Comp</a></li>
                            <?php } else { ?>
                                <li><a href="clinical.html">Clinical</a></li>
                                <li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                                <li><a href="checkoffs.html?studentId=<?php echo EncodeQueryData($studentId); ?>">Final Comp</a></li>
                        <?php }
                        }   ?>

                        <li class="active"><?php echo ($bedCrumTitle); ?></li>
                    </ol>
                </div>

            </div>
        </div>
    <?php  } else  ?>

    <div class="container">

        <form id="frmevaluation" data-parsley-validate class="form-horizontal" method="POST" action="clinicalEvalsubmit.html?clinicalEvaluationMasterId=<?php echo (EncodeQueryData($clinicalEvaluationMasterId)); ?>
																									&clinicalRotationId=<?php echo (EncodeQueryData($clinicalRotationId)); ?>">

            <div class="row">
                <!-- Mobile redirect -->
                <input type="hidden" name="IsMobile" id="IsMobile" value="<?php echo $IsMobile; ?>">
                <input type="hidden" name="checkoffId" id="checkoffId" value="<?php echo $checkoffId; ?>">
                <input type="hidden" name="preceptorId" id="preceptorId" value="<?php echo $preceptorId; ?>">



                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="evaluationDate">Evaluation Date</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='evaluationDate' style="position: relative;">

                                <input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date evaluationDate " value="<?php echo ($evaluationDate); ?>" required data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cboTerm">Term</label>
                        <div class="col-md-12">
                            <select id="cboTerm" name="cboTerm" class="form-control input-md required-input select2_single disabledClass" required onChange="getstudent(this.value);">
                                <option value="" selected>Select</option>
                                <?php
                                if ($rotation != "") {
                                    while ($row = mysqli_fetch_assoc($rotation)) {
                                        $selRotationId  = $row['rotationId'];
                                        $name  = stripslashes($row['title']);
                                ?>
                                        <option value="<?php echo ($selRotationId); ?>" <?php if ($clinicalRotationId == $selRotationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-cboTerm"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="Adult">Evaluator</label>
                        <div class="col-md-12">
                            <select id="cboclinician" name="cboclinician" class="form-control input-md required-input select2_single disabledClass" required data-parsley-errors-container="#cboclinician-err">
                                <option value="" selected>Select</option>
                                <?php
                                if ($Clinician != "") {
                                    while ($row = mysqli_fetch_assoc($Clinician)) {
                                        $selClinicianId  = $row['clinicianId'];
                                        $firstname  = stripslashes($row['firstName']);
                                        $lastname  = stripslashes($row['lastName']);

                                        $name = $firstname . ' ' . $lastname;


                                ?>
                                        <option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

                                <?php

                                    }
                                }

                                ?>
                            </select>
                            <div id="cboclinician-err"></div>
                        </div>
                    </div>

                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="evaluatorDate">Evaluator Signature Date</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='evaluatorDate' style="position: relative;">

                                <input type='text' name="evaluatorDate" id="evaluatorDate" class="form-control input-md required-input rotation_date evaluatorDate" value="<?php echo ($evaluatorDate); ?>" required data-parsley-errors-container="#error-evaluatorDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-evaluatorDate"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">

                <div class="col-md-6">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cboStudent">Student</label>
                        <div class="col-md-12">
                            <select id="cboStudent" name="cboStudent" class="form-control input-md required-input select2_single" required data-parsley-errors-container="#error-cboStudent">
                                <option value="" selected>Select</option>
                                <?php
                                if ($totalstudent > 0) {

                                    while ($row = mysqli_fetch_array($rowsstudent)) {
                                        $selstudentId = $row['studentId'];
                                        $firstName = $row['firstName'];
                                        $lastName = $row['lastName'];
                                        $rank = $row['rank'];
                                        $fullName = $firstName . ' ' . $lastName;

                                ?>
                                        <option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($fullName); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-cboStudent"></div>
                        </div>
                    </div>

                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="studentDate">Student Signature Date</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='studentDate' style="position: relative;">

                                <input type='text' name="studentDate" id="studentDate" class="form-control input-md required-input rotation_date studentDate " value="<?php echo ($studentDate); ?>" required data-parsley-errors-container="#error-studentDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-studentDate"></div>
                        </div>
                    </div>
                </div>

            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="preceptorsignature">Technologist Signature</label>
                        <div class="col-md-12">
                            <div id='preceptorsignature'>
                                <input type='text' name="preceptorsignature" id="preceptorsignature" class="form-control input-md rotation_date disableClass" value="<?php echo ($preceptorsignature);
                                                                                                                                                                        ?>" data-parsley-errors-container="#error-preceptorsignature" />
                            </div>
                            <div id="error-preceptorsignature"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="preceptorSignatureDate">Date Of Technologist Signature</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='preceptorSignatureDate' style="position: relative;">

                                <input type='text' name="preceptorSignatureDate" id="preceptorSignatureDate" class="form-control input-md  rotation_date preceptorSignatureDate " value="<?php echo ($preceptorSignatureDate); ?>" data-parsley-errors-container="#error-preceptorSignatureDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-preceptorSignatureDate"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="exam">EXAM</label>

                        <div class="col-md-12">
                            <select id="exam" name="exam" class="form-control input-md required-input select2_single " required data-parsley-errors-container="#exam-err">
                                <option value="" selected>Select</option>
                                <?php
                                if ($rowsCheckoffTopics != "") {
                                    while ($row = mysqli_fetch_assoc($rowsCheckoffTopics)) {
                                        $selSchoolTopicId  = $row['schoolTopicId'];
                                        $schooltitle  = stripslashes($row['schooltitle']);
                                        $checkoffTitleId  = stripslashes($row['checkoffTitleId']);

                                        $title = $checkoffTitleId . ' - ' . $schooltitle;


                                ?>
                                        <option value="<?php echo ($selSchoolTopicId); ?>" <?php if ($schoolTopicId == $selSchoolTopicId) { ?> selected="true" <?php } ?>><?php echo ($title); ?></option>

                                <?php

                                    }
                                }

                                ?>
                            </select>
                            <div id="exam-err"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="accession">NUMBER</label>
                        <div class="col-md-12">
                            <div id='accession'>
                                <input type='text' name="accession" id="accession" class="form-control input-md rotation_date disableClass" value="<?php echo ($accession);
                                                                                                                                                    ?>" data-parsley-errors-container="#error-accession" />
                            </div>
                            <div id="error-accession"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="col-md-12 col-sm-12 col-xs-12">

                            <label class="control-label mr-15" for="floorCough">TYPE OF EVALUATION</label>&nbsp;&nbsp;
                            <label class="radio-inline control-label"><input type="radio" class=" input-md" value="0" name="radioType" <?php echo ($radioType == '0') ? "checked" : ""; ?>>Competency</label>
                            <label class="radio-inline control-label"><input type="radio" class="input-md" value="1" name="radioType" <?php echo ($radioType == '1') ? "checked" : ""; ?>> Simulation</label>

                            </label>
                        </div>
                    </div>
                </div>
            </div> -->
            <!-- 1st SECTION div start -->
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="instructions:"></label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class="panel panel-default border-12  mt-10 mb-10">
                                <div class="panel-body">
                                    <p><b>This competency evaluation form has been designed for evaluating a maximum of three positions/projections per radiographic procedure (i.e., Foot: AP, oblique, lateral). The evaluator MUST OBSERVE the exam and will mark each area with a check to indicate that point value. The student is evaluated according to how well he/she meets the objectives for each position/projection. See reverse side for examples.</br>
                                            PLEASE DO NOT COMPARE A STUDENT'S PERFORMANCE WITH THAT OF A TECHNOLOGIST.</br>
                                            *A student is not permitted to get any written or oral assistance when attempting to comp/simulation*
                                        </b></p>
                                    <p> <b>Point Scale: </b><br>
                                        <b>0 = Performance is unacceptable. Evaluation will be considered “observation/attempt.” Will not count toward
                                            competency requirements. <br>
                                            1 = Performance meets some objectives. Improvement needed. <br>
                                            2 = Performance meets objectives, (i.e., according to student’s clinical level).</b>

                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="instructions:"></label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class="panel-group" id="posts">
                                <?php
                                $objProjection = new clsProjection();

                                while ($row = mysqli_fetch_array($clinicianSection)) {
                                    // print_r($row);exit;
                                    $sectionMasterId = $row['clinicalSectionId'];
                                    $title = $row['title'];
                                    $isTechnologist = $row['isTechnologist'];
                                    $sectionDiv = ($isTechnologist) ? 'technologistDiv' : 'evaluatorDiv';
                                    $projectionClass = ($isTechnologist) ? 'projection' : '';
                                ?>
                                    <div class="panel panel-default <?php echo $sectionDiv; ?> ">
                                        <a class="collapsible" style="color: #000; text-decoration: none;" href="#<?php echo $sectionMasterId; ?>" data-toggle="collapse" data-parent="#posts" id="collapse-link">
                                            <div class="panel-heading" style="display: flex; justify-content: space-between; align-items: center;">
                                                <h4 class="panel-title">
                                                    <?php echo  $title; ?>
                                                </h4>
                                                <span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
                                            </div>
                                        </a>
                                        <div id="<?php echo $sectionMasterId; ?>" class="panel-collapse collapse">
                                            <div class="panel-body row mt-15">
                                                <div class="col-md-3 border-right" style="margin-top: 20px;margin-bottom: 10px;">
                                                    <div class="col-md-12 p-0">
                                                        <b> Question </b>
                                                    </div>
                                                    <div class="col-md-12 dropdown-width" style="visibility: hidden;">
                                                        <?php
                                                        $projectionAId = $objProjection->GetprojectionIdByCompId($clinicalEvaluationMasterId, 'A', $isTechnologist);
                                                        ?>
                                                        <select id="hidden" name="hidden" class="form-control input-md required-input select2_single " data-parsley-errors-container="#exam-err">
                                                            <option value="1" selected>Select</option>
                                                        </select>
                                                        <div id="exam-err"></div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 border-right" style="margin-top: 20px;margin-bottom: 10px;">
                                                    <div class="col-md-12">
                                                        <b>A Position/Projection</b>
                                                    </div>
                                                    <div class="col-md-12 dropdown-width">
                                                        <?php
                                                        $projectionAId = $objProjection->GetprojectionIdByCompId($clinicalEvaluationMasterId, 'A', $isTechnologist);
                                                        ?>
                                                        <select id="projection_A_<?php echo $isTechnologist; ?>" name="projection_A_<?php echo $isTechnologist; ?>" class="form-control input-md required-input select2_single <?php echo $projectionClass; ?>" data-parsley-errors-container="#exam-projection_A_<?php echo $isTechnologist; ?>">
                                                            <option value="" selected>Select</option>
                                                            <?php
                                                            $rsProjectionList = $objProjection->GetAllProjectionMasterForA();
                                                            $projectionListCount = ($rsProjectionList != '') ? mysqli_num_rows($rsProjectionList) : 0;

                                                            if ($projectionListCount) {
                                                                while ($row = mysqli_fetch_assoc($rsProjectionList)) {
                                                                    $selprojectionId  = $row['projectionId'];
                                                                    $title  = stripslashes($row['title']);



                                                            ?>
                                                                    <option value="<?php echo ($selprojectionId); ?>" <?php if ($projectionAId == $selprojectionId) { ?> selected="true" <?php } ?>><?php echo ($title); ?></option>

                                                            <?php

                                                                }
                                                            }

                                                            ?>
                                                        </select>
                                                        <div id="exam-projection_A_<?php echo $isTechnologist; ?>"></div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3 border-right" style="margin-top: 20px;margin-bottom: 10px;">
                                                    <div class="col-md-12">
                                                        <b>B Position/Projection</b>
                                                    </div>
                                                    <div class="col-md-12 dropdown-width">
                                                        <?php
                                                        $projectionBId = $objProjection->GetprojectionIdByCompId($clinicalEvaluationMasterId, 'B', $isTechnologist);
                                                        ?>
                                                        <select id="projection_B_<?php echo $isTechnologist; ?>" name="projection_B_<?php echo $isTechnologist; ?>" class="form-control input-md required-input select2_single <?php echo $projectionClass; ?>" data-parsley-errors-container="#exam-projection_B_<?php echo $isTechnologist; ?>">
                                                            <option value="" selected>Select</option>
                                                            <?php
                                                            $rsProjectionList = $objProjection->GetAllProjectionMaster();
                                                            $projectionListCount = ($rsProjectionList != '') ? mysqli_num_rows($rsProjectionList) : 0;

                                                            if ($projectionListCount) {
                                                                while ($row = mysqli_fetch_assoc($rsProjectionList)) {
                                                                    $selprojectionId  = $row['projectionId'];
                                                                    $title  = stripslashes($row['title']);



                                                            ?>
                                                                    <option value="<?php echo ($selprojectionId); ?>" <?php if ($projectionBId == $selprojectionId) { ?> selected="true" <?php } ?>><?php echo ($title); ?></option>

                                                            <?php

                                                                }
                                                            }

                                                            ?>
                                                        </select>
                                                        <div id="exam-projection_B_<?php echo $isTechnologist; ?>"></div>
                                                    </div>
                                                </div>
                                                <div class="col-md-3" style="margin-top: 20px;margin-bottom: 10px;">
                                                    <div class="col-md-12">
                                                        <b>C Position/Projection</b>
                                                    </div>
                                                    <div class="col-md-12 dropdown-width">
                                                        <?php
                                                        $projectionCId = $objProjection->GetprojectionIdByCompId($clinicalEvaluationMasterId, 'C', $isTechnologist);
                                                        ?>
                                                        <select id="projection_C_<?php echo $isTechnologist; ?>" name="projection_C_<?php echo $isTechnologist; ?>" class="form-control input-md required-input select2_single <?php echo $projectionClass; ?>" data-parsley-errors-container="#exam-projection_C_<?php echo $isTechnologist; ?>">
                                                            <option value="" selected>Select</option>
                                                            <?php

                                                            $rsProjectionList = $objProjection->GetAllProjectionMaster();
                                                            $projectionListCount = ($rsProjectionList != '') ? mysqli_num_rows($rsProjectionList) : 0;

                                                            if ($projectionListCount) {
                                                                while ($row = mysqli_fetch_assoc($rsProjectionList)) {
                                                                    $selprojectionId  = $row['projectionId'];
                                                                    $title  = stripslashes($row['title']);



                                                            ?>
                                                                    <option value="<?php echo ($selprojectionId); ?>" <?php if ($projectionCId == $selprojectionId) { ?> selected="true" <?php } ?>><?php echo ($title); ?></option>

                                                            <?php

                                                                }
                                                            }

                                                            ?>
                                                        </select>
                                                        <div id="exam-projection_C_<?php echo $isTechnologist; ?>"></div>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php
                                            // for question
                                            $totalPerformanceevaluation = 0;
                                            $evaluationquestion = $objClinicalEval->GetAllEvaluationQuestionMaster($currentSchoolId, $sectionMasterId);

                                            if ($evaluationquestion != '')
                                                $totalPerformanceevaluation = mysqli_num_rows($evaluationquestion);

                                            if ($totalPerformanceevaluation > 0) {
                                                while ($rowQuestion = mysqli_fetch_array($evaluationquestion)) {
                                                    // print_r($rowQuestion);exit;
                                                    // if (isset($_GET['clinicalEvaluationMasterId']))
                                                    //     $clinicalEvaluationMasterId = DecodeQueryData($_GET['clinicalEvaluationMasterId']);
                                                    // else
                                                    //     $clinicalEvaluationMasterId = 0;

                                                    $clinicalQuestionId = $rowQuestion['clinicalQuestionId'];
                                                    $questionText = $rowQuestion['questionText'];
                                                    $clinicalQuestionType = $rowQuestion['clinicalQuestionType'];
                                                    // $qhtml = GetClinicalEvaluationQuestionHtml($clinicalQuestionId, $clinicalQuestionType, $clinicalEvaluationMasterId, $currentSchoolId);

                                                    $questionComment = '';

                                                    // if ($clinicalEvaluationMasterId > 0) {
                                                    //     if (!isset($objDB) || !is_object($objDB))
                                                    //         

                                                    //     $questionComment = $objDB->GetSingleColumnValueFromTable('performanceevaluationdetails', 'comment', 'evaluationMasterId', $clinicalEvaluationMasterId, 'schoolEvaluationQuestionId', $performanceQuestionId);
                                                    //     unset($objDB);
                                                    // }


                                            ?>
                                                    <div class="panel-body isAllRadioButton">
                                                        <?php if ($clinicalQuestionType == 2) { ?>
                                                            <div class="row d-flex">
                                                                <div class="col-md-3 border-right">
                                                                    <b> <?php echo ($questionText); ?> </b>
                                                                </div>
                                                                <div class="col-md-3 border-right">
                                                                    <?php
                                                                    $optionsType = "A";
                                                                    $qhtml = GetClinicalEvaluationQuestionHtml($clinicalQuestionId, $clinicalQuestionType, $clinicalEvaluationMasterId, $currentSchoolId, $optionsType);

                                                                    echo $qhtml;
                                                                    ?>
                                                                </div>
                                                                <div class="col-md-3 border-right">
                                                                    <?php
                                                                    $optionsType = "B";
                                                                    $qhtml = GetClinicalEvaluationQuestionHtml($clinicalQuestionId, $clinicalQuestionType, $clinicalEvaluationMasterId, $currentSchoolId, $optionsType);

                                                                    echo $qhtml;
                                                                    ?>
                                                                </div>
                                                                <div class="col-md-3 last-col">
                                                                    <?php
                                                                    $optionsType = "C";
                                                                    $qhtml = GetClinicalEvaluationQuestionHtml($clinicalQuestionId, $clinicalQuestionType, $clinicalEvaluationMasterId, $currentSchoolId, $optionsType);

                                                                    echo $qhtml;
                                                                    ?>
                                                                </div>
                                                                <?php //if ($performanceQuestionType == 2) { 
                                                                ?>
                                                            </div>
                                                        <?php } ?>
                                                        <!-- <br /><br />
                                                        <b> Comment: </b><br /><br />

                                                        <textarea name="textarea_<?php echo $performanceQuestionId; ?>" id="textarea_<?php echo $performanceQuestionId; ?>" rows="4" class="form-control"><?php echo $questionComment; ?></textarea> -->
                                                        <?php //} 
                                                        ?>
                                                    </div>
                                            <?php
                                                }
                                            }

                                            ?>
                                        </div>

                                    </div>
                                <?php
                                }

                                ?>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <br>
            <div class="row hideForEvaluator">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="col-md-12 col-sm-12 col-xs-12">

                            <label class="control-label mr-15" for="floorCough">Were repeats needed? (Check one)</label>&nbsp;&nbsp;
                            <label class="radio-inline control-label"><input type="radio" class=" input-md radioRepeat" value="0" name="radioRepeat" <?php echo ($radioRepeat == '0') ? "checked" : ""; ?>>No</label>
                            <label class="radio-inline control-label"><input type="radio" class="input-md radioRepeat" value="1" name="radioRepeat" <?php echo ($radioRepeat == '1') ? "checked" : ""; ?>> Yes</label>

                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row radioRepeatReasonDiv hideForEvaluator">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="col-md-12 col-sm-12 col-xs-12">

                            <label class="control-label mr-15" for="floorCough">Was the repeat reason</label>&nbsp;&nbsp;
                            <label class="radio-inline control-label"><input type="radio" class=" input-md radioRepeatReason" value="0" name="radioRepeatReason" <?php echo ($radioRepeatReason == '0') ? "checked" : ""; ?>>Preventable</label>
                            <label class="radio-inline control-label"><input type="radio" class="input-md radioRepeatReason" value="1" name="radioRepeatReason" <?php echo ($radioRepeatReason == '1') ? "checked" : ""; ?>> Not Preventable</label>
                            <!-- <br><label class="control-label mr-15" for="floorCough"> <small>Note: Preventable repeats = automatic failure</small> </label> -->

                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- <br> -->
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="col-md-12 col-sm-12 col-xs-12">

                            <label class="control-label mr-15" for="floorCough">Result</label>&nbsp;&nbsp;
                            <label class="radio-inline control-label"><input type="radio" class=" input-md radioResult" value="0" name="radioResult" <?php echo ($radioResult == '0') ? "checked" : ""; ?>>Pass</label>
                            <label class="radio-inline control-label"><input type="radio" class="input-md radioResult" value="1" name="radioResult" <?php echo ($radioResult == '1') ? "checked" : ""; ?>> Fail</label>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row reasonDiv hideForTechnologist">
                <div class="col-md-12 col-sm-12 col-xs-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="reason">Reason</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div id="reason" style="width: 100%;">
                                <textarea name="reason" id="textarea" class="form-control input-md clstextarea disableClass reason" rows="4" cols="100"><?php echo ($reason); ?></textarea>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-sm-12 col-xs-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="comment">Technologist Comments</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div id="comment" style="width: 100%;">
                                <textarea name="comment" id="textarea" class="form-control input-md clstextarea disableClass" rows="4" cols="100"><?php echo ($comment); ?></textarea>

                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-12 col-sm-12 col-xs-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="comment">School Comments</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div id="comment" style="width: 100%;">
                                <textarea name="schoolComment" id="textarea" class="form-control input-md clstextarea schoolComment" rows="4" cols="100"><?php echo ($schoolComment); ?></textarea>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <br>
            <div class="row m-0">
                <label class="col-md-12 control-label" for="">Technologist Score</label>
                <!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
                <div class="col-md-12 p-0">

                    <input type="hidden" name="technologistScore" class="technologistScore" value="0">
                    <input type="hidden" name="evaluatorScore" class="evaluatorScore" value="0">
                    <input type="hidden" name="totalScore" class="totalScore" value="0">

                    <div class="grid-layout">

                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										/ background: #EEF5FF; /
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/trachestomy-care.png" alt="briefcase">
                                </div>
                                <p class="card-title">
                                    A Position/Projection Score
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="technologistPoints_A">0</span><span class="card-count-span">&nbsp;/&nbsp;<span id="techASpan" class="techPositionSpan">12</span></span>
                                </p>
                            </div>
                        </div>

                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										/ background: #EEF5FF; /
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/HFOV.png" alt="briefcase">
                                </div>
                                <p class="card-title">
                                    B Position/Projection Score
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="technologistPoints_B">0</span><span class="card-count-span">&nbsp;/&nbsp;<span id="techBSpan" class="techPositionSpan">12</span></span>
                                </p>
                            </div>
                        </div>

                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										/ background: #EEF5FF; /
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/person.png" alt="teacher">
                                </div>
                                <p class="card-title">
                                    C Position/Projection Score
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="technologistPoints_C">0</span><span class="card-count-span">&nbsp;/&nbsp;<span id="techCSpan" class="techPositionSpan">12</span></span>
                                </p>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
            <br>
            <div class="row m-0">
                <label class="col-md-12 control-label" for="">Evaluator Score</label>
                <!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
                <div class="col-md-12 p-0">

                    <div class="grid-layout">

                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										/ background: #EEF5FF; /
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/trachestomy-care.png" alt="briefcase">
                                </div>
                                <p class="card-title">
                                    A Position/Projection Score
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="evaluatorPoints_A">0</span><span class="card-count-span">&nbsp;/&nbsp;<span id="evalASpan" class="evalPositionSpan">10</span></span>
                                </p>
                            </div>
                        </div>

                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										/ background: #EEF5FF; /
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/HFOV.png" alt="briefcase">
                                </div>
                                <p class="card-title">
                                    B Position/Projection Score
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="evaluatorPoints_B">0</span><span class="card-count-span">&nbsp;/&nbsp;<span id="evalBSpan" class="evalPositionSpan">10</span></span>
                                </p>
                            </div>
                        </div>

                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										/ background: #EEF5FF; /
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/person.png" alt="teacher">
                                </div>
                                <p class="card-title">
                                    C Position/Projection Score
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="evaluatorPoints_C">0</span><span class="card-count-span">&nbsp;/&nbsp;<span id="evalCSpan" class="evalPositionSpan">10</span></span>
                                </p>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <br>
            <div class="row m-0">
                <label class="col-md-12 control-label" for="">Totals</label>
                <!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
                <div class="col-md-12 p-0">

                    <!-- <input type="hidden" name="totalPoints_A" class="totalPoints_A" value="0">
                    <input type="hidden" name="totalPoints_B" class="totalPoints_B" value="0">
                    <input type="hidden" name="totalPoints_C" class="totalPoints_C" value="0"> -->

                    <div class="grid-layout">




                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										/ background: #EEF5FF; /
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/trachestomy-care.png" alt="briefcase">
                                </div>
                                <p class="card-title">
                                    Technologist Score
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="totalTechnologistScore">0</span><span class="card-count-span">&nbsp;/&nbsp;<span id="techScoreSpan">36</span></span>
                                </p>
                            </div>
                        </div>

                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										/ background: #EEF5FF; /
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/HFOV.png" alt="briefcase">
                                </div>
                                <p class="card-title">
                                    Evaluator Score
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="totalEvaluatorScore">0</span><span class="card-count-span">&nbsp;/&nbsp;<span id="evalScoreSpan">30</span></span>
                                </p>
                            </div>
                        </div>

                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										/ background: #EEF5FF; /
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/person.png" alt="teacher">
                                </div>
                                <p class="card-title">
                                    Total Score
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="totalScore">0</span><span class="card-count-span">&nbsp;/&nbsp;<span id="totalScoreSpan">66</span></span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" style="margin: 0;">
                <div class="col-md-12">
                    <div class="form-group">
                        <!-- <label class="col-md-2 control-label"></label> -->
                        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                            <?php //if ((strtotime($currentDate)) < (strtotime($endDate))) {
                            if ($view == '') {
                            ?>

                                <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                            <?php }
                            //} 
                            ?>
                            <?php if ($IsMobile) { ?>
                                <a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=ciEvaluation" class="btn btn-default">Cancel</a>
                            <?php } else if ($checkoffId) { ?>
                                <a type="button" href="checkoffs.html?studentId=<?php echo EncodeQueryData($studentId); ?>" class="btn btn-default">Cancel</a>
                            <?php } else { ?>
                                <a type="button" href="clinicalEvaluationList.html?clinicalRotationId=<?php echo EncodeQueryData($clinicalRotationId); ?>" class="btn btn-default">Cancel</a>
                            <?php }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </form>


    </div>

    <?php include('includes/footer.php'); ?>

    <?php //print_r($_SESSION); 
    ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>




    <script type="text/javascript">
        $(window).load(function() {
            // $(".isAllRadioButton").trigger('click');

            $('#frmevaluation').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    $('#frmevaluation').find(':disabled').removeAttr('disabled');
                    return true; // Don't submit form for this demo
                });

            $('#evaluationDate').datetimepicker({
                format: 'MM/DD/YYYY',
                defaultDate: moment()
            });
            $('#studentDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#evaluatorDate').datetimepicker({
                format: 'MM/DD/YYYY',
                defaultDate: moment()
            });

            $('#preceptorsignitureDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });

            //for searching dropdown
            $(".select2_single").select2();
            $('#select2-cbohospitalsites-container').addClass('required-select2');
            $('#select2-cboclinician-container').addClass('required-select2');
            $('#select2-cboStudent-container').addClass('required-select2');
            $('#select2-weeks-container').addClass('required-select2');
            $('#select2-cboTerm-container').addClass('required-select2');

            // $("input[type=radio]").attr('disabled', true);
            $(".disabledClass").attr('disabled', true);

            ['A', 'B', 'C'].forEach(CalculateTotalPoints);
            // disable the form
            $('#frmevaluation input, #frmevaluation textarea, #frmevaluation select').prop('disabled', true);
            $(".schoolComment").prop('disabled', false);
            $("#cboclinician").prop('required', false);
            $(".studentDate").prop('required', false);

            var view = '<?php echo $view; ?>';
            if (view == 'V') {
                $('#frmevaluation input, #frmevaluation textarea, #frmevaluation select').prop('disabled', true);
            }

            var projectionBId = '<?php echo $projectionBId; ?>';
            var projectionCId = '<?php echo $projectionCId; ?>';

            if (projectionBId == '21') {
                $('#techBSpan,#evalBSpan').text('0');
            } else {
                $('#techBSpan').text('12');
                $('#evalBSpan').text('10');
            }

            if (projectionCId == '21') {
                $('#techCSpan,#evalCSpan').text('0');
            } else {
                $('#techCSpan').text('12');
                $('#evalCSpan').text('10');
            }
            calculateTotalScore();
        });

        $(document).ready(function() {

            $('.radioButton_A').on('click', function() {
                var selectedValues = [];
                CalculateTotalPoints('A')

            });
            $('.radioButton_B').on('click', function() {
                var selectedValues = [];
                CalculateTotalPoints('B')

            });
            $('.radioButton_C').on('click', function() {
                var selectedValues = [];
                CalculateTotalPoints('C')

            });

            $('.radioRepeat:checked').trigger('click');
            $('.radioResult:checked').trigger('click');

        });

        // function CalculateTotalPoints($type) {
        //     var sumCheckedButton = 0;
        //     var totalScore = 0;
        //     $('.radioButton_' + $type + ':checked').each(function() {
        //         var checkboxName = $.trim($(this).attr('name'));
        //         var checkboxValue = $.trim($(this).parent().text());

        //         sumCheckedButton += parseInt(($.trim(checkboxValue)));
        //     });
        //     $("#totalPoints_" + $type).text(sumCheckedButton.toFixed(0));
        //     $(".totalPoints_" + $type).val(sumCheckedButton.toFixed(0));
        //     var totalScore = parseInt($(".totalPoints_A").val()) + parseInt($(".totalPoints_B").val()) + parseInt($(".totalPoints_C").val());
        //     $("#totalScore").text(totalScore.toFixed(0));
        //     // console.log('totalScore'+totalScore);
        // }
        //Hide show Repeat Reason div
        $('.radioRepeat').on('click', function() {
            var radioRepeatValue = $('.radioRepeat:checked').val();
            if (radioRepeatValue == 1) {
                $('.radioRepeatReasonDiv').show();
            } else {
                $('.radioRepeatReasonDiv').hide();
            }
        });
        //Hide show reson div
        $('.radioResult').on('click', function() {
            var radioResultValue = $('.radioResult:checked').val();
            if (radioResultValue == 1) {
                $('.reasonDiv').show();
            } else {
                $('.reasonDiv').hide();
            }
        });

        function CalculateTotalPoints(type) {
            var sumCheckedButton = 0;
            var totalScore = 0;
            $('.radioButton_' + type + ':checked').each(function() {
                var checkboxName = $.trim($(this).attr('name'));
                var checkboxValue = $.trim($(this).parent().text());

                sumCheckedButton += parseInt(($.trim(checkboxValue)));
            });
            $("#totalPoints_" + type).text(sumCheckedButton.toFixed(0));
            $(".totalPoints_" + type).val(sumCheckedButton.toFixed(0));

            var sumCheckedButton = 0;
            var totalScore = 0;
            $('.evaluatorDiv .radioButton_' + type + ':checked').each(function() {
                var checkboxName = $.trim($(this).attr('name'));
                var checkboxValue = $.trim($(this).parent().text());

                sumCheckedButton += parseInt(($.trim(checkboxValue)));
            });
            $("#evaluatorPoints_" + type).text(sumCheckedButton.toFixed(0));

            var sumCheckedButton = 0;
            var totalScore = 0;
            $('.technologistDiv .radioButton_' + type + ':checked').each(function() {
                var checkboxName = $.trim($(this).attr('name'));
                var checkboxValue = $.trim($(this).parent().text());

                sumCheckedButton += parseInt(($.trim(checkboxValue)));
            });

            $("#technologistPoints_" + type).text(sumCheckedButton.toFixed(0));

            var totalTechnologistScore = parseInt($("#technologistPoints_A").text()) + parseInt($("#technologistPoints_B").text()) + parseInt($("#technologistPoints_C").text());
            var totalEvaluatorScore = parseInt($("#evaluatorPoints_A").text()) + parseInt($("#evaluatorPoints_B").text()) + parseInt($("#evaluatorPoints_C").text());
            var totalScore = parseInt(totalTechnologistScore) + parseInt(totalEvaluatorScore);
            $("#totalTechnologistScore").text(totalTechnologistScore.toFixed(0));
            $("#totalEvaluatorScore").text(totalEvaluatorScore.toFixed(0));
            $("#totalScore").text(totalScore.toFixed(0));

            $(".technologistScore").val(totalTechnologistScore.toFixed(0));
            $(".evaluatorScore").val(totalEvaluatorScore.toFixed(0));
            $(".totalScore").val(totalScore.toFixed(0));

        }

        function calculateTotalScore() {
            var techAPosition = $('#techASpan').text();
            var techBPosition = $('#techBSpan').text();
            var techCPosition = $('#techCSpan').text();
            var evalAPosition = $('#evalASpan').text();
            var evalBPosition = $('#evalBSpan').text();
            var evalCPosition = $('#evalCSpan').text();

            var techScore = parseInt(techAPosition) + parseInt(techBPosition) + parseInt(techCPosition);
            var evalScore = parseInt(evalAPosition) + parseInt(evalBPosition) + parseInt(evalCPosition);
            var totalScore = techScore + evalScore;

            $('#techScoreSpan').text(techScore);
            $('#evalScoreSpan').text(evalScore);
            $('#totalScoreSpan').text(totalScore);
        }
    </script>

    <script>
        // Get all collapsible button elements
        var buttons = document.querySelectorAll(".collapsible");
        var contents = document.querySelectorAll(".panel-collapse");

        // Add click event listeners to all buttons
        buttons.forEach(function(button, index) {
            button.addEventListener("click", function() {
                // Check if the content is currently expanded
                var isExpanded = contents[index].style.display === "block";

                // Close all sections
                contents.forEach(function(content) {
                    content.style.display = "none";
                });

                // Reset the "expanded" class for all buttons
                buttons.forEach(function(btn) {
                    btn.classList.remove("expanded");
                });

                // Toggle the content for the clicked section
                if (!isExpanded) {
                    contents[index].style.display = "block";
                    button.classList.add("expanded");
                }
            });
        });
    </script>
</body>

</html>