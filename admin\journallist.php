<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsStudent.php');
include('../class/clsJournal.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../setRequest.php');


$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0;
$loggedUserId = isset($_SESSION['loggedUserId']) ? $_SESSION['loggedUserId'] : 0;
$selrotationId = '';
$rotationId = 0;
$currentstudentId = 0;
$from_date = '';
$to_date = '';
$selrotationIdFilter = '';
$Totalminutes = 0;
$display_from_date = '';
$display_to_date = '';
$canvasStatus = '';
$objrotation = new clsRotation();
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSearch'])) {
	$canvasStatus = isset($_POST['canvasStatus']) ? $_POST['canvasStatus'] : '';
	$currentstudentId = $_POST['hidenstudentid'];
	$currentstudentId = DecodeQueryData($currentstudentId);
	$selrotationId = $_POST['cboRotation'];
	$selrotationId = DecodeQueryData($selrotationId);
	$display_from_date = (isset($_POST['fromDate']) && !empty($_POST['fromDate'])) ? date("Y-m-d", strtotime($_POST['fromDate'])) : '';
	$display_to_date = (isset($_POST['toDate']) && !empty($_POST['toDate'])) ? date("Y-m-d", strtotime($_POST['toDate'])) : '';
}

$type = isset($_GET['type']) ? $_GET['type'] : '';
if ($type == 'canvas')
	$canvasStatus = 1;

if (isset($_GET['studentId'])) {
	$currentstudentId = DecodeQueryData($_GET['studentId']);
}
if (isset($_GET['rotationId'])) {
	$selrotationId = DecodeQueryData($_GET['rotationId']);
}
if (isset($_GET['selrotationId'])) {
	$selrotationIdFilter = DecodeQueryData($_GET['selrotationId']);
}

$objJournal = new clsJournal();
$rowsJournalData = $objJournal->GetAllJournals($currentSchoolId, $display_from_date, $display_to_date, $selrotationId, $currentstudentId, $canvasStatus);
$totalJournalCount = 0;
if ($rowsJournalData != '') {
	$totalJournalCount = mysqli_num_rows($rowsJournalData);
}

unset($objJournal);

//For Student 
$objStudent = new clsStudent();
$students = $objStudent->GetAllStudents($currentSchoolId);
$StudentName = $objStudent->GetSingleStudent($currentSchoolId, $currentstudentId);
$studentfullname = $StudentName ? ($StudentName['firstName'] . ' ' . $StudentName['lastName']) : '';
unset($objStudent);

//For Rotation Name
$rotation = $objrotation->GetRotationBySchool($currentSchoolId);
$RotationName = $objrotation->GetSingleRotation($currentSchoolId, $selrotationId);
$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] : '';

?>

<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title>Journal</title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
	<?php include("includes/datatablecss.php") ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<?php if ($type == 'canvas') { ?>
						<li><a href="dashboard.html">Home</a></li>
						<li><a href="settings.html">Settings</a></li>
						<li class="active">Daily Journal</li>
					<?php } else { ?>
						<li><a href="dashboard.html">Home</a></li>
						<?php if ($selrotationId) {
							if (isset($_GET['studentId']) && (isset($_GET['journalId'])) && ($_GET['rotationId'])) { ?>
								<li><a href="rotations.html">Rotations</a></li>
								<li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
								<li class="active">Daily Journal</li>
							<?php } elseif ($currentstudentId) {; ?>
								<li><a href="clinical.html">Clinical</a></li>
								<li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
								<li class="active">Daily Journal</li>
							<?php } elseif ($selrotationId) {  ?>
								<li><a href="rotations.html">Rotations</a></li>
								<li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
								<li class="active">Daily Journal</li>

							<?php }
						} elseif ($currentstudentId) { ?>
							<li><a href="clinical.html">Clinical</a></li>
							<li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
							<li class="active">Daily Journal</li>
						<?php } ?>
					<?php } ?>

				</ol>
			</div>

		</div>
	</div>

	<div class="container">


		<?php
		if (isset($_GET["status"])) {
			if ($_GET["status"] == "Added") {
		?>
				<div class="alert alert-success alert-dismissible fade in" role="alert">
					<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
					</button>Journal added successfully.
				</div>
			<?php
			} else if ($_GET["status"] == "Updated") {
			?>
				<div class="alert alert-success alert-dismissible fade in" role="alert">
					<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
					</button> Journal updated successfully.
				</div>
			<?php
			} else if ($_GET["status"] == "Error") {
			?>
				<div class="alert alert-danger alert-dismissible fade in" role="alert">
					<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
					</button> Error occurred.
				</div>
		<?php
			}
		}
		?>

		<div id="divTopLoading">Loading...</div>

		<?php if ($type != 'canvas') {
			if (isset($_GET['studentId']))
				$formAction = "journallist.html?studentId=" . EncodeQueryData($currentstudentId);
			else
				$formAction = "journallist.html?selrotationId=" . EncodeQueryData($selrotationId);

		?>
			<form name="journallist" id="journallist" method="POST" action="<?php echo ($formAction); ?>">
				<div class="row">
					<div class="col-md-3  margin_bottom_ten">
						<div class="form-group">
							<label class="col-md-12 px-0 control-label" for="fromDate" style="margin-top:8px">From Date</label>
							<div class='"col-md-12 input-group date w-full relative' name="fromDate" id='fromDate'>
								<input type='text' name="fromDate" id="fromDate" value="<?php if ($display_from_date != '') echo date('m/d/Y', strtotime($display_from_date)); ?>" class="dateInputFormat form-control input-md r rotation_date" data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
					<div class="col-md-3  margin_bottom_ten">
						<div class="form-group">
							<label class="col-md-12 px-0 control-label" for="toDate" style="margin-top:8px">To Date</label>

							<div class='col-md-12 input-group date w-full relative' id='toDate'>

								<input type='text' name="toDate" id="toDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php if ($display_to_date != '') echo date('m/d/Y', strtotime($display_to_date)); ?>" data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
					<div class="col-md-3">
						<div class="form-group">
							<label class="col-md-12 px-0 control-label" for="cbostudent" style="margin-top:8px">Rotation</label>
							<div class="col-md-12 padding_right_zero padding_left_zero">
								<select id="cboRotation" name="cboRotation" class="form-control input-md required-input select2_single">
									<option value="" selected>Select</option>

									<?php

									if ($rotation != "") {
										while ($row = mysqli_fetch_assoc($rotation)) {
											$selrotationIdDropdown  = $row['rotationId'];
											$name  = stripslashes($row['title']);

									?>
											<option value="<?php echo (EncodeQueryData($selrotationIdDropdown)); ?>" <?php if ($selrotationId == $selrotationIdDropdown) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
									<?php

										}
									}

									?>
								</select>

								<input type="hidden" name="hidenstudentid" id="hidenstudentid" value="<?php echo EncodeQueryData($currentstudentId); ?>">
							</div>
						</div>
					</div>
					<?php if ($isActiveCanvas) { ?>
						<div class="col-md-3">
							<div class="form-group">
								<label class="col-md-4 control-label padding_right_zero" for="" style="margin-top:8px">Canvas Status</label>
								<div class="col-md-8 padding_right_zero padding_left_zero">
									<select id="canvasStatus" name="canvasStatus" class="form-control input-md required-input select2_single">
										<option value="" selected>All</option>
										<option value="1" <?php if ($canvasStatus == 1) { ?> selected="true" <?php } ?>>Sent</option>
										<option value="0" <?php if ($canvasStatus == 0 && $canvasStatus != '') { ?> selected="true" <?php } ?>>Not Sent</option>
									</select>
								</div>
							</div>
						</div>
					<?php } ?>
					<div class="col-md-2  margin_bottom_ten">
						<div class="form-group">
						<label class="col-md-12 control-label padding_right_zero" for="" style="margin-top:8px; visibility: hidden;">Canvas Status</label>
							<button id="btnSearch" name="btnSearch" class="col-md-12 btn btn-success">Search</button>
						</div>
					</div>


				</div>
				<div class="row">
					
				</div>
			</form>
		<?php } ?>

		<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
			<thead>
				<tr>

					<th> Date</th>
					<?php
					if (isset($_GET['rotationId'])) {
						if (isset($_GET['studentId']) && ($_GET['journalId']) && ($_GET['rotationId'])) { ?>
							<th>First Name</th>
							<th>Last Name</th>
						<?php } elseif (isset($_GET['rotationId'])) { ?>
							<th>First Name</th>
							<th>Last Name</th>
						<?php 	}
					} elseif (isset($_GET['selrotationId'])) { ?>
						<th>First Name</th>
						<th>Last Name</th>
					<?php
					} elseif ($type == 'canvas') { ?>
						<th>First Name</th>
						<th>Last Name</th>
						<th>Rotation</th>
					<?php } else { ?>
						<th>Rotation</th>
					<?php 	} ?>
					<?php if ($currentSchoolId == 59) { ?>
						<th style="text-align:center">Amount of<br>Time Spent</th>
					<?php } ?>
					<th>Course</th>
					<th>Hospital Site</th>
					<th style="text-align: center">Evaluator<br>Response</th>
					<th style="text-align: center">School<br>Response</th>
					<?php if ($type != 'canvas') { ?>
						<th style="text-align: center">Action</th>
					<?php } ?>
					<?php if ($isActiveCanvas && $type != 'canvas') { ?>
						<th class="text-center">Canvas Status</th>
					<?php } ?>
				</tr>
			</thead>
			<tbody>
				<?php
				if ($totalJournalCount > 0) {
					while ($row = mysqli_fetch_array($rowsJournalData)) {

						$studentId = ($row['studentId']);
						$fName = stripslashes($row['firstName']);
						$lastName = stripslashes($row['lastName']);
						$fullName =  $fName . ' ' . $lastName;
						$journalDate = stripslashes($row['journalDate']);
						$selrotationId = stripslashes($row['rotationId']);
						$courselocationId = $row['locationId'];
						$parentRotationId = stripslashes($row['parentRotationId']);
						$rotationLocationId = stripslashes($row['rotationLocationId']);

						$locationId = 0;
						if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
							if ($parentRotationId > 0) {
								if (!$rotationLocationId)
									$locationId = $objrotation->GetLocationByRotation($selrotationId);
								else
									$locationId  = $rotationLocationId;
							}
						} else {
							$locationId  = $courselocationId;
						}

						//Get Time Zone By Rotation 
						$objLocation = new clsLocations();
						$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
						unset($objLocation);
						if ($TimeZone == '')
							$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
						$journalDate = converFromServerTimeZone($journalDate, $TimeZone);
						$journalDate = date("m/d/Y", strtotime($journalDate));
						$journalId = $row['journalId'];
						$rotationname = $row['rotationname'];
						$rotationId = $row['rotationId'];
						$rank = stripslashes($row['rankname']);
						$timeSpent = $row['timeSpent'];
						$hospitalSiteId = stripslashes($row['hospitalname']);
						$coursename = stripslashes($row['coursename']);
						$schoolresponse = stripslashes($row['journalSchoolResponse']);
						$ClinicianResponse = stripslashes($row['journalClinicianResponse']);
						if ($schoolresponse != '')
							$schoolresponse = 'Yes';
						else
							$schoolresponse = 'No';

						if ($ClinicianResponse != '')
							$ClinicianResponse = 'Yes';
						else
							$ClinicianResponse = 'No';

						$Totalminutes += $timeSpent;

						// For Canvas
						$isSendToCanvas  = isset($row['isSendToCanvas']) ? $row['isSendToCanvas'] : 0;

						$isSendToCanvasClass = 'isSendRecordToCanvas';
						$isSentToCanvasClass = 'hide';
						if ($isSendToCanvas) {
							$isSendToCanvasClass = 'hide';
							$isSentToCanvasClass = '';
						}

						$isUserCanSendCompletedRecordToCanvas = 0;
						if ($ClinicianResponse == 'Yes' || $schoolresponse == 'Yes')
							$isUserCanSendCompletedRecordToCanvas = 1;
				?>
						<tr>
							<td> <?php echo ($journalDate); ?> </td>
							<?php
							if (isset($_GET['rotationId'])) {
								if (isset($_GET['studentId']) && ($_GET['journalId']) && ($_GET['rotationId'])) { ?>
									<td>
										<?php echo ($fName); ?>
									</td>
									<td>
										<?php echo ($lastName); ?>
									</td>
								<?php 	} elseif (isset($_GET['rotationId'])) { ?>

									<td>
										<?php echo ($fName); ?>
									</td>
									<td>
										<?php echo ($lastName); ?>
									</td>
								<?php   }
							} elseif (isset($_GET['selrotationId'])) { ?>
								<td><?php echo ($fName); ?></td>
								<td><?php echo ($lastName); ?></td>
							<?php
							} elseif ($type == 'canvas') { ?>
								<td><?php echo ($fName); ?></td>
								<td><?php echo ($lastName); ?></td>
								<td><?php echo ($rotationname); ?></td>
							<?php } else { ?>
								<td><?php echo ($rotationname); ?></td>
							<?php	}  ?>
							<?php if ($currentSchoolId == 59) { ?>
								<td align="center">
									<?php if ($timeSpent > 0) echo ($timeSpent); ?>
								</td>
							<?php } ?>
							<td>
								<?php echo ($coursename); ?>
							</td>
							<td>
								<?php echo ($hospitalSiteId); ?>
							</td>
							<td style="text-align: center">
								<?php echo ($ClinicianResponse); ?>
							</td>
							<td style="text-align: center">
								<?php echo ($schoolresponse); ?>
							</td>

							<?php if ($type != 'canvas') { ?>
								<td style="text-align: center">
									<?php
									$rotationStatus = checkRotationStatus($rotationId);
									$viewParam = ($rotationStatus) ? '&view=V' : ''; // Add '&view=V' if $rotationStatus is true
									$linkText = ($rotationStatus) ? 'View' : 'Edit'; // Change link text based on $rotationStatus
									?>

									<?php if (isset($_GET['rotationId'])) {
										$rotationId = DecodeQueryData($_GET['rotationId']);
									?>
										<a href="addjournal.html?editjournalid=<?php echo EncodeQueryData($journalId); ?>&rotationId=<?php echo EncodeQueryData($rotationId); ?><?php echo $viewParam; ?>"><?php echo $linkText; ?></a>
									<?php } elseif ($currentstudentId) { ?>
										<a href="addjournal.html?editjournalid=<?php echo EncodeQueryData($journalId); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?><?php echo $viewParam; ?>"><?php echo $linkText; ?></a>
									<?php } else { ?>
										<a href="addjournal.html?editjournalid=<?php echo EncodeQueryData($journalId); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>&rotationId=<?php echo EncodeQueryData($selrotationId); ?><?php echo $viewParam; ?>"><?php echo $linkText; ?></a>
									<?php } ?>
									| <a href="javascript:void(0);" class="deleteAjaxRow" journalId="<?php echo EncodeQueryData($journalId); ?>" schoolStudentName="<?php echo htmlspecialchars($fullName); ?>">Delete</a>
								</td>


							<?php } ?>
							<?php if ($isActiveCanvas && $type != 'canvas') {
								if ($loggedUserSendRecordToCanvas && $isUserCanSendCompletedRecordToCanvas) { ?>
									<td class="text-center">
										<a href="javascript:void(0);" id="isSendToCanvas_<?php echo $journalId; ?>" class="<?php echo $isSendToCanvasClass; ?>" journalDate="<?php echo $journalDate; ?>" rotation="<?php echo $rotationname; ?>" coursename="<?php echo $coursename; ?>" hospitalSiteName="<?php echo $hospitalSiteId; ?>" clinicianResponse="<?php echo $ClinicianResponse; ?>" schoolresponse="<?php echo $schoolresponse; ?>" studentfullname="<?php echo $fullName; ?>" studentId="<?php echo $studentId; ?>" journalId="<?php echo $journalId; ?>">
											Send to Canvas
										</a>
										<label for="" class="isSentToCanvas_<?php echo $journalId; ?> <?php echo $isSentToCanvasClass; ?>">Sent</label>

									</td>

								<?php } else { ?>
									<td class="text-center"><label for="" class=""> -
											<?php //if($isSendToCanvas>0) { echo 'Sent'; } else { echo 'Send to canvas'; } 
											?>
										</label></td>

							<?php }
							}
							?>
						</tr>
				<?php


					}
				}
				unset($objrotation);
				?>
			</tbody>
			<!-- Only aaupor schools can see this -->
			<?php if ($currentSchoolId == 59) { ?>
				<tfoot>
					<tr>
						<?php if ($selrotationId || $selrotationIdFilter) { ?>
							<td colspan="3" align="right"><b>Total:</b></td>
						<?php } else { ?>
							<td colspan="2" align="right"><b>Total:</b></td>
						<?php } ?>
						<td align="center">
							<?php echo sprintf("%02d:%02d", floor($Totalminutes / 60), $Totalminutes % 60); ?>
						</td>
						<td>&nbsp;</td>
						<td>&nbsp;</td>
						<td>&nbsp;</td>
						<td>&nbsp;</td>
						<td>&nbsp;</td>
					</tr>
				</tfoot>
			<?php } ?>
		</table>
	</div>

	<?php include('includes/footer.php'); ?>
	<?php include("includes/datatablejs.php") ?>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

	<script type="text/javascript">
		alertify.defaults.transition = "slide";
		alertify.defaults.theme.ok = "btn btn-success";
		alertify.defaults.theme.cancel = "btn btn-danger";
		alertify.defaults.theme.input = "form-control";

		$(window).load(function() {
			$("#divTopLoading").addClass('hide');
			$(".select2_single").select2();



			$(function() {
				$("#fromDate").datetimepicker({
					format: "MM/DD/YYYY"
				});
			});

			$(function() {
				$("#toDate").datetimepicker({
					format: "MM/DD/YYYY"
				});
			});

		});

		<?php
		if (isset($_GET['rotationId'])) { ?>
			var current_datatable = $("#datatable-responsive").DataTable({
				"ordering": true,
				"order": [
					[0, "desc"]
				],
				"aoColumns": [{
						"sWidth": "10%"
					},
					<?php if ($currentSchoolId == 59) { ?> {
							"sWidth": "15%"
						},
					<?php } ?> {
						"sWidth": "15%"
					}, {
						"sWidth": "15%"
					}, {
						"sWidth": "15%"
					}, {
						"sWidth": "10%"
					}, {
						"sWidth": "10%"
					}, {
						"sWidth": "10%",
						"sClass": "alignCenter"

					}, {
						"sWidth": "15%",
						"sClass": "alignCenter",
						"bSortable": false
					}
					<?php if ($isActiveCanvas) { ?>, {
							"sWidth": "10%"
						},
					<?php } ?>
				]

			});
		<?php } else { ?>
			var current_datatable = $("#datatable-responsive").DataTable({
				"ordering": true,
				"order": [
					[0, "desc"]
				],
				"aoColumns": [{
						"sWidth": "10%"
					}, {
						"sWidth": "20%"
					}, {
						"sWidth": "10%"
					},
					<?php if ($currentSchoolId == 59) { ?> {
							"sWidth": "15%"
						},
					<?php } ?> {
						"sWidth": "20%"
					}, {
						"sWidth": "10%"
					}, {
						"sWidth": "10%",
						"sClass": "alignCenter"

					}, {
						"sWidth": "30%",
						"sClass": "alignCenter",
						"bSortable": false
					}
					<?php if ($isActiveCanvas) { ?>, {
							"sWidth": "10%"
						},
					<?php } ?>
				]

			});

		<?php } ?>



		//Delete Student
		$(document).on('click', '.deleteAjaxRow', function() {

			var current_datatable_row = current_datatable.row($(this).parents('tr'));
			var journalId = $(this).attr('journalId');
			var schoolStudentName = $(this).attr('schoolStudentName');
			var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';	
			alertify.confirm('Student Name: ' + schoolStudentName, 'Continue with delete?', function() {
				$.ajax({
					type: "GET",
					url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
					data: {
						id: journalId,
						type: 'journal_student',
						userId: userId
					},
					success: function() {
						current_datatable.row(current_datatable_row).remove().draw(false);
						alertify.success('Deleted');
					}
				});
			}, function() {});

		});

		//Send Records To Canvas
		$(document).on('click', '.isSendRecordToCanvas', function() {
			var that = this;
			var journalDate = $(this).attr('journalDate');
			var rotation = $(this).attr('rotation');
			var coursename = $(this).attr('coursename');
			var studentfullname = $(this).attr('studentfullname');
			var studentId = $(this).attr('studentId');
			var journalId = $(this).attr('journalId');
			var hospitalSiteName = $(this).attr('hospitalSiteName');
			var clinicianResponse = $(this).attr('clinicianResponse');
			var schoolresponse = $(this).attr('schoolresponse');
			var schoolId = "<?php echo $currentSchoolId; ?>";

			alertify.confirm('Daily Journal ', 'Continue with send record to Canvas?', function() {
				$(that).text('Loading..');
				$(that).prop('disabled', true);

				$.ajax({
					type: "POST",
					url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_record_to_canvas.html",
					data: {

						journalDate: journalDate,
						coursename: coursename,
						journalId: journalId,
						hospitalSiteName: hospitalSiteName,
						clinicianResponse: clinicianResponse,
						schoolresponse: schoolresponse,
						rotation: rotation,
						studentFullName: studentfullname,
						studentId: studentId,
						schoolId: schoolId,
						type: 'DailyJournal'
					},
					success: function(response) {
						if (response == 'Success') {
							$(that).addClass('hide');
							$('.isSentToCanvas_' + journalId).removeClass('hide')
							alertify.success('Record Successfully Sent to Canvas.');
						} else {
							alertify.success(response);
						}

					}
				});
			}, function() {});

		});
	</script>
</body>

</html>