<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsClinician.php');
include('../class/clsClinicianHospitalSite.php');
include("../class/PasswordHash.php");
include('../class/Zebra_Image.php');
include("../class/class.phpmailer.php");
// include("../class/class.smtp.php");
include('../class/clsSendEmails.php');
include('../class/clsSMTPSettings.php');

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
	//Get Clinician Id and status
	$currentSchoolId = DecodeQueryData($_GET['schoolId']);
	$systemUserId = isset($_GET['id']) ? DecodeQueryData($_GET['id']) : 0;
	$status = ($systemUserId > 0) ? 'updated' : 'added';

	//Get inputs		
	$firstName  = trim($_POST['txtFirstName']);
	$middleName  = trim($_POST['txtMiddleName']);
	$lastName  = trim($_POST['txtLastName']);
	$email  = trim($_POST['txtEmail']);
	$phone  = trim($_POST['txtPhone']);
	$cellPhone  = trim($_POST['txtcellPhone']);
	$address1  = trim($_POST['txtAddress1']);
	$address2  = trim($_POST['txtAddress2']);
	$cboCountry  = trim($_POST['cboCountry']);
	$cbolocation  = trim($_POST['cbolocation']);
	$cbohospitalsites  = $_POST['cbohospitalsites'];
	$city  = trim($_POST['txtCity']);
	$state  = trim($_POST['cboState']);
	$zipCode  = trim($_POST['txtZipCode']);
	$userName = trim($_POST['txtUsername']);
	$cborole  = trim($_POST['cborole']);
	$emailtopassword  = trim($_POST['chkemailtopassword']);
	//Check firstName, lastName email and userName blank
	if ($firstName == "" || $lastName == "" || $email == "" || $userName == "") {
		header('location:addclinician.html?status=mandatory&id=' . EncodeQueryData($systemUserId));
		exit();
	}
	$objClinician = new clsClinician();
	//Generate password
	if ($systemUserId == 0 && $emailtopassword == 1) {
		$password = GenerateRandomAlphaNumericNumber(5);
		$objClinician->passwordHash = PasswordHash::hash($password);
	}
	$objSendEmails = new clsSendEmails($currentSchoolId);
	//save data
	$objClinician->locationId = $cbolocation;
	$objClinician->clinicianRoleId = $cborole;
	$objClinician->hospitalSiteId = $cbohospitalsites;
	$objClinician->firstName = ucfirst($firstName);
	$objClinician->middleName = ucfirst($middleName);
	$objClinician->lastName = ucfirst($lastName);
	$objClinician->email = $email;
	$objClinician->phone = $phone;
	$objClinician->cellPhone = $cellPhone;
	$objClinician->address1 = $address1;
	$objClinician->address2 = $address2;
	$objClinician->cboCountry = $cboCountry;
	$objClinician->city = $city;
	$objClinician->stateId = $state;
	$objClinician->zip = $zipCode;
	$objClinician->isActive = '1';
	$objClinician->username = $userName;
	$objClinician->schoolId = $currentSchoolId;
	$objClinician->emailtopassword = $emailtopassword;
	$objClinician->createdBy = $_SESSION['loggedUserId'];
	$retClinicianId = $objClinician->SaveClinician($systemUserId);

	if ($systemUserId == 0 && $emailtopassword == 1) {
		$objSendEmails->SendClinicianLoginDetails($retClinicianId, $password);
		// $objSendEmails->SendPasswordToClinician($password,$retClinicianId,$email,$currenSchoolLogoImagePath,$currenschoolDisplayname);
	}
	if ($retClinicianId > 0) {
		// Handle cropped image data first
		$coverImage = $_POST['fileLogo'] ?? '';
		if (!empty($coverImage)) {
			$ext = getFileExtensionFromBase64($coverImage);
			$imageData = explode(',', $coverImage)[1];
			$decodedImage = base64_decode($imageData);

			// Check User Directory
			$uploaddir = "../upload/schools/" . $currentSchoolId;
			if (!file_exists($uploaddir)) {
				mkdir($uploaddir);
			}

			$uploaddir .= "/clinician/";
			if (!file_exists($uploaddir)) {
				mkdir($uploaddir);
			}

			$uploaddir .= $retClinicianId . "/";
			if (!file_exists($uploaddir)) {
				mkdir($uploaddir);
			}

			// Save cropped image files
			$smallFilename = 'PROFILE_SMALL_' . $retClinicianId . '.' . $ext;
			$largeFilename = 'PROFILE_LARGE_' . $retClinicianId . '.' . $ext;

			file_put_contents($uploaddir . $smallFilename, $decodedImage);
			file_put_contents($uploaddir . $largeFilename, $decodedImage);

			// Update session and database
			$_SESSION["loggedClinicianProfileImagePath"] = GetClinicianImagePath($retClinicianId, $currentSchoolId, $smallFilename);
			$objClinician->UpdateClinicianPhotosFileName($retClinicianId, $smallFilename, $largeFilename);
		}
		// Handle regular file upload if no cropped image
		elseif (isset($_FILES['filePhoto'])) {
			$Image = $_FILES['filePhoto']['name'];
			if ($Image) {
				$ext = strtolower(pathinfo($_FILES['filePhoto']['name'], PATHINFO_EXTENSION));
				if ($ext != "png" && $ext != "jpg" && $ext != "jpeg" && $ext != "gif") {
					header('location:editprofile.html?status=InvalidFile');
					exit();
				}

				//Check User Directory			

				$uploaddir = "../upload/schools/" . $currentSchoolId;
				if (!file_exists($uploaddir)) {
					mkdir($uploaddir);
				}

				$uploaddir .= "/clinician/";
				if (!file_exists($uploaddir)) {
					mkdir($uploaddir);
				}

				$uploaddir .= $retClinicianId . "/";

				if (!file_exists($uploaddir)) {
					mkdir($uploaddir);
				}

				//-----------------------------------
				//Save SMALL Path
				//-----------------------------------
				$smallFilename = 'PROFILE_SMALL_' . $retClinicianId . '.' . $ext;
				$UploadSmallFilePath = $uploaddir . $smallFilename;

				if (copy($_FILES['filePhoto']['tmp_name'], $UploadSmallFilePath)) {
					if (isset($_POST['chkAutoCrop'])) {
						$image = new Zebra_Image();
						$image->source_path = $UploadSmallFilePath;
						$image->target_path = $UploadSmallFilePath;
						$image->jpeg_quality = 85;
						$image->preserve_aspect_ratio = true;
						$image->enlarge_smaller_images = true;
						$image->preserve_time = true;
						$image->resize(50, 50, ZEBRA_IMAGE_CROP_CENTER, '-1');
						unset($image);
					}
					$_SESSION["loggedClinicianProfileImagePath"] = $defaultProfileImagePath = GetClinicianImagePath($retClinicianId, $currentSchoolId, $smallFilename);
				}

				//Save LARGE Path
				$largeFilename = 'PROFILE_LARGE_' . $retClinicianId . '.' . $ext;
				$UploadLargeFilePath = $uploaddir . $largeFilename;
				if (copy($_FILES['filePhoto']['tmp_name'], $UploadLargeFilePath)) {
					if (isset($_POST['chkAutoCrop'])) {
						//-----------------------------------
						//Crop and Resize Image
						//-----------------------------------
						$image = new Zebra_Image();
						$image->source_path = $UploadLargeFilePath;
						$image->target_path = $UploadLargeFilePath;
						$image->jpeg_quality = 85;
						$image->preserve_aspect_ratio = true;
						$image->enlarge_smaller_images = true;
						$image->preserve_time = true;
						$image->resize(200, 200, ZEBRA_IMAGE_CROP_CENTER, '-1');
						unset($image);
						//-----------------------------------

					}
					$_SESSION["loggedClinicianProfileLargeImagePath"] = GetClinicianImagePath($retClinicianId, $currentSchoolId, $largeFilename);
				}
				//Update File Name to DB
				//-----------------------------------
				$objClinician->UpdateClinicianPhotosFileName($retClinicianId, $smallFilename, $largeFilename);
				//-----------------------------------

			}
		}

		//Add hospital sites to clinician
		$objClinicianHospitalSite = new clsClinicianHospitalSite();
		//Delete all clinician hospitalsites
		$objClinicianHospitalSite->DeleteAllClinicianHopitalSites($retClinicianId);
		if (count($cbohospitalsites)) {
			//Now save new sites
			foreach ($cbohospitalsites as $hospitalSiteId) {
				$clinicianHospitalSiteId = $objClinicianHospitalSite->SaveClinicianHopitalSite($retClinicianId, $hospitalSiteId);
			}
		}
		unset($objClinicianHospitalSite);

		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = ($systemUserId > 0) ? $objLog::EDIT : $objLog::ADD;
		$userType = $objLog::ADMIN; // User type is set to ADMIN
		$IsMobile = 0;

		$objClinician = new clsClinician();
		$objClinician->saveClinicianAuditLog($retClinicianId, $_SESSION['loggedUserId'], $userType, $action, $IsMobile);

		unset($objLog);
		//Audit Log End


		unset($objClinician);
		header('location:schoolclinicians.html?status=' . $status);
	} else {
		unset($objClinician);
		header('location:addclinician.html?status=error');
	}
} else {
	header('location:addclinician.html');
	exit();
}
