<?php
ini_set('memory_limit', '656M');
ini_set('max_execution_time', 1200);
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsSchool.php');
include('../class/clsFormative.php');
include('../class/clsMidterm.php');
include('../class/clsSummative.php');
include('../class/clsCIevaluation.php');
include('../class/clsSiteevaluation.php');
include('../class/clsEquipment.php');
include('../class/clsIrr.php');
include('../class/clsInteraction.php');
include('../class/clscheckoff.php');
include('../class/clsJournal.php');
include('../class/clsStudent.php');
include('../class/clsAttendance.php');
include('../class/clsCountryStateMaster.php');
include('../class/clsProcedureCount.php');
include('../class/clsDaily.php');
include('../class/clsMasteryEval.php');
include('../class/clsClinicianAttendance.php');
include('../setRequest.php');
include('../class/clsExternalPreceptors.php');
require '../vendor/autoload.php';  // Include PhpSpreadsheet's autoloader

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;

$spreadsheet = new Spreadsheet();

$currentSchoolId;
$objSchool = new clsSchool();
$GetSchoolName = $objSchool->GetSchoolNames($currentSchoolId);
$schoolname = $GetSchoolName['displayName'];

date_default_timezone_set('Asia/Kolkata');
$today = (date('m/d/Y, H:i A'));
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnExport']))
    $reportType = $_POST['cboreporttype'];
$rotationId = $_POST['cborotation'];
$individual_student = $_POST['cboindividualstudent'];
// $individual_student  = $individual_student ? explode(',',$individual_student ) : '';

$skill = $_POST['cboskill'];
$student_rank = $_POST['cbostudentrank'];
$school_location = $_POST['cbolocation'];
$evaluator = $_POST['cboevaluator'];
$hospital_site = $_POST['cbohospitalsite'];
$checkoffTopic = $_POST['checkoffTopic'];
$selTopicId = $_POST['selTopicId'];
$startDate = $_POST['startDate'];
$AscDesc = $_POST['AscDesc'];
$sordorder = $_POST['sordorder'];
$cbosemester = $_POST['cbosemester'];
$subcborotation = $_POST['subcborotation'];
$courseTopicCompletion = isset($_POST['courseTopicCompletion']) ? $_POST['courseTopicCompletion'] : '';
$courseId = isset($_POST['courseId']) ? $_POST['courseId'] : 0;

$startDate = ($startDate) ? date('Y-m-d', strtotime(str_replace('-', '/', $startDate))) : "";

$endDate = $_POST['endDate'];
$endDate = ($endDate) ? date('Y-m-d', strtotime(str_replace('-', '/', $endDate))) : "";
$objStudent = new clsStudent();

$singlestudentfirstname = '';
$singlestudentlastname = '';
$singlestudentrank = '';

switch ($reportType) {
    case "Student_Details":
        include('reports/studentdetailsreport.php');
        break;
    case "Absence":
        include('reports/absenceReport.php');
        break;
    case "IRR Report":
        include('reports/irrreport.php');
        break;
    case "Journal":
        include('reports/journalreport.php');
        break;
    case "Attendance":
        include('reports/attendancereport.php');
        break;
    case "ClinicianAttendance":
        include('reports/clinicianattendancereport.php');
        break;
    case "Attendance_Daily_Weekly":
        include('reports/attendanceDailyWeeklyreport.php');
        break;
    case "AttendanceSummary":
        include('reports/attendancereSummaryReport.php');
        break;
    case "Student":
        include('reports/studentreport.php');
        break;
    case "Formative":
        include('reports/formativereport.php');
        break;
    case "Summative":
        include('reports/summativereport.php');
        break;
    case "DailyEval":
        include('reports/dailyEvalreport.php');
        break;
    case "Midterm":
        include('reports/Midtermreport.php');
        break;
    case "CI_Eval":
        include('reports/cievalreport.php');
        break;
    case "Site_Eval":
        include('reports/siteevalreport.php');
        break;
    case "Equipments":
        include('reports/equipmentreport.php');
        break;
    case "Dr_Points":
        include('reports/drinteractionreport.php');
        break;
    case "ClinicalSiteUnit":
        include('reports/ClinicalSiteUnitReport.php');
        break;
    case "Checkoff":
        include('reports/checkoffreport.php');
        break;
    case "CheckoffByCourses":
        include('reports/checkoffByCoursesreport.php');
        break;
    case "DailyDetail":
        include('reports/dailyWeeklyDetailEvalutionReport.php');
        break;
    case "Time_Exception":
        include('reports/timeexceptionreport.php');
        break;
    case "Procidure_Count":
        include('reports/procedurecountreport.php');
        break;
    case "Procedure_Details":
        include('reports/procedurecountdetailsreport.php');
        break;
    case "Mastery":
        include('reports/masteryreport.php');
        break;

    default:
        echo "<b>Please Select Valid Type.</b>";
        break;
}

// Set active sheet index to the first sheet, so Excel opens this as the first sheet
if ($reportType == 'Student_Details') {
    $spreadsheet = new Spreadsheet();
    $spreadsheet->setActiveSheetIndex(0);
}

// Redirect output to a client's web browser (Excel5)
$currentDate = date('m_d_Y_h_i');
header('Content-type: application/vnd.ms-excel; charset=UTF-8');
header('Content-Disposition: attachment;filename="' . $reportname . $today . '.xls"');
header("Pragma: no-cache");
header("Expires: 0");

$writer = new Xls($spreadsheet);
$writer->save('php://output');
?>