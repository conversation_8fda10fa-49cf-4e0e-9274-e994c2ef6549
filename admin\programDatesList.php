<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include('../class/clsStudent.php');
include('../class/clsStudentRankMaster.php');

$display_from_date = '';
$display_to_date = '';

if (isset($_GET['fromDate']) && isset($_GET['toDate'])) {
    $display_from_date = $_GET['fromDate'];
    $display_from_date = date("m/d/Y", strtotime($display_from_date));
    $display_to_date = $_GET['toDate'];
    $display_to_date = date("m/d/Y", strtotime($display_to_date));
}
$rankId = 0;
if (isset($_GET['rankId'])) {
    $rankId = $_GET['rankId'];
    $rankId = DecodeQueryData($rankId);
}
$objStudent = new clsStudent();
//Get All Student List
$rowsStudentData = $objStudent->GetAllSchoolStudentProgramDateList($currentSchoolId, $rankId, 0, $display_from_date, $display_to_date);
$totalStudentCount = 0;
if ($rowsStudentData != '') {
    $totalStudentCount = mysqli_num_rows($rowsStudentData);
}
unset($objStudent);
//StudentRank
$objStudentRankMaster = new clsStudentRankMaster();
$ranks = $objStudentRankMaster->GetAllStudentRankBySchool($currentSchoolId);
unset($objStudentRankMaster);


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Program Dates List</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <style>
        .mt-1 {
            margin-top: 10px;
            padding-left: 55px;
        }

        table.dataTable thead .sorting:after,
        table.dataTable thead .sorting_asc:after,
        table.dataTable thead .sorting_desc:after,
        table.dataTable thead .sorting_asc_disabled:after,
        table.dataTable thead .sorting_desc_disabled:after {
            top: 50%;
            transform: translateY(-50%);
        }

        .program-date-checkbox {
            width: 15px;
            height: 15px;
            margin-top: 0 !important;
        }

        .mb-15 {
            margin-bottom: 15px;
        }

        .table-scroll-x {
            overflow-x: auto;
        }

        .multiple-update-form-container {
            margin-left: auto;
            margin-right: auto;
            max-width: 1500px;
        }

        .date {
            min-width: 150px;
        }

        .bootstrap-datetimepicker-widget.dropdown-menu{
            inset: 34px 0px !important;
            height: fit-content;
        }

        .bootstrap-datetimepicker-widget.dropdown-menu.top:after,
        .bootstrap-datetimepicker-widget.dropdown-menu.top:before{
            display: none;
        }
        .multiple-update-form-container {
    margin-left: auto;
    margin-right: auto;
    max-width: 1500px;
}

    </style>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">Program Dates</li>
                </ol>
            </div>
            <div class="pull-right">
                <a class="btn btn-link impostStudentList" href="importstudent.html?type=2">Import</a>|<a class="btn btn-link" href="exportStudentProgramDates.html">Export</a>
            </div>
        </div>
    </div>

    <div class="custom-container">

        <?php

        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Imported") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Student Program Dates Imported
                </div>
            <?php
            } else if ($_GET["status"] == "Importerror") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Please Import .csv file
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>
        <form name="programDatesList" id="programDatesList">
            <div class="row">
                <div class=" multiple-update-form-container">
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="col-md-12 control-label text-right margin_top_seven" for="fromDate">From Date</label>
                        <input type='hidden' name="rankId" id="rankId" value="<?php echo EncodeQueryData($rankId); ?>" >
                           <div class="col-md-12">
                        <div class='input-group date col-md-7 w-full' name="fromDate" id='fromDate'>
                            <input type='text' name="fromDate" id="fromDate" value="<?php echo $display_from_date; ?>" class="dateInputFormat form-control input-md r rotation_date fromDate" data-parsley-errors-container="#error-txtDate" required placeholder="MM-DD-YYYY"/>
                            <span class="input-group-addon calender-icon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        </div>
                        <div id="error-txtDate"></div>
                    </div>
                </div>
                </div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="col-md-12 control-label text-right margin_top_seven" for="toDate">To Date</label>
                        <div class="col-md-12">
                        <div class='input-group date col-md-12 w-full' id='toDate'>
                            <input type='text' name="toDate" id="toDate" class="dateInputFormat form-control input-md  rotation_date toDate" value="<?php echo $display_to_date; ?>" data-parsley-errors-container="#error-txtDate" required placeholder="MM-DD-YYYY"/>
                            <span class="input-group-addon calender-icon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        </div>
                        <div id="error-txtDate"></div>
                    </div>
                </div>
                <div class="col-md-2">
                                            <label class="col-md-12 control-label text-right margin_top_seven" for="toDate" style="visibility: hidden;">To Date</label>
                    <div class="form-group" >
                        <button id="btnSearch" name="btnSearch" class="btn btn-success">Search</button>
                    </div>
                </div>
            </div>
        </form>
        <hr>
        <div class="row">
            <!-- <div class="col-xl-1"></div> -->
            <div class="multiple-update-form-container">
                <h3 class="col-md-12" style="text-align: center;margin-bottom: 20px;margin-top: 15px;">Multiple Update</h3>
                <div class="col-md-4 mb-15">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="ProgramEnrollmentDate">Enrollment</label>
                        <div class="col-md-12">
                            <div class='input-group date w-full' id='ProgramEnrollmentDate1'>

                                <input type='text' name="ProgramEnrollmentDate" id="ProgramEDate" class="form-control input-md rotation_date multipleUpdateDates dateInputFormat" value="" placeholder="MM-DD-YYYY" >
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-ProgramEnrollmentDate"></div>
                        </div>
                    </div>
                </div>


                <div class="col-md-4 mb-15">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="OntimeGraduationDate">On time Grad</label>
                        <div class="col-md-12">
                            <div class='input-group date w-full' id='OntimeGraduationDate'>

                                <input type='text' name="OntimeGraduationDate" id="OntimeGDate" class="form-control input-md  rotation_date multipleUpdateDates ontimeGraduateDate dateInputFormat" value="" data-parsley-errors-container="#error-OntimeGraduationDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-OntimeGraduationDate"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-15">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="ActualGraduationDate">Actual Grad</label>
                        <div class="col-md-12">
                            <div class='input-group date w-full' id='ActualGraduationDate'>

                                <input type='text' name="ActualGraduationDate" id="ActualGDate" class="form-control input-md  rotation_date multipleUpdateDates graduateDate dateInputFormat" value="" data-parsley-errors-container="#error-ActualGraduationDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-ActualGraduationDate"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-15">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="CRTCompletionDate">TMC Completion</label>
                        <div class="col-md-12">
                            <div class='input-group date w-full' id='CRTCompletionDate'>

                                <input type='text' name="CRTCompletionDate" id="CRTCDate" class="form-control input-md  rotation_date multipleUpdateDates dateInputFormat" value="" data-parsley-errors-container="#error-CRTCompletionDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-CRTCompletionDate"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-15">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="WRRTCompletionDate">CSE Completion</label>
                        <div class="col-md-12">
                            <div class='input-group date w-full ' id='WRRTCompletionDate'>

                                <input type='text' name="WRRTCompletionDate" id="WRRTCDate" class="form-control input-md  rotation_date multipleUpdateDates dateInputFormat" value="" data-parsley-errors-container="#error-WRRTCompletionDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-WRRTCompletionDate"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-4 mb-15">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="ProgramDropDate">Program Drop</label>
                        <div class="col-md-12">
                            <div class='input-group date w-full' id='ProgramDropDate'>

                                <input type='text' name="ProgramDropDate" id="ProgramDDate" class="form-control input-md  rotation_date multipleUpdateDates dateInputFormat" value="" data-parsley-errors-container="#error-ProgramDropDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-ProgramDropDate"></div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- <div class="col-md-4">

                <div class="form-group">
                    <label class="col-md-4" for="DropDateReason">Drop Date Reason:</label>
                    <div class="col-md-8">
                        <div class='input-group date' id='DropDateReason'>

                            <label><input type="radio" id="radio" name="Reason" value="Academic"> Academic</label>
                            &nbsp; <label><input type="radio" id="radio" name="Reason" value="Non-Academic"> Non-Academic</label>
                        </div>

                    </div>
                </div>
            </div> -->
            <!-- <div class="col-xl-1"></div> -->

            <div class="col-md-12" style="margin-top: 5px;margin-bottom: 0px;">
                <div class="col-md-12">
                    <div class="form-group" style="display: flex;justify-content: center;">
                        <button id="btnUpdate" name="btnUpdate" class="btn btn-success">Update</button>
                    </div>
                </div>
            </div>




        </div>

        <hr>

        <div id="divTopLoading">Loading...</div>
        <div class="row" style="margin-top: 15px;">
            <div class="col-md-3 pull-right">
                <div class="form-group">
                    <label class="col-md-4 control-label mt-10" for="cborank">Rank :</label>
                    <div class="col-md-8" style="padding: 0;">
                        <select id="cborank" name="cborank" class="form-control select2_single">
                            <option value="" selected>Select All</option>

                            <?php
                            if ($ranks != "") {
                                while ($row = mysqli_fetch_assoc($ranks)) {
                                    $selrankId  = $row['rankId'];
                                    $name  = stripslashes($row['title']);

                            ?>
                                    <option value="<?php echo EncodeQueryData($selrankId); ?>" <?php if ($rankId == $selrankId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                            <?php
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <br>
        <div class="col-md-12 table-scroll-x" style="padding: 0;">
            <table id="datatable-responsive" class="table table-bordered nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>

                        <th style="vertical-align: middle; width: 64px;" class="sorting_disabled" rowspan="1" colspan="1" aria-label="Select All  "><input class="selectall" type="checkbox" id="selectall" name="selectall[0]" value="0"> &nbsp;Select All </th>

                        <th class="text-center" style="vertical-align: middle;">Enrollment</th>
                        <th class="text-center" style="vertical-align: middle;">On Time Grad</th>
                        <th class="text-center" style="vertical-align: middle;">Actual Grad</th>
                        <th class="text-center" style="vertical-align: middle;">TMC Completion <br> Date</th>
                        <th class="text-center" style="vertical-align: middle;">CSE Completion <br> Date</th>
                        <th class="text-center" style="vertical-align: middle;">Program Drop Date</th>
                        <th style="vertical-align: middle;">Student Info</th>
                        <th class="text-center" style="vertical-align: middle;">Account <br>Created</th>
                    </tr>
                </thead>
                <tbody id="tableData">

                </tbody>
            </table>
        </div>
    </div>


    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>

    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {


            getProgramDatesData();
            var newUrl = 'programDatesList.html';
            history.pushState({}, null, newUrl);

            $("#divTopLoading").addClass('hide');

            $(function() {
                $("#fromDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            $(function() {
                $("#toDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            $(function() {
                $("#ProgramEnrollmentDate1").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            $(function() {
                $("#OntimeGraduationDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            $(function() {
                $("#ActualGraduationDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            $(function() {
                $("#CRTCompletionDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });
            $(function() {
                $("#WRRTCompletionDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });
            $(function() {
                $("#ProgramDropDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            // $('.updateData').each(function(){

        });


        $('.impostStudentList').magnificPopup({
            type: 'ajax',
            'closeOnBgClick': false
        });

        $("#btnSearch").click(function() {
            var fromDate = $('.fromDate').val();
            var toDate = $('.toDate').val();
            if (fromDate != '' && toDate != '') {
                getProgramDatesData();
                return false;
            } else {
                alertify.error('Please select from Date and to date');
                return false;
            }
        });

        $("#cborank").change(function() {
            getProgramDatesData();
            return false;
        });

        $(".select2_single").select2();
        $('#select2-copySchool-container').addClass('required-select2');
        $('#form-control step2 input-md select2_single').addClass('required-select2');

        $('.selectall').click(function() {

            if ($(this).is(':checked')) {
                $('#btnsendrequest').attr('disabled', false);
                $('input:checkbox:not(:disabled)').prop('checked', true);
            } else {
                $('#btnsendrequest').attr('disabled', true);
                $('input:checkbox:not(:disabled)').prop('checked', false);
            }
        });
        
        $('#btnUpdate').click(function() {
            var ProgramEDate = $('#ProgramEDate').val();
            var OntimeGDate = $('#OntimeGDate').val();
            var ActualGDate = $('#ActualGDate').val();
            var CRTCDate = $('#CRTCDate').val();
            var WRRTCDate = $('#WRRTCDate').val();
            var ProgramDDate = $('#ProgramDDate').val();
            if (ProgramEDate != '' || OntimeGDate != '' || ActualGDate != '' || CRTCDate != '' || WRRTCDate != '' || ProgramDDate != '') {
                // Get the selected checkboxes using the class or name
                var selectedCheckboxes = $('.studentsId:checked'); // or $('input[name="studentIds[]"]:checked');

                // Count how many checkboxes are selected
                var selectedStudentscount = selectedCheckboxes.length;
                if (selectedStudentscount > 0) {
                    // Map the values of the selected checkboxes to an array
                    var studentIds = selectedCheckboxes.map(function() {
                        return $(this).val(); // Map each checkbox value
                    }).get(); // Get the array of values

                    // console.log('Selected student IDs: ' + selectedValues);

                    $.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_updateStudentProgramDates.html",
                        data: {
                            studentIds: studentIds,
                            ProgramEDate: ProgramEDate,
                            OntimeGDate: OntimeGDate,
                            ActualGDate: ActualGDate,
                            CRTCDate: CRTCDate,
                            WRRTCDate: WRRTCDate,
                            ProgramDDate: ProgramDDate,
                        },
                        success: function() {
                            getProgramDatesData();
                            alertify.success('Updated');
                            $('.multipleUpdateDates').val('');
                            // $(ProgramEDate != '' || OntimeGDate != '' || ActualGDate != '' || CRTCDate != '' || WRRTCDate != '' || ProgramDDate != '')
                        }
                    });
                } else {
                    alertify.error('Please select at least one student');
                    return false;
                }
                console.log('Selected checkboxes count: ' + selectedStudentscount);
            } else {
                alertify.error('Please select at least one date');
                return false;
            }


        });

        function getProgramDatesData() {
            var rankId = $('#cborank').val();
            var fromDate = $('.fromDate').val();
            var toDate = $('.toDate').val();
            $.ajax({
                type: "POST",
                url: "../ajax/ajax_get_program_dates.html",
                data: {
                    rankId: rankId,
                    fromDate: fromDate,
                    toDate: toDate
                },
                dataType: 'json',
                success: function(resp) {
                    // console.log(resp);

                    // Clear the DataTable before appending new data
                    current_datatable.clear().draw();
                    // Check if response contains data
                    if (resp.length > 0) {

                        // Define an array to hold new rows
                        var rows = [];

                        // Loop through the response array and generate rows dynamically
                        resp.forEach(function(data) {

                            var studentId = data.studentId;
                            var programEnrollmentDate = data.programEnrollmentDate || '';
                            var ontimeGreduationDate = data.ontimeGreduationDate || '';
                            var actualGraduationDate = data.actualGraduationDate || '';
                            var TMCDate = data.TMCDate || '';
                            var CSEDate = data.CSEDate || '';
                            var programDropDate = data.programDropDate || '';
                            var fullName = data.fullName || '';
                            var rank = data.rank || '';
                            var email = data.email || '';
                            var createdDate = data.createdDate || '';

                            // Create the dynamic HTML row
                            var html = `
                    <tr>
                        <td style="text-align: center">
                            <input type="checkbox" id="students" class="studentsId" name="studentIds[]" value="${studentId}">
                        </td>
                        <td class="text-center">
                            <div class="input-group date ProgramEnrollmentDateDiv updateData" updateTtype="1" studentId="${studentId}" studId="${studentId}">
                                <input type="text" name="ProgramEnrollmentDate" id="ProgramEnrollmentDate_${studentId}" class="form-control input-md rotation_date dateInputFormat updateAjaxRow" updateTtype="1" studentId="${studentId}" value="${programEnrollmentDate}" placeholder="MM-DD-YYYY">
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class='input-group date OntimeGraduationDateDiv updateData' updateTtype="2" studentId="${studentId}" studId="${studentId}">
                                <input type='text' name="OntimeGraduationDate" id="OntimeGraduationDate_${studentId}" class="form-control input-md rotation_date ontimeGraduateDate dateInputFormat" value="${ontimeGreduationDate}" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class='input-group date ActualGraduationDateDiv updateData' updateTtype="3" studentId="${studentId}" studId="${studentId}">
                                <input type='text' name="ActualGraduationDate" id="ActualGraduationDate_${studentId}" class="form-control input-md rotation_date graduateDate dateInputFormat" value="${actualGraduationDate}" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class='input-group date TMCDateDiv updateData' updateTtype="4" studentId="${studentId}" studId="${studentId}">
                                <input type='text' name="TMCDate" id="TMCDate_${studentId}" class="form-control input-md rotation_date ontimeGraduateDate dateInputFormat" value="${TMCDate}" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class='input-group date CSEDateDiv updateData' updateTtype="5" studentId="${studentId}" studId="${studentId}">
                                <input type='text' name="CSEDate" id="CSEDate_${studentId}" class="form-control input-md rotation_date graduateDate dateInputFormat" value="${CSEDate}" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </td>
                        <td class="text-center">
                            <div class='input-group date programDropDateDiv updateData' updateTtype="6" studentId="${studentId}" studId="${studentId}">
                                <input type='text' name="programDropDate" id="programDropDate_${studentId}" class="form-control input-md rotation_date graduateDate dateInputFormat" value="${programDropDate}" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                        </td>
                        <td>
                            ${fullName}<br>
                            Rank: ${rank} <br>
                            Email: ${email} <br>
                        </td>
                        <td class="text-center">${createdDate}</td>
                    </tr>`;

                            // Push the HTML row to the array as a jQuery object
                            rows.push($(html)[0]);
                        });

                        // Add new rows to DataTable
                        current_datatable.rows.add(rows).draw();

                        // Re-initialize the datetimepicker for the new rows
                        initializeDateTimePicker();
                        $('#selectall').prop('checked', false);
                    }
                },

                error: function(jqXHR, textStatus, errorThrown) {
                    console.log('Error fetching data:', textStatus, errorThrown);
                }
            });
        }

        function initializeDateTimePicker() {
            $('.updateData').datetimepicker({
                format: "MM/DD/YYYY",
                useCurrent: false
            }).off('dp.change').on('dp.change', function(e) {
                var studentId = $(this).attr('studentId');
                var studId = $(this).attr('studId');
                var type = $(this).attr('updateTtype');
                var value = $(this).find('input').val(); // Get the value from the input field

                // Call updateAjaxData only when the event is triggered
                updateAjaxData(studentId, type, value);
            });
        }

        var current_datatable = $("#datatable-responsive").DataTable({
            "aaSorting": [],
            'iDisplayLength': 100,
            "aoColumns": [{
                    "sWidth": "5%",
                    "bSortable": false
                },
                {
                    "sWidth": "10%",
                    "bSortable": false
                },
                {
                    "sWidth": "10%",
                    "bSortable": false
                },
                {
                    "sWidth": "10%",
                    "bSortable": false
                },
                {
                    "sWidth": "10%",
                    "bSortable": false
                },
                {
                    "sWidth": "10%",
                    "bSortable": false
                },
                {
                    "sWidth": "10%",
                    "bSortable": false
                },
                {
                    "sWidth": "20%"
                },
                {
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                }
            ]
        });

        // Call this once to initialize the datetime picker for existing rows
        initializeDateTimePicker();

        // ajax call for update      
        function updateAjaxData(studentId, type, value) {
            console.log('studentId' + studentId);
            console.log('type' + type);
            console.log('value' + value);
            //    return false;
            $.ajax({
                type: "GET",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_updateStudentProgramDates.html",
                data: {
                    id: studentId,
                    value: value,
                    type: type,
                },
                success: function() {
                    alertify.success('Updated');
                }
            });
        }
    </script>
</body>

</html>