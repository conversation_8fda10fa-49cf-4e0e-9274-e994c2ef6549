<?php
	include('includes/validateUserLogin.php');
    include('../includes/config.php');
    include('../class/clsDB.php');
    include('../includes/commonfun.php');
    include('../class/clsRotation.php');	
    include('../class/clsStudent.php'); 
    include('../class/clsLocations.php'); 
    include('../class/clsRotationDetails.php'); 
    
    include('../setRequest.php');
	require_once "PHPExcel/PHPExcel.php"; // Or whatver the path to your PHPExcel library is
	require_once "PHPExcel/PHPExcel/IOFactory.php";
	
    
            $currentSchoolId;
            $TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
            
            //Get RotationId
            $rotationId = $_GET['rotationId'];
            $default_rotationId = DecodeQueryData($rotationId);
            
            //For Rotation Name
            $objRotation = new clsRotation();
            $rotation = $objRotation->GetrotationDetails($default_rotationId,$currentSchoolId);
            $rotationtitle= isset($rotation['title']) ? $rotation['title'] : '';
            

            //For Rotation List
            $objRotationDetails = new clsRotationDetails();
            $rowsRotations = $objRotationDetails->GetAllAssignSubRotationStudents($default_rotationId);

            $totalRotations = 0;
            if($rowsRotations !='')
            {
                $totalRotations =mysqli_num_rows($rowsRotations);
            }
            
         
			$title='Student Assign Rotation List';			
			date_default_timezone_set('Asia/Kolkata');
            $today= (date('m/d/Y, H:i A'));
            
            if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnStudentExport']))	
            				
				$reportType = $_POST['cboreporttype'];
												
											
		 switch($reportType)
			{		
				case "StudentRotationAssignList":
								
						$objPHPExcel = new \PHPExcel();
						
						// Set document properties
						$objPHPExcel->getProperties()->setCreator('Schools')
													 ->setLastModifiedBy('JCC')
													 ->setTitle('Reports')
													 ->setSubject('Student Assign Rotation List')
													 ->setDescription('All School Reports');
													 
						//Active Sheet
						$objPHPExcel->setActiveSheetIndex(0);
						$objPHPExcel->getActiveSheet()->setTitle();				
						
						//Print Heading	
						$headerstyleArray = array('font'  => array('bold'  => true,'size'  => 16));
						
						$objPHPExcel->getActiveSheet()->mergeCells("B2:I2");
						$objPHPExcel->getActiveSheet()->setCellValue('B2', $title);
						$objPHPExcel->getActiveSheet()->getStyle('B2')->applyFromArray($headerstyleArray);
						$objPHPExcel->getActiveSheet()->getStyle('B2')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()
									 ->getStyle('B2')
									 ->getFill()
									 ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
									 ->getStartColor()
									 ->setRGB('E0E0E0');
							
						$styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));
						$objPHPExcel->getActiveSheet()->getStyle('B2:I2')->applyFromArray($styleBorderArray);
						
						
						$styleArray = array('font'  => array('bold'  => true,'size'  => 12));
						
						$objPHPExcel->getActiveSheet()->mergeCells("B4:I4");
						$objPHPExcel->getActiveSheet()->setCellValue('B4','Rotation Name : '.$rotationtitle);
						$objPHPExcel->getActiveSheet()->getStyle('B4')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('B4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
						
						$objPHPExcel->getActiveSheet()
									 ->getStyle('B4')
									 ->getFill()
									 ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
									 ->getStartColor()
									 ->setRGB('E0E0E0');
									
						$styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));
						$objPHPExcel->getActiveSheet()->getStyle('B4:I4')->applyFromArray($styleBorderArray);
						
						//Make Table Heading
						$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
						
						$objPHPExcel->getActiveSheet()->setCellValue('B6', 'Subrotation');
						$objPHPExcel->getActiveSheet()->getStyle('B6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('B6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                        $objPHPExcel->getActiveSheet()->getDefaultColumnDimension('B6')->setWidth('30');

						$objPHPExcel->getActiveSheet()->setCellValue('C6', 'Course');
						$objPHPExcel->getActiveSheet()->getStyle('C6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('C6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('D6', 'Hospital Site');
						$objPHPExcel->getActiveSheet()->getStyle('D6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('D6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('E6', 'Start Date');
						$objPHPExcel->getActiveSheet()->getStyle('E6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('E6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                        
                        $objPHPExcel->getActiveSheet()->setCellValue('F6', 'Start Time');
						$objPHPExcel->getActiveSheet()->getStyle('F6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('F6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						$objPHPExcel->getActiveSheet()->getDefaultColumnDimension('F6')->setWidth('30');
						
						$objPHPExcel->getActiveSheet()->setCellValue('G6', 'End Date');
						$objPHPExcel->getActiveSheet()->getStyle('G6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('G6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('H6', 'End Time');
						$objPHPExcel->getActiveSheet()->getStyle('H6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('H6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('I6', 'Duration');
						$objPHPExcel->getActiveSheet()->getStyle('I6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('I6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->getStyle('B6:I6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
						
						$printStartRowCounter = 7;
                        if($totalRotations>0)
                        {
							   $styleArray = array('font'  => array('size'  => 10));
                               while($row = mysqli_fetch_array($rowsRotations))
                                    {
                                        $firstName =$row['firstName'];
                                        $rotationId =$row['rotationId'];
                                    
                                        $lastName =$row['lastName'];
                                        $Rankname =$row['Rankname'];
                                        $title =$row['title'];
                                        $startDateTime =$row['startDate'];
                                        
                                        $parentRotationId = $row['parentRotationId'];
                                        $rotationLocationId  = ($row['rotationLocationId']);
                                        $courselocationId = $row['locationId'];
                                    
                                        $locationId = 0;
                                        if($rotationLocationId != $courselocationId && $parentRotationId > 0)
                                        {
                                            if(!$rotationLocationId)
                                                $locationId = $objRotation->GetLocationByRotation($rotationId);
                                            else
                                                $locationId  = $rotationLocationId;
                                            
                                        }
                                        else
                                        {	
                                            $locationId  = $courselocationId;
                                        }
                                            
                                        //Get Time Zone By Rotation 
                                        $objLocation = new clsLocations();
                                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                                        unset($objLocation);
                                        
                                        if($TimeZone =='')
                                            $TimeZone= $_SESSION["loggedUserSchoolTimeZone"];

                                        $startDateTime = converFromServerTimeZone($startDateTime,$TimeZone);

                                        $startDate = date('m/d/Y', strtotime($startDateTime));
                                        $startTime = date('h:i A', strtotime($startDateTime));
                                        $endDateTime =$row['endDate'];
                                        $startDateTime = converFromServerTimeZone($endDateTime,$TimeZone);
                                        $endDate = date('m/d/Y', strtotime($endDateTime));
                                        $endTime = date('h:i A', strtotime($endDateTime));
                                        
                                        $duration =$row['duration'];
                                        $courseTitle =$row['courseTitle'];
                                        $hospitalSite =$row['hospitalSite'];
                                        
								
                                        $objPHPExcel->getActiveSheet()->setCellValue('B'.$printStartRowCounter,$title );
                                        $objPHPExcel->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                                        $objPHPExcel->getActiveSheet()->getStyle('B'.$printStartRowCounter)->applyFromArray($styleArray);
                                        
                                        $objPHPExcel->getActiveSheet()->setCellValue('C'.$printStartRowCounter,$courseTitle );
                                        $objPHPExcel->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                        $objPHPExcel->getActiveSheet()->getStyle('C'.$printStartRowCounter)->applyFromArray($styleArray);
                                                
                                        $objPHPExcel->getActiveSheet()->setCellValue('D'.$printStartRowCounter,$hospitalSite);
                                        $objPHPExcel->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                        $objPHPExcel->getActiveSheet()->getStyle('D'.$printStartRowCounter)->applyFromArray($styleArray);
                                        
                                        $objPHPExcel->getActiveSheet()->setCellValue('E'.$printStartRowCounter, $startDate);
                                        $objPHPExcel->getActiveSheet()->getStyle('E'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                        $objPHPExcel->getActiveSheet()->getStyle('E'.$printStartRowCounter)->applyFromArray($styleArray);
                                    
                                        $objPHPExcel->getActiveSheet()->setCellValue('F'.$printStartRowCounter,$startTime);
                                        $objPHPExcel->getActiveSheet()->getStyle('F'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                                        $objPHPExcel->getActiveSheet()->getStyle('F'.$printStartRowCounter)->applyFromArray($styleArray);
                                        
                                        $objPHPExcel->getActiveSheet()->setCellValue('G'.$printStartRowCounter, $endDate);
                                        $objPHPExcel->getActiveSheet()->getStyle('G'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                        $objPHPExcel->getActiveSheet()->getStyle('G'.$printStartRowCounter)->applyFromArray($styleArray);
                                                
                                        $objPHPExcel->getActiveSheet()->setCellValue('H'.$printStartRowCounter, $endTime);
                                        $objPHPExcel->getActiveSheet()->getStyle('H'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                        $objPHPExcel->getActiveSheet()->getStyle('H'.$printStartRowCounter)->applyFromArray($styleArray);
                                        
                                        $objPHPExcel->getActiveSheet()->setCellValue('I'.$printStartRowCounter, $duration);
                                        $objPHPExcel->getActiveSheet()->getStyle('I'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                        $objPHPExcel->getActiveSheet()->getStyle('I'.$printStartRowCounter)->applyFromArray($styleArray);
                                    
                                        $printStartRowCounter++;
                                        
                                         //Students

                                         $rowsRotationsDetails = $objRotationDetails->GetAllAssignStudentsDetails($rotationId);
                     
                                         
                                         $styleStudentColumn = array('font'  => array('bold'  => true,'size'  => 11));
                                         $objPHPExcel->getActiveSheet()->setCellValue('B'.$printStartRowCounter,'First Name');
                                         $objPHPExcel->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                                         $objPHPExcel->getActiveSheet()->getStyle('B'.$printStartRowCounter)->applyFromArray($styleStudentColumn);

                                         $objPHPExcel->getActiveSheet()->setCellValue('C'.$printStartRowCounter,'Last Name');
                                         $objPHPExcel->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                                         $objPHPExcel->getActiveSheet()->getStyle('C'.$printStartRowCounter)->applyFromArray($styleStudentColumn);

                                         $objPHPExcel->getActiveSheet()->setCellValue('D'.$printStartRowCounter,'Rank');
                                         $objPHPExcel->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                                         $objPHPExcel->getActiveSheet()->getStyle('D'.$printStartRowCounter)->applyFromArray($styleStudentColumn);
                                         
         
                                         $printStartRowCounter++;
                                         if($rowsRotationsDetails !='')
                                         {
                                            
                                             while($row = mysqli_fetch_array($rowsRotationsDetails))
                                             {
                                              
                                                

                                                 $firstName =$row['firstName'];
                                                 $lastName =$row['lastName'];
                                                 $Rankname =$row['Rankname'];
    
                                                 $objPHPExcel->getActiveSheet()->setCellValue('B'.$printStartRowCounter, $firstName);
                                                 $objPHPExcel->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                                                 
                                                 $objPHPExcel->getActiveSheet()->setCellValue('C'.$printStartRowCounter, $lastName);
                                                 $objPHPExcel->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
     
                                                 $objPHPExcel->getActiveSheet()->setCellValue('D'.$printStartRowCounter, $Rankname);
                                                 $objPHPExcel->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));

                                               
                                                 $printStartRowCounter++;
                                             }
                                             $printStartRowCounter++;
                                         }
                                         
                                         

                                    }
                                    unset($objRotation);
                                          
						}					  
						//Make Border
						$printStartRowCounter--;
						$styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));
						$objPHPExcel->getActiveSheet()->getStyle('B6:I6'.$printStartRowCounter)->applyFromArray($styleBorderArray);
						
						// Auto size columns for each worksheet
						foreach(range('B','I') as $columnID)
						{
							$objPHPExcel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
						}
						$reportname='StudentRotationAssignListReport_';
				break;
				
			default:	echo "<b>Please Select Valid Type.</b>";
						break;
			}
						$objPHPExcel->setActiveSheetIndex();
						
						$currentDate = date('m_d_Y_h_i');
						
						header('Content-type: application/vnd.ms-excel; charset=UTF-8');
						header('Content-Disposition: attachment;filename="'.$reportname.$today.'.xls"');		
						header("Pragma: no-cache");
						header("Expires: 0");
						
						$objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
						$objWriter->save('php://output');
?>