<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsFormative.php');
    include('../class/clsQuestionOption.php'); 
	include('../setRequest.php');
	

	 $studentFormativeMasterId=0;	 
	 if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
			$rotationId=0;
			$rotation=0;
			$formativerotationid=0;
			$schoolIncidentQuestionId='';
			$schoolIncidentQuestionType='';
			
			if(isset($_GET['studentFormativeMasterId'])) 
			{
				$studentFormativeMasterId = $_GET['studentFormativeMasterId'];
				$studentFormativeMasterId = DecodeQueryData($studentFormativeMasterId);
			}
			if(isset($_GET['formativerotationid'])) 
			{
					$formativerotationid = $_GET['formativerotationid'];
					$formativerotationid = DecodeQueryData($formativerotationid);
			
			}
		
			$studentFormativeMasterId = isset($_GET['studentFormativeMasterId']) ? DecodeQueryData($_GET['studentFormativeMasterId']) : 0;
			$status = ($studentFormativeMasterId > 0) ? 'updated' : 'added';
			if(isset($_GET['studentId'])) 
				{
					$student = $_GET['studentId'];
					$student = DecodeQueryData($student);		
				}
				else
				{ 
					$student=($_POST['cbostudent']);
				}
			$cboclinician  = ($_POST['cboclinician']);	
			$evaluationDate=GetDateStringInServerFormat($_POST['evaluationDate']);
			$evaluationDate = str_replace('00:00:00','12:00 PM',$evaluationDate);
			$evaluationDate = date('Y-m-d H:i',strtotime($evaluationDate));

			$studentsigniture  = ($_POST['studentsigniture']);
			$studentsignitureDate  = GetDateStringInServerFormat($_POST['studentsignitureDate']);
			$studentsignitureDate = str_replace('00:00:00','12:00 PM',$studentsignitureDate);
			$studentsignitureDate = date('Y-m-d H:i',strtotime($studentsignitureDate));
			
			$studentcomment  = ($_POST['studentcomment']);
			$rotation  = ($_POST['cborotation']);
		
			$objFormative = new clsFormative();
			$objFormative->rotationId =$formativerotationid ? $formativerotationid : $rotation;
			$objFormative->clinicanId =$cboclinician;				
			$objFormative->schoolId =$currentSchoolId;
			$objFormative->studentId =$student;
			$objFormative->evaluationDate = $evaluationDate;	
			$objFormative->studentSignature = $studentsigniture;	
			$objFormative->dateOfStudentSignature = $studentsignitureDate;	
			$objFormative->studentComment = $studentcomment;
			$objFormative->createdBy =$_SESSION["loggedUserId"];		
			$retformativeId = $objFormative->SaveAdminFormative($studentFormativeMasterId);	
			
			$objFormative->DeleteStudentFormativeDetails($retformativeId);
			
				foreach($_POST as $id=>$value)
			{
			
				if (strpos($id, 'questionoptions_') === 0) 
				{			
				$id = explode("_", $id)[1];
				
				$objFormative->studentFormativeMasterId = $retformativeId;
				$objFormative->schoolFormativeQuestionId = $id;		 		
				$objFormative->schoolFormativeOptionValue = $value[0];
				$objFormative->schoolFormativeOptionAnswerText ='';	
				$studentFormativeDetailId=$objFormative->SaveStudentFormativeDetail($retformativeId);
				}
			}
			foreach($_POST as $id=>$value)
			{
			
				if (strpos($id, 'questionoptionst_') === 0) 
				{			
				$id = explode("_", $id)[1];
				
				$objFormative->studentFormativeMasterId = $retformativeId;
				$objFormative->schoolFormativeQuestionId = $id;		 		
				$objFormative->schoolFormativeOptionValue ='';
				$objFormative->schoolFormativeOptionAnswerText =$value[0];	
				$studentFormativeDetailId=$objFormative->SaveStudentFormativeDetail($retformativeId);
				}
			}
			
			unset($objFormative);
			
			if($retformativeId > 0)
			{
				if(isset($_GET['studentId'])) 
				{
				header('location:formativelist.html?studentId='.EncodeQueryData($student).'&status='.$status);
				exit();
				}
				else
				{
				header('location:formativelist.html?studentFormativeMasterId='.EncodeQueryData($studentFormativeMasterId).'&formativerotationid='.EncodeQueryData($formativerotationid).'&status='.$status);	
				exit();
				}
			}
			else
			{
				header('location:formative.html?status=error');
			}
		 
	}
	{
		header('location:formativelist.html');
		exit();
	}	
?>