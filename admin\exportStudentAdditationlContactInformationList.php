<?php
	include('includes/validateUserLogin.php');
    include('../includes/config.php');
    include('../class/clsDB.php');
    include('../includes/commonfun.php');
    include('../class/clsAccreditation.php');	
    include('../class/clsStudent.php'); 
    include('../class/clsCountryStateMaster.php'); 
    include('../class/clsStudentadditationlContactInformation.php'); 
    include('../setRequest.php');
	require '../vendor/autoload.php';  // Include PhpSpreadsheet's autoloader

	use PhpOffice\PhpSpreadsheet\Spreadsheet;
	use PhpOffice\PhpSpreadsheet\IOFactory;

	
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnStudentExport']))		
	$accreditationId=0; 
	$currentSchoolId = $_GET['currentSchoolId'];
	$currentSchoolId = DecodeQueryData($currentSchoolId);
	$studentId = $_GET['studentId'];
	$studentId = DecodeQueryData($studentId);

	// Get Post Data
	$EmployerContactInfo= "Employer Contact Information";
		
	 $businessName  = ($_POST['txtbusinessName']);
	 $ContactFirstName  = ($_POST['txtContactFirstName']);
	 $ContactLastName  = ($_POST['txtContactLastName']);
	 $contactFullName=$ContactFirstName. ' ' . $ContactLastName;
	 $streetAddress  = ($_POST['txtstreetAddress']);
	 $suiteDeptFloor  = ($_POST['txtsuiteDeptFloor']);
	 $city  = ($_POST['txtCity']);
	 $stateId  =  ($_POST['cboState']);
	 $objCountryStateMaster = new clsCountryStateMaster();
	 $employerStateName = $objCountryStateMaster->GetSingleState($stateId);
	 unset($objCountryStateMaster);
		
	 $postalCode  = ($_POST['txtZipCode']);
	 $emailAddress  = ($_POST['txtEmail']);
	 $workPhone  = ($_POST['txtworkPhone']);
	 $cellPhone  = ($_POST['txtcellPhone']);
	 
	 $EmergencyContactInfo= "Emergency  Contact Information";

	 $emergencyStreetAddress  = ($_POST['txtemergencyStreetAddress']);
	 $emergencySuiteAptNoFloor  = ($_POST['txtemergencySuiteAptNoFloor']);
	 $emergencyCity  = ($_POST['txtemergencyCity']);
	 $emergencyPostalCode  = ($_POST['txtemergencyPostalCode']);
	 $emergencyEmailAddress  = ($_POST['txtemergencyEmailAddress']);
	 $emergencyWorkPhone  = ($_POST['txtemergencyWorkPhone']);
	 $emergencyCellPhone  = ($_POST['txtemergencyCellPhone']);
	 $emergencyContactsName  = ($_POST['txtemergencyContactsName']);
	 $emergencyState  = ($_POST['cboemergencyState']) ?($_POST['cboemergencyState']):'';
	 $objCountryStateMaster = new clsCountryStateMaster();
	 $emergencyStateName = $objCountryStateMaster->GetSingleState($emergencyState);
	 unset($objCountryStateMaster);
	
	 $OfficeContactInfo= "Office Contact Information";
	 
	 $officeBusinessName  = ($_POST['txtofficeBusinessName']);
	 $officeStreetAddress  = ($_POST['txtofficeStreetAddress']);
	 $officeCity  = ($_POST['txtofficeCity']);
	 $officeState  = ($_POST['cboofficeState']);
	 $objCountryStateMaster = new clsCountryStateMaster();
	 $officeStateName = $objCountryStateMaster->GetSingleState($officeState);
	 unset($objCountryStateMaster);
	 
	 $officePostalCode  = ($_POST['txtofficePostalCode']);
	 $officePhone  = ($_POST['txtofficePhone']);
	 $officeFax  = ($_POST['txtofficeFax']);
	 $officeSuiteDeptFloor  = ($_POST['txtofficeSuiteDeptFloor']);
	 $officeWebsite  = ($_POST['txtofficeWebsite']);
	 
	 $PermanentContactInfo= "Permanent Mailing Address";
	 
	 $permanentStreetAddress  = ($_POST['txtpermanentStreetAddress']);
	 $permanentCity  = ($_POST['txtpermanentCity']);
	 $permanentState  = ($_POST['cbopermanentState']);
	 $objCountryStateMaster = new clsCountryStateMaster();
	 $permanentStateName = $objCountryStateMaster->GetSingleState($permanentState);
	 unset($objCountryStateMaster);
	 
	 $permanentPostalCode  = ($_POST['txtpermanentPostalCode']);
	 $permanentEmailAddress  = ($_POST['txtpermanentEmailAddress']);
	 $permanentWorkPhone  = ($_POST['txtpermanentWorkPhone']);
	 $permanentCellPhone  = ($_POST['txtpermanentCellPhone']);
	 $permanentSuiteAptNoFloor  = ($_POST['txtpermanentSuiteAptNoFloor']);
	 $ContactsRelationshipId  = ($_POST['cbopermanentContactsRelationship']);
	
	// for contacts relationship master
	$objContactInformation = new clsStudentadditationlContactInformation();
	$contactsrelationshipName = $objContactInformation->GetSingleContactsRelationshipName($ContactsRelationshipId);
	unset($objContactInformation);
		
	// Object
	$objStudent = new clsStudent();
	$rowStudent = $objStudent->GetStudentDetails($studentId);
	unset($objStudent);
	$firstName=$rowStudent['firstName'];
	$lastName=$rowStudent['lastName'];
	$fullName=$firstName.' '.$lastName;
	// echo '<pre>'; 
	// print_r($_POST);
	// echo '</pre>'; 
	// exit;
	$title="Student Additationl Contact Information - $fullName";
	date_default_timezone_set('Asia/Kolkata');
	$today= (date('m/d/Y, H:i A'));
				
	$spreadsheet = new Spreadsheet();
	
	// Set document properties
	$spreadsheet->getProperties()->setCreator('Schools')
								 ->setLastModifiedBy('JCC')
								 ->setTitle('Reports')
								 ->setSubject('Student Accreditation List')
								 ->setDescription('All School Reports');
								 
	//Active Sheet
	$spreadsheet->setActiveSheetIndex(0);
	$spreadsheet->getActiveSheet();				
	
	//Print Heading	
	$headerstyleArray = array('font'  => array('bold'  => true,'size'  => 16));
	
	$spreadsheet->getActiveSheet()->mergeCells("B2:K2");
	$spreadsheet->getActiveSheet()->setCellValue('B2', $title);
	$spreadsheet->getActiveSheet()->getStyle('B2')->applyFromArray($headerstyleArray);
	$spreadsheet->getActiveSheet()->getStyle('B2')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('B2')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
		
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B2:K6')->applyFromArray($styleBorderArray);
	
	$styleArray = array('font'  => array('bold'  => true,'size'  => 12));
	$spreadsheet->getActiveSheet()->mergeCells("B3:K3");
	$spreadsheet->getActiveSheet()->mergeCells("B4:K4");
	$spreadsheet->getActiveSheet()->setCellValue('B4',$EmployerContactInfo);
	$spreadsheet->getActiveSheet()->getStyle('B4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('B4')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
				
	$styleBorderArray =['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B4:K6')->applyFromArray($styleBorderArray);
	$spreadsheet->getActiveSheet()->mergeCells("B5:K5");
	//Make Table Heading
	$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
	
	$spreadsheet->getActiveSheet()->setCellValue('B6', 'Business Name');
	$spreadsheet->getActiveSheet()->getStyle('B6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B6')->setWidth('30');
	
	$spreadsheet->getActiveSheet()->setCellValue('C6', 'Contact Name');
	$spreadsheet->getActiveSheet()->getStyle('C6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('C6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('D6', 'Street Address');
	$spreadsheet->getActiveSheet()->getStyle('D6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('D6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('E6', 'Suite/Dept/Floor');
	$spreadsheet->getActiveSheet()->getStyle('E6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('E6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('F6', 'City');
	$spreadsheet->getActiveSheet()->getStyle('F6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('F6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('G6', 'State');
	$spreadsheet->getActiveSheet()->getStyle('G6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('G6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('H6', 'Postal Code');
	$spreadsheet->getActiveSheet()->getStyle('H6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('H6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('I6', 'Email');
	$spreadsheet->getActiveSheet()->getStyle('I6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('I6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('J6', 'Work Phone');
	$spreadsheet->getActiveSheet()->getStyle('J6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('J6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('K6', 'Cell Phone');
	$spreadsheet->getActiveSheet()->getStyle('K6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('K6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->getStyle('B6:K6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
	
	$printStartRowCounter = 7;
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B7:K7')->applyFromArray($styleBorderArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('B'.$printStartRowCounter, $businessName);
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('C'.$printStartRowCounter, $contactFullName);
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('D'.$printStartRowCounter, $streetAddress);
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('E'.$printStartRowCounter, $suiteDeptFloor);
	$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter)->applyFromArray($styleArray);
			
	$spreadsheet->getActiveSheet()->setCellValue('F'.$printStartRowCounter, $city);
	$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('G'.$printStartRowCounter, $employerStateName);
	$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('H'.$printStartRowCounter, $postalCode);
	$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('I'.$printStartRowCounter, $emailAddress);
	$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter)->applyFromArray($styleArray);

	$spreadsheet->getActiveSheet()->setCellValue('J'.$printStartRowCounter, $workPhone);
	$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('K'.$printStartRowCounter, $cellPhone);
	$spreadsheet->getActiveSheet()->getStyle('K'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('K'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->mergeCells("B8:J8");
	// Emergency Contact Info
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B9:J9')->applyFromArray($styleBorderArray);
	
	$styleArray = array('font'  => array('bold'  => true,'size'  => 12));
	
	$spreadsheet->getActiveSheet()->mergeCells("B9:J9");
	$spreadsheet->getActiveSheet()->setCellValue('B9',$EmergencyContactInfo);
	$spreadsheet->getActiveSheet()->getStyle('B9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B9')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('B9')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
				 
	//Make Table Heading
	$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
	$spreadsheet->getActiveSheet()->mergeCells("B10:J10");
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B11:J11')->applyFromArray($styleBorderArray);
				 
	$spreadsheet->getActiveSheet()->setCellValue('B11', 'Street Address');
	$spreadsheet->getActiveSheet()->getStyle('B11')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B11')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B11')->setWidth('30');
	
	$spreadsheet->getActiveSheet()->setCellValue('C11', 'Suite/Dept/Floor');
	$spreadsheet->getActiveSheet()->getStyle('C11')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('C11')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('D11', 'City');
	$spreadsheet->getActiveSheet()->getStyle('D11')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('D11')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('E11', 'State');
	$spreadsheet->getActiveSheet()->getStyle('E11')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('E11')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('F11', 'Postal Code');
	$spreadsheet->getActiveSheet()->getStyle('F11')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('F11')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('G11', 'Email');
	$spreadsheet->getActiveSheet()->getStyle('G11')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('G11')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('H11', 'Work Phone');
	$spreadsheet->getActiveSheet()->getStyle('H11')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('H11')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('I11', 'Cell Phone');
	$spreadsheet->getActiveSheet()->getStyle('I11')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('I11')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('J11', 'Contact Name');
	$spreadsheet->getActiveSheet()->getStyle('J11')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('J11')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	
	$spreadsheet->getActiveSheet()->getStyle('B11:J11')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
	
	$printStartRowCounter1 = 12;
	
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B12:J12')->applyFromArray($styleBorderArray);
	$spreadsheet->getActiveSheet()->mergeCells("B13:K13");
	$spreadsheet->getActiveSheet()->setCellValue('B'.$printStartRowCounter1, $emergencyStreetAddress);
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('C'.$printStartRowCounter1, $emergencySuiteAptNoFloor);
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('D'.$printStartRowCounter1, $emergencyCity);
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('E'.$printStartRowCounter1, $officeStateName);
	$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter1)->applyFromArray($styleArray);
			
	$spreadsheet->getActiveSheet()->setCellValue('F'.$printStartRowCounter1, $emergencyPostalCode);
	$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('G'.$printStartRowCounter1, $emergencyEmailAddress);
	$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('H'.$printStartRowCounter1, $emergencyWorkPhone);
	$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('I'.$printStartRowCounter1, $emergencyCellPhone);
	$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter1)->applyFromArray($styleArray);

	$spreadsheet->getActiveSheet()->setCellValue('J'.$printStartRowCounter1, $emergencyContactsName);
	$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter1)->applyFromArray($styleArray);

	
	// Office Contact Info
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B14:J14')->applyFromArray($styleBorderArray);
	
	$styleArray = array('font'  => array('bold'  => true,'size'  => 12));
	
	$spreadsheet->getActiveSheet()->mergeCells("B14:J14");
	$spreadsheet->getActiveSheet()->setCellValue('B14',$OfficeContactInfo);
	$spreadsheet->getActiveSheet()->getStyle('B14')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B14')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('B14')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
				 
	//Make Table Heading
	$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
	$spreadsheet->getActiveSheet()->mergeCells("B15:K15");
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B16:J16')->applyFromArray($styleBorderArray);
				 
	$spreadsheet->getActiveSheet()->setCellValue('B16', 'Business Name');
	$spreadsheet->getActiveSheet()->getStyle('B16')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B16')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B16')->setWidth('30');
	
	$spreadsheet->getActiveSheet()->setCellValue('C16', 'Street Address');
	$spreadsheet->getActiveSheet()->getStyle('C16')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('C16')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('D16', 'Suite/Dept/Floor');
	$spreadsheet->getActiveSheet()->getStyle('D16')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('D16')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('E16', 'City');
	$spreadsheet->getActiveSheet()->getStyle('E16')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('E16')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('F16', 'State');
	$spreadsheet->getActiveSheet()->getStyle('F16')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('F16')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('G16', 'Postal Code');
	$spreadsheet->getActiveSheet()->getStyle('G16')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('G16')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('H16', 'Office Phone');
	$spreadsheet->getActiveSheet()->getStyle('H16')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('H16')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('I16', 'Fax');
	$spreadsheet->getActiveSheet()->getStyle('I16')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('I16')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('J16', 'Website');
	$spreadsheet->getActiveSheet()->getStyle('J16')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('J16')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->getStyle('B16:J16')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
	
	$printStartRowCounter1 = 17;
	
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B17:J17')->applyFromArray($styleBorderArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('B'.$printStartRowCounter1, $officeBusinessName);
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('C'.$printStartRowCounter1, $officeStreetAddress);
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('D'.$printStartRowCounter1, $officeSuiteDeptFloor);
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('E'.$printStartRowCounter1, $officeCity);
	$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter1)->applyFromArray($styleArray);
			
	$spreadsheet->getActiveSheet()->setCellValue('F'.$printStartRowCounter1, $emergencyStateName);
	$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('G'.$printStartRowCounter1, $officePostalCode);
	$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('H'.$printStartRowCounter1, $officePhone);
	$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('I'.$printStartRowCounter1, $officeFax);
	$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter1)->applyFromArray($styleArray);

	$spreadsheet->getActiveSheet()->setCellValue('J'.$printStartRowCounter1, $officeWebsite);
	$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->mergeCells("B18:J18");
	
	// Permanent Mailing Address
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B19:J19')->applyFromArray($styleBorderArray);
	
	$styleArray = array('font'  => array('bold'  => true,'size'  => 12));
	
	
	$spreadsheet->getActiveSheet()->setCellValue('B19',$PermanentContactInfo);
	$spreadsheet->getActiveSheet()->getStyle('B19')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B19')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('B19')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
				 
	//Make Table Heading
	$spreadsheet->getActiveSheet()->mergeCells("B19:J19");
	$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
	$spreadsheet->getActiveSheet()->mergeCells("B20:J20");
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B20:J20')->applyFromArray($styleBorderArray);
	
	// $styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));
	$spreadsheet->getActiveSheet()->getStyle('B21:J21')->applyFromArray($styleBorderArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('B21', 'Street Address');
	$spreadsheet->getActiveSheet()->getStyle('B21')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B21')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B21')->setWidth('30');
	
	$spreadsheet->getActiveSheet()->setCellValue('C21', 'Suite/Dept/Floor');
	$spreadsheet->getActiveSheet()->getStyle('C21')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('C21')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('D21', 'City');
	$spreadsheet->getActiveSheet()->getStyle('D21')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('D21')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('E21', 'State');
	$spreadsheet->getActiveSheet()->getStyle('E21')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('E21')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('F21', 'Postal Code');
	$spreadsheet->getActiveSheet()->getStyle('F21')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('F21')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('G21', 'Email');
	$spreadsheet->getActiveSheet()->getStyle('G21')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('G21')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('H21', 'Work Phone');
	$spreadsheet->getActiveSheet()->getStyle('H21')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('H21')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('I21', 'Cell Phone');
	$spreadsheet->getActiveSheet()->getStyle('I21')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('I21')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('J21', 'Contact Relationship');
	$spreadsheet->getActiveSheet()->getStyle('J21')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('J21')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->getStyle('B21:J21')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
	
	$printStartRowCounter1 = 22;
	
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B22:J22')->applyFromArray($styleBorderArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('B'.$printStartRowCounter1, $permanentStreetAddress);
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('C'.$printStartRowCounter1, $permanentSuiteAptNoFloor);
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('D'.$printStartRowCounter1, $permanentCity);
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('E'.$printStartRowCounter1, $permanentStateName);
	$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter1)->applyFromArray($styleArray);
			
	$spreadsheet->getActiveSheet()->setCellValue('F'.$printStartRowCounter1, $permanentPostalCode);
	$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('G'.$printStartRowCounter1, $permanentEmailAddress);
	$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('H'.$printStartRowCounter1, $permanentWorkPhone);
	$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('I'.$printStartRowCounter1, $permanentCellPhone);
	$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter1)->applyFromArray($styleArray);

	$spreadsheet->getActiveSheet()->setCellValue('J'.$printStartRowCounter1, $contactsrelationshipName);
	$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter1)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter1)->applyFromArray($styleArray);
	
	$reportname='StudentAdditationlContactInformation Report_';	
	
	$spreadsheet->setActiveSheetIndex(0);
	
	$currentDate = date('m_d_Y_h_i');
	
	foreach ($spreadsheet->getAllSheets() as $sheet) 
	{
		for ($col = 0; $col <= \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($sheet->getHighestDataColumn()); $col++) {
			$sheet->getColumnDimensionByColumn($col)->setAutoSize(true);
		}
	}	
	$sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows

	header('Content-type: application/vnd.ms-excel; charset=UTF-8');
	header('Content-Disposition: attachment;filename="'.$reportname.$today.'.xls"');		
	header("Pragma: no-cache");
	header("Expires: 0");
	
	$writer = IOFactory::createWriter($spreadsheet, 'Xls');
    $writer->save('php://output');
?>