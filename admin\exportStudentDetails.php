<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsAttendance.php');
include('../class/clscheckoff.php');
include('../class/clsProcedureCount.php');
include('../class/clsInteraction.php');
include('../class/clsMidterm.php');
include('../class/clsSummative.php');
include('../class/clsFormative.php');

include('../setRequest.php');
require '../vendor/autoload.php';  // Include PhpSpreadsheet's autoloader

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;

$start = '';
$length = '';
$searchExpression = '';
$orderByColumn = '';
$orderByColumnDir = '';
$AttendaceTitle = 'Attendance';
$rotationId = 0;
$courseId = 0;
$isActiveCheckoff = 0;
$currentSchoolId = $_GET['currentSchoolId'];
$currentSchoolId = DecodeQueryData($currentSchoolId);
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$studentId = $_GET['studentId'];
$studentId = DecodeQueryData($studentId);

if (isset($_GET['rotationId'])) {
	$rotationId = DecodeQueryData($_GET['rotationId']);
}

if (isset($_GET['courseId'])) {
	$courseId = DecodeQueryData($_GET['courseId']);
}

$objAttendance = new clsAttendance();

$rowsAttendance = $objAttendance->GetServerSideStudentAttendanceDetailsForreport($currentSchoolId, $studentId, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir, $rotationId, $courseId);


$title = 'Student Detail Report';
date_default_timezone_set('Asia/Kolkata');
$today = (date('m/d/Y, H:i A'));
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnStudentExport']))

	$spreadsheet = new Spreadsheet();

// Set document properties
$spreadsheet->getProperties()->setCreator('Schools')
	->setLastModifiedBy('JCC')
	->setTitle('Reports')
	->setSubject('Student Immunization List')
	->setDescription('All School Reports');

//Active Sheet
$spreadsheet->setActiveSheetIndex(0);
// $spreadsheet->getActiveSheet()->setTitle();

//Print Heading	
$headerstyleArray = array('font'  => array('bold'  => true, 'size'  => 16));

$spreadsheet->getActiveSheet()->mergeCells("B2:H2");
$spreadsheet->getActiveSheet()->setCellValue('B2', $title);
$spreadsheet->getActiveSheet()->getStyle('B2')->applyFromArray($headerstyleArray);
$spreadsheet->getActiveSheet()->getStyle('B2')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()
	->getStyle('B2')
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B2:H2')->applyFromArray($styleBorderArray);


$styleArray = array('font'  => array('bold'  => true, 'size'  => 12));

$spreadsheet->getActiveSheet()->mergeCells("B4:H4");
$spreadsheet->getActiveSheet()->setCellValue('B4', $AttendaceTitle);
$spreadsheet->getActiveSheet()->getStyle('B4')->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()
	->getStyle('B4')
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B4:H4')->applyFromArray($styleBorderArray);

//Make Table Heading
$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

$spreadsheet->getActiveSheet()->setCellValue('B6', 'Rotation');
$spreadsheet->getActiveSheet()->getStyle('B6')->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B6')->setWidth('30');


$spreadsheet->getActiveSheet()->setCellValue('C6', 'Clock In Time');
$spreadsheet->getActiveSheet()->getStyle('C6')->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('C6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('D6', 'Clock Out Time');
$spreadsheet->getActiveSheet()->getStyle('D6')->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('D6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('E6', 'Approved');
$spreadsheet->getActiveSheet()->getStyle('E6')->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('E6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('F6', 'Approved By');
$spreadsheet->getActiveSheet()->getStyle('F6')->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('F6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('G6', 'Original Hours');
$spreadsheet->getActiveSheet()->getStyle('G6')->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('G6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('H6', 'Approved Hours');
$spreadsheet->getActiveSheet()->getStyle('H6')->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('H6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));


$spreadsheet->getActiveSheet()->getStyle('B6:H6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');


$printStartRowCounter = 7;
if ($rowsAttendance) {

	$styleArray = array('font'  => array('size'  => 10));
	while ($row = mysqli_fetch_array($rowsAttendance)) {
		$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
		$school = stripslashes($row['school']);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$rank = stripslashes($row['rank']);
		$clockInDateTime = stripslashes($row['clockInDateTime']);
		$clockInDateTime = converFromServerTimeZone($clockInDateTime, $TimeZone);
		$clockOutDateTime = stripslashes($row['clockOutDateTime']);
		$clockOutDateTime = converFromServerTimeZone($clockOutDateTime, $TimeZone);
		$rotationname = stripslashes($row['title']);
		$approved = stripslashes($row['status']);
		if ($approved == 1) {
			$approved = 'Yes';
		} else {
			$approved = 'No';
		}
		$approvedby = stripslashes($row['adminname']);
		$orignalhours = stripslashes($row['orignalhours']);
		$approvedhours = stripslashes($row['approvedhours']);



		$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $rotationname);
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, $clockInDateTime);
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, $clockOutDateTime);
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, $approved);
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, $approvedby);
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, $orignalhours);
		$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, $approvedhours);
		$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);




		$printStartRowCounter++;
	}
}

//Make Border

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B6:H6' . $printStartRowCounter)->applyFromArray($styleBorderArray);

$printStartRowCounter++;

$printStartRowCounter++;

//FOR Checkoff

$styleArray = array('font'  => array('bold'  => true, 'size'  => 12));

$spreadsheet->getActiveSheet()->mergeCells('B' . $printStartRowCounter . ':J' . $printStartRowCounter);
$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Checkoff');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':J' . $printStartRowCounter)->applyFromArray($styleBorderArray);

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':J' . $printStartRowCounter)->applyFromArray($styleBorderArray);

$printStartRowCounter++;
$printStartRowCounter++;
//Make Table Heading
$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Rotation');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B' . $printStartRowCounter)->setWidth('30');

$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, 'Procedure Code');
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, 'Topic');
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, 'Evaluator');
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, 'Checkoff Date');
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

if ($isActiveCheckoff == 0) {

	$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, 'Stud');
	$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

	$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, 'Precp');
	$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

	$spreadsheet->getActiveSheet()->setCellValue('I' . $printStartRowCounter, 'Lab');
	$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

	$spreadsheet->getActiveSheet()->setCellValue('J' . $printStartRowCounter, 'Clinical');
	$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
} else {
	$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, 'Yes');
	$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

	$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, 'No');
	$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

	$spreadsheet->getActiveSheet()->setCellValue('I' . $printStartRowCounter, 'Comments');
	$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

	$spreadsheet->getActiveSheet()->setCellValue('J' . $printStartRowCounter, 'Score');
	$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
}

$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':J' . $printStartRowCounter)->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
$objcheckoff = new clscheckoff();

$rowscheckoffFilter = $objcheckoff->GetServerSideStudentCheckoff($currentSchoolId, $studentId, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir, $rotationId, $courseId);
$printStartRowCounter++;
if ($rowscheckoffFilter) {
	while ($row = mysqli_fetch_array($rowscheckoffFilter)) {
		$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

		$checkoffId = stripslashes($row['checkoffId']);
		$checkoffTitleId = stripslashes($row['checkoffTitleId']);
		$rotationname = stripslashes($row['rotationname']);
		$schooltitle = stripslashes($row['schooltitle']);
		$fullname = stripslashes($row['fullname']);
		$checkoffDateTime = stripslashes($row['checkoffDateTime']);
		$checkoffDateTime = converFromServerTimeZone($checkoffDateTime, $TimeZone);
		$checkoffDateTime = date('m/d/Y', strtotime($checkoffDateTime));

		//For checkoff score
		//$Score=$objcheckoff->GetCHeckoffStudentScore($checkoffId);

		if ($isActiveCheckoff == 1) {
			$standardScore = $row['calculatedScore'];
			$Score = number_format((float)$standardScore, 2, '.', '');
		} else {
			$usafScore = $row['calculatedUsafScore'];
			$Score = number_format((float)$usafScore, 2, '.', '');
		}
		//Get All Count
		$GetYesCheckoffCount = $objcheckoff->GetYesCountForCheckoffQuestion($checkoffId);

		//For Selected Preceptor Questions
		//$selectedPreceptor=$objcheckoff->GetSelectedPreceptorQuestions($row["checkoffId"]);
		$selectedPreceptor = stripslashes($row['calculatedSelectedPreceptor']);
		if ($selectedPreceptor > 0) {
			$selectedPreceptor = $selectedPreceptor * 20;
		}

		//For Lab Selected Questions
		//$selectedLab=$objcheckoff->GetSelectedLabQuestions($row["checkoffId"]);
		$selectedLab = $row['calculatedSelectedLab'];
		if ($selectedLab > 0)
			$selectedLab = 1;
		else
			$selectedLab = 0;

		//For Clinical Selected Questions
		//$selectedClinical=$objcheckoff->GetSelectedClinicalQuestions($row["checkoffId"]);
		$selectedClinical = $row['calculatedSelectedClinical'];
		if ($selectedClinical > 0)
			$selectedClinical = 1;
		else
			$selectedClinical = 0;

		//For Student Selected Questions
		//$selectedStudent=$objcheckoff->GetSelectedStudentQuestions($row["checkoffId"]);
		$selectedStudent = $row['calculatedSelectedStudentQuestions'];
		if ($selectedStudent > 0)
			$selectedStudent = 1;
		else
			$selectedStudent = 0;

		$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $rotationname);
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, $checkoffTitleId);
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, $schooltitle);
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, $fullname);
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, $checkoffDateTime);
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);

		$printStartRowCounter++;
		if ($isActiveCheckoff == 0) {
			$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, $selectedStudent);
			$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
			$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);

			$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, $selectedPreceptor);
			$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
			$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);

			$spreadsheet->getActiveSheet()->setCellValue('I' . $printStartRowCounter, $selectedLab);
			$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
			$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->applyFromArray($styleArray);

			$spreadsheet->getActiveSheet()->setCellValue('J' . $printStartRowCounter, $selectedClinical);
			$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->applyFromArray($styleArray);
		} else {
			while ($row = mysqli_fetch_array($GetYesCheckoffCount)) {

				$YesCount = $row['YesCount'];
				$NoCount = $row['NoCount'];
				$checkoffComment = $row['Comments'];

				$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, $YesCount);
				$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);

				$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, $NoCount);
				$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);

				$spreadsheet->getActiveSheet()->setCellValue('I' . $printStartRowCounter, $checkoffComment);
				$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
				$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->applyFromArray($styleArray);

				$spreadsheet->getActiveSheet()->setCellValue('J' . $printStartRowCounter, $Score);
				$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
				$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->applyFromArray($styleArray);

				$printStartRowCounter++;
			}
		}

		$printStartRowCounter--;
	}
}


$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':J' . $printStartRowCounter)->applyFromArray($styleBorderArray);


// Auto size columns for each worksheet
foreach (range('B' . $printStartRowCounter, 'H' . $printStartRowCounter) as $columnID) {
	$spreadsheet->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
}

$printStartRowCounter++;

$printStartRowCounter++;

//FOR Procedure Count 

$styleArray = array('font'  => array('bold'  => true, 'size'  => 12));

$spreadsheet->getActiveSheet()->mergeCells('B' . $printStartRowCounter . ':I' . $printStartRowCounter);
$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Procedure Count');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':I' . $printStartRowCounter)->applyFromArray($styleBorderArray);

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':I' . $printStartRowCounter)->applyFromArray($styleBorderArray);

$printStartRowCounter++;

$printStartRowCounter++;
//Make Table Heading
$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Procedure');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B' . $printStartRowCounter)->setWidth('30');

$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, 'Asstd');
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, 'Obsvd');
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, 'Prfmd');
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, 'Prfmd');
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, 'Obsvd Total');
$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, 'Prfmd Total');
$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('I' . $printStartRowCounter, 'Total');
$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));


$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':I' . $printStartRowCounter)->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
$objProcedureCount = new clsProcedureCount();

$rowsProcedureCountFilter = $objProcedureCount->GetServerSideStudentProcedureCount($studentId, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir, $isActiveCheckoff);
$printStartRowCounter++;
if ($rowsProcedureCountFilter) {
	while ($row = mysqli_fetch_array($rowsProcedureCountFilter)) {

		$procedures = stripslashes($row['advanceProcedures']);
		$proceduteCountTopicId = stripslashes($row['proceduteCountTopicId']);
		$totalCount = $objProcedureCount->GetTotalCountByProcedureId($studentId, $proceduteCountTopicId);
		$procedurePointsAssist = stripslashes($totalCount['totalProcedurePointsAssist']);
		$procedurePointsObserve = stripslashes($totalCount['totalProcedurePointsObserve']);
		$procedurePointsPerform = stripslashes($totalCount['totalProcedurePointsPerform']);
		$procedurePointsPerformTotal = stripslashes($totalCount['totalProcedurePointsPerformTotal']);
		$procedureTotal = stripslashes($totalCount['totalProcedureTotal']);

		$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $procedures);
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, $procedurePointsAssist);
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, $procedurePointsObserve);
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, $procedurePointsPerform);
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, $procedurePointsAssist);
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, $procedurePointsObserve);
		$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, $procedurePointsPerformTotal);
		$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('I' . $printStartRowCounter, $procedureTotal);
		$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->applyFromArray($styleArray);

		$printStartRowCounter++;
	}
}

//FOR Dr. Interaction 

$printStartRowCounter++;

$styleArray = array('font'  => array('bold'  => true, 'size'  => 12));

$spreadsheet->getActiveSheet()->mergeCells('B' . $printStartRowCounter . ':K' . $printStartRowCounter);
$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Dr. Interaction');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':K' . $printStartRowCounter)->applyFromArray($styleBorderArray);

$printStartRowCounter++;

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':K' . $printStartRowCounter)->applyFromArray($styleBorderArray);

$printStartRowCounter++;

//Make Table Heading
$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'First Name');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B' . $printStartRowCounter)->setWidth('30');

$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, 'Last Name');
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, 'Last Name');
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, 'Rotation');
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, 'Clinician');
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, 'Interaction Date');
$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, 'School Signed');
$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('I' . $printStartRowCounter, 'CI Signed');
$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('J' . $printStartRowCounter, 'Points');
$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('K' . $printStartRowCounter, 'Time Spent(In Minutes)');
$spreadsheet->getActiveSheet()->getStyle('K' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('K' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));


$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':K' . $printStartRowCounter)->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
$objInteraction = new clsInteraction();

$rowsInteraction = $objInteraction->GetSeverStudentInteractionDetailsForreport($currentSchoolId, $studentId, 0, 0, $searchExpression, $orderByColumn, $orderByColumnDir, $courseId, $rotationId);

$printStartRowCounter++;
if ($rowsInteraction) {
	while ($row = mysqli_fetch_array($rowsInteraction)) {
		$interactionId = stripslashes($row['interactionId']);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);

		$pointsAwarded = stripslashes($row['pointsAwarded']);
		$timeSpent = stripslashes($row['timeSpent']);
		$Rankname = stripslashes($row['rank']);
		$interactionDate = stripslashes($row['interactionDate']);

		$interactionSchoolDate = stripslashes($row['interactionSchoolDate']);
		$interactionSchoolDate = date('m/d/Y', strtotime($interactionSchoolDate));
		$interactionClinicianDate = stripslashes($row['interactionClinicianDate']);
		$interactionClinicianDate = date('m/d/Y', strtotime($interactionClinicianDate));
		$Rotationname = stripslashes($row['rotationname']);
		$clinicianfname = stripslashes($row['clinicianfname']);
		$clinicianlname = stripslashes($row['clinicianlname']);
		$clinicianfullname = $clinicianfname . ' ' . $clinicianlname;

		$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $firstName);
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, $lastName);
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, $Rankname);
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, $Rotationname);
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, $clinicianfullname);
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, $interactionDate);
		$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, $interactionSchoolDate);
		$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('I' . $printStartRowCounter, $interactionClinicianDate);
		$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('J' . $printStartRowCounter, $pointsAwarded);
		$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('K' . $printStartRowCounter, $timeSpent);
		$spreadsheet->getActiveSheet()->getStyle('K' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('K' . $printStartRowCounter)->applyFromArray($styleArray);

		$printStartRowCounter++;
	}
}

$printStartRowCounter++;

//FOR Clinical Site Unit

$printStartRowCounter++;
$styleArray = array('font'  => array('bold'  => true, 'size'  => 12));

$spreadsheet->getActiveSheet()->mergeCells('B' . $printStartRowCounter . ':H' . $printStartRowCounter);
$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Clinical Site Unit');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':H' . $printStartRowCounter)->applyFromArray($styleBorderArray);

$printStartRowCounter++;
$printStartRowCounter++;

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':H' . $printStartRowCounter)->applyFromArray($styleBorderArray);

//Make Table Heading
$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Interaction Date');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B' . $printStartRowCounter)->setWidth('30');

$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, 'Clinical Instructor');
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, 'Clinician Signature ');
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, 'School Signature');
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, 'Clinical Sites Unit ');
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, 'Time Spent');
$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, 'Points Awarded');
$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));


$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':H' . $printStartRowCounter)->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');

$objInteraction = new clsInteraction();

$rowsInteraction = $objInteraction->GetSeverStudentInteractionDetailsForreport($currentSchoolId, $studentId, 0, 0, $searchExpression, $orderByColumn, $orderByColumnDir, $courseId, $rotationId);

$printStartRowCounter++;
if ($rowsInteraction) {
	while ($row = mysqli_fetch_array($rowsInteraction)) {
		$interactionId = stripslashes($row['interactionId']);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);

		$pointsAwarded = stripslashes($row['pointsAwarded']);
		$timeSpent = stripslashes($row['timeSpent']);
		$Rankname = stripslashes($row['rank']);
		$interactionDate = stripslashes($row['interactionDate']);
		$interactionSchoolDate = stripslashes($row['interactionSchoolDate']);
		$interactionSchoolDate = date('m/d/Y', strtotime($interactionSchoolDate));
		$interactionClinicianDate = stripslashes($row['interactionClinicianDate']);
		$interactionClinicianDate = date('m/d/Y', strtotime($interactionClinicianDate));
		$Rotationname = stripslashes($row['rotationname']);
		$hospitalname = stripslashes($row['hospitalname']);
		$clinicianfname = stripslashes($row['clinicianfname']);
		$clinicianlname = stripslashes($row['clinicianlname']);
		$clinicianfullname = $clinicianfname . ' ' . $clinicianlname;

		$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $interactionDate);
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, $clinicianfullname);
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, $interactionClinicianDate);
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, $interactionSchoolDate);
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, $hospitalname);
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, $timeSpent);
		$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, $pointsAwarded);
		$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);


		$printStartRowCounter++;
	}
}

//FOR Midterm Evaluation

$printStartRowCounter++;
$styleArray = array('font'  => array('bold'  => true, 'size'  => 12));

$spreadsheet->getActiveSheet()->mergeCells('B' . $printStartRowCounter . ':J' . $printStartRowCounter);
$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Midterm Evaluation');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':J' . $printStartRowCounter)->applyFromArray($styleBorderArray);

$printStartRowCounter++;
$printStartRowCounter++;

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':J' . $printStartRowCounter)->applyFromArray($styleBorderArray);

//Make Table Heading
$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Signature');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B' . $printStartRowCounter)->setWidth('30');

$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, 'Rotation');
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, 'Evaluator');
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, 'Eval Date');
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, 'Absences');
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, 'Tardy');
$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, 'Succeeding');
$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('I' . $printStartRowCounter, 'Progressing');
$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('I' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('J' . $printStartRowCounter, 'Unsatisfactory');
$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('J' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));


$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':J' . $printStartRowCounter)->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');

$objMidterm = new clsMidterm();

$rowsMidterm = $objMidterm->GetSeverSideStudentMidtermForreport($currentSchoolId, $studentId, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir, $courseId, $rotationId);

$printStartRowCounter++;
if ($rowsMidterm) {
	while ($row = mysqli_fetch_array($rowsMidterm)) {
		$studentMidtermMasterId = $row['studentMidtermMasterId'];
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$rank = stripslashes($row['title']);
		$rotationname = stripslashes($row['rotationname']);
		$studentSignature = stripslashes($row['dateOfStudentSignature']);
		$studentSignature = date('m/d/Y', strtotime($studentSignature));
		$clinicianfname = stripslashes($row['clinicianfname']);
		$clinicianlname = stripslashes($row['clinicianlname']);
		$clinicianfullname = $clinicianfname . ' ' . $clinicianlname;
		$evaluationDate = stripslashes($row['evaluationDate']);

		$absence = stripslashes($row['absence']);
		$daysTardy = stripslashes($row['daysTardy']);


		$rowsMidtermSuccedingCount = $objMidterm->GetSucceedingCount($studentMidtermMasterId);
		if ($rowsMidtermSuccedingCount != '') {
			$totalMidtermSuccedingCount = mysqli_num_rows($rowsMidtermSuccedingCount);
		}

		$rowsMidtermProgressingCount = $objMidterm->GetProgressingCount($studentMidtermMasterId);
		if ($rowsMidtermProgressingCount != '') {
			$totalMidtermProgressingCount = mysqli_num_rows($rowsMidtermProgressingCount);
		}

		$rowsMidtermUnsatisfactoryCount = $objMidterm->GetUnsatisfactoryCount($studentMidtermMasterId);
		if ($rowsMidtermUnsatisfactoryCount != '') {
			$totalMidtermUnsatisfactoryCount = mysqli_num_rows($rowsMidtermUnsatisfactoryCount);
		}

		$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $studentSignature);
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, $rotationname);
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, $clinicianfullname);
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, $evaluationDate);
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, $absence);
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, $daysTardy);
		$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, $totalMidtermSuccedingCount);
		$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('G' . $printStartRowCounter, $totalMidtermProgressingCount);
		$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('H' . $printStartRowCounter, $totalMidtermUnsatisfactoryCount);
		$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);


		$printStartRowCounter++;
	}
}

//FOR Summative  Evaluation

$printStartRowCounter++;
$styleArray = array('font'  => array('bold'  => true, 'size'  => 12));

$spreadsheet->getActiveSheet()->mergeCells('B' . $printStartRowCounter . ':F' . $printStartRowCounter);
$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Summative  Evaluation');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':F' . $printStartRowCounter)->applyFromArray($styleBorderArray);

$printStartRowCounter++;
$printStartRowCounter++;

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':F' . $printStartRowCounter)->applyFromArray($styleBorderArray);

//Make Table Heading
$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Rotation');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B' . $printStartRowCounter)->setWidth('30');

$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, 'Eval Date');
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, 'Evaluator');
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, 'Phase');
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, 'Signature');
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));


$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':F' . $printStartRowCounter)->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');

$objSummative = new clsSummative();

$rowsSummative = $objSummative->GetServerSideStudentSummativeDetailsForreport($currentSchoolId, $studentId, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir, $courseId, $rotationId);

$printStartRowCounter++;
if ($rowsSummative) {
	while ($row = mysqli_fetch_array($rowsSummative)) {
		$studentSummativeMasterId = stripslashes($row['studentSummativeMasterId']);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$rank = stripslashes($row['title']);
		$rotationname = stripslashes($row['rotationname']);
		$studentSignature = stripslashes($row['dateOfStudentSignature']);
		$studentSignature = date('m/d/Y', strtotime($studentSignature));
		$clinicianfname = stripslashes($row['clinicianfname']);
		$clinicianlname = stripslashes($row['clinicianlname']);
		$clinicianfullname = $clinicianfname . ' ' . $clinicianlname;
		$evaluationDate = stripslashes($row['evaluationDate']);

		$getSummativeScore = $objSummative->GetSummativeScore($studentSummativeMasterId);
		$avgTotalScore = $getSummativeScore['EvaluationScore'];

		$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $rotationname);
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, $evaluationDate);
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, $clinicianfullname);
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, $avgTotalScore);
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, $studentSignature);
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);


		$printStartRowCounter++;
	}
}


//FOR Formative  Evaluation

$printStartRowCounter++;
$styleArray = array('font'  => array('bold'  => true, 'size'  => 12));

$spreadsheet->getActiveSheet()->mergeCells('B' . $printStartRowCounter . ':F' . $printStartRowCounter);
$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Formative  Evaluation');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':F' . $printStartRowCounter)->applyFromArray($styleBorderArray);

$printStartRowCounter++;
$printStartRowCounter++;

$spreadsheet->getActiveSheet()
	->getStyle('B' . $printStartRowCounter)
	->getFill()
	->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
	->getStartColor()
	->setRGB('E0E0E0');

$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':F' . $printStartRowCounter)->applyFromArray($styleBorderArray);

//Make Table Heading
$styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, 'Rotation');
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B' . $printStartRowCounter)->setWidth('30');

$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, 'Eval Date');
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, 'Evaluator');
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, 'Phase');
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));

$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, 'Signature');
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);
$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));


$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':F' . $printStartRowCounter)->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');

$objFormative = new clsFormative();

$rowsFormative = $objFormative->GetServerSideStudentFormativeDetailsForreport($currentSchoolId, $studentId, $start, $length, $searchExpression, $orderByColumn, $orderByColumnDir, $courseId, $rotationId);

$printStartRowCounter++;
if ($rowsFormative) {
	while ($row = mysqli_fetch_array($rowsFormative)) {
		$studentFormativeMasterId = stripslashes($row['studentFormativeMasterId']);
		$firstName = stripslashes($row['firstName']);
		$lastName = stripslashes($row['lastName']);
		$rank = stripslashes($row['title']);
		$rotationname = stripslashes($row['rotationname']);
		$studentSignature = stripslashes($row['dateOfStudentSignature']);
		$studentSignature = date('m/d/Y', strtotime($studentSignature));
		$clinicianfname = stripslashes($row['clinicianfname']);
		$clinicianlname = stripslashes($row['clinicianlname']);
		$clinicianfullname = $clinicianfname . ' ' . $clinicianlname;
		$evaluationDate = stripslashes($row['evaluationDate']);

		$getFormativeScore = $objFormative->GetFormativeScore($studentFormativeMasterId);
		$avgTotalScore = $getFormativeScore['FormativeScore'];

		$spreadsheet->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $rotationname);
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('C' . $printStartRowCounter, $evaluationDate);
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('D' . $printStartRowCounter, $clinicianfullname);
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('E' . $printStartRowCounter, $avgTotalScore);
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
		$spreadsheet->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);

		$spreadsheet->getActiveSheet()->setCellValue('F' . $printStartRowCounter, $studentSignature);
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
		$spreadsheet->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);


		$printStartRowCounter++;
	}
}



//Make Border
$printStartRowCounter--;
$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
$spreadsheet->getActiveSheet()->getStyle('B' . $printStartRowCounter . ':F' . $printStartRowCounter)->applyFromArray($styleBorderArray);


$reportname = 'StudentDetailReport_';

$spreadsheet->setActiveSheetIndex(0);


$currentDate = date('m_d_Y_h_i');

header('Content-type: application/vnd.ms-excel; charset=UTF-8');
header('Content-Disposition: attachment;filename="' . $reportname . $today . '.xls"');
header("Pragma: no-cache");
header("Expires: 0");

$objWriter = IOFactory::createWriter($spreadsheet, 'Xls');
$objWriter->save('php://output');
