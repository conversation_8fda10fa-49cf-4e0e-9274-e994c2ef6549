<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');       
    include('../class/clsCoarc.php');            
    include('../setRequest.php'); 

	
		
	$display_from_date= date('m/d/Y', strtotime('-15 days'));
    $display_to_date= date('m/d/Y');
    $currentDate=date("m/d/Y"); 
	$currentSchoolId;
    $studentId =0;
    $clinicianId=0;
    $coarcSurveyMasterId =0;
    
    $view='V';
    $loggedUserId = $_SESSION["loggedUserId"];   
    $TimeZone=$_SESSION["loggedUserSchoolTimeZone"];
    
	if(isset($_GET['coarcSurveyMasterId']))
	{
		$coarcSurveyMasterId=DecodeQueryData($_GET['coarcSurveyMasterId']);
    }
    
    //For Irr List
	$objCoarc = new clsCoarc();	
	$rowsCoarcData = $objCoarc->GetAllIRRAssignmentsDetails($currentSchoolId);
	$totalCoarcCount = 0;
	if($rowsCoarcData !='')
	{
		$totalCoarcCount = mysqli_num_rows($rowsCoarcData);
	}
	
	
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>JRCERT Survey List</title>
        <?php include('includes/headercss.php');?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
        <?php include("includes/datatablecss.php") ?>
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css"> 
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

	</head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Setting</a></li>						
						<li class="active">JRCERT Survey List</li>						
                    </ol>
                </div>
               <div class="pull-right"> 
					<ol class="breadcrumb">						
						<a href="assigncoarcsurvey.html">Add</a> 
                    </ol>
               </div>
            </div>
        </div>

        <div class="container">


            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="Added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>JRCERT Survey  added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> JRCERT Survey updated successfully.
                </div>
                <?php 
					}                   
                    else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
				<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
							
                           <th>Title</th>                           
                            <th>Students/Evaluators</th>                            
                            <th>Start</th>
                            <th>End</th>
                            <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalCoarcCount > 0)
                        {
                            while($row = mysqli_fetch_array($rowsCoarcData))
                            {								
                                $coarcSurveyMasterId = ($row['coarcSurveyMasterId']);					
                                $DetailcoarcSurveyMasterId = ($row['DetailcoarcSurveyMasterId']);					
                                $title = ($row['title']); 
                                $coarctype = ($row['coarctype']);       
                                $startDate = stripslashes($row['startDate']);
								$startDate = converFromServerTimeZone($startDate,$TimeZone);
                                $StartDate = date('m/d/Y',  strtotime($startDate));
								$endDate = stripslashes($row['endDate']); 
								$endDate = converFromServerTimeZone($endDate,$TimeZone);								 
                                $EndDate = date('m/d/Y',  strtotime($endDate));
								$firstName=stripslashes($row['firstName']);								
								$lastName=stripslashes($row['lastName']);
								
								$Studentcountrow=$objCoarc->GetStudentCount($coarcSurveyMasterId,$studentId);
                                $studentcount = stripslashes($Studentcountrow['studentId']);                           
                                     
                                //For Clinician Count
                                $clinicianCountrow=$objCoarc->GetClinicianCount($coarcSurveyMasterId,$clinicianId);
                                $clinicianCount = stripslashes($clinicianCountrow['clinicianId']); 
                               ?>
                            <tr>
								
								<td><?php echo ($title); ?></td>	
                                <?php if($coarctype == 2) { ?>
                                    <td class="text-center"><a title="View To Clinicians" href="clinicianCoarclist.html?coarcSurveyMasterId=<?php echo (EncodeQueryData($coarcSurveyMasterId)); ?>" class="studentdetails"><?php echo ($clinicianCount); ?></a></td>
                                <?php } else { ?>    						
								<td class="text-center"><a title="View To Student" href="studentlist.html?coarcSurveyMasterId=<?php echo (EncodeQueryData($coarcSurveyMasterId)); ?>" class="studentdetails"><?php echo ($studentcount); ?></a></td>
                                <?php } ?>
								<td><?php echo ($StartDate); ?></td>
								<td><?php echo ($EndDate); ?></td>   
                                <td style="text-align: center">
                                <a  href="assigncoarcsurvey.html?coarcSurveyMasterId=<?php echo EncodeQueryData($coarcSurveyMasterId); ?>&coarcSurveyTypeId=<?php echo $coarctype; ?>">Edit</a>
						 <?php if(isset($_SESSION["loggedAsBackUserId"])) {?>
						 |  <a href="javascript:void(0);" class="deleteAjaxRow" coarcSurveyMasterId="<?php echo EncodeQueryData($coarcSurveyMasterId); ?>" IrrName="<?php echo($title); ?>" coarctype="<?php echo($coarctype); ?>">Delete</a><?php }?>
                                </td>
                            </tr>
                            <?php
                            }
                        }
                        unset($objIrr);
                    ?>
					</tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
		<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
		<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
		<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
		<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>

        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

			$('.studentdetails').magnificPopup({'type': 'ajax',});	
			
            $(window).load(function(){
                
                $("#divTopLoading").addClass('hide');
				$(".select2_single").select2();
				
				
				
					$(function () {
							$("#fromDate").datetimepicker({
								format: "MM/DD/YYYY",
								defaultDate: moment().subtract(15, 'days'),
								useCurrent: false
							});
					});
					
					$(function () {
							$("#toDate").datetimepicker({
								format: "MM/DD/YYYY",
								defaultDate: moment(),
								useCurrent: false
							});
					});
				
				
            });
                 var current_datatable = $("#datatable-responsive").DataTable({
                    responsive: false,
                    scrollX: true,
                    "aaSorting": [],
                      "aoColumns": [{
                        "sWidth": "30%"
                    },{
                        "sWidth": "3%"
                    },{
                        "sWidth": "10%"
                    },{
                        "sWidth": "10%"
                    },{
                        "sWidth": "10%",
                        "sClass": "alignCenter",
                        "bSortable": false
                    } ]
					
				 });    

		//delete student
				$(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var coarcSurveyMasterId = $(this).attr('coarcSurveyMasterId');
                var IrrName = $(this).attr('IrrName');
                var coarctype = $(this).attr('coarctype');
                var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
                var isUser = 1; //for Admin

                alertify.confirm('JRCERT Survey: '+IrrName, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete_allsurvey.html",
                        data: {
                            id: coarcSurveyMasterId,
                            userId: userId,
                            isUser: isUser,
                            coarctype: coarctype
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });
			
			

        </script>
    </body>
    </html>