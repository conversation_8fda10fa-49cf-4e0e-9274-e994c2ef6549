<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include('../class/clsStudent.php');
include('../class/clsSystemUserRolePermission.php');
include('../class/clsStudentRankMaster.php');

$studentSearch = '';
$userPermissions = '';
$schoolsArray = $settingsArray = $headersArray = '';
$schools = $settings = $headers = '';
$studentSearch = isset($_GET['studentSearch']) ? $_GET['studentSearch'] : '';
$totalStudentCount = 0;
$schoolId = 0;




$type = isset($_GET['type']) ? $_GET['type'] : '';
$role = isset($_GET['role']) ? DecodeQueryData($_GET['role']) : '';
$checkoffType = isset($_GET['checkoffType']) ? DecodeQueryData($_GET['checkoffType']) : '';
$rotationType = isset($_GET['rotationType']) ? DecodeQueryData($_GET['rotationType']) : '';


//get rank 
$objStudentRank = new clsStudentRankMaster();
$rank = $objStudentRank->GetAllRanks();



$checkoffTypeList = array();
$roleList = array();
$rankList = array();
// Role list
if ($type == 'activeAdmins') {
    $roleList = array(
        'Administrator' => 'Administrator',
        'D.C.E.' => 'D.C.E.',
        'Coordinator' => 'Coordinator',
        'P.D.' => 'P.D.',
        'Preceptor' => 'Preceptor',
        'Others' => 'Others'
    );
} else if ($type == 'lockedUsers') {
    // Role list
    $roleList = array(
        'Administrator' => 'Administrator',
        'D.C.E.' => 'D.C.E.',
        'Coordinator' => 'Coordinator',
        'P.D.' => 'P.D.',
        'Preceptor' => 'Preceptor',
        'Clinician' => 'Clinician',
        'Student' => 'Student',
        'Others' => 'Others'
    );
} else if ($type == 'checkoffType') {
    $checkoffTypeList = array(
        '1' => 'A Type',
        '2' => 'AA Type',
        '3' => 'AAA Type',
        '0' => 'A & AA Type'
    );
} else if ($type == 'rotationsSchool') {
    $rotationTypeList = array(
        '1' => 'Rotations',
        '2' => 'Hospital Sites',
        '3' => 'Schedules'
    );
} else if ($type == 'selfUnlocked') {
    $roleList = array(
        'Student' => 'Student',
    );
}


$objStudent = new clsStudent();
// if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSearch'])) {
//     $studentSearch = $_POST['studentSearch'];

//     // Check if the user's role ID is available in the session
//     $loggedUserRoleId = $_SESSION['loggedUserRoleId'];

//     // Initialize a new instance of the clsSystemUserRolePermission class
//     $objSystemUserRolePermission = new clsSystemUserRolePermission();

//     // Get system user role permissions based on the user's role ID
//     $userPermissions = $objSystemUserRolePermission->GetSystemUserRolePermissions($loggedUserRoleId);

//     // Check if any user permissions were returned
//     if ($userPermissions != '') {
//         // Extract the user permissions for schools, headers, and settings
//         $schools = $userPermissions['schools'];
//         $headers = $userPermissions['headers'];
//         $settings = $userPermissions['settings'];

//         // Convert the user permissions for schools, headers, and settings into arrays
//         $schoolsArray = explode(",", $schools);
//         $headersArray = explode(",", $headers);
//         $settingsArray = explode(",", $settings);
//     }

//     $loggedUserId = $_SESSION["loggedUserId"];
//     $objDB = new clsDB();
//     $isPrimaryUser = 0;
//     $isPrimaryUser = $objDB->GetSingleColumnValueFromTable('systemusermaster', 'isPrimaryUser', 'systemUserMasterId', $loggedUserId);
//     unset($objDB);
//     //Get All Student List
//     if ($isPrimaryUser)
//         $rowsStudentData = $objStudent->GetStudentDetailForSuperAdmin($studentSearch);
//     else
//         $rowsStudentData = $objStudent->GetStudentDetailForSuperAdminBySchoolIds($studentSearch, $schools);

//     $totalStudentCount = 0;
//     if ($rowsStudentData != '') {
//         $totalStudentCount = mysqli_num_rows($rowsStudentData);
//     }
// }

// if (isset($_GET['studentSearch'])) {
//     //Get All Student List
//     $rowsStudentData = $objStudent->GetStudentDetailForSuperAdmin($studentSearch);
//     $totalStudentCount = 0;
//     if ($rowsStudentData != '') {
//         $totalStudentCount = mysqli_num_rows($rowsStudentData);
//     }
// }

unset($objStudent);

$objSchool = new clsSchool();
$rowsSchool = $objSchool->GetAllSchools(1);
unset($objSchool);




?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>User Details</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <style>
        .text-center {
            text-align: center;
        }

        #datatable-responsive_wrapper {
            overflow-x: auto;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active">Details</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if (isset($_GET["status"])) {
            if ($_GET["status"] == "copy") {
                ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span
                            aria-hidden="true">×</span>
                    </button>Student Copy successfully.
                </div>
                <?php
            }
        }
        ?>
        <div id="divTopLoading">Loading...</div>
        <div class="row">
            <div class="col-md-12">
                <h4 id="heading"></h4>
            </div>
        </div>
        <?php if ($type == 'lockedUsers' || $type == 'activeAdmins' || $type == 'selfUnlocked') { ?>
            <div class="row">
                <?php if ($type == 'selfUnlocked') { ?>

                    <!-- <div class="col-md-4">
    <div class="form-group">
        <label class="col-md-12 control-label padding_right_zero padding_left_zero" for="cborank" style="margin-top:8px">Rank</label>
        <div class="col-md-12 padding_right_zero padding_left_zero">
            <select id="rank" name="rank" class="form-control input-md  select2_single">
                <option value="" selected>Select</option>

                <?php
                // if ($rank != "") {
                //     while ($row = mysqli_fetch_assoc($rank)) {
                //         $selRank = $row['rankId'];
                //         $title = $row['title'];
                ?>
                        <option value="<?php echo $selRank; ?>" <?php if ($selRank == $rank) { ?> selected="true" <?php } ?>><?php echo $title; ?></option>
                <?php
                //     }
                // }
        
                ?>
            </select>

        </div>
    </div>
</div> -->

                <?php } else { ?>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-12 control-label padding_right_zero padding_left_zero" for="cbostudent"
                                style="margin-top:8px">Role</label>
                            <div class="col-md-12 padding_right_zero padding_left_zero">
                                <select id="role" name="role" class="form-control input-md  select2_single">
                                    <option value="" selected>Select</option>

                                    <?php
                                    foreach ($roleList as $key => $value) {
                                        $selRole = $key;
                                        $title = $value;

                                        ?>
                                        <option value="<?php echo (($value)); ?>" <?php if ($selRole == $role) { ?> selected="true"
                                            <?php } ?>><?php echo ($title); ?></option>
                                        <?php

                                    }

                                    ?>
                                </select>

                            </div>
                        </div>
                    </div>

                <?php } ?>



                <div class="col-md-4">
                    <div class="form-group">
                        <label class="col-md-12 padding_right_zero padding_left_zero control-label" for="cbostudent"
                            style="margin-top:8px">School</label>
                        <div class="col-md-12 padding_right_zero padding_left_zero">
                            <select id="cboSchool" name="cboSchool" class="form-control input-md  select2_single">
                                <option value="" selected>Select</option>

                                <?php

                                if ($rowsSchool != "") {
                                    while ($row = mysqli_fetch_assoc($rowsSchool)) {
                                        $selSchoolId = $row['schoolId'];
                                        $name = stripslashes($row['displayName']);

                                        ?>
                                        <option value="<?php echo (($selSchoolId)); ?>" <?php if ($schoolId == $selSchoolId) { ?>
                                                selected="true" <?php } ?>><?php echo ($name); ?></option>
                                        <?php

                                    }
                                }

                                ?>
                            </select>

                        </div>
                    </div>
                </div>
            </div>
        <?php } else if ($type == 'checkoffType') { ?>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-12 control-label padding_right_zero padding_left_zero" for="cbostudent"
                                style="margin-top:8px">Checkoff Type</label>
                            <div class="col-md-12 padding_right_zero padding_left_zero">
                                <select id="checkoffType" name="checkoffType" class="form-control input-md  select2_single">
                                    <!-- <option value="" selected>Select</option> -->

                                    <?php
                                    foreach ($checkoffTypeList as $key => $value) {
                                        $selCheckoffType = $key;
                                        $title = $value;

                                        ?>
                                        <option value="<?php echo (($selCheckoffType)); ?>" <?php if ($selCheckoffType == $checkoffType) { ?> selected="true" <?php } ?>>
                                        <?php echo ($title); ?></option>
                                    <?php

                                    }

                                    ?>
                                </select>

                            </div>
                        </div>
                    </div>
                </div>
        <?php } else if ($type == 'rotationsSchool') { ?>
                    <div class="row">
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="col-md-12 control-label padding_right_zero padding_left_zero" for="cbostudent"
                                    style="margin-top:8px">Rotations Type</label>
                                <div class="col-md-12 padding_right_zero padding_left_zero">
                                    <select id="rotationType" name="rotationType" class="form-control input-md  select2_single">
                                        <option value="" selected>Select</option>

                                    <?php
                                    foreach ($rotationTypeList as $key => $value) {
                                        $selRotationType = $key;
                                        $title = $value;

                                        ?>
                                            <option value="<?php echo (($selRotationType)); ?>" <?php if ($selRotationType == $rotationType) { ?> selected="true" <?php } ?>>
                                        <?php echo ($title); ?></option>
                                    <?php

                                    }

                                    ?>
                                    </select>

                                </div>
                            </div>
                        </div>
                    </div>
        <?php } ?>
        <br>
        <?php if ($type == 'lockedUsers' || $type == 'activeAdmins') {
            ?>
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0"
                width="100%">
                <thead>
                    <tr>
                        <th>Sr.No</th>
                        <th>Name</th>
                        <th>Type</th>
                        <?php if ($role == 'Student' || $role == 'Clinician') { ?>
                            <th>Rank</th>
                        <?php } ?>
                        <th>Role</th>
                        <th>School</th>
                        <th>Email</th>
                        <th>Phone No</th>
                        <?php if ($type == 'lockedUsers') { ?>
                            <th style="text-align:center">Account <br>Created Date</th>
                            <th style="text-align:center">Action</th>
                        <?php } ?>
                    </tr>
                </thead>
                <tbody>

                </tbody>
            </table>
        <?php } else if ($type == 'checkoffType' || $type == 'rotationsSchool') { ?>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0"
                    width="100%">
                    <thead>
                        <tr>
                            <th>Sr.No</th>
                            <th>School Name</th>
                            <th>School Code</th>
                            <th>Platform</th>
                            <th style="text-align:center">Action</th>
                        </tr>
                    </thead>
                    <tbody>

                    </tbody>
                </table>
        <?php } else if ($type == 'selfUnlocked') {
            ?>
                    <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0"
                        width="100%">
                        <thead>
                            <tr>
                                <th>Sr.No</th>
                                <th>Name</th>
                                <th>Rank</th>
                                <!-- <th>Role</th>s -->
                                <th>School</th>
                                <th>Email</th>
                                <th>Unlocked Date </th>
                                <!-- <th>Phone No</th>
                        <th style="text-align:center">Action</th> -->
                            </tr>
                        </thead>
                        <tbody>


                        </tbody>
                    </table>
        <?php } ?>

    </div>


    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>


    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function () {
            $("#divTopLoading").addClass('hide');

            fetchData();


        });

        $('.addCopyPopup').magnificPopup({
            'type': 'ajax',

            'closeOnBgClick': false
        });

        $(".select2_single").select2();
        $('#select2-copySchool-container').addClass('required-select2');
        $('#form-control step2 input-md select2_single').addClass('required-select2');

        $.magnificPopup.instance._onFocusIn = function (e) {
            // Do nothing if target element is select2 input
            if ($(e.target).hasClass('select2-input')) {
                return true;
            }
            // Else call parent method
            $.magnificPopup.proto._onFocusIn.call(this, e);
        }

        $('#rank').on('change', function () {
            var rank = $('#rank').val();
            // console.log(rank);
            fetchData();
        });


        // Function to fetch data via AJAX
        function fetchData() {
            var type = '<?php echo $type; ?>';
            var role = $('#role').val();
            var rank = $('#rank').val();
            console.log(rank);

            var schoolId = $('#cboSchool').val();
            var checkoffType = $('#checkoffType').val();
            var rotationType = $('#rotationType').val();
            var checkoffTypeText = $('#checkoffType option:selected').text();
            var rotationTypeText = $('#rotationType option:selected').text();
            var label = "Active Admin Details: All"
            if (type == 'activeAdmins')
                var label = 'Active Admin Details: ' + role;
            else if (type == 'lockedUsers')
                var label = 'Locked User Details: ' + role;
            else if (type == 'checkoffType')
                var label = 'Schools With Checkoff Type: ' + checkoffTypeText;
            else if (type == 'rotationsSchool')
                var label = 'Schools With Rotation Type: ' + rotationTypeText;
            else if (type == 'selfUnlocked')
                var label = 'Self Unlocked Students';


            if (type == 'selfUnlocked') {
                var reqData = {
                    type: type,
                    role: role,
                    rank: rank,
                    checkoffType: checkoffType,
                    rotationType: rotationType,
                    schoolId: schoolId
                }
            } else {
                var reqData = {
                    type: type,
                    role: role,
                    checkoffType: checkoffType,
                    rotationType: rotationType,
                    schoolId: schoolId
                }
            }
            // console.log(reqData);
            // return false;

            if (type == 'selfUnlocked') {

                $.ajax({
                    url: '../ajax/ajax_get_users_details_for_dashboard.html',
                    method: 'POST',
                    dataType: 'json',
                    data: reqData, // Send additional data if needed

                    success: function (data) {
                        // console.log(data);
                        // return false;
                        // Clear existing rows
                        $('#datatable-responsive').DataTable().clear().draw();

                        // Add new rows
                        $('#datatable-responsive').DataTable().rows.add(data).draw();

                        $('#heading').text(label);

                    },
                    error: function (xhr, status, error) {
                        console.error('Error fetching data:', error);
                    }
                });

            } else {

                $.ajax({
                    url: '../ajax/ajax_get_users_details_for_dashboard.html',
                    method: 'POST',
                    dataType: 'json',
                    data: reqData, // Send additional data if needed
                    success: function (data) {
                        // console.log(data);
                        // return false;
                        // Clear existing rows
                        $('#datatable-responsive').DataTable().clear().draw();

                        // Add new rows
                        $('#datatable-responsive').DataTable().rows.add(data).draw();

                        $('#heading').text(label);

                    },
                    error: function (xhr, status, error) {
                        console.error('Error fetching data:', error);
                    }
                });
            }

        }

        $(document).ready(function () {

            // Initialize DataTable
            var table = $('#datatable-responsive').DataTable({
                columnDefs: [{
                    targets: 0,
                    className: 'text-center'
                } // Align first column content to center
                ]
            });
            // Initial data fetch
            // fetchData('all');

            // Change event handler for select element
            $('#cboSchool, #role, #checkoffType, #rotationType').on('change', function () {
                //Get Data

                fetchData();
                // $('#heading').text('Locked User Details: ' + role);
            });

            $(document).on('click', '.unlockUser', function () {

                var current_datatable_row = table.row($(this).parents('tr'));
                var userId = $(this).attr('userId');
                var type = $(this).attr('type');
                var role = $(this).attr('role');
                var action = 'unlockUser';
                // alertify.confirm('User: ' + schoolUserFullName, 'Continue with delete?', function() {
                $.ajax({
                    type: "post",
                    url: "../ajax/ajax_get_users_details_for_dashboard.html",
                    data: {
                        userId: userId,
                        type: type,
                        role: role,
                        action: action
                    },
                    success: function () {
                        table.row(current_datatable_row).remove().draw(false);
                        alertify.success('Account Unlocked successfully');
                    }
                });
                // }, function() {});

            });
        });
    </script>


</body>

</html>