<?php
    include('includes/validateUserLogin.php');
    include('../includes/config.php');
    include('../class/clsDB.php');
    include('../includes/commonfun.php');
	include('../setRequest.php');
    include_once('../class/clsSchool.php'); 
    include('../class/clsStudent.php');
    include('../class/clsStudentRankMaster.php');
    
    $studentSearch='';
    $rankSearch ='';
    $studentEmails ='';
    $studentSearch = isset($_GET['studentSearch']) ? $_GET['studentSearch'] : '';
    $rankIds = [];
    $display_from_date= '';
    $display_to_date= '';
    
    $objStudent = new clsStudent();
    if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSearch']) )
	{
        $studentSearch = $_POST['studentSearch'];

        $rankId = 0;
        $rankIds = isset($_POST['ranks']) ? $_POST['ranks'] : [];
        
        $rankIdList = implode(',', $rankIds); 

        $from_date ='';
        $to_date ='';
        
        $display_from_date=$_POST['fromDate'];
		$from_date =date("Y-m-d",strtotime($display_from_date));
		$display_to_date=$_POST['toDate'];
		$to_date =date("Y-m-d",strtotime($display_to_date));

        if($rankIds || $studentSearch)
        {
            $emails =  [];
            $studentEmails = $objStudent->GetStudentDetailForAdmin($currentSchoolId,$studentSearch,$rankIdList,$from_date,$to_date);
            while($row = mysqli_fetch_assoc($studentEmails))
            {
                $emails[] = $row['email'];
            }

            $studentEmails = implode('; ', $emails); 
        }
    }
    
    unset($objStudent);

    //StudentRank
	$objStudentRankMaster = new clsStudentRankMaster();
	$ranks = $objStudentRankMaster->GetAllStudentRankBySchool($currentSchoolId);
    unset($objStudentRankMaster);
    

  

 ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Student Email</title>
    <?php include('includes/headercss.php');?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css"> 
    <link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">


    <style>
        .form-control {
            height: 45px;
        }

        .select2-container--default .select2-selection--multiple {
            min-height: 45px;
            background-color: #f6f6ff !important;
            border-radius: 12px !important;
            box-shadow: none !important;
            border: none !important;
        }
        input:read-only {
            background-color: transparent !important;
        }

        .select2-container--default.select2-container--disabled .select2-selection--multiple {
            background-color: #f6f6ff !important;
            cursor: default;
        }

        .select2-container--default .select2-selection--multiple {
            min-height: 45px;
            background-color: #f6f6ff !important;
            border-radius: 12px !important;
            box-shadow: none !important;
            border: none !important;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__rendered li {
            text-wrap: wrap;
        }

        .select2-hidden-accessible~.select2-container--disabled {
            min-height: 45px !important;
            height: fit-content !important;
        }
    </style>

</head>

<body>
    <?php include('includes/header.php');?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Setting</a></li>
                    <li class="active">Student Email</li>
                </ol>
            </div>
        </div>
    </div>
          
    <div class="container">
        <?php if (isset($_GET["status"]))
            {
                if($_GET["status"] =="copy")
                {
                    ?>
            <div class="alert alert-success alert-dismissible fade in" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                </button>Student Copy successfully.
            </div>
            <?php 
                } 
            }
        ?>
        <div id="divTopLoading" >Loading...</div>
        
        <form name="studentList" id="studentList" method="POST" action="">
            <div class="row">
                <div class="form-group col-md-6 control-label">
                    <label class="col-md-12 p-0" >Student Name :</label>
                    <div class="col-md-12 p-0">
                        <input type="text" class="form-control" placeholder ="Search by First Name And Last Name" name="studentSearch" id="studentSearch" value="<?php echo $studentSearch; ?>">
                    </div>
                </div>
                <div class="form-group col-md-6 control-label">
                    <label class="col-md-12">Rank:</label>
                    <div class="col-md-12 p-0">
                        <select id="rank"   name="ranks[]" multiple="multiple"  class="form-control input-md  select2_tags" placeholder="Select Rank" >
                            <?php
                                if($ranks!="")
                                {
                                    while($row = mysqli_fetch_assoc($ranks))                                    {
                                        
                                            $selrankId  = $row['rankId'];
                                            $name  = stripslashes($row['title']);

                                            ?>
                                            <option value="<?php echo ($selrankId); ?>" <?php if( in_array( $selrankId,$rankIds)){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
                                            <?php

                                    }
                                }
                            ?>
                        </select>
                    </div>
                </div>
				   
                <!--div class="col-md-2">
                    <div class="form-group">
                    <button id="btnSearch" name="btnSearch" class="btn btn-success">Go</button>
                    </div>
                </div!-->
            </div>
          <div class="row">
                <div class="col-md-6 ">
                    <div class="form-group">
                        <label class="col-md-12 control-label pl-0" for="fromDate" style="margin-top:8px;text-align:end">From Date</label>
                            
                        <div class='input-group date col-md-12 w-full' name="fromDate" id='fromDate'>
                            <input type='text' name="fromDate"  id="fromDate" value="<?php echo ($display_from_date); ?>" class="dateInputFormat form-control input-md r rotation_date"  data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY"/>
                            <span class="input-group-addon  calender-icon">										
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        <div id="error-txtDate"></div>
                    </div>
                </div>
                                
                <div class="col-md-6  ">
                    <div class="form-group">
                        <label class="col-md-12 control-label p-0" for="toDate" style="margin-top:8px;text-align:end">To Date</label>
                        <div class='input-group date col-md-12 w-full' id='toDate'>
                            <input type='text' name="toDate"  id="toDate" class="dateInputFormat form-control input-md  rotation_date" value="<?php echo ($display_to_date); ?>" data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY"/>
                            <span class="input-group-addon  calender-icon">										
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        <div id="error-txtDate"></div>
                    </div>
                </div>
                <div class="col-md-12 py-10">
                    <div class="form-group pull-right">
                    <button id="btnSearch" name="btnSearch" class="btn btn-success">Go</button>
                    </div>
                </div>
            </div> 
            
        </form>
        
        <textarea class="form-control" name="email" id="" cols="30" rows="15"><?php echo $studentEmails; ?></textarea>    

        <div class="form-group pull-right py-10">
            <button id="copyText" name="copyText" class="btn btn-success margin_top_ten">Copy</button>
        </div>

    </div>


    <?php include('includes/footer.php');?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script> 
    <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

    <script type="text/javascript">

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function(){
           $("#divTopLoading").addClass('hide');

           $("#copyText").click(function(){
                $("textarea").select();
                document.execCommand('copy');
                alertify.success('Copy Emails');
            });

            $(function () {
                    $("#fromDate").datetimepicker({
                        format: "MM/DD/YYYY",
                    });
                });
                
                $(function () {
                    $("#toDate").datetimepicker({
                        format: "MM/DD/YYYY",
                    });
                });

        });

        $('.addCopyPopup').magnificPopup({
        'type': 'ajax',
        
        'closeOnBgClick': false
        });

        $(".select2_single").select2();
        $(".select2_tags").select2({'placeholder':'Select'});
        $('#select2-copySchool-container').addClass('required-select2');
        $('#form-control step2 input-md select2_single').addClass('required-select2');
        
        $.magnificPopup.instance._onFocusIn = function(e) {
            // Do nothing if target element is select2 input
            if( $(e.target).hasClass('select2-input') ) {
            return true;
            } 
            // Else call parent method
            $.magnificPopup.proto._onFocusIn.call(this,e);
        }

    </script>


</body>

</html>