<?php

	include('includes/validateUserLogin.php');
    include('../includes/config.php');
    include('../class/clsDB.php');
    include('../class/clsStudent.php'); 
	include('../class/clsSchool.php'); 
	include('../includes/commonfun.php');
	include('../setRequest.php');

	if(isset($_POST['id'])) //Edit Mode
	{
		$type='';
		$studentId = $_POST['id'];
		$type = $_POST['type'];
		if($type == 'status')
		{
			//Create object
			$objstudent = new clsstudent();
			$newStatus = $_POST['newStatus'];
			//echo $newStatus; exit;
			$objstudent->SetStudentStatus($studentId,$newStatus,$currentSchoolId);
			$returnValue = $newStatus ? 0 : 1;


			$userId = isset($_POST['userId']) ? DecodeQueryData($_POST['userId']) : 0;
			$isUser = isset($_POST['isUser']) ? ($_POST['isUser']) : 0;

			// $isUser 1 for Admin and 2 for Clinician and 3 for Student
			// Instantiate the Logger class
			$objLog = new clsLogger();
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			if ($isUser == 1) {
				$userType = $objLog::ADMIN;
			} else if ($isUser == 3) {
				$userType = $objLog::STUDENT;
			} else if ($isUser == 2) {
				$userType = $objLog::EVALUATOR;
			}

			$objstudent->saveStudentAuditLog($studentId, $userId, $userType, $logAction);



			echo $returnValue;exit;
			unset($objstudent);
			//header('location:schoolstudents.html?status=StatusUpdated');
		}
		
		else
		{
			$objstudent = new clsstudent();
			$newblockStatus = $_POST['newblockstatus'];
			$objstudent->SetStudentBlockUnblock($studentId,$newblockStatus,$currentSchoolId);
			//header('location:schoolstudents.html?status=blockStatusUpdated');
			$returnValue = $newblockStatus ? 0 : 1;


			
			$userId = isset($_POST['userId']) ? DecodeQueryData($_POST['userId']) : 0;
			$isUser = isset($_POST['isUser']) ? ($_POST['isUser']) : 0;

			// $isUser 1 for Admin and 2 for Clinician and 3 for Student
			// Instantiate the Logger class
			$objLog = new clsLogger();
			$logAction = ($newblockStatus == 0) ? $objLog::UNLOCK : $objLog::LOCK;
			if ($isUser == 1) {
				$userType = $objLog::ADMIN;
			} else if ($isUser == 3) {
				$userType = $objLog::STUDENT;
			} else if ($isUser == 2) {
				$userType = $objLog::EVALUATOR;
			}

			$objstudent->saveStudentAuditLog($studentId, $userId, $userType, $logAction);

			unset($objstudent);			
			echo $returnValue;exit;
		}
		
	}
	else
	{
		exit();
	}
?>