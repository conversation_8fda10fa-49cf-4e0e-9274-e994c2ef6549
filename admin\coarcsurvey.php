<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsStudent.php');
include('../class/clsCoarc.php');
include('../class/clsStudentCoarcMaster.php');
include('../setRequest.php');


$currenschoolDisplayname = '';

$objCoarc = new clsCoarc();

$surveyId = 0;

if (isset($_GET['surveyId'])) {
    $surveyId = $_GET['surveyId'];
    $surveyId = DecodeQueryData($surveyId);
}

//For Student List
$objStudent = new clsStudent();
$rowsStudentData = $objStudent->GetAllSchoolStudentsForCoarc($currentSchoolId, $surveyId);
$totalStudentCount = 0;
if ($rowsStudentData != '') {
    $totalStudentCount = mysqli_num_rows($rowsStudentData);
}

$rowsSurveyList = $objStudent->GetAllSchoolStudentsForCoarcList($currentSchoolId);

unset($objStudent);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>JRCERT Survey</title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Setting</a></li>
                    <li class="active">Student JRCERT Survey</li>
                </ol>
            </div>
            <div class="pull-right">
            </div>
        </div>
    </div>

    <div class="container">


        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Send request successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Request updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>

        <div class="row">
            <div class="col-md-8"></div>
            <div class="col-md-4 pull-right">
                <div class="form-group">
                    <label class="col-md-5 control-label margin_top_seven padding_zero" for="cborank">JRCERT Survey Title</label>
                    <div class="col-md-7 padding_zero">
                        <select id="surveyTitle" name="surveyTitle" class="form-control select2_single">
                            <option value="" selected>Select All</option>

                            <?php
                            if ($rowsSurveyList != '') {
                                while ($row = mysqli_fetch_assoc($rowsSurveyList)) {

                                    $selsurveyId  = $row['coarcSurveyMasterId'];
                                    $name  = stripslashes($row['surveyTitle']);

                            ?>
                                    <option value="<?php echo EncodeQueryData($selsurveyId); ?>" <?php if ($surveyId == $selsurveyId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                            <?php

                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
         <form name="coarcsurvey" id="coarcsurvey" data-parsley-validate method="POST" action="coarcsurveysubmit.html">
        <div class="row">
            <div class="col-md-10  margin_bottom_ten"></div>
            <div class="col-md-2  margin_bottom_ten">

                <div class="form-group">
                    <!--button id="btncoarcrequest" name="btncoarcrequest" class="btn btn-success">Send JRCERT Survey</button-->
                </div>
            </div>

        </div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr currenSchoolLogoImagePath="<?php echo ($currenSchoolLogoImagePath); ?>" currenschoolDisplayname="<?php echo ($currenschoolDisplayname); ?>">

                    <th><input type="checkbox" id="selectall" name="selectall[0]" class="check" value=""> &nbsp; Select All</th>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Rank</th>
                    <th>JRCERT Survey Title</th>
                    <th>Status</th>
                    <th style="text-align: center">Action</th>

                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalStudentCount > 0) {
                    while ($row = mysqli_fetch_array($rowsStudentData)) {

                        $studentId = $row['studentId'];
                        $fName = stripslashes($row['firstName']);
                        $lastName = stripslashes($row['lastName']);
                        $fullName =  $fName . ' ' . $lastName;
                        $rank = stripslashes($row['rank']);
                        // $status = stripslashes($row['status']);
                        $surveyTitle = stripslashes($row['surveyTitle']);

                        // $coarcId = stripslashes($row['coarcId']); 

                        //Get Coarc Survey MasterId 
                        $coarcSurveyMasterId = stripslashes($row['coarcSurveyMasterId']);
                        $isDelivery = $row['isDelivery'];
                        $surveyDetail = $objCoarc->GetStudentCoarcSurveyByStudent($studentId, $coarcSurveyMasterId);
                        $coarcId = $surveyDetail ? stripslashes($surveyDetail['coarcId']) :'';
                        $status = $surveyDetail ? stripslashes($surveyDetail['status']) : '';

                        $updaterow = 'updaterow';
                        if ($coarcId) {
                            if ($status == 1) {
                                $status = 'Completed';
                            } elseif ($status == 0) {
                                $status = 'Pending';
                            }
                        } else {
                            $status = '-';
                        }

                        //Get Student CoarcId
                        $getStudentCoarcId = $objCoarc->GetCoarcIdForStudent($studentId);

                        $getCoarcdetails = $objCoarc->GetAllCoarcSurvey($studentId);
                        $totalCoarcCount = 0;
                        if ($getCoarcdetails != '') {
                            $totalCoarcCount = mysqli_num_rows($getCoarcdetails);
                        }

                ?>
                        <tr <?php if ($status == 'Pending') { ?> class="<?php echo $updaterow; ?>" <?php } ?>>
                            <td>
                                <input coarcSurveyMasterId="<?php echo EncodeQueryData($coarcSurveyMasterId); ?>" studentId="<?php echo EncodeQueryData($studentId); ?>" SchoolId="<?php echo EncodeQueryData($currentSchoolId); ?>" type="checkbox" id="coarcsurvey" name="coarcsurvey[]" value="<?php echo ($studentId); ?>" <?php if (($status == 'Completed') || ($status == 'Pending')) { ?> checked id="coarcsurvey" class="coarcsurvey" <?php } else { ?> class="sendrequest" <?php } ?>>
                            </td>
                            <td><?php echo ($fName); ?></td>
                            <td><?php echo ($lastName); ?></td>
                            <td><?php echo ($rank); ?></td>
                            <td><?php echo ($surveyTitle); ?></td>
                            <td><?php echo ($status); ?></td>
                            <td align="center">
                                <?php if ($status == 'Completed') {
                                ?>
                                    <a href="addcoarcsurvey.html?studentCoarcMasterId=<?php echo (EncodeQueryData($coarcSurveyMasterId)); ?>
										&studentId=<?php echo (EncodeQueryData($studentId)); ?>
										&coarcId=<?php echo (EncodeQueryData($coarcId)); ?>">View</a>
                                    |<?php } elseif ($status == 'Pending') { ?>

                                    <?php if ($isDelivery == '0') { ?>
                                        <a href="javascript:void(0);" coarcSurveyMasterId="<?php echo EncodeQueryData($coarcSurveyMasterId); ?>" getStudentCoarcId="<?php echo EncodeQueryData($getStudentCoarcId); ?>" studentId="<?php echo EncodeQueryData($studentId); ?>" SchoolId="<?php echo EncodeQueryData($currentSchoolId); ?>" class="reSendrequest">Resend Email</a> |
                                    <?php } elseif ($isDelivery == '1') { ?>
                                        <a href="javascript:void(0);" coarcSurveyMasterId="<?php echo EncodeQueryData($coarcSurveyMasterId); ?>" getStudentCoarcId="<?php echo EncodeQueryData($getStudentCoarcId); ?>" studentId="<?php echo EncodeQueryData($studentId); ?>" SchoolId="<?php echo EncodeQueryData($currentSchoolId); ?>" class="reSendrequest">Resend SMS</a> |
                                        <a href="javascript:void(0);" coarcSurveyMasterId="<?php echo ($coarcSurveyMasterId); ?>" getStudentCoarcId="<?php echo ($getStudentCoarcId); ?>" studentId="<?php echo EncodeQueryData($studentId); ?>" SchoolId="<?php echo ($currentSchoolId); ?>" evaluationType="student" class="copyLink" onclick="copyLinkUrl(this)">Click to Copy URL</a> |

                                <?php }
                                    }
                                ?>
                                <a href="javascript:void(0);" class="deleteAjaxRow" coarcId="<?php echo EncodeQueryData($coarcId); ?>" schoolStudentName="<?php echo ($fullName); ?>" coarcSurveyMasterId="<?php echo EncodeQueryData($coarcSurveyMasterId); ?>" studentId="<?php echo (EncodeQueryData($studentId)); ?>" href="coarcsurvey.html?coarcId=<?php echo (EncodeQueryData($coarcId)); ?>&studentId=<?php echo (EncodeQueryData($studentId)); ?>&studentCoarcMasterId=<?php echo (EncodeQueryData($coarcSurveyMasterId)); ?>">Delete</a>
                            </td>

                        </tr>
                <?php


                    }
                }
                ?>
            </tbody>
        </table>
        </form>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        var current_datatable = $("#datatable-responsive").DataTable({
            responsive: false,
            scrollX: true,
            "aoColumns": [{
                "sWidth": "1%",
                "bSortable": false
            }, {
                "sWidth": "5%"
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "5%",

                "bSortable": false

            }]
        });

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();

            // check all start
            $("#selectall").change(function() {
                $("input:checkbox").prop('checked', $(this).prop("checked"));
            })

            //Check Or Uncheck All

            var rows = current_datatable.rows().nodes();
            if ($(".coarcsurvey", rows).is(':checked')) {

                $('.coarcsurvey', rows).attr('disabled', true);
            } else {
                $('.coarcsurvey', rows).attr('disabled', false);
            }
        });

        //Delete Student
        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var coarcId = $(this).attr('coarcId');
            var studentId = $(this).attr('studentId');
            var coarcSurveyMasterId = $(this).attr('coarcSurveyMasterId');
            var schoolStudentName = $(this).attr('schoolStudentName');

            alertify.confirm('Student Name: ' + schoolStudentName, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: coarcId,
                        studentId: studentId,
                        coarcSurveyMasterId: coarcSurveyMasterId,
                        type: 'coarc_request_student'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $("#selectall").change(function() {
            $('.sendrequest').trigger('change');
        })

        //FOR SEND REQUEST 1 by 1
        $('#datatable-responsive').on("change", ".sendrequest", function(event) {

            var studentId = $(this).attr('studentId');
            var SchoolId = $(this).attr('SchoolId');
            var coarcSurveyMasterId = $(this).attr('coarcSurveyMasterId');
            var currenSchoolLogoImagePath = $(this).attr('currenSchoolLogoImagePath');
            var currenschoolDisplayname = $(this).attr('currenschoolDisplayname');
            var getStudentCoarcId = $(this).attr('coarcSurveyMasterId');
            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/send_coarc_request.html",
                data: {
                    id: studentId,
                    SchoolId: SchoolId,
                    coarcSurveyMasterId: coarcSurveyMasterId,
                    currenSchoolLogoImagePath: currenSchoolLogoImagePath,
                    currenschoolDisplayname: currenschoolDisplayname,
                    getStudentCoarcId: getStudentCoarcId,
                    type: 'Sent_Request'
                },
                success: function() {
                    alertify.success('Sent');
                    // history.go(0);
                }
            });

        });


        $(document).on('click', '.reSendrequest', function() {

            var studentId = $(this).attr('studentId');
            var SchoolId = $(this).attr('SchoolId');
            var getStudentCoarcId = $(this).attr('getStudentCoarcId');
            var coarcSurveyMasterId = $(this).attr('coarcSurveyMasterId');

            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/send_coarc_request.html",
                data: {
                    id: studentId,
                    SchoolId: SchoolId,
                    getStudentCoarcId: getStudentCoarcId,
                    coarcSurveyMasterId: coarcSurveyMasterId,
                    type: 'Sent_Request'
                },
                success: function() {
                    alertify.success('Sent');
                }
            });

        });

        $("#surveyTitle").change(function() {
            var surveyId = $(this).val();

            if (surveyId) {
                window.location.href = "coarcsurvey.html?surveyId=" + surveyId;
            } else {
                window.location.href = "coarcsurvey.html";
            }
        });
    </script>
</body>

</html>