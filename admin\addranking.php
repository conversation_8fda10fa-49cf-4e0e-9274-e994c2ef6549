<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');   
    include('../class/clsStudentRankMaster.php');
	include('../setRequest.php'); 

    $schoolId = 0;
    if(isset($_GET['schoolId'])) //Edit Mode
	{
		$schoolId = $_GET['schoolId'];
        $schoolId = DecodeQueryData($schoolId);
    }
    else
    {
        $schoolId= $currentSchoolId;
        $title ="Add Student Rank - ".$currenschoolDisplayname;
    }

    $page_title ="Add Student Rank";
    $rankId = 0;
    $title = '';
	$sordOrder ='';
    $bedCrumTitle = 'Add';
    if(isset($_GET['id'])) //Edit Mode
	{
        $rankId = DecodeQueryData($_GET['id']);
	    $page_title ="Edit Rank";
        $bedCrumTitle = 'Edit';

        //For Rank Details
        $objStudentRankMaster = new clsStudentRankMaster();
		$row = $objStudentRankMaster->GetAllStudentRankMaster($schoolId,$rankId);
        unset($objStudentRankMaster);
        if($row=='')
        {
            header('location:addranking.html');
            exit;
        }

        $title  = stripslashes($row['title']);
        $sordOrder  = $row['sordOrder'];
    }
    
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html?id=<?php echo(EncodeQueryData($currentSchoolId)); ?>">Settings</a></li>
                    <li><a href="viewranking.html?schoolId=<?php echo(EncodeQueryData($currentSchoolId)); ?>">Rank</a></li>
                    <li class="active"><?php echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

         <form id="frmRank" data-parsley-validate class="form-horizontal" method="POST" action="addrankingsubmit.html?id=<?php echo(EncodeQueryData($rankId)); ?>" >

            <div class="row">
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtRank">Rank</label>
                        <div class="col-md-12">
                            <input id="txtRank"  name="txtRank" value="<?php echo($title); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">

                        </div>
                    </div>
                </div>
                <div class="col-md-6">
					<div class="form-group">
                        <label class="col-md-12 control-label" for="txtRankOrder">Sort Order</label>
                        <div class="col-md-12">
                            <input id="txtRankOrder"  name="txtRankOrder" value="<?php echo($sordOrder); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">

                        </div>
                    </div>
					
                     
                </div>
            </div>
            
            </div>
				<div class="form-group">
                        <!-- <label class="col-md-2 control-label"></label> -->
						<div class="col-md-12" style="display: flex; justify-content: center;margin: 20px 0;gap: 15px;">                       
                            <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                            <a type="button" href="viewranking.html" class="btn btn-default">Cancel</a>
                     </div>
                </div>
        </form>


    </div>

    <?php include('includes/footer.php');?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>



    <script type="text/javascript">
 
        $(window).load(function(){

             $('#frmRank').parsley().on('field:validated', function() {
			    var ok = $('.parsley-error').length === 0;
            })
            .on('form:submit', function() {
                ShowProgressAnimation();									  
                return true; // Don't submit form for this demo
            });


        });
		
		

    </script>
    



</body>

</html>