<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSystemUser.php');
include('../class/clsSchool.php');
include('../class/clsSystemUserRoleMaster.php');
include('../setRequest.php');

if (isset($_GET['userId'])) {
	$systemUserMasterId = DecodeQueryData($_GET['userId']);
	$type = isset($_GET['type']) ? $_GET['type'] : '';

	$backFrom = '';
	$adminUser = '';
	//----------------------------------
	if (isset($_SESSION["loggedAsStudentBackUserId"])) {
		$backFrom = "Student";
		unset($_SESSION["loggedAsStudentBackUserId"]);
	} else if (isset($_SESSION["loggedAsClinicianBackUserId"])) {
		$backFrom = "Evaluator";
		unset($_SESSION["loggedAsClinicianBackUserId"]);
	} else if (isset($_SESSION["loggedAsBackUserId"])) {
		$backFrom = "School Admin";
		unset($_SESSION["loggedAsBackUserId"]);

		// for Super Admin login As Another Admin
		if ($type == 'adminUser') {
			$adminUserId = $_SESSION["loggedUserId"];
		}
	} else {
		$_SESSION["loggedAsBackUserId"] = ($type == 'backtoAdmin') ? $systemUserMasterId : $_SESSION["loggedUserId"];

		// for Schoold Admin login As Another Admin
		if ($type == 'adminUser') {
			$adminUserId = $_SESSION["loggedUserId"];

		}

	}


	if (isset($_GET['schoolId'])) //Edit Mode
	{
		$schoolId = $_GET['schoolId'];
		$schoolId = DecodeQueryData($schoolId);
	}


	$objSystemUser = new clsSystemUser();
	$rowUser = $objSystemUser->GetSystemUserDetails($systemUserMasterId, $schoolId);

	unset($objSystemUser);

	if ($rowUser != "") {
		$schoolId = $rowUser['schoolId'];
		//------------------------------------------------------------------------------------------
		// Get school Details
		//------------------------------------------------------------------------------------------
		$objSchool = new clsSchool();
		$schoolDetails = $objSchool->GetschoolDetails($schoolId);
		$slug = trim(stripslashes($schoolDetails['slug']));
		unset($objSchool);

		//------------------------------------------------------------------------------------------
		// check isSystemUserRole Primary
		//------------------------------------------------------------------------------------------
		$IsSystemUserRolePrimary = 0;
		$objSystemUserRoleMaster = new clsSystemUserRoleMaster();
		$IsSystemUserRolePrimary = $objSystemUserRoleMaster->IsSystemUserRolePrimary($rowUser['systemUserRoleMasterId']);
		unset($objSystemUserRoleMaster);


		$dynamicLoginURL = BASE_PATH . '/school/' . $slug . '/admin/dashboard.html';

		$profileImageName = stripslashes($rowUser['smallProfilePic']);
		$defaultProfileImagePath = GetUserImagePath($systemUserMasterId, $schoolId, $profileImageName);

		$profileLargeImageName = stripslashes($rowUser['profilePic']);
		$defaultProfileLargeImagePath = GetUserImagePath($systemUserMasterId, $schoolId, $profileLargeImageName);

		@session_start();
		$_SESSION["loggedUserId"] = $systemUserMasterId;
		$_SESSION["loggedUserName"] = stripslashes($rowUser['username']);
		$_SESSION["loggedUserFirstName"] = stripslashes($rowUser['firstName']);
		$_SESSION["loggedUserLastName"] = stripslashes($rowUser['lastName']);
		$_SESSION["loggedUserProfileImagePath"] = $defaultProfileImagePath;
		$_SESSION["loggedUserProfileLargeImagePath"] = $defaultProfileLargeImagePath;
		$_SESSION["loggedUserRoleId"] = $rowUser['systemUserRoleMasterId'];
		$_SESSION["loggedUserIsRolePrimary"] = $rowUser['systemUserMasterId'];
		$_SESSION["loggedUserLocation"] = ($rowUser['locationId']);
		$_SESSION["loggedUserEmail"] = ($rowUser['email']);
		$_SESSION["loggedUserSchoolTimeZone"] = ($rowUser['timezone']);
		$_SESSION["loggedUserRoleType"] = ($rowUser['type']);
		$_SESSION["isActiveCheckoff"] = ($rowUser['isActiveCheckoffForStudent']);
		$_SESSION["isDefaultCiEval"] = ($rowUser['isDefaultCiEval']);
		$_SESSION["loggedUserSendRecordToCanvas"] = ($rowUser['isSendRecordToCanvas']);

		//------------------------------------------------------------------------------------------
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$logAction = ($type == 'backtoAdmin') ? $objLog::BACKTO : $objLog::LOGINAS;
		$isSuperAdmin = isset($_SESSION["loggedAsBackUserId"]) ? 1 : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
		$user_id = ($type == 'backtoAdmin') ? $_SESSION["loggedUserId"] : $_SESSION["loggedAsBackUserId"];

		// for Super Admin & school Admin login As Another Admin
		if ($type == 'adminUser') {
			$user_id = $adminUserId;
			$userType = $objLog::ADMIN;
		}
		$IsMobile = 0;

		$objSystemUser = new clsSystemUser();
		$objSystemUser->saveSchoolAdminAuditLog($systemUserMasterId, $user_id, $userType, $logAction, 0, 0, '', 0, $backFrom);

		unset($objSystemUser);
		unset($objLog);
		//Audit Log End

		header("location:" . $dynamicLoginURL);
		exit();
	} else {
		header('location:schoolusers.html?Error=1');
	}
} else {
	header('location:schoolusers.html?Error=2');
}
exit();
?>