<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');
    include('../setRequest.php'); 
    include('../class/clsStudent.php');
	include('../class/clscheckoff.php');
	include('../class/clsRotation.php'); 
	include('../class/clsStudentRankMaster.php');
    include('../class/clsAttendance.php');
    include('../class/clsCourses.php');

    $TimeZone=$_SESSION["loggedUserSchoolTimeZone"]; 
    $isActiveCheckoff =$_SESSION["isActiveCheckoff"]; 
	$currentstudentId =0;
	$rotationId   =0;
    $rotationtitle ="";
    $rankId='';
    $schoolId = $currentSchoolId;
    $transchooldisplayName=$currenschoolDisplayname;
    $Type='';
	$totalCheckOffDetail='';
	$studentId=0;
    $rotationId = 0;
    $originalMinutes='';
    $originalMinutes='';
    $totalApprovedhours='';
    $courseId = 0;
    if(isset($_GET['studentId']))
	{
		$currentstudentId = $_GET['studentId'];
        $currentstudentId = DecodeQueryData($currentstudentId);
    }
    if(isset($_GET['reportType']))
	{
		$reportType = $_GET['reportType'];
    }

    
    if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSearch']) )
	{

        $courseId = $_POST['cboCourse'];
		$courseId = DecodeQueryData($courseId);
		$rotationId = $_POST['rotationlist'];
		$rotationId = DecodeQueryData($rotationId);
    
    }
    
    //For Student Name
	$objStudent = new clsStudent();
	$rowsStudents = $objStudent->GetSingleStudent($currentSchoolId,$currentstudentId);
	$studentfullname= $rowsStudents ? ($rowsStudents['firstName'] . ' ' . $rowsStudents['lastName']) : '';
	$Students = $objStudent->GetAllStudents($currentSchoolId);
    unset($objStudent);
    
    //For Rotation
	$objrotation = new clsRotation();
	$rotation = $objrotation->GetAllRotationByStudent($currentSchoolId,$currentstudentId);
    unset($objrotation);

    $objAttendance = new clsAttendance();
    $totals = $objAttendance->GetTotalHoursForReport($rotationId,$currentstudentId,$courseId);
    $totalApprovedhours = $totals['totalApprovedhours'];
    if($totalApprovedhours !='')
    {
        $splitOriginalHours = explode(':', $totalApprovedhours);
        $originalHours = $splitOriginalHours[0];
        $originalMinutes = $splitOriginalHours[1];
        $totalApprovedhours = $originalHours.':'. $originalMinutes;
    }
    else 
    {
        $totalApprovedhours=" ";
    }
    $totalOrignalhours = $totals['totalOrignalhours'];
    if($totalOrignalhours !='')
    {
        $splitOriginalHours = explode(':', $totalOrignalhours);
        $originalHours = $splitOriginalHours[0];
        $originalMinutes = $splitOriginalHours[1];
        $totalOrignalhours = $originalHours.':'. $originalMinutes;
    }
    else 
    {
        $totalOrignalhours=" ";
    }

    //Get course name for dropdown
    $objCourses = new clsCourses();
	$courseList = $objCourses->GetCourseNameByRotation($currentSchoolId,$currentstudentId);
    unset($objCourses);
    
    

?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Student Detail Report </title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css"> 
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
        <style>
        div.dataTables_wrapper div.dataTables_processing
         {
        top: -45px;
        }
        </style>
    
    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="reports.html">Report</a></li>
                        <li><a href="reportsubmit.html?reportType=<?php echo $reportType; ?>">Student Portfolio</a></li>
                    </ol>
                </div>
                <div class="pull-right">
                    <form id="frmexportreport" data-parsley-validate class="form-horizontal" method="POST" action="exportStudentDetails.html?currentSchoolId=<?php echo(EncodeQueryData($currentSchoolId)); ?>&studentId=<?php echo(EncodeQueryData($currentstudentId)); ?>&courseId=<?php EncodeQueryData($courseId);?>&rotationId=<?php echo EncodeQueryData($rotationId);?>" enctype="multipart/form-data">
                    <input type="submit" name="btnStudentExport" id="btnStudentExport" class="btn btn-link" value="Export to Excel">
                    </form>
                </div>
             </div>
        </div>
		<div class="container">
			 
            <div id="divTopLoading" >Loading...</div>	
            <form name="studentReport" id="studentReport" method="POST" action="studentDetailReport.html?studentId=<?php echo(EncodeQueryData($currentstudentId)); ?>&reportType=<?php echo($reportType); ?>">
            <div class="row">
                <div class="col-md-5">
                    <h4><?php echo $studentfullname; ?></h4>
                </div>
                <div class="col-md-3 margin_bottom_ten">
                    <div class="form-group ">
                        <label class="col-md-4 control-label margin_top_seven" for="cboCourse">Course</label>
                        <div class="col-md-8">
                            <select id="cboCourse" name="cboCourse" class="form-control select2_single">
                            <option value="" selected>Select</option>
                            <?php
                                if($courseList!="")
                                {
                                    while($row = mysqli_fetch_assoc($courseList))
                                    {
                                        $selcourseId  = $row['courseId'];
                                        $name  = stripslashes($row['courseTitle']);

                                        ?>
                                        <option value="<?php echo(EncodeQueryData($selcourseId)); ?>" <?php if($courseId==$selcourseId){ ?>  selected="true" <?php }?>><?php echo($name); ?></option>
                                        <?php
                                    }
                                }
                            ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-3  margin_bottom_ten">
                    <div class="form-group ">
                        <label class="col-md-4 control-label margin_top_seven" for="rotationlist">Rotation</label>
                        <div class="col-md-8">
                            <select id="rotationlist" name="rotationlist" class="form-control select2_single"  >
                            <option value="" selected>Select </option>
                                <?php
                                if($rotation!="")
                                {
                                    while($row = mysqli_fetch_assoc($rotation))
                                    {
                                        $selrotationId  = $row['rotationId'];
                                        $name  = stripslashes($row['title']);

                                        ?>
                                        <option value="<?php echo EncodeQueryData($selrotationId); ?>" <?php if($rotationId==$selrotationId){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
                                        <?php

                                    }
                                }
                            ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-1">
                    <button id="btnSearch" name="btnSearch" class="btn btn-success">Go</button>
                </div>
            </div>
            </form>
            <!-- For Attendance -->
            <div class="formSubHeading">Attendance</div>
            <div class="row">
               
			</div>	
            <table id="datatableAttendance" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                    <th>Rotation</th>   
                    <th>Clock In Time</th>   
                    <th>Clock Out Time</th> 
                    <th>Approved</th>   
                    <th>Approved By</th> 
                    <th>Original<br>Hours</th> 											  
                    <th>Approved<br>Hours</th> 
                    </tr>
                </thead> 
                <tfoot>
                    <tr>
                        <th colspan="5" style="text-align:right">Total:</th>
                        <th><?php echo $totalOrignalhours; ?></th>
                        <th><?php echo $totalApprovedhours; ?></th>
                    </tr>
                </tfoot>                 
                
            </table>

             <!-- For Checkoff -->
             <div class="formSubHeading">Checkoff</div>	
            <table id="datatableCheckoff" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                    <th>Rotation</th>   
                    <th>Procedure<br>Code</th>   
                    <th>Topic</th>   
                    <th>Evaluator</th>
                    <th>Checkoff<br>Date</th>   
                    <?php if($isActiveCheckoff == 0) { ?>   
                    <th>Stud</th>   
                    <th>Precp</th> 
                    <th>Lab</th>   
                    <th>Clinical</th> 
                    <?php } else { ?>
                    <th>Yes</th> 
                    <th>No</th>   
                    <th>Comments</th> 
                    <th>Score</th>
                    <?php } ?>
                  
                    </tr>
                </thead>                    
                
            </table>
            
            <!-- For Procedure Count -->
            <div class="formSubHeading">Procedure Count</div>	
            <table id="datatableProcedureCount" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th>Procedure</th>   
                        <th>Asstd</th>   
                        <th>Obsvd</th>   
                        <th>Prfmd</th>									
                        <th>Asstd Total</th>									
                        <th>Obsvd Total</th> 
                        <th>Prfmd Total</th> 
                        <th>Total</th>
                    </tr>		
                </thead>    
            </table>

            <!-- For Dr. Interaction -->
            <div class="formSubHeading">Dr. Interaction</div>	
            <table id="datatableInteraction" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th>First Name</th>   
                        <th>Last Name</th>   
                        <th>Rank</th>   
                        <th>Rotation</th>									
                        <th>Clinician</th>									
                        <th>Interaction Date</th> 
                        <th>School Signed</th> 
                        <th>CI Signed</th>   
                        <th>Points</th>   
                        <th>Time Spent<br>
                            (In Minutes)</th>
                    </tr>		
                </thead>    
            </table>

             <!-- For Clinical Site Unit -->
            <div class="formSubHeading">Clinical Site Unit</div>	
            <table id="datatableClinicalSiteUnit" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th>Interaction Date</th>   
                        <th>Clinical Instructor</th>   
                        <th>Clinician Signature </th>   
                        <th>School Signature</th>									
                        <th>Clinical Sites Unit </th>									
                        <th>Time Spent</th> 
                        <th>Points Awarded</th>  
                    </tr>		
                </thead>    
            </table>

             <!-- For Midterm Evaluation -->
             <div class="formSubHeading">Midterm Evaluation</div>	
            <table id="datatableMidtermEvaluation" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th>Signature</th> 
                        <th>Rotation</th>											
                        <th>Evaluator</th> 
                        <th>Eval Date</th> 
                        <th>Absences</th>
                        <th>Tardy</th>  
                        <th>Succeeding</th>   
                        <th>Progressing</th>   
                        <th>Unsatisfactory</th>   	
                    </tr>		
                </thead>    
            </table>

            <!-- For Summative Evaluation -->
            <div class="formSubHeading">Summative Evaluation</div>	
            <table id="datatableSummativeEvaluation" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th>Rotation</th>   
                        <th>Eval Date</th>
                        <th>Evaluator</th>   
                        <th>Phase</th>   
                        <th>Signature</th>	
                    </tr>		
                </thead>    
            </table>

            <!-- For Formative Evaluation -->
            <div class="formSubHeading">Formative Evaluation</div>	
            <table id="datatableFormativeEvaluation" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th>Rotation</th>   
                        <th>Eval Date</th>
                        <th>Evaluator</th>   
                        <th>Phase</th>   
                        <th>Signature</th>	
                    </tr>		
                </thead>    
            </table>
           

        </div>

         <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
      
      <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
       <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script> 
       <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script> 
        <script type="text/javascript">

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function(){
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();
        });

        // function getRotation(val,selectedval)
        // {
        //     selectedval = selectedval == undefined ? 0 : selectedval;
        //     $.ajax({						
        //         type: "POST",
        //         url: "<?php echo ($dynamicOrgUrl); ?>/admin/get_roation.html",
        //         data:'courseId='+val+"&studentId="+selectedval,
        //         success: function(data){
        //             $("#rotationlist").html(data);
                    
        //         }
        //     });
        // } 

        //Server side Pagination Start
        var studentId='<?php echo ($currentstudentId); ?>';
        var schoolId='<?php echo ($schoolId); ?>';
        var isActiveCheckoff='<?php echo ($isActiveCheckoff); ?>';
        var rotationId='<?php echo ($rotationId); ?>';
        var courseId='<?php echo ($courseId); ?>';
        //For Attendacne
		$(document).ready(function() {
                var dataTable = $('#datatableAttendance').DataTable( {
                    searching : false,
                    "processing": true,
                    "bServerSide": true,
                    "bInfo": false,
                    "bPaginate": false,
                    "bLengthChange": false,
					 "ordering": true,
					"order": [[1, "desc" ]],
				
                    "aoColumns": [{
                    "sWidth": "10%",
                    "bSortable": true
                },{
					
                    "sWidth": "10%",
                    "bSortable": true
                },{
                    "sWidth": "10%",
                    "bSortable": true
                }, {
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "5%",
                    "bSortable": false
                } ,{
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }], 
                
                 
            
                "ajax":{
                    url :"studentAttendanceReport.html", // json datasource
                    type: "POST",  // method  , by default get
                    data: {
                        'studentId': studentId,
                        'schoolId': schoolId,
                        'rotationId' : rotationId,
                        'courseId' : courseId

                    },
                    error: function(){  // error handling
                        $(".employee-grid-error").html("");
                        $("#datatable-responsive").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
                        $("#employee-grid_processing").css("display","none");
                    }
                },
               
				} );
			 //End Attendance Server side Pagination
        });
        
        //For Checkoff
        $(document).ready(function() {
                var dataTable = $('#datatableCheckoff').DataTable( {

                    "scrollX": true,
                    responsive :false,
                    searching : false,
                    "processing": true,
                    "bServerSide": true,
                    "bInfo": false,
                    "bPaginate": false,
                    "bLengthChange": false,
                    "ordering": false,
                     
            
                "ajax":{
                    url :"studentCheckoffReport.html", // json datasource
                    type: "POST",  // method  , by default get
                    data: {
                        'studentId': studentId,
                        'schoolId': schoolId,
                        'isActiveCheckoff' : isActiveCheckoff,
                        'rotationId' : rotationId,
                        'courseId' : courseId

                    },
                    error: function(){  // error handling
                        $(".employee-grid-error").html("");
                        $("#datatableCheckoff").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
                        $("#employee-grid_processing").css("display","none");
                    }
                }
				} );
			 //End Checkoff Server side Pagination
        });

        //For Procedure Count
        $(document).ready(function() {
                var dataTable = $('#datatableProcedureCount').DataTable( {
                    searching : false,
                    "processing": true,
                    "bServerSide": true,
                    "bInfo": false,
                    "bPaginate": false,
                    "bLengthChange": false,

                    "aoColumns": [{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                }, {
                    "sWidth": "5%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                }, {
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }],  
            
                "ajax":{
                    url :"studentProcedureCountReport.html", // json datasource
                    type: "POST",  // method  , by default get
                    data: {
                        'studentId': studentId,
                        'schoolId': schoolId

                    },
                    error: function(){  // error handling
                        $(".employee-grid-error").html("");
                        $("#datatableProcedureCount").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
                        $("#employee-grid_processing").css("display","none");
                    }
                }
				} );
			 //End Attendance Server side Pagination
        });

        //For Dr. Intercation
        $(document).ready(function() {
                var dataTable = $('#datatableInteraction').DataTable( {
                    searching : false,
                    "processing": true,
                    "bServerSide": true,
                    "bInfo": false,
                    "bPaginate": false,
                    "bLengthChange": false,

                    "aoColumns": [{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                }, {
                    "sWidth": "5%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                }, {
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "5%",
                    "bSortable": false
                } ,{
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }],  
            
                "ajax":{
                    url :"studentDrInteractionReport.html", // json datasource
                    type: "POST",  // method  , by default get
                    data: {
                        'studentId': studentId,
                        'schoolId': schoolId,
                        'rotationId' : rotationId,
                        'courseId' : courseId

                    },
                    error: function(){  // error handling
                        $(".employee-grid-error").html("");
                        $("#datatable-responsive").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
                        $("#employee-grid_processing").css("display","none");
                    }
                }
				} );
			 //End Attendance Server side Pagination
        });

         //For Clinical Site Unit
         $(document).ready(function() {
                var dataTable = $('#datatableClinicalSiteUnit').DataTable( {
                    searching : false,
                    "processing": true,
                    "bServerSide": true,
                    "bInfo": false,
                    "bPaginate": false,
                    "bLengthChange": false,

                    "aoColumns": [{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                }, {
                    "sWidth": "5%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }],  
            
                "ajax":{
                    url :"studentClinicalSiteUnitReport.html", // json datasource
                    type: "POST",  // method  , by default get
                    data: {
                        'studentId': studentId,
                        'schoolId': schoolId,
                        'rotationId' : rotationId,
                        'courseId' : courseId

                    },
                    error: function(){  // error handling
                        $(".employee-grid-error").html("");
                        $("#datatableClinicalSiteUnit").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
                        $("#employee-grid_processing").css("display","none");
                    }
                }
				} );
			 //End  Server side Pagination
        });

         //For Midterm Evalution
         $(document).ready(function() {
                var dataTable = $('#datatableMidtermEvaluation').DataTable( {
                    searching : false,
                    "processing": true,
                    "bServerSide": true,
                    "bInfo": false,
                    "bPaginate": false,
                    "bLengthChange": false,

                    "aoColumns": [{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                }, {
                    "sWidth": "5%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }],  
            
                "ajax":{
                    url :"studentMidtermReport.html", // json datasource
                    type: "POST",  // method  , by default get
                    data: {
                        'studentId': studentId,
                        'schoolId': schoolId,
                        'rotationId' : rotationId,
                        'courseId' : courseId

                    },
                    error: function(){  // error handling
                        $(".employee-grid-error").html("");
                        $("#datatableMidtermEvaluation").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
                        $("#employee-grid_processing").css("display","none");
                    }
                }
				} );
			 //End Attendance Server side Pagination
        });


         //For Summative
         $(document).ready(function() {
                var dataTable = $('#datatableSummativeEvaluation').DataTable( {
                    searching : false,
                    "processing": true,
                    "bServerSide": true,
                    "bInfo": false,
                    "bPaginate": false,
                    "bLengthChange": false,

                    "aoColumns": [{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                }, {
                    "sWidth": "5%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }],  
            
                "ajax":{
                    url :"studentSummativeEvaluationReport.html", // json datasource
                    type: "POST",  // method  , by default get
                    data: {
                        'studentId': studentId,
                        'schoolId': schoolId,
                        'rotationId' : rotationId,
                        'courseId' : courseId

                    },
                    error: function(){  // error handling
                        $(".employee-grid-error").html("");
                        $("#datatableSummativeEvaluation").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
                        $("#employee-grid_processing").css("display","none");
                    }
                }
				} );
			 //End Attendance Server side Pagination
        });

         //For Formative
         $(document).ready(function() {
                var dataTable = $('#datatableFormativeEvaluation').DataTable( {
                    searching : false,
                    "processing": true,
                    "bServerSide": true,
                    "bInfo": false,
                    "bPaginate": false,
                    "bLengthChange": false,

                    "aoColumns": [{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "bSortable": false
                }, {
                    "sWidth": "5%",
                    "bSortable": false
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }],  
            
                "ajax":{
                    url :"studentFormativeEvaluationReport.html", // json datasource
                    type: "POST",  // method  , by default get
                    data: {
                        'studentId': studentId,
                        'schoolId': schoolId,
                        'rotationId' : rotationId,
                        'courseId' : courseId

                    },
                    error: function(){  // error handling
                        $(".employee-grid-error").html("");
                        $("#datatableFormativeEvaluation").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
                        $("#employee-grid_processing").css("display","none");
                    }
                }
				} );
			 
        });

        // $("#rotationlist").change(function(){
        //     var rotationId = $(this).val();
        //     var reportType = '<?php //echo ($reportType); ?>';
        //     var studentId = '<?php //echo EncodeQueryData($currentstudentId); ?>';
        //     if(rotationId)
        //     {
        //         window.location.href = "studentDetailReport.html?studentId="+studentId+"&reportType="+reportType+"&rotationId="+rotationId;
        //     }
        //     else{
        //         window.location.href = "studentDetailReport.html?studentId="+studentId+"&reportType="+reportType;
        //     }
		// });

        </script>
		
        
       
    </body>
    </html>