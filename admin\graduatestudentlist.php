<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsCoarc.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');

$objSchool = new clsSchool();
$objDB = new clsDB();

$schoolId = 0;


if (isset($_GET['active']))
    $active = DecodeQueryData($_GET['active']);

//For School
if (isset($_GET['schoolId'])) {
    $schoolId = DecodeQueryData($_GET['schoolId']);
}

$loggedUserId = $_SESSION["loggedUserId"];
$isPrimaryUser = 0;
$isPrimaryUser = $objDB->GetSingleColumnValueFromTable('systemusermaster', 'isPrimaryUser', 'systemUserMasterId', $loggedUserId);
unset($objDB);


if ($isPrimaryUser)
    $rowStudent = $objSchool->GetAllGraduateStudent($active, $schoolId);

$totalStudents = ($rowStudent != '') ?  mysqli_num_rows($rowStudent) : 0;
$reportType = 'Graduate_Student_Report';


$rowsSchool = $objSchool->GetAllSchools(1);
unset($objSchool);

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">

    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Graduate</title>

    <style>
        /* Hide the DataTables export button */
        .btn-primary.btnExport {
            display: none;
        }
    </style>
</head>
<?php include('includes/headercss.php'); ?>
<?php include("includes/datatablecss.php") ?>
<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/tableStyle.css" />
<link href="https://cdn.datatables.net/fixedcolumns/3.2.4/css/fixedColumns.bootstrap4.min.css" rel="stylesheet" />
<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

<body>
    <?php include('includes/header.php'); ?>


    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active">Graduate List</li>
                </ol>
            </div>
            <?php if ($isPrimaryUser) { ?>

                <div class="pull-right">

                    <input type="submit" name="btnSchoolExport" id="btnSchoolExport" class="btn btn-link" value="Export to Excel">
                </div>
            <?php } ?>
        </div>
    </div>

    <div class="container">
        <div class="row">

            <div class="col-md-4">
                <div class="form-group">
                    <label class="col-md-12 padding_right_zero padding_left_zero control-label" for="cbostudent" style="margin-top:8px">School</label>
                    <div class="col-md-12 padding_right_zero padding_left_zero">
                        <select id="cboSchool" name="cboSchool" class="form-control input-md  select2_single">
                            <option value="" selected>Select</option>

                            <?php

                            if ($rowsSchool != "") {
                                while ($row = mysqli_fetch_assoc($rowsSchool)) {
                                    $selSchoolId  = $row['schoolId'];
                                    $name  = stripslashes($row['displayName']);

                            ?>
                                    <option value="<?php echo (($selSchoolId)); ?>" <?php if ($schoolId == $selSchoolId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                            <?php

                                }
                            }

                            ?>
                        </select>

                    </div>
                </div>
            </div>
        </div>
        <br />
        <table id="studentlist_table" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>School</th>
                    <th>Email</th>
                    <th>Phone Number</th>

                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalStudents > 0) {
                    while ($row = mysqli_fetch_array($rowStudent)) {

                        $studentId = stripslashes($row['studentId']);
                        $firstName = stripslashes($row['firstName']);
                        $lastName = stripslashes($row['lastName']);
                        $email = stripslashes($row['email']);
                        $phone = stripslashes($row['phone']);
                        $schoolname = stripslashes($row['schoolname']);



                ?>
                        <tr>
                            <td style="background:white"><?php echo ($firstName); ?></td>
                            <td style="background:white"><?php echo ($lastName); ?></td>
                            <td style="background:white"><?php echo ($schoolname); ?></td>
                            <td style="background:white"><?php echo ($email); ?></td>
                            <td style="background:white"><?php echo ($phone); ?></td>
                        </tr>
                <?php
                    }
                }
                unset($objCoarc);
                ?>
            </tbody>
        </table>
    </div>
    </div>
    </div>
    </div>
    </div>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="https://cdn.datatables.net/buttons/1.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/2.5.0/jszip.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/1.2.2/js/buttons.html5.min.js"></script>

    <script>
        var todaysDate = new Date();
        var todaysDate = todaysDate.toISOString().split('T')[0];
        var current_clinicianlist_table = $("#studentlist_table").DataTable({
            "columnDefs": [{
                    "width": "20%",
                    "targets": 0
                },
                {
                    "width": "20%",
                    "targets": 1
                },
                {
                    "width": "20%",
                    "targets": 2
                },
                {
                    "width": "20%",
                    "targets": 3
                },
                {
                    "width": "20%",
                    "targets": 4
                }
            ],
            dom: 'Bfrtip',
            buttons: [{
                extend: 'excelHtml5',
                text: 'Export to Excel',
                title: "Graduate_List_" + todaysDate,
                className: 'btn-primary btnExport'
            }]
        });

        // Store a reference to the DataTables export button
        var exportButton = $('.btn-primary.btnExport');

        // Trigger the DataTables export button when the custom submit button is clicked
        $('#btnSchoolExport').on('click', function(e) {
            e.preventDefault(); // Prevent the default form submission
            exportButton.trigger('click'); // Trigger the DataTables export button click
        });

        $(".select2_single").select2();

        //School Dropdown
        var active = '<?php echo EncodeQueryData($active); ?>';
        $('#cboSchool').change(function() {
            var schoolId = $(this).val();
            window.location.href = "graduatestudentlist.html?active=" + active + "&schoolId=" + btoa(schoolId);


        });
    </script>
</body>

</html>