<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsCoarc.php');
include('../class/clsPersonnelCoarc.php');

$currentSchoolId;
$tranSchoolDisplayname = $currenschoolDisplayname;
$schoolId = $currentSchoolId;
$title = '';
$selclinicianId = 0;
$clinicianId = 0;
$AssignedcoarcSurveyMasterId = 0;
$TimeZone = $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

if (isset($_GET['coarcSurveyMasterId'])) {
   $coarcSurveyMasterId = DecodeQueryData($_GET['coarcSurveyMasterId']);
}


if (isset($_GET['AssignedcoarcSurveyMasterId'])) {
   $AssignedcoarcSurveyMasterId = DecodeQueryData($_GET['AssignedcoarcSurveyMasterId']);
}


if (isset($_GET['clinicianId'])) {
   $clinicianId = DecodeQueryData($_GET['clinicianId']);
}

//For Student List
$objCoarc = new clsCoarc();
$rowsData = $objCoarc->GetClinicianDetailsByCoarcSurvey($coarcSurveyMasterId, $currentSchoolId);
$totalCount = 0;
if ($rowsData != '') {
   $totalCount = mysqli_num_rows($rowsData);
}

$totalSection = 0;
$objPersonnelCoarc = new clsPersonnelCoarc();
$CoarcSection = $objPersonnelCoarc->GetSections($currentSchoolId);
if ($CoarcSection != '') {
   $totalSection = mysqli_num_rows($CoarcSection);
}

$reportType = "PersonnelServey";

//Student count list
$rowsClinicianData = $objCoarc->GetClinicianDetailsByCoarcSurvey($coarcSurveyMasterId, $currentSchoolId);
$totalClinicianCount = 0;
if ($rowsClinicianData != '') {
   $totalClinicianCount = mysqli_num_rows($rowsClinicianData);
}

//Get Corac Survey Title
$coarcSurveyTitle = $objCoarc->GetCoarcSurveyTitle($coarcSurveyMasterId);

?>
<!DOCTYPE html>
<html lang="en">

<head>
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge">
   <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
   <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
   <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
   <title>Coarc Report</title>
   <?php include('includes/headercss.php'); ?>
   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
   <?php include("includes/datatablecss.php") ?>
   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

   <style>
      .collapsible {
         cursor: pointer;
         /* padding: 15px; */
         /* border: 1px solid #181818; */
         /* background-color: #f9f9f9; */
         display: flex;
         justify-content: space-between;
         align-items: center;
         /* border-radius: 14px; */
      }

      .collapsible p {
         margin: 0;
      }

      .collapsible-arrow {
         font-size: 18px;
         transition: transform 0.3s ease;
      }

      .content {
         display: none;
         padding: 10px 0;
         /* border-top: 1px solid #ececec; */
      }

      .content.active {
         display: block;
      }

      .active.collapsible-arrow {
         transform: rotate(180deg);
      }

      .row-delete-icon {
         position: absolute;
         top: -82px;
         right: 20px;
      }

      .mobile-block {
         display: block;
      }
   </style>

</head>

<body>
   <?php include('includes/header.php'); ?>
   <div class="row margin_zero breadcrumb-bg">
      <div class="container">
         <div class="pull-left">
            <ol class="breadcrumb">
               <li><a href="dashboard.html">Home</a></li>
               <li><a href="settings.html">Settings</a></li>
               <li><a href="personnelCoarcSurveyReport.html"> Personnel JRCERT Survey Report</a></li>
               <li class="active">Personnel JRCERT Report</a></li>
            </ol>
         </div>
         <div class="pull-right">
            <form id="frmexportreport" data-parsley-validate class="form-horizontal" method="POST" action="exportPersonnelServey.html?Type=<?php echo ($reportType); ?>&coarcSurveyMasterId=<?php echo (EncodeQueryData($coarcSurveyMasterId)); ?>&AssignedcoarcSurveyMasterId=<?php echo (EncodeQueryData($AssignedcoarcSurveyMasterId)); ?>&clinicianId=<?php echo (EncodeQueryData($clinicianId)); ?>" enctype="multipart/form-data">
               <input type="hidden" name="cboreporttype" value="<?php echo ($reportType); ?>">
               <input type="submit" name="btnStudentExport" id="btnStudentExport" class="btn btn-link" value="Export to Excel">
            </form>
         </div>
      </div>
   </div>
   <div class="container">
      <div class="row">
         <div class="col-md-8">
            <h4>Report Title: <?php echo $coarcSurveyTitle; ?></h4>
         </div>
         <div class="col-md-4 pull-right padding_zero  margin_zero">
            <div class="form-group">
               <div class="col-md-4 text-right">
                  <label class="control-label" for="cbostudent" style="margin-top:8px">Clinician:</label>
               </div>
               <div class="col-md-8">
                  <select id="cbostudent" name="cbostudent" class="form-control input-md  select2_single">
                     <option value="" selected>Select</option>
                     <?php
                     if ($totalCount > 0) {
                        while ($row = mysqli_fetch_array($rowsData)) {
                           $selclinicianId = $row['clinicianId'];
                           $coarcSurveyMasterId = ($row['coarcSurveyMasterId']);
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $name  = $firstName . ' ' . $lastName;

                     ?>
                           <option value="<?php echo EncodeQueryData($selclinicianId); ?>" <?php if ($clinicianId == $selclinicianId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                     <?php
                        }
                     }
                     ?>
                  </select>
               </div>
            </div>
         </div>
      </div>
      <br><br>
      <div class="row">
         <div class="col-md-12">
            <div class="panel-group" id="posts">
               <?php
               if ($totalSection > 0) {
                  while ($row = mysqli_fetch_array($CoarcSection)) {
                     $sectionMasterId = $row['sectionMasterId'];
                     $title = $row['title'];
                     //$subTitle = $row['subTitle'];
               ?>
                     <div class="panel panel-default">
                        <a class="collapsible" style="color: #000; text-decoration: none;" href="#collapse1" data-toggle="collapse" data-parent="#accordion">
                           <div class="panel-heading" style="width : 100%; display: flex; justify-content: space-between; align-items: center;">
                              <h4 class="panel-title">
                                 <?php echo $title; ?>
                              </h4>
                              <span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
                           </div>
                        </a>
                        <div class="panel-body panel-collapse panel-body collapse">
                           <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover mytablecustom" cellspacing="0" width="100%">
                              <thead>
                                 <tr>
                                    <th>Student Program Resource Survey</th>
                                    <th>Strongly Agree</th>
                                    <th>Generally Agree</th>
                                    <th>Neutral</th>
                                    <th>Generally Disagree</th>
                                    <th>Strongly Disagree</th>
                                 </tr>
                              </thead>
                              <tbody>
                                 <?php
                                 $totalCoarc = 0;
                                 $StronglyAgreePer = '-';
                                 $AgreePer = '-';
                                 $NeutralPer = '-';
                                 $DisagreePer = '-';
                                 $StronglyDisagreePer = '-';

                                 $Coarcquestion = $objPersonnelCoarc->GetAllSurveyQuestionBySchool($sectionMasterId);

                                 if ($Coarcquestion != '') {
                                    $totalCoarcquestion = mysqli_num_rows($Coarcquestion);
                                 }

                                 if ($totalCoarcquestion > 0) {
                                    while ($row = mysqli_fetch_array($Coarcquestion)) {

                                       $coarcQuestionTitle = stripslashes($row['questionText']);
                                       $schoolPersonnelCoarcQuestionId = $row['schoolPersonnelCoarcQuestionId'];
                                       $schoolPersonnelCoarcQuestionType = $row['schoolPersonnelCoarcQuestionType'];

                                       $GetCalrows = $objPersonnelCoarc->GetCalculationsforPersonnelReport($schoolPersonnelCoarcQuestionId, $clinicianId, $coarcSurveyMasterId, $AssignedcoarcSurveyMasterId);

                                       $CountStronglyAgree = $GetCalrows ? $GetCalrows['CountStronglyAgree'] :0;
                                       $CountAgree = $GetCalrows ? $GetCalrows['CountAgree'] :0;
                                       $CountNeutral = $GetCalrows ? $GetCalrows['CountNeutral'] :0;
                                       $CountDisagree = $GetCalrows ? $GetCalrows['CountDisagree'] :0;
                                       $CountStronglyDisagree = $GetCalrows ? $GetCalrows['CountStronglyDisagree'] :0;
                                       $CoarcOptionAnswerText = $GetCalrows ? $GetCalrows['schoolCoarcOptionAnswerText'] :0;
                                       $TotalCount = $CountStronglyAgree + $CountAgree + $CountNeutral + $CountDisagree + $CountStronglyDisagree;

                                       if ($schoolPersonnelCoarcQuestionType > 0) {
                                          if ($TotalCount > 0) {
                                             $StronglyAgreePer = $CountStronglyAgree * 100 / $TotalCount;
                                             $AgreePer = $CountAgree * 100 / $TotalCount;
                                             $NeutralPer = $CountNeutral * 100 / $TotalCount;
                                             $DisagreePer = $CountDisagree * 100 / $TotalCount;
                                             $StronglyDisagreePer = $CountStronglyDisagree * 100 / $TotalCount;

                                             if ($StronglyAgreePer > 0)
                                                $StronglyAgreePer = number_format((float)$StronglyAgreePer, 2, '.', '') . '%';
                                             else
                                                $StronglyAgreePer = '-';

                                             if ($AgreePer > 0)
                                                $AgreePer = number_format((float)$AgreePer, 2, '.', '') . '%';
                                             else
                                                $AgreePer = '-';

                                             if ($NeutralPer > 0)
                                                $NeutralPer = number_format((float)$NeutralPer, 2, '.', '') . '%';
                                             else
                                                $NeutralPer = '-';

                                             if ($DisagreePer > 0)
                                                $DisagreePer = number_format((float)$DisagreePer, 2, '.', '') . '%';
                                             else
                                                $DisagreePer = '-';

                                             if ($StronglyDisagreePer > 0)
                                                $StronglyDisagreePer = number_format((float)$StronglyDisagreePer, 2, '.', '') . '%';
                                             else
                                                $StronglyDisagreePer = '-';
                                          }
                                       } else {
                                          $StronglyAgreePer = "-";
                                          $AgreePer = "-";
                                          $NeutralPer = "-";
                                          $DisagreePer = "-";
                                          $StronglyDisagreePer = "-";
                                       }
                                 ?>
                                       <tr>
                                          <td <?php if ($schoolPersonnelCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo $coarcQuestionTitle; ?></td>
                                          <?php if ($schoolPersonnelCoarcQuestionType == 5) { ?>
                                             <td colspan="5" <?php if ($schoolPersonnelCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo ($CoarcOptionAnswerText); ?></td>
                                          <?php } else { ?>
                                             <td <?php if ($schoolPersonnelCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo $StronglyAgreePer; ?></td>
                                             <td <?php if ($schoolPersonnelCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo $AgreePer; ?></td>
                                             <td <?php if ($schoolPersonnelCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo $NeutralPer; ?></td>
                                             <td <?php if ($schoolPersonnelCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo $DisagreePer; ?></td>
                                             <td <?php if ($schoolPersonnelCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo $StronglyDisagreePer; ?></td>
                                          <?php } ?>
                                       </tr>
                                 <?php
                                    }
                                 }
                                 ?>
                              </tbody>
                           </table>
                        </div>
                     </div>
               <?php
                  }
               }  ?>

               <?php
               if (!isset($_GET['clinicianId'])) {
               ?>
                  <div class="panel panel-default">
                     <a class="collapsible" style="color: #000; text-decoration: none;" href="#collapse1" data-toggle="collapse" data-parent="#accordion">
                        <div class="panel-heading" style="width : 100%; display: flex; justify-content: space-between; align-items: center;">
                           <h4 class="panel-title">
                              9. Clinical Trac JRCERT Survey Results
                           </h4>
                           <span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
                        </div>
                     </a>
                     <div class="panel-body panel-collapse panel-body collapse">
                        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover mytablecustom" cellspacing="0" width="100%">
                           <thead>
                              <tr>
                                 <th style="text-align: center">Survey Start Date</th>
                                 <th style="text-align: center">Survey End Date</th>
                                 <th style="text-align: center">Number of Surveys Sent</th>
                                 <th style="text-align: center">Number of Surveys Completed</th>
                                 <th style="text-align: center">Number of Surveys Pending</th>
                                 <th style="text-align: center">Return Rate</th>
                              </tr>
                           </thead>
                           <tbody>
                              <?php
                              $completedStatus = 0;
                              if ($totalClinicianCount > 0) {
                                 while ($row = mysqli_fetch_array($rowsClinicianData)) {

                                    $clinicianId = $row['clinicianId'];
                                    $startDate = $row['startDate'];
                                    $startDate = converFromServerTimeZone($startDate, $TimeZone);
                                    $endDate = $row['endDate'];
                                    $endDate = converFromServerTimeZone($endDate, $TimeZone);
                                    $startDateTimestamp = strtotime($startDate);
                                    $endDateTimestamp = strtotime($endDate);

                                    $status = $objCoarc->GetPersonnelCoarcSurveyStatus($coarcSurveyMasterId, $clinicianId);

                                    if ($status > 0) {
                                       $completedStatus += $status;
                                    }
                                 }
                              }
                              $pendingStatus = $totalClinicianCount - $completedStatus;
                                //For Return Rate
                                $returnRate = ($completedStatus / $totalClinicianCount) * 100;
                                $returnRate = number_format((float)$returnRate, 2, '.', '') . '%';
  

                              ?>
                              <tr>

                                 <td style="text-align: center"><?php echo (date('m/d/Y', $startDateTimestamp)); ?></td>
                                 <td style="text-align: center"><?php echo (date('m/d/Y', $endDateTimestamp)); ?></td>
                                 <td style="text-align: center"><?php echo $totalClinicianCount; ?></td>
                                 <td style="text-align: center"><?php echo $completedStatus; ?></td>
                                 <td style="text-align: center"><?php echo $pendingStatus; ?></td>
                                 <td style="text-align: center"><?php echo $returnRate; ?></td>

                              </tr>
                           <?php
                        }
                        unset($objCoarc);
                           ?>
                           </tbody>
                        </table>
                     </div>
                  </div>
            </div>
         </div>
      </div>
   </div>
   <?php include('includes/footer.php'); ?>
   <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
   <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
   <script type="text/javascript">
      alertify.defaults.transition = "slide";
      alertify.defaults.theme.ok = "btn btn-success";
      alertify.defaults.theme.cancel = "btn btn-danger";
      alertify.defaults.theme.input = "form-control";

      $(window).load(function() {

         $(".select2_single").select2();
      });

      <?php
      if ($clinicianId) { ?>
         $('.hide').removeClass('hide');
      <?php } ?>

      $("#cbostudent").change(function() {
         var clinicianId = $(this).val();
         var coarcSurveyMasterId = "<?php echo EncodeQueryData($coarcSurveyMasterId); ?>";
         var AssignedcoarcSurveyMasterId = "<?php echo EncodeQueryData($AssignedcoarcSurveyMasterId); ?>";

         if (clinicianId) {
            window.location.href = "personnelCoarcSurveyReportView.html?coarcSurveyMasterId=" + coarcSurveyMasterId + "&clinicianId=" + clinicianId + "&AssignedcoarcSurveyMasterId=" + AssignedcoarcSurveyMasterId;
         } else {
            window.location.href = "personnelCoarcSurveyReportView.html?coarcSurveyMasterId=" + coarcSurveyMasterId + "&AssignedcoarcSurveyMasterId=" + AssignedcoarcSurveyMasterId;
         }
      });
   </script>
   <script>
      // Get all collapsible button elements
      var buttons = document.querySelectorAll(".collapsible");
      var contents = document.querySelectorAll(".panel-collapse");

      // Add click event listeners to all buttons
      buttons.forEach(function(button, index) {
         button.addEventListener("click", function() {
            // Check if the content is currently expanded
            var isExpanded = contents[index].style.display === "block";

            // Close all sections
            contents.forEach(function(content) {
               content.style.display = "none";
            });

            // Reset the "expanded" class for all buttons
            buttons.forEach(function(btn) {
               btn.classList.remove("expanded");
            });

            // Toggle the content for the clicked section
            if (!isExpanded) {
               contents[index].style.display = "block";
               button.classList.add("expanded");
            }
         });
      });
   </script>
</body>

</html>