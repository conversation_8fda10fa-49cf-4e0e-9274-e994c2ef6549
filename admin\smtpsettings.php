<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsSMTPSettings.php');

$SMTPHost = '';
$SMTPUserName = '';
$SMTPPassword = '';
$SMTPPort = '';
$SMTPFromName = '';
$SMTPFromEmail = '';
$type = '';
//Get SMTP Details
$objSMTPSettings = new clsSMTPSettings();
$row = $objSMTPSettings->GetSMTPSettingDeatils($currentSchoolId);
if ($row != '' || $row != 0) {
	$SMTPHost = stripslashes($row['SMTPHost']);
	$SMTPUserName = stripslashes($row['SMTPUserName']);
	$SMTPPassword  = stripslashes($row['SMTPPassword']);
	$SMTPPort  = stripslashes($row['SMTPPort']);
	$SMTPFromName  = stripslashes($row['SMTPFromName']);
	$SMTPFromEmail  = stripslashes($row['SMTPFromEmail']);
	$type  = stripslashes($row['type']);
} else {
	$SMTPHost = '1';
	$SMTPUserName = '<EMAIL>';
	$SMTPPassword  = '123456';
	$SMTPPort  = '993';
	$SMTPFromName  = 'Clinicaltrac';
	$SMTPFromEmail  = '<EMAIL>';
	$type  = 'P';
}
unset($objSMTPSetting);
?>

<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<title>SMTP</title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

	<style>
		.input-group-addon {
			position: absolute;
			right: 7px;
			/* width: 100%; */
			z-index: 99;
			width: 35px;
			margin: auto;
			top: 5px;
			border-left: 1px solid #ccc;
			border-radius: 50% !important;
			padding: 10px -2px;
			height: 35px;
			/* background: #01A750; */
			/* color: #fff; */
			color: #555;
			background: #f6f6ff;
			border: none;
			display: flex;
			justify-content: center;
			align-items: center;
		}

		.panel-default>.panel-heading {
			background-color: transparent !important;
		}

		.btn-success,
		.btn-default {
			padding: 8px 25px;
			border-radius: 10px;
		}

		.panel {
			border-radius: 14px !important;
		}

		.select2-container--default .select2-selection--single .select2-selection__rendered {
			line-height: 45px !important;
		}

		.required-select2 {
			border-left: 3px solid red !important;
			border-radius: 12px !important;
		}

		.select2-container--default .select2-selection--single {
			background-color: #f6f6ff !important;
			cursor: default !important;
			height: 45px !important;
			border-radius: 10px !important;
		}

		.select2-container--default .select2-selection--single {
			border: none !important;
		}

		.panel,
		.form-group {
			margin-bottom: 10px;
		}

		.bootstrap-datetimepicker-widget {
			border-radius: 12px !important;
		}

		.form-control {
			height: 45px;
		}


		.input-group {
			width: 100%;
		}

		.required-input {
			border-left: 3px solid red !important;
		}

		.input-group-addon {
			background: transparent;
		}

		.formSubHeading {
			border-bottom: 2px solid #d9d6d657;
			padding: 3px 0;
			position: relative;
		}

		input[type="file"] {
			background-color: #fff !important;
		}

		.formSubHeading:before {
			position: absolute;
			width: 60px;
			height: 2px;
			background: #2e3192;
			content: "";
			bottom: -2px;
		}
/* For Chrome, Safari, Edge, and Opera */
input[type=number]::-webkit-outer-spin-button,
input[type=number]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* For Firefox */
input[type=number] {
    -moz-appearance: textfield;
}

		@media screen and (max-width: 500px) {
			.panel-body ol {
				padding-left: 20px;
			}

			.panel-default>.panel-heading {
				padding-left: 5px;
			}
		}
	</style>

</head>

<body>
	<div>
		<?php include('includes/header.php'); ?>

		<div class="row margin_zero breadcrumb-bg">
			<div class="container">
				<div class="pull-left">
					<ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>

						<li><a href="settings.html">Settings</a></li>
						<li class="active">SMTP</li>

					</ol>
				</div>

			</div>
		</div>

		<div class="container">

			<div class="form-horizontal">
				<div class="row">


					<?php
					if (isset($_GET["status"])) {
						if ($_GET["status"] == "mandatory") {
					?>
							<div class="alert alert-danger alert-login" role="alert">
								<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
								</button>
								Mandatory fields are required.
							</div>

						<?php
						} else if ($_GET["status"] == "update") {
						?>
							<div class="alert alert-success alert-login margin_bottom_ten" role="alert">
								<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
								</button> Updated
							</div>
					<?php
						}
					}
					?>

					<form class="form_validation_ttip form-horizontal" accept-charset="UTF-8" role="form" data-parsley-validate="" action="smtpsettingsubmit.html" method="post" id="change_password">

						<div class="row margin_zero">
							<div class="col-md-6">
								<div class="form-group">
									<label class="control-label col-md-12 col-sm-12 col-xs-12" for="SMTPHost">Email Type</label>
									<div class="col-md-12 col-sm-12 col-xs-12">
										<label class="radio-inline">
											<input id="SMTP" class="type" value="S" name="type" type="radio" <?php if ($type == 'S') { ?> checked="checked" <?php } ?>>
											SMTP
										</label>
										<label class="radio-inline">
											<input id="MAIL" class="type" value="M" name="type" type="radio" <?php if ($type == 'M') { ?> checked="checked" <?php } ?>>
											MAIL
										</label>
										<label class="radio-inline">
											<input id="PHP" class="type" value="P" name="type" type="radio" <?php if ($type == 'P') { ?> checked="checked" <?php } ?>>
											PHP Mail
										</label>
									</div>
								</div>
							</div>

							<div class="col-md-6">
								<div class="form-group">
									<label class="control-label col-md-12 col-sm-12 col-xs-12" for="SMTPFromname">From Name</label>
									<div class="col-md-12 col-sm-12 col-xs-12">
										<input type="text" name="SMTPFromname" id="SMTPFromname" value="<?php echo ($SMTPFromName); ?>" placeholder="SMTP Fromname" required="required" class="form-control input-md required-input">
									</div>
								</div>
							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label class="control-label col-md-12 col-sm-12 col-xs-12" for="SMTPFromemail">From Email</label>
									<div class="col-md-12 col-sm-12 col-xs-12">
										<input type="text" name="SMTPFromemail" id="SMTPFromemail" value="<?php echo ($SMTPFromEmail); ?>" placeholder="SMTP Fromemail" required="required" class="form-control input-md required-input">
									</div>
								</div>
							</div>

							<div class="col-md-6" id="SMTP_Fields">
								<div class="form-group">
									<label class="control-label col-md-12 col-sm-12 col-xs-12" for="SMTPHost">SMTP Host</label>
									<div class="col-md-12 col-sm-12 col-xs-12">
										<input type="text" name="SMTPHost" id="SMTPHost" value="<?php echo ($SMTPHost); ?>" placeholder="SMTP Host" required="required" class="form-control input-md required-input">
									</div>
								</div>

							</div>
							<div class="col-md-6">
								<div class="form-group">
									<label class="control-label col-md-12 col-sm-12 col-xs-12" for="SMTPUsername">SMTP Username</label>
									<div class="col-md-12 col-sm-12 col-xs-12">
										<input type="text" name="SMTPUsername" id="SMTPUsername" value="<?php echo ($SMTPUserName); ?>" placeholder="SMTP Username" required="required" class="form-control input-md required-input">
									</div>
								</div>
							</div>

							<div class="col-md-6">

								<div class="form-group">
									<label class="control-label col-md-12 col-sm-12 col-xs-12" for="SMTPPassword">SMTP Password</label>
									<div class="col-md-12 col-sm-12 col-xs-12">
										<input type="password" name="SMTPPassword" id="SMTPPassword" value="<?php echo ($SMTPPassword); ?>" placeholder="SMTP Password" required="required" class="form-control input-md required-input">
									</div>
								</div>
							</div>
							<div class="col-md-6">

								<div class="form-group">
									<label class="control-label col-md-12 col-sm-12 col-xs-12" for="SMTPPort">SMTP Port</label>
									<div class="col-md-12 col-sm-12 col-xs-12">
										<input type="number" name="SMTPPort" id="SMTPPort" value="<?php echo ($SMTPPort); ?>" placeholder="SMTP Port" required="required" class="form-control input-md required-input">
									</div>
								</div>



							</div>
						</div>

						<div class="form-group mx-0">
							<!-- <label class="control-label col-md-3 col-sm-3 col-xs-12"></label> -->
							<div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
								<button class="btn btn-success" name="btnSave" type="submit">Update</button>
								<a href="settings.html" class="btn btn-default">Cancel</a>
							</div>
						</div>
					</form>
				</div>
			</div>
		</div>
	</div>


	<?php //include('includes/left-menu.php'); 
	?>
	<?php include('includes/footer.php'); ?>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript">
		$(document).ready(function() {
			$('.type').trigger("change");

		});

		$(".type").change(function() {

			if ($("#SMTP").prop("checked")) {
				$('#SMTP_Fields').removeClass('hide');
				$('#SMTPFromemail').prop('required', true);
				$('#SMTPPort').prop('required', true);
				$('#SMTPHost').prop('required', true);
				$('#SMTPUsername').prop('required', true);
				$('#SMTPPassword').prop('required', true);

			} else if ($("#MAIL").prop("checked")) {
				$('#SMTPPort').removeAttr('required');
				$('#SMTPHost').removeAttr('required');
				$('#SMTPUsername').removeAttr('required');
				$('#SMTPPassword').removeAttr('required');
				$('#SMTP_Fields').addClass('hide');
			} else if ($("#PHP").prop("checked")) {
				$('#SMTPPort').removeAttr('required');
				$('#SMTPHost').removeAttr('required');
				$('#SMTPUsername').removeAttr('required');
				$('#SMTPPassword').removeAttr('required');
				$('#SMTP_Fields').addClass('hide');
			}
		});
	</script>
</body>

</html>