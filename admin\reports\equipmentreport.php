<?php
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;


$objEquipment = new clsEquipment();
$individual_student = unserialize($individual_student);
$rowsEquipment = $objEquipment->GetStudentEquipmentDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);

// Create new Spreadsheet object
$spreadsheet = new Spreadsheet();

// Set document properties
$spreadsheet->getProperties()->setCreator('Jacson Community College')
                             ->setLastModifiedBy('JCC')
                             ->setTitle('Reports')
                             ->setSubject('School Report')
                             ->setDescription('All School Reports');

// Set Active Sheet
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('Equipment Reports');	

// Print Heading	
$headerstyleArray = [
    'font'  => ['bold' => true, 'size' => 16],
	'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
];

$styleArray = [
    'font'  => ['bold' => true, 'size' => 12],
	'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
];

// School Name Heading
$sheet->mergeCells("B2:H2");
$sheet->setCellValue('B2', $schoolname);
$sheet->getStyle('B2')->applyFromArray($headerstyleArray);
$sheet->getStyle('B2')->getAlignment()->applyFromArray(['horizontal' => Alignment::HORIZONTAL_CENTER]);

// Fill background color for header
$sheet->getStyle('B2')
      ->getFill()
      ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
      ->getStartColor()
      ->setRGB('E0E0E0');

// Borders for header
$styleBorderArray = [
    'borders' => [
        'allBorders' => ['borderStyle' => Border::BORDER_THIN]
    ]
];
$sheet->getStyle('B2:H2')->applyFromArray($styleBorderArray);

// Report Title
$sheet->mergeCells("B4:H4");
$sheet->setCellValue('B4', 'Equipment Report');
$sheet->getStyle('B4')->applyFromArray($styleArray);
$sheet->getStyle('B4')->getAlignment()->applyFromArray(['horizontal' => Alignment::HORIZONTAL_CENTER]);

// Fill background color for title
$sheet->getStyle('B4')
      ->getFill()
      ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
      ->getStartColor()
      ->setRGB('E0E0E0');

// Borders for title
$sheet->getStyle('B4:H4')->applyFromArray($styleBorderArray);

// Table Headings
$headingStyle = [
    'font' => ['bold' => true, 'size' => 10]
];

$columns = ['B' => 'First Name', 'C' => 'Last Name', 'D' => 'Rank', 'E' => 'Rotation', 'F' => 'Supervised By', 'G' => 'Equipment Used', 'H' => 'Equipment Usage Date'];
$row = 6;

foreach ($columns as $col => $text) {
    $sheet->setCellValue($col . $row, $text);
    $sheet->getStyle($col . $row)->applyFromArray($headingStyle);
    $sheet->getStyle($col . $row)->getAlignment()->applyFromArray(['horizontal' => Alignment::HORIZONTAL_CENTER]);
}

$sheet->getStyle('B6:H6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');

// Start printing rows from the next row
$row++;

if ($rowsEquipment) {
    $styleArray = ['font' => ['size' => 10]];

    while ($rowData = mysqli_fetch_array($rowsEquipment)) {
        $firstName = stripslashes($rowData['firstName']);
        $lastName = stripslashes($rowData['lastName']);
        $Rankname = stripslashes($rowData['Rankname']);
        $equipmentUsageDate = stripslashes($rowData['equipmentUsageDate']);
        $Rotationname = stripslashes($rowData['rotationname']);
        $clinicianfname = stripslashes($rowData['clinicianfname']);
        $clinicianlname = stripslashes($rowData['clinicianlname']);
        $clinicianfullname = $clinicianfname . ' ' . $clinicianlname;

        // Populate cells with the row data
        $sheet->setCellValue('B' . $row, $firstName);
        $sheet->getStyle('B' . $row)->getAlignment()->applyFromArray(['horizontal' => Alignment::HORIZONTAL_LEFT]);
        $sheet->getStyle('B' . $row)->applyFromArray($styleArray);

        $sheet->setCellValue('C' . $row, $lastName);
        $sheet->getStyle('C' . $row)->getAlignment()->applyFromArray(['horizontal' => Alignment::HORIZONTAL_LEFT]);
        $sheet->getStyle('C' . $row)->applyFromArray($styleArray);

        $sheet->setCellValue('D' . $row, $Rankname);
        $sheet->getStyle('D' . $row)->getAlignment()->applyFromArray(['horizontal' => Alignment::HORIZONTAL_LEFT]);
        $sheet->getStyle('D' . $row)->applyFromArray($styleArray);

        $sheet->setCellValue('E' . $row, $Rotationname);
        $sheet->getStyle('E' . $row)->getAlignment()->applyFromArray(['horizontal' => Alignment::HORIZONTAL_LEFT]);
        $sheet->getStyle('E' . $row)->applyFromArray($styleArray);

        $sheet->setCellValue('F' . $row, $clinicianfullname);
        $sheet->getStyle('F' . $row)->getAlignment()->applyFromArray(['horizontal' => Alignment::HORIZONTAL_LEFT]);
        $sheet->getStyle('F' . $row)->applyFromArray($styleArray);

        $sheet->setCellValue('G' . $row, '-');
        $sheet->getStyle('G' . $row)->getAlignment()->applyFromArray(['horizontal' => Alignment::HORIZONTAL_CENTER]);
        $sheet->getStyle('G' . $row)->applyFromArray($styleArray);

        $sheet->setCellValue('H' . $row, $equipmentUsageDate);
        $sheet->getStyle('H' . $row)->getAlignment()->applyFromArray(['horizontal' => Alignment::HORIZONTAL_CENTER]);
        $sheet->getStyle('H' . $row)->applyFromArray($styleArray);

        $row++;
    }
}

// Apply borders to the data rows
$sheet->getStyle('B6:H' . ($row - 1))->applyFromArray(['borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]]);

// Auto size columns
foreach (range('B', 'H') as $columnID) {
	$sheet->getColumnDimension($columnID)->setAutoSize(true);
}
$sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows

// Write to file
$reportname = 'EquipmentReport_';
?>
