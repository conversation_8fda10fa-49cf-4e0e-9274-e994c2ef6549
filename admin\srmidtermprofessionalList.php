<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsMasteryEval.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsCourses.php');
include('../class/clsSrMidtermProfessionalEval.php');
include('../class/clsExternalPreceptors.php');


$loggedUserId = $_SESSION["loggedUserId"];
$midtermrotationid = 0;
$schoolId = 0;
$currentstudentId = 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$rotationId = 0;
$courseId = 0;
$Type = '';
$rotationtitle = '';
$SrMidtermProfessionalEvaldetails = '';



//For Rotation Site
if (isset($_GET['srMidTermRotationId'])) {
    $rotationId = $_GET['srMidTermRotationId'];
    $rotationId = DecodeQueryData($rotationId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

if (isset($_GET['Type'])) {
    $Type = ($_GET['Type']);
}

$studentId = isset($_GET['studentId']) ? DecodeQueryData($_GET['studentId']) : 0;
$title = "Sr Professional Evaluation |" . $transchooldisplayName;

//For Daily Weekly List
$objSrMidtermProfessionalEval = new clsSrMidtermProfessionalEval();
$SrMidtermProfessionalEvaldetails = $objSrMidtermProfessionalEval->GetAllSrMidTermEvaluationList($schoolId, $rotationId, $studentId);


$SrMidtermProfessionalEvalCount = 0;
if ($SrMidtermProfessionalEvaldetails != '') {
    $SrMidtermProfessionalEvalCount = mysqli_num_rows($SrMidtermProfessionalEvaldetails);
}
unset($objSrMidtermProfessionalEval);

//For Rotation Name
$objRotation = new clsRotation();
if ($rotationId != '') {
    $rotationtitle = $objRotation->GetrotationTitle($rotationId, $schoolId);
}
//Get Course List for dropdown
$objCourses = new clsCourses();
$courseList = $objCourses->GetAllCoursesBySchool($currentSchoolId);
unset($objCourses);

// for Student
$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $studentId);
$studentfullname = $Rowstudent ? $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']:'';
unset($objStudent);


$objDB = new clsDB();
$getTimezoneId = $objDB->GetSingleColumnValueFromTable('schools', 'timeZoneId', 'schoolId', $currentSchoolId);
$TimeZone = $objDB->GetSingleColumnValueFromTable('timezonemaster', 'timezone', 'timeZoneId', $getTimezoneId);
unset($objDB);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link href="https://cdn.datatables.net/fixedcolumns/3.2.4/css/fixedColumns.bootstrap4.min.css" rel="stylesheet" />
    <style>
        .DTFC_LeftBodyLiner {
            overflow-y: unset !important
        }

        .DTFC_RightBodyLiner {
            overflow-y: unset !important
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="rotations.html">Rotations</a></li>

                    <?php if (isset($_GET['studentId'])) { ?> <li><a href="clinical.html">Clinical</a></li>
                        <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>

                    <?php } else { ?>
                        <li><a href="rotations.html?active=1"><?php echo ($rotationtitle); ?></a></li>
                    <?php } ?>
                    <li>Sr Professional Evaluation</li>
                </ol>
            </div>

            <div class="pull-right">
                <?php
                if ($rotationId > 0) { ?>
                    <!-- <a class="btn btn-link" href="addMidtermProfessional.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>">Add</a> -->
                <?php } else { ?>
                    <!-- <a class="btn btn-link" href="addMidtermProfessional.html">Add</a> -->
                <?php } ?>
            </div>


        </div>
    </div>

    <div class="custom-container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Sr Midterm Professional Evaluation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Sr Midterm Professional Evaluation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Sr Midterm Professional Evaluation deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>
        <!-- <?php if ($rotationId != 0) { ?>
            <div class="row">
                <div class="col-md-6 pull-right">
                    <div class="col-md-8">
                        <label class=" control-label  pull-right margin_top_seven" for="cboCourse " >Course:</label>
                    </div>
                    <div class="form-group col-md-4">
                        <select id="cboCourse" name="cboCourse" class="form-control input-md required-input select2_single" rotationId=<?php echo (EncodeQueryData($rotationId)); ?>>
                            <option value="" selected>Select</option>
                            <?php
                            if ($courseList != "") {
                                while ($row = mysqli_fetch_assoc($courseList)) {
                                    $selcourseId  = $row['courseId'];
                                    $name  = stripslashes($row['title']);

                            ?>
                                    <option value="<?php echo (EncodeQueryData($selcourseId)); ?>" <?php if ($courseId == $selcourseId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                            <?php
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
            </div>
        <?php } ?> -->

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>First<br>Name</th>
                    <th>Last<br>Name</th>
                    <th>Rotation</th>
                    <th style="text-align: center">Evaluation<br>Date</th>
                    <th style="text-align: center">Student<br>Sign<br>Date</th>
                    <th style="text-align: center">Technologist<br>Sign<br>Date</th>
                    <th style="text-align: center">Exceeds <br>Expectation</th>
                    <th style="text-align: center">Meets <br>Expectations</th>
                    <th style="text-align: center">Below <br>Expectations</th>
                    <th style="text-align: center">Failing</th>
                    <th style="text-align: center">Total <br> Evaluation Points</th>
                    <th style="text-align: center">Total <br>Percentage</th>
                    <th style="text-align: center">Comment</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($SrMidtermProfessionalEvalCount > 0) {
                    while ($row = mysqli_fetch_array($SrMidtermProfessionalEvaldetails)) {

                        //$title = $row['title'];
                        $rotationame = $row['rotationName'];
                        // $Ranktitle = $row['Ranktitle'];
                        $studentId = $row['studentId'];
                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $studentName = $firstName . ' ' . $lastName;
                        $studentSrMidTermEvalId = $row['studentSrMidTermEvalId'];
                        $evaluationDate = stripslashes($row['evaluationDate']);
                        $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                        $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                        // $courselocationId = $row['locationId'];
                        $rotationId = $row['rotationId'];

                        $summarycomments = $row['summeryComments'];
                        $comment = ($summarycomments != '') ? 'Yes' : 'No';

                        // $studentcomments = $row['studentcomments'];    
                        // $parentRotationId = stripslashes($row['parentRotationId']);
                        // $rotationLocationId = stripslashes($row['rotationLocationId']);
                        // $locationId = 0;

                        // if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                        //     if (!$rotationLocationId)
                        //         $locationId = $objRotation->GetLocationByRotation($rotationId);
                        //     else
                        //         $locationId  = $rotationLocationId;
                        // } else {
                        //     $locationId  = $courselocationId;
                        // }

                        //Get Time Zone By Rotation 
                        // $objLocation = new clsLocations();
                        // $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        // unset($objLocation);
                        // if ($TimeZone == '')
                        //     $TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];

                        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                        $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));

                        $technologiestId = $row['technologiestId'];
                        $isTechnologistCompletedStatus = $row['isTechnologistCompletedStatus'];
                        if ($technologiestId > 0) {
                            $objExternalPreceptors = new clsExternalPreceptors();
                            $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($technologiestId);
                            $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                            $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                            $preceptorNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
                            $maskedPreceptorNum = maskNumber($preceptorNum);
                            $technologiestFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                        }


                        $dateOftechnologiestSignature = stripslashes($row['dateOftechnologiestSignature']);
                        $dateOftechnologiestSignature = converFromServerTimeZone($dateOftechnologiestSignature, $TimeZone);
                        $dateOftechnologiestSignature = date("m/d/Y", strtotime($dateOftechnologiestSignature));


                        //For Avg
                        $firstSectionAvg = $row['firstSectionAvg'];
                        $secondSectionAvg = $row['secondSectionAvg'];
                        $thirdSectionAvg = $row['thirdSectionAvg'];
                        $fourthSectionAvg = $row['fourthSectionAvg'];
                        $totalEvalPoints = $row['totalEvalPoints'];

                        $totalAvg = $row['totalAvg'];

                ?>
                        <tr>
                            <td><?php echo ($firstName); ?></td>
                            <td><?php echo ($lastName); ?></td>
                            <td><?php echo ($rotationame); ?></td>
                            <td style="text-align: center"><?php echo ($evaluationDate); ?></td>
                            <td style="text-align: center"><?php
                                                            if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {

                                                                echo ($dateOfStudentSignature);
                                                            } else {
                                                                echo "-";
                                                            }
                                                            ?></td>
                            <td>
                                <?php
                                if ($technologiestId > 0) {

                                    $technologiestInfo = 'Name: ' . $technologiestFullName . '</br>Phone: ' . $maskedPreceptorNum;

                                    if ($isTechnologistCompletedStatus)
                                        $technologiestInfo .= '</br>Status: Completed';
                                    else

                                        $technologiestInfo .= '</br>Status: Pending';

                                    if ($dateOftechnologiestSignature != '' && $dateOftechnologiestSignature != '12/31/1969' && $dateOftechnologiestSignature != '01/01/1970' && $dateOftechnologiestSignature != '0000-00-00')
                                        $technologiestInfo .= '</br>Date: ' . $dateOftechnologiestSignature;

                                    echo $technologiestInfo;

                                    if ($isTechnologistCompletedStatus == 0) { ?>
                                        <br> <!--<a href="javascript:void(0);" class="reSendrequest" data-toggle="modal" data-target="#resendModal" preceptorId="<?php echo $technologiestId; ?>" preceptorNum="<?php echo $preceptorNum; ?>" evaluationId="<?php echo $studentSrMidTermEvalId; ?>" evaluationType='SPE' rotationId="<?php echo $rotationId; ?>" onclick="ShowEvaluationDetails(this)">Resend SMS</a> -->
                                        <a href="javascript:void(0);" class="copyLink" preceptorId=<?php echo EncodeQueryData($technologiestId); ?> preceptornum="<?php echo $preceptorNum; ?>" evaluationId="<?php echo $studentSrMidTermEvalId; ?>" rotationId="<?php echo ($rotationId); ?>" evaluationType='SPE' onclick="copyLinkUrl(this)">Click to Copy URL</a>
                                        <!-- <br> <a href="javascript:void(0);" class="reSendEmailrequest" data-toggle="modal" preceptorId="<?php echo EncodeQueryData($technologiestId); ?>" preceptorNum="<?php echo $preceptorNum; ?>" evaluationId="<?php echo $studentSrMidTermEvalId; ?>" rotationId="<?php echo $rotationId; ?>" data-target="#resendEmailModal" email="<?php echo $loggedStudentEmailId; ?>" evaluationType='SPE' onclick="ShowEvaluationDetailsForEmail(this)">Send URL to Email</a> -->
                                <?php  }
                                } else {
                                    echo "-";
                                }
                                ?>
                            </td>
                            <td style="text-align: center"><?php echo $firstSectionAvg; ?></td>
                            <td style="text-align: center"><?php echo $secondSectionAvg; ?></td>
                            <td style="text-align: center"><?php echo $thirdSectionAvg; ?></td>
                            <td style="text-align: center"><?php echo $fourthSectionAvg; ?></td>
                            <td style="text-align: center"><?php echo $totalEvalPoints; ?></td>

                            <td style="text-align: center"><?php echo $totalAvg; ?></td>
                            <td style="text-align: center"><?php echo ($comment); ?></td>
                            <td style="text-align: center">

                                <a href="addMidtermProfessional.html?studentSrMidTermEvalId=<?php echo (EncodeQueryData($studentSrMidTermEvalId)); ?>&view=V">View</a>

                                <?php if (isset($_SESSION["loggedAsBackUserId"])) { ?>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow" studentSrMidTermEvalId="<?php echo EncodeQueryData($studentSrMidTermEvalId); ?>">Delete</a>
                                <?php } ?>



                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objRotation);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="https://cdn.datatables.net/fixedcolumns/3.2.4/js/dataTables.fixedColumns.min.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });
        $(".select2_single").select2();

        var current_datatable = $("#datatable-responsive").DataTable({

            responsive: false,
            scrollX: true,
            "ordering": true,
            "order": [
                [1, "desc"]
            ],
            "aoColumns": [{
                    "sWidth": "20%"
                }, {
                    "sWidth": "20%",

                }, {
                    "sWidth": "30%",

                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "20%"
                }, {
                    "sWidth": "35%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "25%"
                }, {
                    "sWidth": "25%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "25%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "15%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
                {
                    "sWidth": "15%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
                {
                    "sWidth": "15%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }
            ]
        });

        // ajax call for deleteAjaxRow
        $(document).on('click', '.deleteAjaxRow', function() {
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentSrMidTermEvalId = $(this).attr('studentSrMidTermEvalId');
            var title = $(this).attr('studentName');

            alertify.confirm('Mastery Eval: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentSrMidTermEvalId,
                        type: 'Mastery'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});
        });

        $("#cboCourse").change(function() {
            var courseId = $(this).val();
            var rotationId = $(this).attr('rotationId');
            if (courseId) {
                window.location.href = "masteryList.html?courseId=" + courseId + "&rotationId=" + rotationId;
            } else {
                window.location.href = "masteryList.html";
            }
        });

        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentSrMidTermEvalId = $(this).attr('studentSrMidTermEvalId');
            var userId='<?php echo EncodeQueryData($loggedUserId) ?>';
            var isUser=1;

            alertify.confirm('SPE ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentSrMidTermEvalId,
                        userId:userId,
                        isUser:isUser,
                        type: 'SPE'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});
        });
    </script>


</body>

</html>