<?php
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSystemUser.php');
	include('../class/clsSchool.php');
    include('../class/clsClinician.php');
	include('../setRequest.php');
    
	$clinicianId=0;
	$clinicianIdImmunizationId=0;
    if(isset($_GET['clinicianId'])) 
	{
		$clinicianId= DecodeQueryData($_GET['clinicianId']);	
		
	}
	if(isset($_GET['Type'])) 
	{
		$Type= ($_GET['Type']);
          
	}
	if(isset($_GET['clinicianIdImmunizationId'])) 
	{
		$clinicianIdImmunizationId= DecodeQueryData($_GET['clinicianIdImmunizationId']);
         
	}
	
	if($Type =='C')
	{
		$recordid =0;
	}
	else
	{
		$recordid =1;
	}
	
    
	$loggedUserId = $_SESSION["loggedUserId"];  
	
	
	if(isset($_POST['btnSubmit']))
	{
			
		
			if(isset($_FILES['clinicianDocument']))
			{

				$docTitle =($_FILES['clinicianDocument']['name']);
				
				$totalDocumentCount =count(array_filter($_FILES['clinicianDocument']['name']));
				if($totalDocumentCount > 0) 
				{
					
					$objclinician = new clsClinician(); 				
					
					if($docTitle) 
					{

						

						//Check User Directory
						$uploaddir = "../upload/schools/".$currentSchoolId;
						if(!file_exists($uploaddir))
						{
							mkdir($uploaddir); 
						}
						$uploaddir .= "/Clinician";
						
						if(!file_exists($uploaddir))
						{
							mkdir($uploaddir); 
						}

						$uploaddir .= "/".$clinicianId."/";
						
						if(!file_exists($uploaddir))
						{
							mkdir($uploaddir); 
						}

						$uploaddir .= "documents/";
						
						if(!file_exists($uploaddir))
						{
							mkdir($uploaddir); 
						}
						
						for ($i = 0; $i < count($_FILES['clinicianDocument']['name']); $i++) 
						{
							if($docTitle[$i]!='')
							{
								$filetitle = preg_replace('/[&%$]+/', '-', $docTitle[$i]);
								$exitsFile =$objclinician->getClinicianFile($clinicianId,$filetitle);
					
								if($exitsFile)
									{
										header('location:clinicianDocumentUpload.html?clinicianId='.EncodeQueryData($clinicianId).'&Type='.$Type.'&clinicianIdImmunizationId='.EncodeQueryData($clinicianIdImmunizationId).'&status=DuplicateFile');
										exit();
									}

								$ext = strtolower(pathinfo($filetitle, PATHINFO_EXTENSION));
								if($ext!="pdf")
								{
									header('location:clinicianDocumentUpload.html?clinicianId='.EncodeQueryData($clinicianId).'&Type='.$Type.'&clinicianIdImmunizationId='.EncodeQueryData($clinicianIdImmunizationId).'&status=InvalidFile');
									exit();
								}

								
						
								
								$UploadLargeFilePath = $uploaddir.$filetitle;
								copy($_FILES['clinicianDocument']['tmp_name'][$i], $UploadLargeFilePath);

							
								
								$objclinician->recordid = $recordid;
								$objclinician->documetype = $Type;
								$objclinician->clinicianId = $clinicianId;
								$objclinician->uploadedBy = $loggedUserId;
								$objclinician->fileTitle = $filetitle;
								$objclinician->clinicianIdImmunizationId = $clinicianIdImmunizationId;
								$objclinician->fileName = $clinicianId.'_'.$filetitle;

								
								//-----------------------------------
								$retclinicianDocumentId = $objclinician->saveClinicianDocument();
								//-----------------------------------
							}
						}	
					}
				}
			}
			
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = $objLog::ADD;
			$type= "Document";
			$userType = $objLog::ADMIN; // User type is set to ADMIN
			$IsMobile = 0;

			$objclinician = new clsClinician(); 
			$objclinician->saveClinicianAuditLog($retclinicianDocumentId, $loggedUserId, $userType, $action, $IsMobile,0,$type);
			unset($objclinician);

			unset($objLog);
			//Audit Log Ends
			
			unset($objclinician);
			header('location:clinicianDocuments.html?clinicianId='.EncodeQueryData($clinicianId).'&Type='.$Type.'&clinicianIdImmunizationId='.EncodeQueryData($clinicianIdImmunizationId));
		
	}
	else
	{
		header('location:clinicianDocuments.html');
	}
	
?>