<?php
 include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');    
    include('../class/clsUsafMasterCheckoffQuestion.php'); 
	include('../setRequest.php');
	
// print_r($_POST);exit;

	$questionId=0;
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		$checkoffQuestionId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;
		
		if(isset($_GET['sectionId']))
		{
			$sectionId=DecodeQueryData($_GET['sectionId']);
		}
		if(isset($_GET['topicid']))
		{
			$topicid=DecodeQueryData($_GET['topicid']);
		}
	
		$questionType  = ($_POST['questionType']);
		$question  = ($_POST['question']);
		$txtsinglechoice  = ($_POST['txtsinglechoice']);
		$txtsinglechoicemarks  = ($_POST['txtsinglechoicemarks']);
		$answersid  = ($_POST['answers']);
		$longans  = ($_POST['longans']);
		$yesnoanswers  = ($_POST['yesnoanswers']);
		$txtyesno  = ($_POST['txtyesno']);
		$SortOrder  = ($_POST['SortOrder']);
		$Marks  = ($_POST['Marks']);
		
		$objQuestionMaster = new clsUsafMasterCheckoffQuestion();
		$objQuestionMaster->questionTitle =$question;
		$objQuestionMaster->questionType =$questionType;		
		$objQuestionMaster->sortOrder =$SortOrder;
		$objQuestionMaster->marks =$Marks;
		$RetQuestionId=$objQuestionMaster->SaveUsafQuestionsMaster($questionId);
		
		if(!isset($_GET['editid']))
		{
				$objQuestionMaster->defaultTopicId=$topicid;
				$objQuestionMaster->sectionId=$sectionId;
				$objQuestionMaster->questionId=$RetQuestionId;
				$objQuestionMaster->SaveUsafCheckoffQuestionInTopicDetails($RetQuestionId);
		}
		switch($questionType)
			{
				case "2": 
									 foreach($txtsinglechoice as $key=>$value)
								{
									  
									   $objQuestionMaster->questionId = $RetQuestionId;
									   $objQuestionMaster->optionText = $value;
									   $objQuestionMaster->optionValue = $answersid[$key];
									   $objQuestionMaster->isCorrectAnswer = $txtsinglechoicemarks[$key];
									 $QuestionDetailId=$objQuestionMaster->SaveUsafQuestionsDetails($RetQuestionId);
								 }
				break;
				case "1": 
									  foreach($txtyesno as $keys=>$value)
								{
									  
									   $objQuestionMaster->questionId = $RetQuestionId;
									   $objQuestionMaster->optionText = $value;
									   $objQuestionMaster->optionValue = $yesnoanswers[$keys];
									   $objQuestionMaster->isCorrectAnswer = 0;
									 $QuestionDetailId=$objQuestionMaster->SaveUsafQuestionsDetails($RetQuestionId);
								 }
				break;
							
			}
			
			unset($objQuestionMaster);
		
		if($RetQuestionId > 0)
		{
			 
			header('location:usafmasterquestion.html?sectionId='.EncodeQueryData($sectionId).'&status=added');	
			//header('location:questions.html?sectionId='.EncodeQueryData($sectionId).'&status='.$status);	
			exit();
		
		}
		else
		{
			header('location:usafmasteraddquestion.html?status=error');
		}
		 
	}	
	else
	{
		// if($topicid > 0)
		// header('location:usafmasterquestion.html?status='.$status.'&topicid='.EncodeQueryData($topicid));
		// else
		// header('location:usafmasterquestion.html?status='.$status);

		exit();
	}
?>