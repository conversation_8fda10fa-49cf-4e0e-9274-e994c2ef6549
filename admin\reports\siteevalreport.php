<?php

require '../vendor/autoload.php'; // PhpSpreadsheet autoload

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

$objSiteevaluation = new clsSiteevaluation();	
$individual_student = unserialize($individual_student);

$rowsSiteevaluation = $objSiteevaluation->GetStudentSiteEvaluationDetailsForreport(
    $currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, 
    $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, 
    $cbosemester, $subcborotation, $selTopicId
);

$spreadsheet = new Spreadsheet();

// Set document properties
$spreadsheet->getProperties()
    ->setCreator($schoolname)
    ->setLastModifiedBy('JCC')
    ->setTitle('Reports')
    ->setSubject('School Report')
    ->setDescription('All School Reports');

// Active sheet
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('Site Evaluation Reports');

// Print Heading	
$headerStyleArray = [
    'font' => ['bold' => true, 'size' => 16]
];

$sheet->mergeCells("B2:J2");
$sheet->setCellValue('B2', $schoolname);
$sheet->getStyle('B2')->applyFromArray($headerStyleArray);
$sheet->getStyle('B2')->getAlignment()->setHorizontal('center');
$sheet->getStyle('B2')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('E0E0E0');

$styleBorderArray = [
    'borders' => ['allBorders' => ['borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN]]
];
$sheet->getStyle('B2:J2')->applyFromArray($styleBorderArray);

$titleStyleArray = [
    'font' => ['bold' => true, 'size' => 12]
];

$sheet->mergeCells("B4:J4");
$sheet->setCellValue('B4', 'Site Evaluation Report');
$sheet->getStyle('B4')->applyFromArray($titleStyleArray);
$sheet->getStyle('B4')->getAlignment()->setHorizontal('center');
$sheet->getStyle('B4')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('E0E0E0');
$sheet->getStyle('B4:J4')->applyFromArray($styleBorderArray);

// Make Table Heading
$tableHeadingStyleArray = [
    'font' => ['bold' => true, 'size' => 10]
];

$headings = ['First Name', 'Last Name', 'Rank', 'Rotation', 'Evaluation Date', 
    'Recommend Clinical Affilate', 'Clinical Location Strengths', 
    'Clinical Location Need To Improve', 'Score'];
$columns = range('B', 'J');

foreach ($columns as $index => $column) {
    $sheet->setCellValue($column . '6', $headings[$index]);
    $sheet->getStyle($column . '6')->applyFromArray($tableHeadingStyleArray);
    $sheet->getStyle($column . '6')->getAlignment()->setHorizontal('center');
}
$sheet->getStyle('B6:J6')->getFill()->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)->getStartColor()->setRGB('E0E0E0');

// Add data rows
$printStartRowCounter = 7;

if ($rowsSiteevaluation) {
    $dataStyleArray = [
        'font' => ['size' => 10]
    ];
    
    while ($row = mysqli_fetch_array($rowsSiteevaluation)) {
        $csEvaluationMasterId = stripslashes($row['csEvaluationMasterId']);
        $firstName = stripslashes($row['firstName']);
        $lastName = stripslashes($row['lastName']);
        $Rankname = stripslashes($row['Rankname']);
        $evaluationDate = stripslashes($row['evaluationDate']);
        $Rotationname = stripslashes($row['Rotationname']);

        $GetSiteevaluationScore = $objSiteevaluation->GetSiteEvaluationScore($csEvaluationMasterId);
        $EvaluationScore = number_format((float)$GetSiteevaluationScore['EvaluationScore'], 2, '.', '');

        $GetRecommendClinicalAffilate = $objSiteevaluation->GetRecommendClinicalAffilate($csEvaluationMasterId);
        $GetClinicalLocationStrengths = $objSiteevaluation->GetClinicalLocationStrengths($csEvaluationMasterId);
        $GetClinicalLocationNeedToImprove = $objSiteevaluation->GetClinicalLocationNeedToImprove($csEvaluationMasterId);

        $sheet->setCellValue('B' . $printStartRowCounter, $firstName);
        $sheet->setCellValue('C' . $printStartRowCounter, $lastName);
        $sheet->setCellValue('D' . $printStartRowCounter, $Rankname);
        $sheet->setCellValue('E' . $printStartRowCounter, $Rotationname);
        $sheet->setCellValue('F' . $printStartRowCounter, date('m/d/Y', strtotime($evaluationDate)));
        $sheet->setCellValue('G' . $printStartRowCounter, $GetRecommendClinicalAffilate);
        $sheet->setCellValue('H' . $printStartRowCounter, $GetClinicalLocationStrengths);
        $sheet->setCellValue('I' . $printStartRowCounter, $GetClinicalLocationNeedToImprove);
        $sheet->setCellValue('J' . $printStartRowCounter, $EvaluationScore);

        $printStartRowCounter++;
    }
}

// Apply borders to data range
$printStartRowCounter--;
$sheet->getStyle("B6:J" . $printStartRowCounter)->applyFromArray($styleBorderArray);

// Auto-size columns
foreach (range('B', 'J') as $columnID) {
    $sheet->getColumnDimension($columnID)->setAutoSize(true);
}
$sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows

// Save spreadsheet
$reportname = 'SiteEvaluationReport_';

?>
