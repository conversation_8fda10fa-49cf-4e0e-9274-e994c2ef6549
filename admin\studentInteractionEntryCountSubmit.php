<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');    
	include('../class/clsStudentMinCharacterEntry.php');
	include('../setRequest.php'); 
	
		
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		
		
		$intercationCount = isset($_POST['txtIntercationCount']) ? $_POST['txtIntercationCount'] : 0;

		$id = isset($_GET['id']) ? $_GET['id'] : 0;
		//Save data
		$objJournal = new clsStudentMinCharacterEntry();
		$objJournal->interactionCharacterCount = $intercationCount;	
		$retCountId = $objJournal->SaveStudentIntercationCount($id,$currentSchoolId);
		unset($objJournal);

		if($retCountId > 0)
		{
				header('location:settings.html?status=added');
		}
		else
		{
			header('location:studentJournalEntryCount.html?status=error');
		}
	}
	else
	{
		header('location:settings.html');
		exit();
	}

?>