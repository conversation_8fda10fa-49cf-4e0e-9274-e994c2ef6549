<?php
ini_set('memory_limit', '256M');
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsRotation.php');
include('../class/clsStudent.php');
include('../class/clsLocations.php');
include('../class/clsRotationDetails.php');

include('../setRequest.php');
require_once "PHPExcel/PHPExcel.php"; // Or whatver the path to your PHPExcel library is
require_once "PHPExcel/PHPExcel/IOFactory.php";


$currentSchoolId;
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

//Get RotationId
$rotationId = $_GET['rotationId'];
$default_rotationId = DecodeQueryData($rotationId);

//For Rotation Name
$objRotation = new clsRotation();
$rotation = $objRotation->GetrotationDetails($default_rotationId, $currentSchoolId);
$rotationtitle = isset($rotation['title']) ? $rotation['title'] : '';
$startDateTime = converFromServerTimeZone($rotation['startDate'], $TimeZone);
$startDate = date('m/d/Y', strtotime($startDateTime));
$endDateTime = converFromServerTimeZone($rotation['endDate'], $TimeZone);
$endDate = date('m/d/Y', strtotime($endDateTime));

$currentDate = date('Y-m-d', strtotime($startDate));
$currentEndDate = date('Y-m-d', strtotime($endDate));

function getDatesBetween($startDate, $endDate)
{
    $dates = array();
    $currentDate = strtotime($startDate);
    $endDate = strtotime($endDate);

    while ($currentDate <= $endDate) {
        $dates[] = date('m/d/Y', $currentDate);
        $currentDate = strtotime('+1 day', $currentDate);
    }

    return $dates;
}


$dateRange = getDatesBetween($startDate, $endDate);

// echo '<pre>';
// print_r($dateRange);exit;
//For Rotation List
$objRotationDetails = new clsRotationDetails();
// $rowsRotations = $objRotationDetails->GetAllAssignSubRotationStudents($default_rotationId);
$rowsRotations = $objRotation->GetAllSubRotation($currentSchoolId, $locationId=0, $courseId=0, $default_rotationId);

$totalRotations = 0;
if ($rowsRotations != '') {
    $totalRotations = mysqli_num_rows($rowsRotations);
}
// echo $totalRotations;exit;

$title = 'Student Assign Rotation List';
date_default_timezone_set('Asia/Kolkata');
$today = (date('m/d/Y, H:i A'));

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnStudentExport']))

    $reportType = $_POST['cboreporttype'];


switch ($reportType) {
    case "StudentRotationAssignList":

        $objPHPExcel = new \PHPExcel();

        // Set document properties
        $objPHPExcel->getProperties()->setCreator('Schools')
            ->setLastModifiedBy('JCC')
            ->setTitle('Reports')
            ->setSubject('Student Assign Rotation List')
            ->setDescription('All School Reports');

        //Active Sheet
        $objPHPExcel->setActiveSheetIndex(0);
        $objPHPExcel->getActiveSheet()->setTitle();

        //Print Heading	
        $headerstyleArray = array('font'  => array('bold'  => true, 'size'  => 16));

        $objPHPExcel->getActiveSheet()->setCellValue('B2', $title);
        $objPHPExcel->getActiveSheet()->getStyle('B2')->applyFromArray($headerstyleArray);
        $objPHPExcel->getActiveSheet()->getStyle('B2')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

        $objPHPExcel->getActiveSheet()
            ->getStyle('B2')
            ->getFill()
            ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('E0E0E0');

        $styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));


        $styleArray = array('font'  => array('bold'  => true, 'size'  => 12));

        $objPHPExcel->getActiveSheet()->setCellValue('B4', 'Rotation Name : ' . $rotationtitle);
        $objPHPExcel->getActiveSheet()->getStyle('B4')->applyFromArray($styleArray);
        $objPHPExcel->getActiveSheet()->getStyle('B4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

        $objPHPExcel->getActiveSheet()
            ->getStyle('B4')
            ->getFill()
            ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
            ->getStartColor()
            ->setRGB('E0E0E0');

        $styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));

        //Make Table Heading
        $styleArray = array('font'  => array('bold'  => true, 'size'  => 10));

        // $objPHPExcel->getActiveSheet()->setCellValue('A6', 'Students');
        // $objPHPExcel->getActiveSheet()->getStyle('A6')->applyFromArray($styleArray);
        // $objPHPExcel->getActiveSheet()->getStyle('A6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        // $objPHPExcel->getActiveSheet()->getDefaultColumnDimension('A6')->setWidth('30');

        $objPHPExcel->getActiveSheet()->setCellValue('B6', 'First Name');
        $objPHPExcel->getActiveSheet()->getStyle('B6')->applyFromArray($styleArray);
        $objPHPExcel->getActiveSheet()->getStyle('B6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        $objPHPExcel->getActiveSheet()->getDefaultColumnDimension('B6')->setWidth('30');

        $objPHPExcel->getActiveSheet()->setCellValue('C6', 'Last Name');
        $objPHPExcel->getActiveSheet()->getStyle('C6')->applyFromArray($styleArray);
        $objPHPExcel->getActiveSheet()->getStyle('C6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

        $objPHPExcel->getActiveSheet()->setCellValue('D6', 'Rank');
        $objPHPExcel->getActiveSheet()->getStyle('D6')->applyFromArray($styleArray);
        $objPHPExcel->getActiveSheet()->getStyle('D6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

        $objPHPExcel->getActiveSheet()->setCellValue('E6', 'Schedule');
        $objPHPExcel->getActiveSheet()->getStyle('E6')->applyFromArray($styleArray);
        $objPHPExcel->getActiveSheet()->getStyle('E6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        $objPHPExcel->getActiveSheet()->getDefaultColumnDimension('E6')->setWidth('30');

        $objPHPExcel->getActiveSheet()->setCellValue('F6', 'Course');
        $objPHPExcel->getActiveSheet()->getStyle('F6')->applyFromArray($styleArray);
        $objPHPExcel->getActiveSheet()->getStyle('F6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

        $objPHPExcel->getActiveSheet()->setCellValue('G6', 'Start Time');
        $objPHPExcel->getActiveSheet()->getStyle('G6')->applyFromArray($styleArray);
        $objPHPExcel->getActiveSheet()->getStyle('G6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

        $objPHPExcel->getActiveSheet()->setCellValue('H6', 'End Time');
        $objPHPExcel->getActiveSheet()->getStyle('H6')->applyFromArray($styleArray);
        $objPHPExcel->getActiveSheet()->getStyle('H6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

        $objPHPExcel->getActiveSheet()->setCellValue('I6', 'Duration');
        $objPHPExcel->getActiveSheet()->getStyle('I6')->applyFromArray($styleArray);
        $objPHPExcel->getActiveSheet()->getStyle('I6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        $objPHPExcel->getActiveSheet()->getDefaultColumnDimension('I6')->setWidth('30');

        $dates = $dateRange;
        $columnHeaders = array();

        // Generate column headers and associate with cell letters
        $columnLetter = 'J'; // Start from column 'M'
        foreach ($dates as $date) {
            $columnHeaders[$columnLetter . '6'] = $date;
            $lastUsedColumnLetter = $columnLetter;
            $columnLetter++;
        }

        // Iterate through the column headers and set headers and styles
        foreach ($columnHeaders as $column => $header) {
            $objPHPExcel->getActiveSheet()->setCellValue($column, $header);
            $objPHPExcel->getActiveSheet()->getStyle($column)->applyFromArray($styleArray);
            $objPHPExcel->getActiveSheet()->getStyle($column)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
            $objPHPExcel->getActiveSheet()->getDefaultColumnDimension($column)->setWidth('10');
        // You can apply additional styling here if needed
        }


        $printStartRowCounter = 8;
        if ($totalRotations > 0) {
            $styleArray = array('font'  => array('size'  => 10));
            while ($row = mysqli_fetch_array($rowsRotations)) {
                // $firstName = $row['firstName'];
               $rotationId = $row['rotationId'];

                // $lastName = $row['lastName'];
                // $Rankname = $row['Rankname'];
                $title = $row['title'];
                $startDateTime = $row['startDate'];

                $parentRotationId = $row['parentRotationId'];
                $rotationLocationId  = ($row['rotationLocationId']);
                $courselocationId = $row['locationId'];

                $locationId = 0;
                if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                    if (!$rotationLocationId)
                        $locationId = $objRotation->GetLocationByRotation($rotationId);
                    else
                        $locationId  = $rotationLocationId;
                } else {
                    $locationId  = $courselocationId;
                }

                //Get Time Zone By Rotation 
                $objLocation = new clsLocations();
                $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                unset($objLocation);

                if ($TimeZone == '')
                    $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

                $startDateTime = converFromServerTimeZone($startDateTime, $TimeZone);

                $startDate = date('m/d/Y', strtotime($startDateTime));
                $startTime = date('h:i A', strtotime($startDateTime));
                $endDateTime = $row['endDate'];
                $startDateTime = converFromServerTimeZone($endDateTime, $TimeZone);
                $endDate = date('m/d/Y', strtotime($endDateTime));
                $endTime = date('h:i A', strtotime($endDateTime));

                $duration = $row['duration'];
                $courseTitle = $row['courseTitle'];
                $hospitalSite = $row['hospitalSite'];
                $rotationScheduleDates = $objRotation->GetScheduleDateById($rotationId);
                $mydates = $dateRangeValues = explode(',', $rotationScheduleDates);
                // $dateRangeValues = implode(', ', $repeatDays);
                // print_r($dateRangeValues);
                $rowsRotationsDetails = $objRotationDetails->GetAllAssignStudentsDetails($rotationId);
                // $rotationStudentCount = ($rowsRotationsDetails != '') ? mysqli_num_rows($rowsRotationsDetails) : 0;
                // if ($rotationStudentCount == 0) {
                //     continue;
                // }
                $objPHPExcel->getActiveSheet()->setCellValue('E' . $printStartRowCounter, $title);
                $objPHPExcel->getActiveSheet()->getStyle('E' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                $objPHPExcel->getActiveSheet()->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);

                $objPHPExcel->getActiveSheet()->setCellValue('F' . $printStartRowCounter, $courseTitle);
                $objPHPExcel->getActiveSheet()->getStyle('F' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                $objPHPExcel->getActiveSheet()->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray);

                $objPHPExcel->getActiveSheet()->setCellValue('G' . $printStartRowCounter, $startTime);
                $objPHPExcel->getActiveSheet()->getStyle('G' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                $objPHPExcel->getActiveSheet()->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray);

                $objPHPExcel->getActiveSheet()->setCellValue('H' . $printStartRowCounter, $endTime);
                $objPHPExcel->getActiveSheet()->getStyle('H' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                $objPHPExcel->getActiveSheet()->getStyle('H' . $printStartRowCounter)->applyFromArray($styleArray);

                $objPHPExcel->getActiveSheet()->setCellValue('I' . $printStartRowCounter, $duration);
                $objPHPExcel->getActiveSheet()->getStyle('I' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                $objPHPExcel->getActiveSheet()->getStyle('I' . $printStartRowCounter)->applyFromArray($styleArray);

               
                $printStartRowCounter++;

                //Students



                //  $styleStudentColumn = array('font'  => array('bold'  => true,'size'  => 11));
                //  $objPHPExcel->getActiveSheet()->setCellValue('B'.$printStartRowCounter,'First Name');
                //  $objPHPExcel->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                //  $objPHPExcel->getActiveSheet()->getStyle('B'.$printStartRowCounter)->applyFromArray($styleStudentColumn);

                //  $objPHPExcel->getActiveSheet()->setCellValue('C'.$printStartRowCounter,'Last Name');
                //  $objPHPExcel->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                //  $objPHPExcel->getActiveSheet()->getStyle('C'.$printStartRowCounter)->applyFromArray($styleStudentColumn);

                //  $objPHPExcel->getActiveSheet()->setCellValue('D'.$printStartRowCounter,'Rank');
                //  $objPHPExcel->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                //  $objPHPExcel->getActiveSheet()->getStyle('D'.$printStartRowCounter)->applyFromArray($styleStudentColumn);


                //  $printStartRowCounter++;
                if ($rowsRotationsDetails != '') {

                    while ($row = mysqli_fetch_array($rowsRotationsDetails)) {



                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $Rankname = $row['Rankname'];
                        $hospitalSiteName = $row['hospitalSiteName'];
                        
                        $objPHPExcel->getActiveSheet()->setCellValue('B' . $printStartRowCounter, $firstName);
                        $objPHPExcel->getActiveSheet()->getStyle('B' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));

                        $objPHPExcel->getActiveSheet()->setCellValue('C' . $printStartRowCounter, $lastName);
                        $objPHPExcel->getActiveSheet()->getStyle('C' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));

                        $objPHPExcel->getActiveSheet()->setCellValue('D' . $printStartRowCounter, $Rankname);
                        $objPHPExcel->getActiveSheet()->getStyle('D' . $printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));

                        foreach ($mydates as $mydate) {
                            $columnLetters = 'J'; // Corresponding column for the current mydate
                            foreach ($dates as $date) {
                                if ($date == $mydate) {
                                    $cell = $columnLetters . $printStartRowCounter;
                                    $objPHPExcel->getActiveSheet()->setCellValue($cell, $hospitalSiteName);
                                    break; // Break the inner loop after finding the match
                                }
                                $columnLetters++;
                            }
                            // $rowNumber++; // Move to the next row for the next mydate
                        }
        

                        $printStartRowCounter++;
                    }
                    $printStartRowCounter++;
                }
            }
            unset($objRotation);
        }
        //Make Border
        // echo $columnLetterww = $columnLetter--;
        $printStartRowCounter--;
        $styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));
        $objPHPExcel->getActiveSheet()->mergeCells('B2:' . $lastUsedColumnLetter . '2');
        $objPHPExcel->getActiveSheet()->getStyle('B2:' . $lastUsedColumnLetter . '2')->applyFromArray($styleBorderArray);
        $objPHPExcel->getActiveSheet()->mergeCells('B4:' . $lastUsedColumnLetter .'4');
        $objPHPExcel->getActiveSheet()->getStyle('B4:' . $lastUsedColumnLetter .'4')->applyFromArray($styleBorderArray);
        $objPHPExcel->getActiveSheet()->getStyle('B6:' . $lastUsedColumnLetter . '6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
        $objPHPExcel->getActiveSheet()->getStyle('B6:' . $lastUsedColumnLetter . '6' . $printStartRowCounter)->applyFromArray($styleBorderArray);

        // Auto size columns for each worksheet
        foreach (range('B', 'I') as $columnID) {
            $objPHPExcel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
        }
        $reportname = 'StudentRotationAssignListReport_';
        break;

    default:
        echo "<b>Please Select Valid Type.</b>";
        break;
}
$objPHPExcel->setActiveSheetIndex();
// exit;
$currentDate = date('m_d_Y_h_i');

header('Content-type: application/vnd.ms-excel; charset=UTF-8');
header('Content-Disposition: attachment;filename="' . $reportname . $today . '.xls"');
header("Pragma: no-cache");
header("Expires: 0");

$objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
$objWriter->save('php://output');
