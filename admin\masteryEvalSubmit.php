<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsMasteryEval.php');
include('../class/clsQuestionOption.php');

$Type = '';
$studentMasteryEvalId = 0;
$evaluationDate = date('Y-m-d');

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
	$rotationId = 0;

	if (isset($_GET['studentMasteryEvalId'])) {
		$studentMasteryEvalId = $_GET['studentMasteryEvalId'];
		$studentMasteryEvalId = DecodeQueryData($studentMasteryEvalId);
	}
	if (isset($_GET['rotationId'])) {
		$rotationId = $_GET['rotationId'];
		$rotationId = DecodeQueryData($rotationId);
	}

	if (isset($_GET['studentId'])) {
		$studentId = $_GET['studentId'];
		$studentId = DecodeQueryData($studentId);
	}
	
	$studentMasteryEvalId = isset($_GET['studentMasteryEvalId']) ? DecodeQueryData($_GET['studentMasteryEvalId']) : 0;
	$status = ($studentMasteryEvalId > 0) ? 'updated' : 'added';

	$schoolDate = GetDateStringInServerFormat($_POST['schoolDate']);
	$schoolDate = GetDateStringInServerFormat($_POST['schoolDate']);
	$schoolDate = str_replace('00:00:00', '12:00 PM', $schoolDate);
	$schoolDate = date('Y-m-d H:i', strtotime($schoolDate));

	$objMasteryEval = new clsMasteryEval();
	
	$objMasteryEval->schoolDate = $schoolDate;
	$retDailyEvalId = $objMasteryEval->SaveAdminMastery($studentMasteryEvalId);


	if ($retDailyEvalId > 0) {
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a ID
		$action = ($studentMasteryEvalId > 0) ? $objLog::EDIT : $objLog::ADD;
		$userType = $objLog::ADMIN; // User type is set to ADMIN
		$IsMobile = 0;

		$objMasteryEval = new clsMasteryEval();
		$objMasteryEval->saveMasteryAuditLog($retDailyEvalId, $_SESSION['loggedUserId'], $userType, $action, $IsMobile);

		unset($objMasteryEval);
		unset($objLog);
		//Audit Log End

		if (isset($_GET['studentMasteryEvalId']))
			header('location:masteryList.html?studentId=' . EncodeQueryData($studentId) . '&selrotationId=' . EncodeQueryData($rotationId) . '&studentMasteryEvalId=' . EncodeQueryData($studentMasteryEvalId) . '&status=' . $status);
		else
			header('location:masteryList.html?studentId=' . EncodeQueryData($studentId) . '&selrotationId=' . EncodeQueryData($rotationId) . '&Type=' . $Type . '&status=' . $status);
		exit();
	} else {
		header('location:addMastery.html?status=error');
	}
} {
	header('location:masteryList.html');
	exit();
}
