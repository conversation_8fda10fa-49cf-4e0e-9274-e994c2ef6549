<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');   
    include('../class/clsStudent.php');
    include('../class/clsRotation.php');
    include('../class/clsLocations.php');
    include('../class/clsSiteevaluation.php');
	include('../setRequest.php');
	
    $currentstudentId='';
    $siteevaluationrotationid = 0;
    $schoolId = 0;
	$schoolId = $currentSchoolId;
    $transchooldisplayName = '';
	$loggedUserId = $_SESSION["loggedUserId"];   
	$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
	$loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0; 
    $canvasStatus= isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';
    $encodedRotationId = '';
    if(isset($_GET['siteevaluationrotationid'])) //Edit Mode
	{
		$siteevaluationrotationid = $_GET['siteevaluationrotationid'];
        $encodedRotationId = $_GET['siteevaluationrotationid'];
        $siteevaluationrotationid = DecodeQueryData($siteevaluationrotationid);
    }
    
    $transchooldisplayName=$currenschoolDisplayname;
    
    $encodedStudentId = '';
	if(isset($_GET['studentId']))
	{
        $encodedStudentId = $_GET['studentId'];
		$currentstudentId = $_GET['studentId'];
        $currentstudentId = DecodeQueryData($currentstudentId);
    }

    $type = isset($_GET['type']) ? $_GET['type'] : '';
	if($type == 'canvas')
		$canvasStatus =1;

    $title ="Site Evaluation| ".$transchooldisplayName;  
        
    //For All Site Evalution
    $objSiteevaluation=new clsSiteevaluation();
    $getCSevaluationdetails=$objSiteevaluation->GetAllCSEvaluation($siteevaluationrotationid,$currentstudentId,$canvasStatus,$currentSchoolId);
    $totalCSevaluationCount = 0;
    if($getCSevaluationdetails !='')
    {
        $totalCSevaluationCount = mysqli_num_rows($getCSevaluationdetails);
    }
    //unset($objSiteevaluation);
    
    //For Rotation Title
    $objRotation=new clsRotation();
    $RotationName=$objRotation->GetrotationDetails($siteevaluationrotationid,$schoolId);
    $rotationtitle= isset($RotationName['title']) ? $RotationName['title'] : '';

    $objStudent=new clsStudent();
    $Rowstudent=$objStudent->GetSingleStudent($schoolId,$currentstudentId);
    $studentfullname= $Rowstudent ? $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'] : '';
    unset($objStudent);
		
?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo($title); ?></title>
        <?php include('includes/headercss.php');?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
        <?php include("includes/datatablecss.php") ?>

		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css"> 


    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <?php if($type == 'canvas') { ?>
							<li><a href="dashboard.html">Home</a></li>
							<li><a href="settings.html">Settings</a></li>
							<li class="active">Site Evaluation</li>
						<?php } else { ?>

                            <li><a href="dashboard.html">Home</a></li>
                            <?php if(isset($_GET['studentId'])) 
                                { ?> <li><a href="clinical.html">Clinical</a></li>
                                <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>				
                                    
                                <?php } else { ?>
                            <li><a href="rotations.html">Rotations</a></li>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                            <?php } ?>
                            <li class="active">Site Evaluation</li>
                        <?php } ?>           
                        
                    </ol>
                </div>
         
                <?php if($type != 'canvas') { ?>
                    <div class="pull-right">
                    <?php if(isset($_GET['studentId'])) 
                                    { ?>
                            <!-- <a class="btn btn-link" href="siteevaluation.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Add</a> -->
            
                                <?php } else { ?>
                            <!-- <a class="btn btn-link" href="siteevaluation.html?siteevaluationrotationid=<?php echo EncodeQueryData($siteevaluationrotationid); ?>">Add</a> -->
                        <?php } ?>
                    </div>
                <?php } ?>
         
            </div>
        </div>

        <div class="container">

            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> CS Evaluation added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Site Evaluation updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Deleted")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> CS Evaluation deleted successfully.
                </div>
                <?php 
					}
                     else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>
			
                <div id="divTopLoading" >Loading...</div>
                <?php if($isActiveCanvas && $type !='canvas') { ?>
                <div class="row margin_bottom_ten">
                    <div class="col-md-8"></div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-6 control-label text-right" for="" style="margin-top:8px">Canvas Status</label>
                            <div class="col-md-6 padding_right_zero padding_left_zero">
                                <select id="canvasStatus" name="canvasStatus" class="form-control input-md required-input select2_single"  >
                                    <option value="" selected>All</option>
                                    <option value="1" <?php if($canvasStatus==1) { ?> selected="true" <?php } ?>>Sent</option>
                                    <option value="0" <?php if($canvasStatus==0 && $canvasStatus!='' ) { ?> selected="true" <?php } ?>>Not Sent</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                <?php } ?>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>First Name</th>
                            <th>Last Name</th>                            
                            <th>Rotation</th>                            
                            <th style="text-align: center">Evaluation Date</th>   
                            <th style="text-align: center">Average Rating</th>  
                            <?php if($type !='canvas') { ?>                      
                                <th style="text-align: center">Action</th>
                            <?php } ?>
                            <?php if($isActiveCanvas && $type !='canvas') { ?>
                                <th class="text-center">Canvas Status</th>
                            <?php } ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalCSevaluationCount > 0)
                        {
                            while($row = mysqli_fetch_array($getCSevaluationdetails))
                            {
								
								$siteevaluationrotationid = $row['rotationId'];
								$RotationName = $row['title'];
								$firstName = $row['firstName'];
								$DBstudentId = $row['studentId'];
								$lastName = $row['lastName'];
								$studentName = $firstName . ' ' . $lastName;
								$csEvaluationMasterId = $row['StudentCsEvaluationMasterId'];
                                $evaluationDate = stripslashes($row['evaluationDate']);	
								$courselocationId = $row['locationId'];
								$parentRotationId = stripslashes($row['parentRotationId']);
								$rotationLocationId = stripslashes($row['rotationLocationId']);
								
								$locationId = 0;
									if($rotationLocationId != $courselocationId && $parentRotationId > 0)
								{
									if($parentRotationId > 0)
									{
										if(!$rotationLocationId)
											$locationId = $objRotation->GetLocationByRotation($siteevaluationrotationid);
										else
											$locationId  = $rotationLocationId;
									}
								}
								else
								{	
										$locationId  = $courselocationId;
								}
									
								//Get Time Zone By Rotation 
								$objLocation = new clsLocations();
								$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
								unset($objLocation);
								if($TimeZone =='')
									$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];								
                                $evaluationDate = converFromServerTimeZone($evaluationDate,$TimeZone);
                                $evaluationDate =date("m/d/Y",strtotime($evaluationDate));

                                $GetSiteevaluationScore=$objSiteevaluation->GetSiteEvaluationScore($csEvaluationMasterId);
                                $EvaluationScore=$GetSiteevaluationScore['EvaluationScore'];
                                
                                
                                $totalUserCount = 0;

                                // For Canvas
								$isSendToCanvas  = isset($row['isSendToCanvas']) ? $row['isSendToCanvas'] : 0;

                                $isSendToCanvasClass ='isSendRecordToCanvas';
                                $isSentToCanvasClass ='hide';
                                if($isSendToCanvas)
                                {
                                    $isSendToCanvasClass ='hide';
                                    $isSentToCanvasClass ='';
                                }

								$isUserCanSendCompletedRecordToCanvas = 0;
								if($evaluationDate)
									$isUserCanSendCompletedRecordToCanvas = 1;
                                
                                /// End Canvas ///

                                $EvaluationScore = number_format((float)$EvaluationScore, 2, '.', '');
                                ?>
                            <tr>
								<td><?php echo($firstName); ?></td>
								<td><?php echo($lastName); ?></td>                                
								<td><?php echo($RotationName); ?></td>                                
                                <td style="text-align: center"><?php echo ($evaluationDate); ?></td>
                                <td style="text-align: center"><?php echo $EvaluationScore; ?></td> 
                                
                                <?php if($type !='canvas') { ?> 
                                <td style="text-align: center">
                                <?php
                                $rotationStatus = checkRotationStatus($siteevaluationrotationid);
                               
                                if ($rotationStatus) { ?>
                                    <a href="siteevaluation.html?csEvaluationMasterId=<?php echo(EncodeQueryData($csEvaluationMasterId)); ?>
									&siteevaluationrotationid=<?php echo(EncodeQueryData($siteevaluationrotationid));?>&view=1">View</a> |
								<?php } elseif(isset($_GET['studentId']))
								{?>
                                    <a href="siteevaluation.html?csEvaluationMasterId=<?php echo(EncodeQueryData($csEvaluationMasterId)); ?>
									&siteevaluationrotationid=<?php echo(EncodeQueryData($siteevaluationrotationid));?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Edit</a> |
								<?php } else { ?>	
								 <a href="siteevaluation.html?csEvaluationMasterId=<?php echo(EncodeQueryData($csEvaluationMasterId)); ?>
									&siteevaluationrotationid=<?php echo(EncodeQueryData($siteevaluationrotationid));?>">Edit</a> |
								
								<?php } ?>
									<a href="javascript:void(0);" class="deleteAjaxRow"
									csEvaluationMasterId="<?php echo EncodeQueryData($csEvaluationMasterId); ?>" studentName="<?php echo($studentName); ?>" >Delete</a>
                                </td>
                                <?php } ?>
                                <?php if($isActiveCanvas && $type !='canvas') 
										{ 
											if($loggedUserSendRecordToCanvas && $isUserCanSendCompletedRecordToCanvas) 
											{ ?>
											<td class="text-center">
												<a href="javascript:void(0);" id="isSendToCanvas_<?php echo $csEvaluationMasterId; ?>" class="<?php echo $isSendToCanvasClass;?>" 
                                                    
													evaluationDate="<?php echo $evaluationDate; ?>"
													avgRating="<?php echo $EvaluationScore; ?>"
                                                    rotation="<?php echo $RotationName; ?>" 
													studentfullname="<?php echo $studentName; ?>"
													studentId="<?php echo $DBstudentId; ?>" 
													csEvaluationMasterId="<?php echo $csEvaluationMasterId; ?>"
													>
													Send to Canvas
												</a>
												<label for=""  class="isSentToCanvas_<?php echo $csEvaluationMasterId; ?> <?php echo $isSentToCanvasClass; ?>">Sent</label>
												
											</td>
									
									<?php } 
											else
											{ ?>
											<td class="text-center"><label for=""  class=""> - 
												<?php //if($isSendToCanvas>0) { echo 'Sent'; } else { echo 'Send to canvas'; } ?>
											</label></td>
												
										<?php } 
										
									}
								?> 
                            </tr>
                            <?php
                            }
                        }
						unset($objRotation);
                    ?>
                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script> 

        <script type="text/javascript">

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
                $(".select2_single").select2();
            });
			
            var current_datatable = $("#datatable-responsive").DataTable({
                "ordering": true,
                "order": [[3, "desc" ]],
                "aoColumns": [{
                    "sWidth": "15%"
                },{
                    "sWidth": "15%"
                }, {
                    "sWidth": "15%"
                },{
                    "sWidth": "25%"
                },{
                    "sWidth": "15%",
                    "sClass": "alignCenter"
                }
                <?php if($type !='canvas') { ?>
                ,{
                    "sWidth": "15%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }
                <?php } ?>
                <?php if($isActiveCanvas && $type !='canvas') { ?>
				, {"sWidth": "10%"}
				<?php } ?>]
            });

            // ajax call for deleteAjaxRow
			
		    
             $(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var csEvaluationMasterId = $(this).attr('csEvaluationMasterId');
                var title = $(this).attr('studentName');
               
                alertify.confirm('Student CS Evaluation: '+ title, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: csEvaluationMasterId,
                            type: 'CSEvaluation'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });

            //Send Records To Canvas
			$(document).on('click', '.isSendRecordToCanvas', function() {

                var that = this;
                var evaluationDate = $(this).attr('evaluationDate');
                var csEvaluationMasterId = $(this).attr('csEvaluationMasterId');
                var avgRating = $(this).attr('avgRating');
                var rotation = $(this).attr('rotation');
                var schoolId = "<?php echo $currentSchoolId; ?>";
                var studentfullname = $(this).attr('studentfullname');
                var studentId = $(this).attr('studentId');

                alertify.confirm('Site Evalution ', 'Continue with send record to Canvas?', function() {
                    $(that).text('Loading..');
                    $(that).prop('disabled',true);

                    $.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_record_to_canvas.html",
                        data: {
                            
                            evaluationDate: evaluationDate,
                            csEvaluationMasterId: csEvaluationMasterId,
                            avgRating: avgRating,
                            rotation: rotation,
                            studentFullName: studentfullname,
                            studentId: studentId,
                            schoolId: schoolId,
                            type: 'SiteEval'
                        },
                        success: function(response) {
                            if(response == 'Success')
                            {
                                $(that).addClass('hide');
                                $('.isSentToCanvas_'+csEvaluationMasterId).removeClass('hide')
                                alertify.success('Record Successfully Sent to Canvas.');
                            }
                            else
                            {
                                alertify.success(response);
                            }
                            
                        }
                    });
                }, function() {});

            });

            $("#canvasStatus").change(function(){
                var canvasStatus=$(this).val();
                var studentId='<?php echo $encodedStudentId; ?>';
                var rotationId='<?php echo $encodedRotationId; ?>';

                if(studentId !='')
                {
                    if(canvasStatus !='')
                        window.location.href = "siteevaluationlist.html?studentId="+studentId+"&canvasStatus="+canvasStatus;
                    else
                        window.location.href = "siteevaluationlist.html?studentId="+studentId;
                }
                else if(rotationId !='')
                {
                    if(canvasStatus !='')
                        window.location.href = "siteevaluationlist.html?siteevaluationrotationid="+rotationId+"&canvasStatus="+canvasStatus;
                    else
                        window.location.href = "siteevaluationlist.html?siteevaluationrotationid="+rotationId;
                }
               
                
            });
        </script>


    </body>

    </html>