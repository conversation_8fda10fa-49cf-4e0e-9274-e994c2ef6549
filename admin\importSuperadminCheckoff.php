<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsStudentRankMaster.php');

$type = isset($_GET['type']) ? $_GET['type'] : '';
$actionType = '';
$samplefilePath = '';
$topicId = 0;
$sectionId = 0;

if ($type == 1) //For Import Standard Topic
{
    $pageTitle = "Import Standard Comps Topic";
    $redirectUrl = 'mastercheckofftopic.html';
    $actionUrl = 'importSuperadminCheckoffSubmit.html';

    if (DEVELOPMENT == 1)
        $samplefilePath = ROOT_PATH . "/upload/checkoff/sample_topic_file.csv";
    else
        $samplefilePath = BASE_PATH . "/upload/checkoff/sample_topic_file.csv";
} else if ($type == 2) //For Import Standard Section
{
    $pageTitle = "Import Standard Comps Section";
    $redirectUrl = 'mastercheckofftopic.html';
    $actionUrl = 'importSuperadminCheckoffSubmit.html';

    if (DEVELOPMENT == 1)
        $samplefilePath = ROOT_PATH . "/upload/checkoff/sample_section_file.csv";
    else
        $samplefilePath = BASE_PATH . "/upload/checkoff/sample_section_file.csv";
} elseif ($type == 3) //For Import Standard Steps
{
    $pageTitle = "Import Standard Comps Steps";
    $redirectUrl = 'mastercheckofftopic.html';
    $actionUrl = 'importSuperadminCheckoffSubmit.html';

    if (DEVELOPMENT == 1)
        $samplefilePath = ROOT_PATH . "/upload/checkoff/sample_step_file.csv";
    else
        $samplefilePath = BASE_PATH . "/upload/checkoff/sample_step_file.csv";
} elseif ($type == 4) //For Import Checkoff Sections
{
    $pageTitle = "Import Section";
    $redirectUrl = 'checkofftopics.html';
    $actionUrl = 'importCheckoffSection.html';
} elseif ($type == 5) //For Import Checkoff Steps
{
    $topicId = isset($_GET['topicid']) ? $_GET['topicid'] : '';
    $sectionId = isset($_GET['sectionId']) ? $_GET['sectionId'] : '';

    $pageTitle = "Import Steps";
    $actionType = 'step';
    $redirectUrl = 'checkofftopics.html';
    $actionUrl = 'importCheckoffSection.html';
} elseif ($type == 6) //For Import Jr Mideterm Performance Evaluation
{
    $topicId = isset($_GET['topicid']) ? $_GET['topicid'] : '';
    $sectionId = isset($_GET['sectionId']) ? $_GET['sectionId'] : '';

    $pageTitle = "Import Mideterm Performance Evaluation";
    // $actionType = 'step';
    $redirectUrl = 'settings.html';
    $actionUrl = 'importJrMidtermPerformanceEvalutionSection.html';
} elseif ($type == 7) //For Import Sr Mideterm Professional Evaluation
{
    $topicId = isset($_GET['topicid']) ? $_GET['topicid'] : '';
    $sectionId = isset($_GET['sectionId']) ? $_GET['sectionId'] : '';

    $pageTitle = "Import Mideterm Professional Evaluation";
    // $actionType = 'step';
    $redirectUrl = 'settings.html';
    $actionUrl = 'importSrMidtermProfessionalEvalutionSection.html';
} elseif ($type == 8) //For Import Performance Evaluation
{
    $topicId = isset($_GET['topicid']) ? $_GET['topicid'] : '';
    $sectionId = isset($_GET['sectionId']) ? $_GET['sectionId'] : '';

    $pageTitle = "Import Performance Evaluation";
    // $actionType = 'step';
    $redirectUrl = 'settings.html';
    // echo 'hi';exit;
    $actionUrl = 'importPerformanceEvaluationSection.html';
} elseif ($type == 9) //For Import Clinical Evaluation
{
    $topicId = isset($_GET['topicid']) ? $_GET['topicid'] : '';
    $sectionId = isset($_GET['sectionId']) ? $_GET['sectionId'] : '';

    $pageTitle = "Import Clinical Evaluation";
    // $actionType = 'step';
    $redirectUrl = 'settings.html';
    // echo 'hi';exit;
    $actionUrl = 'importClinicalEvaluationSection.html';
} 
elseif ($type == 10) //For Import Orientation Checklist
{
    $pageTitle = "Import Orientation Checklist";
    // $actionType = 'step';
    $redirectUrl = 'settings.html';
    // echo 'hi';exit;
    $actionUrl = 'importOrientationChecklist.html';
} else //For Stuedent Import
{
    $pageTitle = 'Import Student';
    if (DEVELOPMENT == 1)
        $samplefilePath = ROOT_PATH . "/upload/sample_file.csv";
    else
        $samplefilePath = BASE_PATH . "/upload/sample_file.csv";

    $redirectUrl = 'schoolstudents.html';
    $actionUrl = 'importstudentlist.html';
}
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo $pageTitle; ?></title>


</head>
<?php include('includes/headercss.php'); ?>
<?php include("includes/datatablecss.php") ?>

<body>

    <div class="container">
        <div class="studentdetails" tabindex="-1" role="dialog">
            <form id="frmexportreport" data-parsley-validate class="form-horizontal" method="POST" action="<?php echo $actionUrl; ?>" enctype="multipart/form-data">
                <input type="hidden" name="type" value="<?php echo $type; ?>">

                <div class="modal-dialog" role="document">
                    <div class="modal-content">
                        <div class="modal-header">
                            <button type="button" class="close" data-dismiss="modal" aria-label="Close"><a href="<?php echo $redirectUrl; ?>" class="btn btn-light btn-form ml-3">X</a></button>
                            <h4 class="modal-title"><?php echo $pageTitle; ?></h4>
                        </div>
                        <div class="modal-body">
                            <input type="file" name="file" style="margin-top: 22px;margin-bottom: 22px;" accept=".csv" class="form-control" id="validationDefaultUsername9" required>
                            <a href="<?php echo $samplefilePath; ?>" style="color:blue;">Download sample file</a>
                            <div class="modal-footer">
                                <button type="submit" style="margin-right: -16px;" class="btn btn-primary ml-3" data-dismiss="modal">Import</button>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script>
        $('#close').on("click", function() {
            $.magnificPopup.close();
        });
    </script>
</body>

</html>