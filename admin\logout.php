<?php
	include('../class/clsSystemUserHistoryId.php');
	 include('../includes/config.php'); 
	include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
	include('../class/clsSystemUser.php');
	include('../class/clsSchool.php');
	include('../class/clsLogger.php');
	include('../setRequest.php');
	
	@session_start();
	  $logoutDate=date("Y-m-d h:i:s");
	 //$typeUserId=$_SESSION["loggedUserRoleId"];

	 $loggedUserId = $_SESSION["loggedUserId"];

	$loggedUserEmail = $_SESSION["loggedUserEmail"];

	$isCurrentSchoolSuperAdmin;

	$device = 1;

	$currentSchoolId;


	//Audit Log Start
	// Instantiate the Logger class
	$objLog = new clsLogger();

	// Determine the action type (EDIT or ADD) based on the presence of a journal ID
	$action = $objLog::LOGOUT;
	$userType = $objLog::ADMIN; // User type is set to ADMIN
	$isSuperAdmin = isset($_SESSION['isSuperAdmin']) ? ($_SESSION['isSuperAdmin']) : 0;
	$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
	$IsMobile = 0;

	$objSystemUser = new clsSystemUser();
	$objSystemUser->saveSchoolAdminAuditLog($_SESSION['loggedUserId'], $_SESSION['loggedUserId'], $userType, $action, $IsMobile);
	unset($objLog);
	//Audit Log End

	 //echo $typeUserId;exit;
	 $objSystelogout = new clsSystemUserHistoryid();
			  $systemUserId= $_SESSION['loggedUserId'];
			  $systemUserloginhistoryId = $_SESSION["systemUserloginhistoryId"];
			  $objSystelogout->logoutDate =$logoutDate;
			  $systemUserlogout = $objSystelogout->PerformUserlogout($systemUserloginhistoryId);
	@session_destroy();
	header('location:index.html?status=logout');
	
?>