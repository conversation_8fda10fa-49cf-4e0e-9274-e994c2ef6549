<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsStudent.php');
include('../class/clsSummative.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsLocations.php');
include('../class/clsSectionStudentName.php');
include('../class/clsClinician.php');
include('../class/clsExternalPreceptors.php');

$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

$schoolId = 0;
$rotationId = 0;
$studentId = 0;
$currentstudentId = 0;
$summativerotationid = 0;
$studentSummativeMasterId = 0;
$clinicianId = 0;
$evaluationDate = '';
$dateOfStudentSignature = '';
$display_to_date = date('m/d/Y');

$schoolId = $currentSchoolId;

$objRotation = new clsRotation();
if (isset($_GET['summativerotationid'])) {
	$summativerotationid = DecodeQueryData($_GET['summativerotationid']);
}
if (isset($_GET['rotationId'])) {
	$summativerotationid = DecodeQueryData($_GET['rotationId']);
	$rotationId = $summativerotationid;
}
//For Student	
if (isset($_GET['studentId'])) {
	$currentstudentId = DecodeQueryData($_GET['studentId']);
}

//For Edit Summative
if (isset($_GET['studentSummativeMasterId']) && ($_GET['summativerotationid'])) {
	$studentSummativeMasterId = DecodeQueryData($_GET['studentSummativeMasterId']);
	$schoolId = $currentSchoolId;
	$page_title = "Edit Summative ";
	$bedCrumTitle = 'Edit';

	//For Summative Details
	$objSummative = new clsSummative();
	$rowSummative = $objSummative->GetStudentSummativeDetails($studentSummativeMasterId);
	unset($objSummative);
	if ($rowSummative == '') {
		header('location:summative.html');
		exit;
	}
	$summativerotationid = ($rowSummative['rotationId']);
	$clinicianId = ($rowSummative['clinicanId']);
	$evaluationDate = ($rowSummative['evaluationDate']);
	$courselocationId = isset($rowSummative['locationId']) ? $rowSummative['locationId'] : 0;
	$parentRotationId = isset($rowSummative['parentRotationId']) ? stripslashes($rowSummative['parentRotationId']) : 0;
	$rotationLocationId = isset($rowSummative['rotationLocationId']) ? stripslashes($rowSummative['rotationLocationId']) : 0;

	$locationId = 0;
	if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
		if ($parentRotationId > 0) {
			if (!$rotationLocationId)
				$locationId = $objRotation->GetLocationByRotation($summativerotationid);
			else
				$locationId  = $rotationLocationId;
		}
	} else {
		$locationId  = $courselocationId;
	}

	//Get Time Zone By Rotation 
	$objLocation = new clsLocations();
	$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
	unset($objLocation);
	if ($TimeZone == '')
		$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
	$evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
	$studentId = ($rowSummative['studentId']);
	$studentSignature = ($rowSummative['studentSignature']);
	$dateOfStudentSignature = ($rowSummative['dateOfStudentSignature']);
	if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00') {
		$dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
		$dateOfStudentSignature = date('m/d/Y', strtotime($dateOfStudentSignature));
	}

	$studentComment = strip_tags($rowSummative['studentComment']);
	//For Rotation List
	$rowstudentrotation = $objRotation->GetRotationByStudent($schoolId, $currentstudentId);
} else {

	$page_title = "Add Summative";
	$bedCrumTitle = 'Add';

	//For Rotation List
	$rowstudentrotation = $objRotation->GetCurrentRotationByStudent($schoolId, $currentstudentId);
}

//For Clinician

//Updated by sunil
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianBySchool($schoolId);
unset($objClinician);

//For Student
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($schoolId, $summativerotationid);


//For Summative Section
$totalSection = 0;
$objSummative = new clsSummative();
$SummativeSection = $objSummative->GetSections($schoolId);
if ($SummativeSection != '') {
	$totalSection = mysqli_num_rows($SummativeSection);
}

//For Rotation Title

$RotationName = $objRotation->GetrotationDetails($summativerotationid, $schoolId);
$rotationtitle = $RotationName['title'];

unset($objRotation);
//For Student Full Name		
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname = $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'];
unset($objStudent);

$preceptorId = 0;
$isPreceptor = isset($_GET['isPreceptor']) ? DecodeQueryData($_GET['isPreceptor']) : '';
$preceptorFullName = '';
if ($isPreceptor > 0) {
	$preceptorId = $isPreceptor;
	$objExternalPreceptors = new clsExternalPreceptors();
	$externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
	$preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
	$preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
	$preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
}
$bedCrumTitle = (isset($_GET['view']) && $_GET['view'] == 'V') ? 'View' : $bedCrumTitle;

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($page_title); ?></title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<style type="text/css">
		.some-class {
			float: left;
			clear: none;
		}
	</style>
</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
					<?php if ($currentstudentId > 0) { ?>
						<li><a href="clinical.html">Clinical</a></li>
						<li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
						<li><a href="summativelist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Summative Evaluation</a></li>
					<?php } else { ?>
						<li><a href="rotations.html">Rotations</a></li>
						<li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
						<li><a href="summativelist.html?summativerotationid=<?php echo EncodeQueryData($summativerotationid); ?>">Summative Evaluation</a></li>
					<?php } ?>
					<li class="active"><?php echo ($bedCrumTitle); ?></li>

				</ol>
			</div>

		</div>
	</div>

	<div class="container">

		<form id="frmsummative" data-parsley-validate class="form-horizontal" method="POST" <?php if ($currentstudentId > 0) { ?>action="summativesubmit.html?studentId=<?php echo (EncodeQueryData($currentstudentId)); ?>&studentSummativeMasterId=<?php echo (EncodeQueryData($studentSummativeMasterId)); ?>" <?php } else { ?>action="summativesubmit.html?studentSummativeMasterId=<?php echo (EncodeQueryData($studentSummativeMasterId)); ?>
																									&summativerotationid=<?php echo (EncodeQueryData($summativerotationid)); ?>" <?php } ?>>

			<div class="row">
				<?php if ($isPreceptor || $preceptorId) { ?>
					<div class="col-md-6">
						<div class="form-group">
							<label class="col-md-4 control-label" for="cboclinician">Preceptor</label>
							<div class="col-md-8">
								<input type="text" name="" id="" value="<?php echo $preceptorFullName; ?>" class="form-control" disabled>
							</div>
						</div>
					</div>
				<?php } else { ?>
					<div class="col-md-6">

						<div class="form-group">
							<label class="col-md-4 control-label" for="cboclinician">Clinician</label>
							<div class="col-md-8">
								<select id="cboclinician" name="cboclinician" class="form-control input-md required-input select2_single">
									<option value="" selected>Select</option>
									<?php
									if ($Clinician != "") {
										while ($row = mysqli_fetch_assoc($Clinician)) {
											$selClinicianId  = $row['clinicianId'];
											$firstName  = stripslashes($row['firstName']);
											$lastName  = stripslashes($row['lastName']);
											$name = $firstName . ' ' . $lastName;


									?>
											<option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

									<?php

										}
									}
									?>
								</select>
							</div>
						</div>
						<!-- ROTATION DD END  -->
					</div>
				<?php } ?>


				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="cbostudent">Student</label>
						<div class="col-md-8">
							<select id="cbostudent" name="cbostudent" class="form-control input-md required-input select2_single">
								<option value="" selected>Select</option>
								<?php
								if ($Student != "") {
									while ($row = mysqli_fetch_assoc($Student)) {
										$selstudentId  = $row['studentId'];
										$firstName  = stripslashes($row['firstName']);
										$lastName  = stripslashes($row['lastName']);
										$name =	$firstName . ' ' . $lastName;
										if ($currentstudentId > 0) { ?>
											<option value="<?php echo ($selstudentId); ?>" <?php if ($currentstudentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
										<?php } else { ?>
											<option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
										<?php }  ?>
								<?php

									}
								}
								?>
							</select>
						</div>
					</div>
				</div>


			</div>



			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="evaluationDate">Evaluation Date</label>
						<div class="col-md-8 col-sm-4 col-xs-12">
							<div class='input-group date' id='evaluationDate'>

								<input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date" value="<?php echo ($evaluationDate); ?>" required data-parsley-errors-container="#error-txtDate" />
								<span class="input-group-addon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>
				<?php if ($currentstudentId > 0) { ?>
					<div class="col-md-6">
						<div class="form-group">
							<label class="col-md-4 control-label" for="cborotation">Rotation</label>
							<div class="col-md-8">
								<select id="cborotation" name="cborotation" class="form-control input-md required-input select2_single" required>
									<option value="" selected>Select</option>
									<?php
									if ($rowstudentrotation != "") {
										while ($row = mysqli_fetch_assoc($rowstudentrotation)) {
											$selrotationId  = $row['rotationId'];
											$title  = stripslashes($row['title']);
									?>
											<option value="<?php echo ($selrotationId); ?>" <?php if ($summativerotationid == $selrotationId) { ?> selected="true" <?php } ?>><?php echo ($title); ?></option>
									<?php }
									}
									?>
								</select>
								<input type="hidden" value="<?php echo ($rotationId); ?>" name="rotationhidden">
							</div>
						</div>
					</div>
				<?php } ?>
			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="studentsigniture">Student Signature</label>
						<div class="col-md-8 col-sm-4 col-xs-12">
							<input type='text' name="studentsigniture" readonly id="studentsigniture" class="form-control input-md required-input " value="<?php if (isset($_GET['studentSummativeMasterId']))  echo ($studentSignature); ?>" />
							<div id="error-txtDate"></div>
						</div>
					</div>

				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="studentsignitureDate">Date Of Student Signature</label>
						<div class="col-md-8 col-sm-4 col-xs-12">
							<div class='input-group date' id='studentsignitureDate'>

								<input type='text' name="studentsignitureDate" readonly id="studentsignitureDate" class="form-control input-md required-input rotation_date" value="<?php echo ($dateOfStudentSignature);  ?>" data-parsley-errors-container="#error-txtDate" />
								<span class="input-group-addon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="studentcomment">Student Comments</label>
						<div class="col-md-10 col-sm-6 col-xs-12">
							<textarea name="studentcomment" readonly id="studentcomment" class="form-control input-md " rows="4" cols="100"><?php if (isset($_GET['studentSummativeMasterId']))  echo ($studentComment); ?></textarea>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="instructions:"><b>DIRECTIONS</b></label>
						<div class="col-md-10 col-sm-6 col-xs-12">
							<div class="panel panel-default">
								<div class="panel-body">
									<ol>
										<li>Check that the identifying data on this page is complete and correct.</li><br>
										<li>Document, on the summary page of this form;the last page, truly exceptional incidents which typify a student's major strong or weak traits. Record incidents from
											<ul>
												<li type="a">direct observation or</li>
												<li type="a">reports from secondary instructors.</li>
											</ul>
										</li><br>
										<li>Just before the scheduled due date for the summative evaluation, rate each student in all attribute categories. Use the following guidelines:
											<ul>
												<li type="a">check the box beside the rating level which best describes the student's behaviors;</li>
												<li type="a">If you strongly desire to include a comment from a rating, enter it in the comment section below the question.</li>
												<li type="a">if a student is given a 'highest' rating then it should be supported by describing, in the Comments section, specific examples of student behavior which justify the excellent rating;</li>
												<li type="a">all ratings below the third level (minimal acceptable) must be substantiated by describing, in the Comments section, specific examples of student behavior which demonstrate the student's less than acceptable rating;</li>
												<li type="a">if in doubt as to a student's performance for a given behavior, arrange, ahead of time, an observation which will demonstrate the characteristic to be evaluated or gather input from secondary instructors who have directly observed the student;</li>
												<li type="a">check the box beside NO RATING if the category is not applicable or you do not have enough information to execute a valid evaluation.</li>

											</ul>
										</li><br>
										<li>On the summary page of this form check all attribute ratings which were below the third level (minimally acceptable). If this is an end of semester evaluation, then on the summary page check any semester mandatory tasks for which student performance will continue to need direct supervision.</li><br>
										<li>If the student's progress is overall unsatisfactory then give specific recommendations indicating what the student must do to improve to an acceptable level of behavior.</li><br>
										<li>Upon completion of the form, review the evaluation in private with the student on the scheduled evaluation due date; sign the summary page and have the student sign to indicate that they have read the evaluation and have had an opportunity to conference with you over the evaluation.</li>

									</ol>
								</div>
							</div>


						</div>
					</div>
				</div>
			</div>
			<!-- 1st SECTION div start -->
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="instructions:"></label>
						<div class="col-md-10 col-sm-6 col-xs-12">

							<div class="panel-group" id="posts">
								<?php


								while ($row = mysqli_fetch_array($SummativeSection)) {
									$sectionMasterId = $row['summativeSectionMasterId'];
									$title = $row['title'];

								?>

									<div class="panel panel-default">
										<div class="panel-heading">
											<h4 class="panel-title">
												<a href="#<?php echo $sectionMasterId; ?>" data-toggle="collapse" data-parent="#posts"><b><?php echo $title; ?></b></a>
											</h4>
										</div>

										<div id="<?php echo $sectionMasterId; ?>" class="panel-collapse collapse">
											<?php
											// sum question
											$totalSummative = 0;



											$Summativequestion = $objSummative->GetAllSummativeQuestionMaster($schoolId, $sectionMasterId);
											if ($Summativequestion != '') {
												$totalSummative = mysqli_num_rows($Summativequestion);
											}
											if ($totalSummative > 0) {
												while ($row = mysqli_fetch_array($Summativequestion)) {
													if (isset($_GET['studentSummativeMasterId'])) {
														$studentSummativeMasterId = DecodeQueryData($_GET['studentSummativeMasterId']);
													} else {
														$studentSummativeMasterId = 0;
													}
													$schoolSummativeQuestionId = $row['schoolSummativeQuestionId'];
													$schoolSummativeQuestionTitle = $row['optionText'];
													$schoolSummativeQuestionType = $row['schoolSummativeQuestionType'];
													$qhtml = GetSummativeQuestionHtml($schoolSummativeQuestionId, $schoolSummativeQuestionType, $studentSummativeMasterId, $currentSchoolId);

													$isMandatoryField = (strpos($schoolSummativeQuestionTitle, 'Mandatory') !== false) ? 1 : 0;
													$isMandatoryField = $isMandatoryField ? 'isMandatoryField' : '';
													$isMandatoryFieldLabel = $isMandatoryField ? "<span class='required-star'>*</span>" : '';

											?>
													<div class="panel-body <?php echo $isMandatoryField; ?>">
														<b><?php echo ($schoolSummativeQuestionTitle) . ' ' . $isMandatoryFieldLabel; ?> </b><br /><br />
														<?php echo $qhtml; ?>
														<!-- <br>
									<div id="error-<?php echo $schoolSummativeQuestionId; ?>" style="margin-left: 24px !important;"></div> -->
													</div>
											<?php
												}
											}
											?>
										</div>
									</div>
								<?php
								}

								?>
							</div>


						</div>

					</div>
				</div>
			</div>

			<div class="row">
				<div class="form-group">
					<label class="col-md-2 control-label"></label>
					<div class="col-md-10">

						<!-- <button style="margin: 0 0 0 11px;" id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button> -->
						<?php
						$rotationStatus = checkRotationStatus($summativerotationid);
						if ($rotationStatus == 0) {
						?>
							<button style="margin: 0 0 0 11px;" id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
						<?php } ?>
						<?php if ($currentstudentId > 0) { ?>
							<a type="button" href="summativelist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>" class="btn btn-default">Cancel</a>
						<?php } else { ?>
							<a type="button" href="summativelist.html?summativerotationid=<?php echo EncodeQueryData($summativerotationid); ?>" class="btn btn-default">Cancel</a>
						<?php } ?>
					</div>
				</div>
			</div>
		</form>
	</div>





	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
	<!-- <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/ckeditor.js"></script> -->



	<script type="text/javascript">
		// ClassicEditor
 		// 	.create(document.querySelector('#studentcomment'))
 		// 	.catch(error => {
 		// 		console.error(error);
 		// 	});	
		$(window).load(function() {

			//Add Required for all mandatory fileds
			$(".isMandatoryField input[type=radio]").each(function() {
				var checkedRadio = ($(this).parent().text());
				$(this).prop('required', true);
			});

			$('#frmsummative').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
					$(".isMandatoryField p").each(function() {
						$(this).removeClass('parsley-success');
						$(this).removeClass('parsley-error');
					});
				})
				.on('form:submit', function() {
					ShowProgressAnimation();
					return true; // Don't submit form sum this demo
				});

			$('#evaluationDate').datetimepicker({
				format: 'MM/DD/YYYY'
			});
			$('#studentsignitureDate').datetimepicker({
				format: 'MM/DD/YYYY'
			});

			//sum searching dropdown
			$(".select2_single").select2();
			$('#select2-cborotation-container').addClass('required-select2');
			$('#select2-cboclinician-container').addClass('required-select2');
			$('#select2-cbostudent-container').addClass('required-select2');

			<?php if (isset($_GET['studentSummativeMasterId']) && ($_GET['summativerotationid'])) { ?>
				$('#cbostudent').prop('disabled', true);
			<?php } ?>

		});



		<?php if (isset($_GET['studentSummativeMasterId']) && ($_GET['summativerotationid'])) { ?>
			$('#cbostudent').prop('disabled', true);
		<?php } ?>

		<?php if ($currentstudentId > 0) { ?>
			$('#cbostudent').prop('disabled', true);
		<?php }
		if ($summativerotationid > 0) { ?>
			document.getElementById("cborotation").required = false;
		<?php }
		if (isset($_GET['rotationId'])) { ?>
			$('#cborotation').prop('disabled', true);
		<?php } ?>
	</script>
</body>

</html>