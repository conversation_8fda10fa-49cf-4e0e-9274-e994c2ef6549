<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsPerformance.php');
include('../setRequest.php');

$sectionMasterId = 0;
$currentSchoolId;
$counter = 1;
if (isset($_GET['sectionMasterId'])) {
    $sectionMasterId = $_GET['sectionMasterId'];
    $sectionMasterId = DecodeQueryData($_GET['sectionMasterId']);
}

$totalJrProfessional = 0;
$objPerformance = new clsPerformance();

if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
    $Performancequestion = $objPerformance->GetAllDefaultPerformanceEvaluationQuestion($sectionMasterId);
else // For School Admin
    $Performancequestion = $objPerformance->GetAllPerformanceEvaluationQuestionToSetting($currentSchoolId, $sectionMasterId);

$totalPerformancequestion = ($Performancequestion != '') ? mysqli_num_rows($Performancequestion)  : 0;
$loggedAsBackUserId = isset($_SESSION["loggedAsBackUserId"]) ? $_SESSION["loggedAsBackUserId"] : 0;
$loggedUserIsRolePrimary = isset($_SESSION["loggedUserIsRolePrimary"]) ? $_SESSION["loggedUserIsRolePrimary"] : 0;

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Performance Evaluation Steps</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Setting </a></li>
                    <li><a href="performanceEvaluationSectionList.html">Performance Evaluation Section</a></li>

                    <li class="active">Steps</li>
                </ol>
            </div>
            <?php
            if ($loggedAsBackUserId || $currentSchoolId == 1) {
            ?>
                <div class="pull-right">
                    <ol class="breadcrumb">
                        <a href="addPerformanceEvalQuestions.html?sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>">Add</a>
                    </ol>
                </div>
            <?php
            }
            ?>
        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Question added successfully.
                </div>
        <?php
            }
        } ?>
        <div id="divTopLoading">Loading...</div>

        <form name="jrprofquestion" id="jrprofquestion" data-parsley-validate method="POST" action="questionsubmit.html">
            <div class="row">
                <div class="col-md-10  margin_bottom_ten"></div>
                <div class="col-md-2  margin_bottom_ten">
                    <div class="form-group">

                    </div>
                </div>
            </div>
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <!-- <th>Sr. No</th> -->
                        <!-- <th></th> -->
                        <?php if ($loggedAsBackUserId  || $currentSchoolId != 1) { ?>
                            <th style="text-align: center">Sort Order</th>
                        <?php } ?>
                        <th>Steps Title</th>
                        <th>Type</th>
                        <th style="text-align: center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($totalPerformancequestion > 0) {
                        while ($row = mysqli_fetch_array($Performancequestion)) {
                            if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
                                $schoolperformanceQuestionId = $row['defaultPerformanceQuestionId'];
                            else // For School Admin
                                $schoolperformanceQuestionId = $row['performanceQuestionId'];

                            $questionText = $row['questionText'];
                            $schoolCIEvaluationQuestionType = $row['questionType'];
                            $sortOrder = isset($row['sortOrder']) ? $row['sortOrder'] : '';

                            $actiontype = '';

                            /*$assignedQuestions = $objJrProfessional->GetAllCIEvaluationAssignQuestionToSection($currentSchoolId,$sectionMasterId);
                      */

                            /*$assignedQuestionId =$assignedQuestions['schoolCIEvaluationQuestionId'];*/

                            if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
                                $assignedQuestionId = $row['defaultPerformanceQuestionId'];
                            else // For School Admin
                                $assignedQuestionId = $row['performanceQuestionId'];

                            if ($assignedQuestionId > 0)
                                $actiontype = "false";
                            else
                                $actiontype = "true";

                            $shortTitlelen = strlen($questionText);

                            if ($shortTitlelen > 80) {

                                $schoolQuestionTitle = substr($questionText, 0, 80);
                                $schoolQuestionTitle .= '...';
                            } else {
                                $schoolQuestionTitle = $questionText;
                            }

                    ?>
                            <tr>
                                <?php if ($loggedAsBackUserId || $currentSchoolId != 1) { ?>
                                    <td style="text-align: center"><?php echo ($sortOrder); ?></td>
                                <?php } ?>
                             
                                <td title="<?php echo ($questionText); ?>">
                                    <?php echo ($schoolQuestionTitle);
                                    ?>
                                </td>
                                <td><?php echo ($schoolCIEvaluationQuestionType); ?></td>
                                <td style="text-align: center">
                                    <a href="addPerformanceEvalQuestions.html?editid=<?php echo EncodeQueryData($schoolperformanceQuestionId); ?>&sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>">Edit</a>
                                    <?php if (isset($_SESSION["loggedAsBackUserId"])) { ?>
                                        <!-- | <a href="javascript:void(0);" class="deleteAjaxRow" questionId="<?php echo EncodeQueryData($schoolperformanceQuestionId); ?>" title="<?php echo ($questionText); ?>">Delete</a> -->
                                        | <a href="javascript:void(0);" class="deleteAjaxRow" questionId="<?php echo EncodeQueryData($schoolperformanceQuestionId); ?>" title="<?php echo ($questionText); ?>">Delete</a>

                                    <?php  } ?>
                                </td>



                            </tr>
                    <?php
                            $counter++;
                        }
                    }
                    ?>
                </tbody>
            </table>
        </form>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {


            var TotalCheckboxCount = $('input[name="jrprofquestion[]"]').length;
            var CheckedCheckboxCount = $('input[name="jrprofquestion[]"]:checked').length;
            if (TotalCheckboxCount == CheckedCheckboxCount) {
                $('.selectall').prop('checked', true);

            } else {
                $('.selectall').prop('checked', false);

            }

            $("#divTopLoading").addClass('hide');

        });
        var current_datatable = $("#datatable-responsive").DataTable({
            "Columns": [
                {
                    "sWidth": "3%",
                    "bSortable": false
                },
                {
                    "sWidth": "80%"
                },
                {
                    "sWidth": "1%"
                }, {
                    "sWidth": "15%",
                    "bSortable": false
                }
            ],

            "LengthMenu": [
                [100, 200, 300, 400, 500, -1],
                [100, 200, 300, 400, 500, "All"]
            ],
            "iDisplayLength": 100,
        });



        $('.chkque').click(function() {
            var TotalCheckboxCount = $('input[name="jrprofquestion[]"]').length;
            var CheckedCheckboxCount = $('input[name="jrprofquestion[]"]:checked').length;
            if (TotalCheckboxCount == CheckedCheckboxCount) {
                $('.selectall').prop('checked', true);

            } else {
                $('.selectall').prop('checked', false);

            }


        });

        $('.addCommentpopup').magnificPopup({
            'type': 'ajax',
        });

        //Delete Questions
        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var QuestionId = $(this).attr('QuestionId');
            var title = $(this).attr('title');

            alertify.confirm('Performance Evaluation Question: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: QuestionId,
                        type: 'PEvalQuestions'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });
    </script>
</body>

</html>