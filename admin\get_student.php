<?php
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php');
	include('../includes/commonfun.php');	
    include('../class/clsDB.php');
    include('../class/clsStudent.php'); 
	

if(!empty($_POST["Rotation_Id"])) {
	$objDB = new clsDB();
		$query = '';
	$query ="SELECT rotationdetails.rotationDetailsId,rotationdetails.studentId,rotationdetails.rotationId ,
			 S.studentId,S.firstName, S.lastName	
	FROM  rotationdetails 
	INNER JOIN student AS S ON rotationdetails.studentId = S.studentId
	WHERE rotationId = '" . $_POST["Rotation_Id"] . "'";
	
	// echo $query;exit;
	$students = $objDB->GetResultSet($query);
	$seletedStudentId = (isset($_POST["Student_Id"]) && $_POST["Student_Id"] > 0) ? $_POST["Student_Id"] : 0;
?>
	<option value="">Select</option>
<?php
	while($rows = mysqli_fetch_assoc($students))
    {
      $studentId  = stripslashes($rows['studentId']);   
	  $firstname  = stripslashes($rows['firstName']);   
      $lastname  = stripslashes($rows['lastName']);
	  
      $name =$firstname . ' ' . $lastname;   
	    
	  echo $seletedString = $seletedStudentId == $studentId ? " selected = 'selected' " : "";

      echo "<option value='{$studentId}' {$seletedString} >{$name}</option>";
	}
}
?>