<?php

// $ServerRootPath	= '/www/wwwroot/rt.clinicaltrac.net';
$ServerRootPath    = 'C:/xampp/htdocs/clinicaltrac';


include($ServerRootPath . '/includes/config.php');
include($ServerRootPath . '/includes/commonfun.php');
include($ServerRootPath . '/class/clsDB.php');
include($ServerRootPath . '/class/clsSchool.php');
include($ServerRootPath . '/class/clsTopic.php');
include_once($ServerRootPath . '/class/clsSendEmails.php');
include($ServerRootPath . "/class/clsSMTPSettings.php");
include($ServerRootPath . "/class/clsCIevaluation.php");
include($ServerRootPath . "/class/clsSiteevaluation.php");
include($ServerRootPath . "/class/clsFormative.php");
include($ServerRootPath . "/class/clsMidterm.php");
include($ServerRootPath . "/class/clsSummative.php");
include($ServerRootPath . '/class/clsSrMidtermProfessionalEval.php');
include($ServerRootPath . '/class/clsJuniorMidtermPerformanceEval.php');
include($ServerRootPath . '/class/clsOrientationChecklist.php');


$isNewSection = 1;

$objSchool = new clsSchool();
$rowSchoolList = $copiedSchoolIdList = $objSchool->GetRemainingDefaultCopySchools();
if ($rowSchoolList != "") {
	$objDB = new clsDB();
	while ($rowSchool = mysqli_fetch_array($rowSchoolList)) {

		$schoolId = $rowSchool['schoolId'];
		// echo "<br>";echo
		$isActiveCheckoff = $rowSchool['isActiveCheckoffForStudent'];


		//--------------------------------------------------------------------
		//Add default topic
		//--------------------------------------------------------------------
		// if ($isActiveCheckoff == 1) {
		$objTopic = new clsTopic();
		$objTopic->CopyRemainingDefaultTopicMaster($schoolId, $isActiveCheckoff);
		unset($objTopic);
		// }

		//--------------------------------------------------------------------		

		//Add New School CIevaluation Question
		//--------------------------------------------------------------------				 
		$objCIevaluation = new clsCIevaluation();
		$objCIevaluation->CopyAllCIEvalQuestionMaster($schoolId, $isActiveCheckoff, $isNewSection);
		unset($objCIevaluation);
		//--------------------------------------------------------------------

		//Add New School Siteevaluation Question
		//--------------------------------------------------------------------				
		$objSiteevaluation = new clsSiteevaluation();
		$objSiteevaluation->CopyAllSiteEvalQuestionMaster($schoolId, $isNewSection);
		unset($objSiteevaluation);
		//--------------------------------------------------------------------	

		//Add New School Formative Question
		//--------------------------------------------------------------------
		$objFormative = new clsFormative();
		$objFormative->CopyAllFormativeQuestionMaster($schoolId, $isNewSection);
		unset($objFormative);

		//--------------------------------------------------------------------	

		//Add New School Midterm Question
		//--------------------------------------------------------------------
		$objMidterm = new clsMidterm();
		$objMidterm->CopyAllMidtermQuestionMaster($schoolId, $isNewSection);
		unset($objMidterm);

		//--------------------------------------------------------------------

		//Add New School Summative Question
		//--------------------------------------------------------------------
		$objSummative = new clsSummative();
		$objSummative->CopyAllSummativeQuestionMaster($schoolId, $isNewSection);
		unset($objSummative);
		//--------------------------------------------------------------------

		//--------------------------------------------------------------------					
		//Add dSrMidtermProfessionalEval Question
		//--------------------------------------------------------------------
		$objSrMidtermProfessionalEval = new clsSrMidtermProfessionalEval();
		$objSrMidtermProfessionalEval->CopyAllSeniorMidtermProfessionalEvaluationQuestionMaster($schoolId, $isNewSection);
		unset($objSrMidtermProfessionalEval);
		// }
		//--------------------------------------------------------------------

		//--------------------------------------------------------------------					
		//Add default Orientation Checklist Question
		//--------------------------------------------------------------------
		$objOrientation = new clsOrientationChecklist();
		$objOrientation->CopyAllOrientationChecklistQuestionMaster($schoolId, $isNewSection);
		unset($objOrientation);
		
		//--------------------------------------------------------------------					
		//Add default Orientation Checklist Question
		//--------------------------------------------------------------------
		$objOrientation = new clsOrientationChecklist();
		$objOrientation->CopyAllOrientationChecklistQuestionMaster($schoolId, $isNewSection);
		unset($objOrientation);
	}

	// exit;


	//Complete Copy Process
	//--------------------------------------------------------------------
	//$tableName, $columnName,$columnValue,$whereColumnName,$whereColumnValue
	$objDB->UpdateSingleColumnValueToTable('defaulttopicmaster', 'isTopicAdded', '0', 'isTopicAdded', 1);
	$objDB->UpdateSingleColumnValueToTable('defaultadvancetopicmaster', 'isTopicAdded', '0', 'isTopicAdded', 1);
	$objDB->UpdateSingleColumnValueToTable('defaultusaftopicmaster', 'isTopicAdded', '0', 'isTopicAdded', 1);


	// CI Eval Completed
	$objDB->UpdateSingleColumnValueToTable('defaultcisectionmaster', 'isSectionAdded', '0', 'isSectionAdded', 1);

	//Site Evalution Completed
	$objDB->UpdateSingleColumnValueToTable('defaultsitesectionmaster', 'isSectionAdded', '0', 'isSectionAdded', 1);

	//Formative Evalution Completed
	$objDB->UpdateSingleColumnValueToTable('defaultformativesectionmaster', 'isSectionAdded', '0', 'isSectionAdded', 1);

	//MidTerm Evalution Completed
	$objDB->UpdateSingleColumnValueToTable('defaultmidtermevalsectionmaster', 'isSectionAdded', '0', 'isSectionAdded', 1);

	//Summative Evalution Completed
	$objDB->UpdateSingleColumnValueToTable('defaultsummativesectionmaster', 'isSectionAdded', '0', 'isSectionAdded', 1);

	// SPE 
	$objDB->UpdateSingleColumnValueToTable('defaultsrmidtermprofessionalevalsectionmaster', 'isSectionAdded', '0', 'isSectionAdded', 1);

	$objDB->UpdateSingleColumnValueToTable('defaultsrmidtermprofessionalevalquestionmaster', 'isQuestionAdded', '0', 'isQuestionAdded', 1);

	//Jr Midterm Professional
	$objDB->UpdateSingleColumnValueToTable('defaultmidtermperformanceevalsectionmaster', 'isSectionAdded', '0', 'isSectionAdded', 1);

	$objDB->UpdateSingleColumnValueToTable('defaultmidtermperformanceevalquestionmaster', 'isQuestionAdded', '0', 'isQuestionAdded', 1);

	// orientation checklist
	$objDB->UpdateSingleColumnValueToTable('defaultorientationchecklistsectionmaster', 'isSectionAdded', '0', 'isSectionAdded', 1);

	$objDB->UpdateSingleColumnValueToTable('defaultorientationchecklistquestionmaster', 'isQuestionAdded', '0', 'isQuestionAdded', 1);


	//--------------------------------------------------------------------	


	unset($objDB);
}
unset($objSchool);
