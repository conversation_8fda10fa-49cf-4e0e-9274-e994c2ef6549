<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsSchool.php');
include('../class/clsEmployerCoarcRequestMaster.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsCoarc.php');

$employercoarcId = 0;

//For Student List
$objStudent = new clsStudent();
$objCoarc = new clsCoarc();
$objSchool = new clsSchool();
$objEmployerCoarcRequestMaster = new clsEmployerCoarcRequestMaster();

$surveyId = 0;

if (isset($_GET['surveyId'])) {
    $surveyId = $_GET['surveyId'];
    $surveyId = DecodeQueryData($surveyId);
}

$rowsStudentData = $objStudent->GetAllSchoolStudentsForEmployerCoarc($currentSchoolId, $surveyId);

$totalStudentCount = 0;
if ($rowsStudentData != '') {
    $totalStudentCount = mysqli_num_rows($rowsStudentData);
}

$rowsSurveyList = $objStudent->GetAllSchoolStudentsForEmployerCoarcList($currentSchoolId);

unset($objStudent);
// echo $currentSchoolId; exit;

$isDeleted = isset($_GET['isDeleted']) ? $_GET['isDeleted'] : 0;
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Employer JRCERT Survey</title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
</head>

<body>
    <?php include('includes/header.php'); ?>

    <form name="coarcsurvey" id="coarcsurvey" data-parsley-validate method="POST" action="sendemployercoarcrequest.html">
        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Employer JRCERT Survey</li>
                    </ol>
                </div>
                <div class="pull-right">
                </div>
                <!--<div class="pull-right">-->
                <!--           <button class="btn btn-default" id="btnsendrequest" disabled="disabled">Send Request</button>-->
                <!--         </div>-->
            </div>
        </div>

        <div class="container">
            <?php
            if (isset($_GET["status"])) {
                if ($_GET["status"] == "added") {
            ?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button>Sent request successfully.
                    </div>
                <?php
                } else if ($_GET["status"] == "updated") {
                ?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Request updated successfully.
                    </div>
                <?php
                } else if ($_GET["status"] == "Error") {
                ?>
                    <div class="alert alert-danger alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Error occurred.
                    </div>
            <?php
                }
            }
            ?>
            <div id="divTopLoading">Loading...</div>
            <div class="row">
                <div class="col-md-8"></div>
                <div class="col-md-4 pull-right">
                    <div class="form-group">
                        <label class="col-md-5 control-label margin_top_seven padding_zero" for="cborank">JRCERT Survey Title</label>
                        <div class="col-md-7 padding_zero">
                            <select id="surveyTitle" name="surveyTitle" class="form-control select2_single">
                                <option value="" selected>Select All</option>

                                <?php
                                if ($rowsSurveyList != '') {
                                    while ($row = mysqli_fetch_assoc($rowsSurveyList)) {

                                        $selsurveyId  = $row['coarcSurveyMasterId'];
                                        $name  = stripslashes($row['surveyTitle']);

                                ?>
                                        <option value="<?php echo EncodeQueryData($selsurveyId); ?>" <?php if ($surveyId == $selsurveyId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-10  margin_bottom_ten"></div>
                <div class="col-md-2  margin_bottom_ten">
                    <div class="form-group">
                        <!--button id="btncoarcrequest" name="btncoarcrequest" class="btn btn-success">Send JRCERT Survey</button-->
                    </div>
                </div>
            </div>
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <!-- <th style="text-align: center">Select All <p style="text-align: center;"><input class="selectall" type="checkbox" id="selectall" name="selectall[0]" ></p> </th> -->
                        <th style="vertical-align: middle; width: 64px;" class="sorting_disabled" rowspan="1" colspan="1" aria-label="Select All  "><input class="selectall" type="checkbox" id="selectall" name="selectall[0]"> &nbsp;Select All </th>
                        <th style="vertical-align: middle;">Business Name</th>
                        <th style="vertical-align: middle;">Contact <br> First Name</th>
                        <th style="vertical-align: middle;">Contact <br> Last Name</th>
                        <th style="vertical-align: middle;">Student Name</th>
                        <th style="vertical-align: middle;">Rank</th>
                        <th style="vertical-align: middle;">JRCERT Survey Title</th>
                        <th style="vertical-align: middle;">Status</th>
                        <th style="vertical-align: middle;">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($totalStudentCount > 0) {
                        while ($row = mysqli_fetch_array($rowsStudentData)) {

                            $studentId = ($row[0]);

                            $firstName = stripslashes($row['firstName']);
                            $lastName = stripslashes($row['lastName']);
                            $fullName =  $firstName . ' ' . $lastName;
                            $ContactFirstName = stripslashes($row['ContactFirstName']);
                            $ContactLastName = stripslashes($row['ContactLastName']);
                            $rank = stripslashes($row['rank']);
                            $businessName = stripslashes($row['businessName']);
                            // $status = stripslashes($row['status']);                           

                            $isDelivery = stripslashes($row['isDelivery']);
                            $updaterow = 'updaterow';
                            $surveyTitle = stripslashes($row['surveyTitle']);
                            $coarcSurveyMasterId = stripslashes($row['coarcSurveyMasterId']);

                            $surveyDetail = $objEmployerCoarcRequestMaster->GetEmployerCoarcSurveyByStudent($studentId, $coarcSurveyMasterId);
                            $employercoarcId = $surveyDetail ? stripslashes($surveyDetail['employercoarcId']) : '0';
                            $status = $surveyDetail ? stripslashes($surveyDetail['status']) : '';

                            if ($employercoarcId) {
                                if ($status == 1) {
                                    $status = 'Completed';
                                } elseif ($status == 0) {
                                    $status = 'Pending';
                                }
                            } else {
                                $status = '-';
                            }

                    ?>
                            <tr <?php if ($status == 'Pending') { ?> class="<?php echo $updaterow; ?>" <?php } ?>>
                                <td style="text-align: center">
                                    <input type="checkbox" id="coarcsurvey" name="coarcsurvey[]" coarcSurveyMasterId="<?php echo EncodeQueryData($coarcSurveyMasterId); ?>" studentId="<?php echo EncodeQueryData($studentId); ?>" getStudentCoarcId="<?php echo EncodeQueryData($employercoarcId); ?>" SchoolId="<?php echo EncodeQueryData($currentSchoolId); ?>" value="<?php echo ($studentId); ?>" id="coarcsurvey" <?php if (($status == 'Completed') || ($status == 'Pending')) { ?> checked id="coarcsurvey" class="uncheck coarcsurvey sendrequest chkstudent" <?php } else { ?> class="sendrequest chkstudent" <?php } ?>>
                                </td>
                                <td><?php echo ($businessName); ?> </td>
                                <td><?php echo ($ContactFirstName); ?> </td>
                                <td><?php echo ($ContactLastName); ?></td>
                                <td><?php echo ($fullName); ?></td>
                                <td><?php echo ($rank); ?></td>
                                <td><?php echo ($surveyTitle); ?></td>
                                <td><?php echo ($status); ?></td>
                                <td align="center">


                                    <?php if ($status == 'Completed') {
                                    ?>
                                        <a href="addemployercoarcsurvey.html?employerCoarcMasterId=<?php echo (EncodeQueryData($coarcSurveyMasterId)); ?>
										&studentId=<?php echo (EncodeQueryData($studentId)); ?>
										&employercoarcId=<?php echo (EncodeQueryData($employercoarcId)); ?>">View</a>
                                        |<?php } elseif ($status == 'Pending') {
                                            if ($isDelivery == '0') { ?>
                                        <a href="javascript:void(0);" coarcSurveyMasterId="<?php echo EncodeQueryData($coarcSurveyMasterId); ?>" getStudentCoarcId="<?php echo EncodeQueryData($employercoarcId); ?>" studentId="<?php echo EncodeQueryData($studentId); ?>" SchoolId="<?php echo EncodeQueryData($currentSchoolId); ?>" class="reSendrequest">Resend Email</a> |
                                    <?php } elseif ($isDelivery == '1') { ?>
                                        <a href="javascript:void(0);" coarcSurveyMasterId="<?php echo EncodeQueryData($coarcSurveyMasterId); ?>" getStudentCoarcId="<?php echo EncodeQueryData($employercoarcId); ?>" studentId="<?php echo EncodeQueryData($studentId); ?>" SchoolId="<?php echo EncodeQueryData($currentSchoolId); ?>" class="reSendrequest">Resend SMS</a> |
                                        <a href="javascript:void(0);" coarcSurveyMasterId="<?php echo ($coarcSurveyMasterId); ?>" getStudentCoarcId="<?php echo ($employercoarcId); ?>" studentId="<?php echo EncodeQueryData($studentId); ?>" SchoolId="<?php echo ($currentSchoolId); ?>" evaluationType='employer' class="copyLink" onclick="copyLinkUrl(this)">Click to Copy URL</a> |

                                <?php }
                                        }
                                ?>
                                <a href="javascript:void(0);" coarcSurveyMasterId="<?php echo $coarcSurveyMasterId; ?>" studentId="<?php echo $studentId; ?>" class="deleteAjaxRow" employercoarcId="<?php echo EncodeQueryData($employercoarcId); ?>" schoolStudentName="<?php echo ($fullName); ?>" href="employercoarcsurvey.html?employercoarcId=<?php echo (EncodeQueryData($employercoarcId)); ?>">Delete</a>
                                </td>
                            </tr>
                    <?php
                        }
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </form>
    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        // $('#selectall').click(function() 
        // {		
        //    //alert('');
        //    if ($(this).is(':checked')) {					
        //       $('.uncheck').prop('checked', true);											
        //    } else {											
        //       $('.uncheck').removeAttr('checked');

        //    }
        // });

        var current_datatable = $("#datatable-responsive").DataTable({
            responsive: false,
            scrollX: true,
            "aaSorting": [],
            "aoColumns": [{
                    "sWidth": "2%",
                    "bSortable": false
                }, {
                    "sWidth": "5%"
                },
                {
                    "sWidth": "5%"
                }, {
                    "sWidth": "5%"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "5%",
                    "bSortable": false
                }
            ]
        });

        $(window).load(function() {
            //Loader
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();

            //Disabled Checked Checkbox 
            var rows = current_datatable.rows().nodes();
            if ($(".coarcsurvey", rows).is(':checked'))
                $('.coarcsurvey', rows).attr('disabled', true);
            else
                $('.coarcsurvey', rows).attr('disabled', false);

            var isDeleted = '<?php echo $isDeleted ?>';
            var surveyId = '<?php echo $surveyId; ?>';

            if (isDeleted > 0) {
                alertify.success('Deleted');
                if (surveyId > 0)
                    newUrl = 'employercoarcsurvey.html?surveyId=' + btoa(surveyId);
                else
                    newUrl = 'employercoarcsurvey.html';

                history.pushState({}, null, newUrl);

            }

        });

        $('.selectall').click(function() {

            if ($(this).is(':checked')) {
                $('#btnsendrequest').attr('disabled', false);
                $('input:checkbox:not(:disabled)').prop('checked', true);
            } else {
                $('#btnsendrequest').attr('disabled', true);
                $('input:checkbox:not(:disabled)').prop('checked', false);
            }
        });

        $('.chkstudent').click(function() {
            var TotalCheckboxCount = $('input[name="coarcsurvey[]"]').length;
            var CheckedCheckboxCount = $('input[name="coarcsurvey[]"]:checked').length;
            if (TotalCheckboxCount == CheckedCheckboxCount) {
                $('.selectall').prop('checked', true);
            } else {
                $('.selectall').prop('checked', false);
            }


            if (CheckedCheckboxCount > 0) {
                $('#btnsendrequest').attr('disabled', false);
            } else {
                $('#btnsendrequest').attr('disabled', true);
            }

        });

        //Delete Student
        $(document).on('click', '.deleteAjaxRow', function() {
            var surveyId = '<?php echo $surveyId; ?>';
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            console.log(current_datatable_row);
            var employercoarcId = $(this).attr('employercoarcId');
            var studentId = $(this).attr('studentId');
            var coarcSurveyMasterId = $(this).attr('coarcSurveyMasterId');
            var schoolStudentName = $(this).attr('schoolStudentName');

            alertify.confirm('Student Name: ' + schoolStudentName, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: employercoarcId,
                        studentId: studentId,
                        coarcSurveyMasterId: coarcSurveyMasterId,
                        type: 'employer_coarc_request_student'
                    },
                    success: function() {
                        $Url = window.location.href;
                        if (surveyId > 0)
                            $Url = $Url + '&isDeleted=1';
                        else
                            $Url = $Url + '?isDeleted=1';
                        window.location.href = $Url;
                    }
                });

            }, function() {});
        });

        $("#selectall").change(function() {
            $('.sendrequest').trigger('change');
        })

        //FOR SEND REQUEST 1 by 1
        $('#datatable-responsive').on("change", ".sendrequest", function(event) {

            var disabled = $(this).attr('disabled');
            if (disabled)
                return false;

            var studentId = $(this).attr('studentId');
            var SchoolId = $(this).attr('SchoolId');
            var coarcSurveyMasterId = $(this).attr('coarcSurveyMasterId');
            var currenSchoolLogoImagePath = $(this).attr('currenSchoolLogoImagePath');
            var currenschoolDisplayname = $(this).attr('currenschoolDisplayname');
            var getStudentCoarcId = $(this).attr('getStudentCoarcId');
            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/send_employer_coarc_request.html",
                data: {
                    id: studentId,
                    SchoolId: SchoolId,
                    coarcSurveyMasterId: coarcSurveyMasterId,
                    currenSchoolLogoImagePath: currenSchoolLogoImagePath,
                    currenschoolDisplayname: currenschoolDisplayname,
                    getStudentCoarcId: getStudentCoarcId,
                    type: 'Sent_Request'
                },
                success: function(data) {
                    alertify.success('Sent');
                    console.log(data);
                    // history.go(0);
                }
            });

        });


        $(document).on('click', '.reSendrequest', function() {

            var studentId = $(this).attr('studentId');
            var SchoolId = $(this).attr('SchoolId');
            var getStudentCoarcId = $(this).attr('getStudentCoarcId');
            var coarcSurveyMasterId = $(this).attr('coarcSurveyMasterId');

            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/send_employer_coarc_request.html",
                data: {
                    id: studentId,
                    SchoolId: SchoolId,
                    getStudentCoarcId: getStudentCoarcId,
                    coarcSurveyMasterId: coarcSurveyMasterId,
                    type: 'Sent_Request'
                },
                success: function(data) {
                    alertify.success('Sent');
                    console.log(data);
                    // history.go(0);
                }
            });

        });

        //copy link



        $("#surveyTitle").change(function() {
            var surveyId = $(this).val();

            if (surveyId) {
                window.location.href = "employercoarcsurvey.html?surveyId=" + surveyId;
            } else {
                window.location.href = "employercoarcsurvey.html";
            }
        });
    </script>
</body>

</html>