<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSiteevaluation.php'); 
    include('../class/clsQuestionOption.php'); 
	include('../setRequest.php');
	
	//print_r($_POST);exit;
	 $userId = ($_SESSION['loggedUserId']) ? $_SESSION['loggedUserId'] : 0;
	 $csEvaluationMasterId=0;	 
	 if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		$rotationId=0;	
		$siteevaluationrotationid=0;	
		 
	   if(isset($_GET['csEvaluationMasterId'])) 
	  {
		$csEvaluationMasterId = $_GET['csEvaluationMasterId'];
        $csEvaluationMasterId = DecodeQueryData($csEvaluationMasterId);
      }
	   if(isset($_GET['siteevaluationrotationid'])) 
	  {
		$siteevaluationrotationid = $_GET['siteevaluationrotationid'];
        $siteevaluationrotationid = DecodeQueryData($siteevaluationrotationid);
		
      }
		
		$csEvaluationMasterId = isset($_GET['csEvaluationMasterId']) ? DecodeQueryData($_GET['csEvaluationMasterId']) : 0;
		$status = ($csEvaluationMasterId > 0) ? 'updated' : 'added';
		
       // $cbostudent=0;
		//$cbostudent=$cbostudent?($_POST['cbostudent']):$cbostudent;
			if(isset($_GET['studentId'])) 
				{
					$student = $_GET['studentId'];
					$student = DecodeQueryData($student);
				
				}
				else
				{ 
					$student=($_POST['cbostudent']);
				}
		$cbohospitalsites  = ($_POST['cbohospitalsites']);
			
		$hospitalID  = ($_POST['cbohospital']);	
		$Neonatal  = ($_POST['Neonatal']);	
		$Pediatric  = ($_POST['Pediatric']);	
		$Adult  = ($_POST['Adult']);
		$evaluationDate=GetDateStringInServerFormat($_POST['evaluationDate']);
		$evaluationDate = str_replace('00:00:00','12:00 PM',$evaluationDate);
		$evaluationDate = date('Y-m-d H:i',strtotime($evaluationDate));

		$siteevaluationrotationid=$siteevaluationrotationid ? $siteevaluationrotationid : $_POST['rotation'];	
			
		$objCSevaluation = new clsSiteevaluation();
		$objCSevaluation->rotationId =$siteevaluationrotationid;						
		$objCSevaluation->schoolClinicalSiteUnitId =$cbohospitalsites;				
		$objCSevaluation->hospitalSiteId =$hospitalID;				
		$objCSevaluation->schoolId =$currentSchoolId;
		$objCSevaluation->studentId =$student;
		$objCSevaluation->evaluationDate = $evaluationDate;	
		$objCSevaluation->patientCareAdultAreaId = $Adult;	    
		$objCSevaluation->patientCarePediatricAreaId = $Pediatric;	    
		$objCSevaluation->patientCareNeonatalaAreaId = $Neonatal;
		$objCSevaluation->createdBy =$_SESSION["loggedUserId"];		
		$retcsEvaluationId = $objCSevaluation->SaveAdminCSevaluation($csEvaluationMasterId);			
		$objCSevaluation->DeleteCSevaluationDetails($retcsEvaluationId);
		
	    foreach($_POST as $id=>$value)
	 {
		
		if (strpos($id, 'questionoptions_') === 0) 
		{
						
		  $id = explode("_", $id)[1];
		 $objCSevaluation->csEvaluationMasterId = $retcsEvaluationId;
		 $objCSevaluation->schoolCSEvaluationQuestionId = $id;		 		
		 $objCSevaluation->schoolCSEvaluationOptionValue = $value[0];
		 $objCSevaluation->schoolCSEvaluationOptionAnswerText ='';	
		 $csEvaluationDetaild=$objCSevaluation->SaveCSevaluationDetails($retcsEvaluationId);

		}
	 }
	  foreach($_POST as $id=>$value)
	 {
		
		if (strpos($id, 'questionoptionst_') === 0) 
		{			
		  $id = explode("_", $id)[1];
		 $objCSevaluation->csEvaluationMasterId = $retcsEvaluationId;
		 $objCSevaluation->schoolCSEvaluationQuestionId = $id;		 		
		 $objCSevaluation->schoolCSEvaluationOptionValue ='';
		 $objCSevaluation->schoolCSEvaluationOptionAnswerText =$value[0];	
		 $csEvaluationDetaild=$objCSevaluation->SaveCSevaluationDetails($retcsEvaluationId);
		}
	 }
		
		unset($objCSevaluation);
		
		if($retcsEvaluationId > 0)
		{
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a ID
			$action = $objLog::EDIT;
			$userType = $objLog::ADMIN; // User type is set to ADMIN

			$objSiteEval = new clsSiteevaluation();
			$objSiteEval->saveSiteEvalAuditLog($csEvaluationMasterId, $retcsEvaluationId,$userId, $userType, $action);
			unset($objSiteEval);

			unset($objLog);

			if(isset($_GET['studentId'])) 
				{
					header('location:siteevaluationlist.html?csEvaluationMasterId='.EncodeQueryData($csEvaluationMasterId).'&studentId='.EncodeQueryData($student).'&status='.$status);	
				}
				else
				{
					header('location:siteevaluationlist.html?csEvaluationMasterId='.EncodeQueryData($csEvaluationMasterId).'&siteevaluationrotationid='.EncodeQueryData($siteevaluationrotationid).'&status='.$status);	
				}
			exit();	
		}
		else
		{
			header('location:siteevaluation.html?status=error');
		}
		 
	}
	{
		header('location:siteevaluationlist.html');
		exit();
	}	
?>