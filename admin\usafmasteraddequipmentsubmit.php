<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');      	
	include('../class/clsusafEquiment.php');
	include('../setRequest.php'); 
	
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		
		$equipmentId = isset($_GET['equipmentId']) ? DecodeQueryData($_GET['equipmentId']) : 0;
		
		$status = ($equipmentId > 0) ? 'updated' : 'added';
		
		
        $title = $_POST['equipmentTitle'];
		
		//Save data
		$objusafEquiment = new clsusafEquiment();
		$objusafEquiment->title = $title;					
		$retEquipmentId = $objusafEquiment->SaveUsafEquipment($equipmentId);
		
		unset($objusafEquiment);
		if($retEquipmentId > 0)
		{	
            header('location:usafmasterviewequipment.html?status='.$status);
		}
		else
		{
			header('location:usafmasteraddequipment.html?status=error');
		}
	}
    exit();
	
?>