<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsSystemUserRoleMaster.php');
include('../class/clsClinicianRoleMaster.php');

$objSchool = new clsSchool();
$schoolId = 0;
// print_r($_POST);
$schoolId = $currentSchoolId;
$adminSms = "Hello {{ADMIN_NAME}}, {{STUDENT_NAME}} is absent today for {{ROTATION_NAME}} rotation.";
$clinicianSms = "Hello {{CLINICIAN_NAME}}, {{STUDENT_NAME}} is absent today for {{ROTATION_NAME}} rotation.";
$studentSms = "Hello {{STUDENT_NAME}}, Today You are absent for {{ROTATION_NAME}} rotation.";
$isCustomSms = 0;
if (isset($_POST['btnSubmit'])) {
    $absenceSMSTime = isset($_POST['absenceSMSTime']) ? $_POST['absenceSMSTime'] : '';
    $absenceSMSStatus = isset($_POST['absenceSMSStatus']) ? $_POST['absenceSMSStatus'] : '';
    $absenceSMSSendTo = isset($_POST['absenceSMSSendTo']) ? implode(",", $_POST['absenceSMSSendTo']) : '';
    $adminRolesAbsenceSMSSendTo = isset($_POST['adminRolesAbsenceSMSSendTo']) ? implode(",", $_POST['adminRolesAbsenceSMSSendTo']) : '';
    $clinitionRoleAbsenceSMSSendTo = isset($_POST['clinitionRoleAbsenceSMSSendTo']) ? implode(",", $_POST['clinitionRoleAbsenceSMSSendTo']) : '';
    $objSchool->absenceSMSTime = $absenceSMSTime;
    $objSchool->absenceSMSSendTo = $absenceSMSSendTo;
    $objSchool->adminRolesAbsenceSMSSendTo = $adminRolesAbsenceSMSSendTo;
    $objSchool->clinitionRoleAbsenceSMSSendTo = $clinitionRoleAbsenceSMSSendTo;
    $objSchool->absenceSMSStatus = $absenceSMSStatus;
    $objSchool->updateAbsenceInfo($currentSchoolId);
    $objSchool->deleteAbsenceDetails($currentSchoolId);

    $isCustomSms = isset($_POST['customSms']) ? $_POST['customSms'] : 0;
    $adminSms = isset($_POST['adminSms']) ? $_POST['adminSms'] : '';
    $clinicianSms = isset($_POST['clinicianSms']) ? $_POST['clinicianSms'] : '';
    $studentSms = isset($_POST['studentSms']) ? $_POST['studentSms'] : '';

    $objSchool->isCustomSms = $isCustomSms;
    $objSchool->adminSms = $adminSms;
    $objSchool->clinicianSms = $clinicianSms;
    $objSchool->studentSms = $studentSms;

    $objSchool->updateAbsenceDetails($currentSchoolId);

    //Audit Log Start
    // Instantiate the Logger class
    $objLog = new clsLogger();
    
    // Determine the action type (EDIT or ADD) based on the presence of a journal ID
    $action = ($currentSchoolId > 0) ? $objLog::EDIT : $objLog::ADD;
    $type = 'sendabsencesms';
    $userType = $objLog::ADMIN; // User type is set to ADMIN
    $IsMobile = 0;

    $objSchool = new clsSchool();
    $objSchool->saveSchoolAuditLog($currentSchoolId, $_SESSION['loggedUserId'], $userType, $action, $IsMobile,$type);
    unset($objLog);
    //Audit Log End


    header('location:sendAbsenceSMS.html?status=updated');
}

//Get School Detail
$schoolDetail = $objSchool->GetSchoolAbsenseSmsDetails($currentSchoolId);
if ($schoolDetail != '') {

    $absenceSMSTime = isset($schoolDetail['absenceSMSTime']) ? $schoolDetail['absenceSMSTime'] : '';
    $absenceSMSStatus = isset($schoolDetail['absenceSMSStatus']) ? $schoolDetail['absenceSMSStatus'] : 0;
    $absenceSMSSendTo = isset($schoolDetail['absenceSMSSendTo']) ? explode(",", $schoolDetail['absenceSMSSendTo']) : [];
    $adminRolesAbsenceSMSSendTo = isset($schoolDetail['adminRolesAbsenceSMSSendTo']) ? explode(",", $schoolDetail['adminRolesAbsenceSMSSendTo']) : [];
    $clinitionRoleAbsenceSMSSendTo = isset($schoolDetail['clinitionRoleAbsenceSMSSendTo']) ? explode(",", $schoolDetail['clinitionRoleAbsenceSMSSendTo']) : [];

    $isCustomSms = isset($schoolDetail['isCustomSms']) ? $schoolDetail['isCustomSms'] : $isCustomSms;
    $adminSms = (isset($schoolDetail['adminSms']) && $schoolDetail['adminSms'] != '') ? $schoolDetail['adminSms'] : $adminSms;
    $clinicianSms = (isset($schoolDetail['clinicianSms']) && $schoolDetail['clinicianSms'] != '') ? $schoolDetail['clinicianSms'] : $clinicianSms;
    $studentSms = (isset($schoolDetail['studentSms']) && $schoolDetail['studentSms'] != '') ? $schoolDetail['studentSms'] : $studentSms;
}

//For ALL System User Role	

$totalCompanyRoles = 0;
$objSystemUserRoleMaster = new clsSystemUserRoleMaster();
$rowsCompanyRoles = $objSystemUserRoleMaster->GetAllSystemUserRolesBySchool($schoolId);
if ($rowsCompanyRoles != '') {
    $totalCompanyRoles = mysqli_num_rows($rowsCompanyRoles);
}

//For Clinician Role List
$totalClinicianRoles = 0;
$objClinicianRoleMaster = new clsClinicianRoleMaster();
$rowsClinicianRoles = $objClinicianRoleMaster->GetAllClinicianRolesBySchool($schoolId);
if ($rowsClinicianRoles != '') {
    $totalClinicianRoles = mysqli_num_rows($rowsClinicianRoles);
}
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Send Absence SMS</title>

    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">


</head>

<body>
    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <ol class="breadcrumb">
                <li><a href="dashboard.html">Home</a></li>
                <li><a href="settings.html">Settings</a></li>
                <li><span class="active">Send Absence SMS</span></li>
            </ol>
        </div>
    </div>

    <div class="container">
        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "updated") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Send Absence SMS updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <form id="frmRank" data-parsley-validate="" class="form-horizontal" method="POST" action="sendAbsenceSMS.html" novalidate="">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12  " for="absenceSMSTime">Status: </label>
                        <div class="col-md-12 ">
                            <input type="radio" name="absenceSMSStatus" class="absenceSMSStatus" id="active" value="1" <?php if ($absenceSMSStatus == 1) { ?> checked <?php } ?>> On
                            <input type="radio" name="absenceSMSStatus" class="absenceSMSStatus" id="inActive" value="0" style="margin-left:10px" <?php if ($absenceSMSStatus == 0) { ?> checked <?php } ?>> Off
                        </div>
                    </div>
                </div>
          
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 " for="absenceSMSTime">Custom SMS: </label>
                        <div class="col-md-12 ">
                            <input type="radio" name="customSms" class="customSms" id="active" value="1" <?php if ($isCustomSms == 1) { ?> checked <?php } ?>> Yes
                            <input type="radio" name="customSms" class="customSms" id="inActive" value="0" style="margin-left:10px" <?php if ($isCustomSms == 0) { ?> checked <?php } ?>> No
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 " for="absenceSMSTime">Send SMS After Rotation Start (In Minutes): </label>
                        <div class="col-md-12 ">
                            <input id="absenceSMSTime" name="absenceSMSTime" value="<?php if ($absenceSMSTime > 0) {
                                                                                        echo $absenceSMSTime;
                                                                                    } ?>" required="" type="text" placeholder="" class="form-control input-md required-input" data-parsley-validation-threshold="1" data-parsley-trigger="keyup" data-parsley-type="digits">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row customSmsClass">
                <div class="col-md-12 col-xs-12" >
                    <div class="panel panel-default border-14">
                        <div class="panel-body">

                            <p><b>Custom SMS Notification Configuration Instructions:</b>
                            </p>
                            <p>1. Please note that the text within the double curly braces {{ }} represents dynamic values that will be replaced with actual information. Therefore, refrain from altering the text enclosed in these brackets.
                            </p>
                            <p>2. Configure the SMS using the following available values for dynamic content. You may incorporate other text directly into your message without enclosing them in brackets:</p>
                            <p> - {{ADMIN_NAME}}: Refers to the name of the Admin available in CT. <br>
                                - {{ROTATION_NAME}}: Denotes the name of the rotation for which the notification has been triggered.<br>
                                - {{CLINICIAN_NAME}}: Represents the name of the Clinician.<br>
                                - {{STUDENT_NAME}}: Signifies the name of the Student.<br>
                            </p>
                            <p>Feel free to personalize your SMS notifications by utilizing these dynamic values to convey relevant and accurate information to selected users, including Students, Clinicians, and Administrators.</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row customSmsClass">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12" for="absenceSMSTime">Admin SMS: </label>
                        <!-- <div class="col-md-3 padding_right_zero"> -->
                        <!-- <textarea name="studentcomment" id="studentcomment" class="form-control input-md mb-10" rows="4" cols="100"></textarea> -->
                        <!-- </div> -->
                    <!-- </div>
                </div>
                    <div class="form-group"> -->
                    <div class="col-md-12" >

                        <textarea name="adminSms" id="adminSms" class="form-control input-md mb-10" rows="4" cols="100" maxlength="200"><?php echo $adminSms; ?></textarea>
                        <div id="adminSms-count" class="character-count" style="float: right;">
                            <span id="adminSms-current">0</span>
                            <span id="adminSms-maximum">/ 200</span>
                        </div>
                    </div>
                </div>
            </div>
            </div>
            <div class="row customSmsClass">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 " for="absenceSMSTime">Clinician SMS </label>
                        <!-- <div class="col-md-3 padding_right_zero"> -->
                        <!-- <textarea name="studentcomment" id="studentcomment" class="form-control input-md mb-10" rows="4" cols="100"></textarea> -->
                        <!-- </div> -->
                    <!-- </div>
                </div> -->
                <div class="col-md-12" >
                    <!-- <div class="form-group"> -->
                        <textarea name="clinicianSms" id="clinicianSms" class="form-control input-md mb-10" rows="4" cols="100" maxlength="200"><?php echo $clinicianSms; ?></textarea>
                        <div id="clinicianSms-count" class="character-count" style="float: right;">
                            <span id="clinicianSms-current">0</span>
                            <span id="clinicianSms-maximum">/ 200</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row customSmsClass">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 " for="absenceSMSTime">Student SMS </label>
                        <!-- <div class="col-md-3 padding_right_zero"> -->
                        <!-- <textarea name="studentcomment" id="studentcomment" class="form-control input-md mb-10" rows="4" cols="100"></textarea> -->
                        <!-- </div> -->
                    <!-- </div> -->
                </div>
                <!-- <div class="col-md-6" style="margin-left: -80px;"> -->
                    <div class="col-md-12">
                        <textarea name="studentSms" id="studentSms" class="form-control input-md mb-10" rows="4" cols="100" maxlength="200"><?php echo $studentSms; ?></textarea>
                        <div id="studentSms-count" class="character-count" style="float: right;">
                            <span id="studentSms-current">0</span>
                            <span id="studentSms-maximum">/ 200</span>
                        </div>
                    </div>
                </div>
            </div>
            </div>
            
            <div class="">
                <div class="col-md-6">
                    <div class="form-group m-0">
                        <label class="col-md-12 p-0" for="absenceSMSSendTo">Send To: </label>
                        <div class="col-md-12 ">
                            <!-- <input type="checkbox" name="absenceSMSSendTo[]" class="absenceSMSSendTo" id="admin" onclick="" value="1" <?php if (in_array("1", $absenceSMSSendTo)) { ?> checked <?php } ?> required data-parsley-errors-container="#error-absenceSMSSendTo"> Admin <br> -->
                            <input type="checkbox" name="absenceSMSSendTo[]" class="absenceSMSSendTo" id="admin" onclick="" value="1" <?php if (in_array("1", $absenceSMSSendTo)) { ?> checked <?php } ?>> Admin <br>
                            <div class="row  margin_top_five margin_left_fifteen">
                                <?php
                                if ($totalCompanyRoles > 0) {
                                    while ($row = mysqli_fetch_array($rowsCompanyRoles)) {

                                        $systemUserRoleMasterId = $row['systemUserRoleMasterId'];
                                        $schoolId = $row['schoolId'];
                                        $title = stripslashes($row['title']);
                                        $sortOrder = stripslashes($row['sortOrder']);
                                        $type = stripslashes($row['type']);
                                        $isPrimaryRole = stripslashes($row['isPrimaryRole']);

                                        $totalUserCount = 0;
                                        $totalUserCount = $objSystemUserRoleMaster->GetSystemUserCountByRole($systemUserRoleMasterId);

                                ?>

                                        <input type="checkbox" name="adminRolesAbsenceSMSSendTo[]" class="absenceSMSSendTo adminRoles" id="admin_<?php echo $systemUserRoleMasterId; ?>" onclick="" value="<?php echo $systemUserRoleMasterId; ?>" <?php if (in_array($systemUserRoleMasterId, $adminRolesAbsenceSMSSendTo)) { ?> checked <?php } ?>> <?php echo $title; ?> <br>

                                <?php
                                    }
                                }
                                ?>
                            </div>
                            <input type="checkbox" name="absenceSMSSendTo[]" class="absenceSMSSendTo" id="clinician" onclick="" value="2" <?php if (in_array("2", $absenceSMSSendTo)) { ?> checked <?php } ?> style="margin-top: 10px !important;"> Clinician <br>
                            <div class="row  margin_top_five margin_left_fifteen">
                                <?php
                                if ($totalClinicianRoles > 0) {
                                    while ($row = mysqli_fetch_array($rowsClinicianRoles)) {

                                        $clinicianRoleId = $row['clinicianRoleId'];
                                        $title = stripslashes($row['title']);
                                        $type = stripslashes($row['type']);
                                        $totalUserCount = 0;
                                        $totalUserCount = $objClinicianRoleMaster->GetClinicianCountByRole($clinicianRoleId, $schoolId);
                                ?>

                                        <input type="checkbox" name="clinitionRoleAbsenceSMSSendTo[]" class="absenceSMSSendTo clinicianRoles" id="admin_<?php echo $clinicianRoleId; ?>" onclick="" value="<?php echo $clinicianRoleId; ?>" <?php if (in_array($clinicianRoleId, $clinitionRoleAbsenceSMSSendTo)) { ?> checked <?php } ?>> <?php echo $title; ?> <br>

                                <?php
                                    }
                                }
                                ?>
                            </div>
                            <input type="checkbox" name="absenceSMSSendTo[]" class="absenceSMSSendTo" id="student" onclick="" value="3" <?php if (in_array("3", $absenceSMSSendTo)) { ?> checked <?php } ?> style="margin-top: 10px !important;"> Student
                            <br>
                            <div id="error-absenceSMSSendTo" class="margin_top_five"></div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
            
            <div class="form-group">
                <!-- <label class="col-md-2 control-label"></label> -->
                <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                    <button id="btnSubmit" name="btnSubmit" class="btn btn-success margin_right_five">Save</button>
                    <a type="button" href="settings.html" class="btn btn-default">Cancel</a>
                </div>
            </div>
            </div>
            
        </form>
    </div>
    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script>
        $(window).load(function() {

            $('#frmRank').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    return true; // Don't submit form for this demo
                });
            var customSmsBtnVal = $('input[name="customSms"]:checked').val();
            console.log('customSmsBtnVal' + customSmsBtnVal);
            // $('.customSms').val(customSmsBtnVal).trigger('click');
            // $('.customSms[value="'+customSmsBtnVal+'"]').prop('checked', true);
            $('input[name="customSms"]:checked').trigger('click');

            $('textarea[id$="Sms"]').each(function() {
                var textareaId = $(this).attr('id');
                updateCharacterCount(textareaId);
            });

        });

        var absenceSMSStatus = '<?php echo $absenceSMSStatus; ?>';
        var adminRoleCount = '<?php echo $totalCompanyRoles; ?>';
        var clinicianRoleCount = '<?php echo $totalClinicianRoles; ?>';

        if (absenceSMSStatus == 0) {
            $('#absenceSMSTime').attr('readonly', true);
            $('.absenceSMSSendTo').attr("onclick", 'return false');
            $('#absenceSMSTime').prop('required', false);
            // $('.absenceSMSSendTo').prop('required',false);
        } else {
            $('#absenceSMSTime').attr('readonly', false);
            $('.absenceSMSSendTo').attr("onclick", 'return true');

            $('#absenceSMSTime').prop('required', true);
            // $('.absenceSMSSendTo').prop('required',true);
        }

        $(document).on('click', '.absenceSMSStatus', function() {
            var value = $(this).val();
            $('#absenceSMSTime').attr('readonly', true);
            $('.absenceSMSSendTo').attr("onclick", 'return false');
            $('#absenceSMSTime').prop('required', false);
            $('.absenceSMSSendTo').prop('required', false);

            if (value == 1) {
                $('#absenceSMSTime').attr('readonly', false);
                // $('.absenceSMSSendTo').attr("readonly", false);
                $('.absenceSMSSendTo').attr("onclick", 'return true');

                $('#absenceSMSTime').prop('required', true);
                // $('.absenceSMSSendTo').prop('required',true);
            }
        });
        $(document).on('click', '#admin', function() {
            var radioValue = $("input[name='absenceSMSStatus']:checked").val();
            if (radioValue == 1)
                $(".adminRoles").prop('checked', $(this).prop("checked"));
        });
        $(document).on('click', '.adminRoles', function() {
            if (adminRoleCount == $('.adminRoles:checked').size())
                $("#admin").prop('checked', $(this).prop("checked"));
            else
                $("#admin").prop('checked', false);

        });
        $(document).on('click', '#clinician', function() {
            var radioValue = $("input[name='absenceSMSStatus']:checked").val();
            if (radioValue == 1)
                $(".clinicianRoles").prop('checked', $(this).prop("checked"));
        });
        $(document).on('click', '.clinicianRoles', function() {
            if (clinicianRoleCount == $('.clinicianRoles:checked').size())
                $("#clinician").prop('checked', $(this).prop("checked"));
            else
                $("#clinician").prop('checked', false);

        });

        $(document).on('click', '.customSms', function() {
            var radioBtnVal = $('input[name="customSms"]:checked').val();
            console.log('radioBtnVal' + radioBtnVal);
            if (radioBtnVal == 1) {
                $('.customSmsClass').show();
            } else {
                $('.customSmsClass').hide();
            }
        });

        $('textarea[id$="Sms"]').on('input', function() {
            // Get the ID of the current textarea
            var textareaId = $(this).attr('id');

            // Update the character count for the current textarea
            updateCharacterCount(textareaId);
        });

        // Reusable function to update character count
        function updateCharacterCount(textareaId) {
            var currentLength = $('#' + textareaId).val().length;
            $('#' + textareaId + '-current').text(currentLength);
        }
    </script>
</body>

</html>