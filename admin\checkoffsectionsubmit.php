<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsCheckoffSectionMaster.php');
include('../class/clscheckoff.php');
include('../setRequest.php');

//print_r($_POST);
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {

	$checkoffSectionId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;

	$status = ($checkoffSectionId > 0) ? 'updated' : 'added';

	if (isset($_GET['topicid'])) {
		$topicid = DecodeQueryData($_GET['topicid']);
	}
	$title = $_POST['txtcheckoffsection'];
	$sortOrder = $_POST['txtsortorder'];
	$description = $_POST['txtdescription'];

	$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
	$isAdvanceCheckoff = ($isActiveCheckoff == 0) ? 1 : 0;
	//Save data
	$objCheckoffSection = new clsCheckoffSectionMaster();
	$objCheckoffSection->schoolSectionTitle = $title;
	$objCheckoffSection->sortOrder = $sortOrder;
	$objCheckoffSection->description = $description;
	$objCheckoffSection->schoolId = $currentSchoolId;
	$objCheckoffSection->isAdvanceCheckoff = $isAdvanceCheckoff;
	$retcheckoffsectionmasterId = $objCheckoffSection->SaveCheckoffSection($checkoffSectionId);

	if (!isset($_GET['editid'])) {
		$objCheckoffSection->schoolTopicId = $topicid;
		$objCheckoffSection->schoolSectionId = $retcheckoffsectionmasterId;
		$objCheckoffSection->SaveCheckoffSectionInTopicDetails($retcheckoffsectionmasterId);
	}
	unset($objCheckoffSection);
	if ($retcheckoffsectionmasterId > 0) {

		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "section";
		$action = ($checkoffSectionId > 0) ? $objLog::EDIT : $objLog::ADD;
		$isSuperAdmin = isset($_SESSION['isCurrentSchoolSuperAdmin']) ? ($_SESSION['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;

		$objCheckoff = new clscheckoff();
		$objCheckoff->saveCompEvaluationAuditLog($retcheckoffsectionmasterId,$_SESSION['loggedUserId'], $userType, $action, 0, $type, $isSuperAdmin);
		unset($objCheckoff);
		unset($objLog);

		header('location:checkoffsection.html?status=' . $status . '&topicid=' . EncodeQueryData($topicid));
	} else {
		header('location:addsection.html?status=error');
	}
} else {
	header('location:checkoffsection.html?status=' . $status . '&topicid=' . EncodeQueryData($topicid));
	//header('location:checkoffsection.html');
	exit();
}
