<?php
$IsMobile  = isset($_POST['IsMobile']) ? $_POST['IsMobile'] : 0;
$preceptorNum = isset($_POST['preceptorNum']) ? $_POST['preceptorNum'] : '';

include('../includes/config.php');
include('includes/validateUserLogin.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsCIevaluation.php');
include('../class/clsQuestionOption.php');
include('../class/clsPerformance.php');
include('../class/clsClinicalEval.php');
include('../class/clscheckoff.php');
include('../class/clsProjection.php');

// echo '<pre>';
// print_r($_POST);
// print_r($_GET);
// exit;

@session_start();
// print_r($_SESSION);
$objDB = new clsDB();

$userId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;
$clinicalEvaluationMasterId = 0;
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
    $rotationId = 0;


    if (isset($_GET['clinicalEvaluationMasterId'])) {
        $clinicalEvaluationMasterId = $_GET['clinicalEvaluationMasterId'];
        $clinicalEvaluationMasterId = DecodeQueryData($clinicalEvaluationMasterId);
    }
    if (isset($_GET['clinicalRotationId'])) {
        $clinicalRotationId = $_GET['clinicalRotationId'];
        $clinicalRotationId = DecodeQueryData($clinicalRotationId);
    }
    //$status =  'Added';	
    $clinicalEvaluationMasterId = isset($_GET['clinicalEvaluationMasterId']) ? DecodeQueryData($_GET['clinicalEvaluationMasterId']) : 0;
    $status = ($clinicalEvaluationMasterId > 0) ? 'updated' : 'added';

    //echo $status.$formativeId;   

    $studentId = $cbostudent = $_POST['cboStudent'];
    $cboclinician  = ($_POST['cboclinician']);
    $exam  = ($_POST['exam']);
    $accession  = ($_POST['accession']);
    $radioType  = ($_POST['radioType']);
    $radioRepeat  = isset($_POST['radioType']) ? $_POST['radioType'] : 0;
    $radioRepeatReason  = ($_POST['radioRepeatReason']);
    $technologistScore  = ($_POST['technologistScore']);
    $evaluatorScore  = ($_POST['evaluatorScore']);
    $totalScore  = ($_POST['totalScore']);
    $radioResult  = ($_POST['radioResult']);
    $reason  = ($_POST['reason']);
    $comment  = ($_POST['comment']);
    $schoolComment  = ($_POST['schoolComment']);

    $evaluationDate = GetDateStringInServerFormat($_POST['evaluationDate']);
    $evaluationDate = str_replace('00:00:00', '12:00 PM', $evaluationDate);
    $evaluationDate = date('Y-m-d H:i', strtotime($evaluationDate));

    $studentDate = $_POST['studentDate'];
    if ($studentDate != '') {
        $studentDate = GetDateStringInServerFormat($_POST['studentDate']);
        $studentDate = str_replace('00:00:00', '12:00 PM', $studentDate);
        $studentDate = date('Y-m-d H:i', strtotime($studentDate));
    }

    $evaluatorDate = $_POST['evaluatorDate'];
    if ($evaluatorDate != '') {
        $evaluatorDate = GetDateStringInServerFormat($_POST['evaluatorDate']);
        $evaluatorDate = str_replace('00:00:00', '12:00 PM', $evaluatorDate);
        $evaluatorDate = date('Y-m-d H:i', strtotime($evaluatorDate));
    }

    $preceptorsignature = $_POST['preceptorsignature'];
    $preceptorId = $_POST['preceptorId'];

    $preceptorSignatureDate = $_POST['preceptorSignatureDate'];
    if ($preceptorSignatureDate != '') {
        $preceptorSignatureDate = GetDateStringInServerFormat($_POST['preceptorSignatureDate']);
        $preceptorSignatureDate = str_replace('00:00:00', '12:00 PM', $preceptorSignatureDate);
        $preceptorSignatureDate = date('Y-m-d H:i', strtotime($preceptorSignatureDate));
    }

    $checkoffId = isset($_POST['checkoffId']) ? $_POST['checkoffId'] : 0;

    // Save School Comment
    $retClinicalEvaluationId = $objDB->UpdateSingleColumnValueToTable('clinicalevaluationmaster', 'schoolComment', $schoolComment, 'clinicalEvaluationMasterId', $clinicalEvaluationMasterId);

    // $objClinicalEval = new clsClinicalEval();
    // $objClinicalEval->rotationId = $clinicalRotationId;
    // $objClinicalEval->clinicianId = $cboclinician;
    // $objClinicalEval->studentId = $cbostudent;
    // $objClinicalEval->schoolId = $currentSchoolId;
    // $objClinicalEval->preceptorId = $preceptorId;
    // $objClinicalEval->evaluationDate = $evaluationDate;
    // $objClinicalEval->evaluatorSignatureDate = $evaluatorDate;
    // $objClinicalEval->studentSignatureDate = $studentDate;
    // $objClinicalEval->preceptorSignatureDate = $preceptorSignatureDate;
    // $objClinicalEval->exam = $exam;
    // $objClinicalEval->accession = $accession;
    // $objClinicalEval->type = $radioType;
    // $objClinicalEval->repeat = $radioRepeat;
    // $objClinicalEval->repeatReason = $radioRepeatReason;
    // $objClinicalEval->technologistScore = $technologistScore;
    // $objClinicalEval->evaluatorScore = $evaluatorScore;
    // $objClinicalEval->totalScore = $totalScore;
    // $objClinicalEval->result = $radioResult;
    // $objClinicalEval->reason = $reason;
    // $objClinicalEval->comment = $comment;
    // $objClinicalEval->schoolComment = $schoolComment;
    // $objClinicalEval->checkoffId = $checkoffId;
    // $objClinicalEval->createdBy = $cbostudent;
    // $retClinicalEvaluationId = $objClinicalEval->SaveClinicalEvaluation($clinicalEvaluationMasterId);

    // if ($retClinicalEvaluationId && $studentDate != '')
    //     $objDB->UpdateSingleColumnValueToTable('checkoff', 'student_evaluationDate', $studentDate, 'checkoffId', $checkoffId);

    // $objClinicalEval->DeleteclinicalDetails($retClinicalEvaluationId);
    // $objProjection = new clsProjection();
    // $objProjection->DeleteClinicalProjectionDetails($retClinicalEvaluationId);
    // //SAVE OPTIONS
    // foreach ($_POST as $id => $value) {

    //     if (strpos($id, 'questionoptionsA_') === 0) {
    //         $arraIds = explode("_", $id);

    //         if (!isset($arraIds[1]))
    //             continue;

    //         $schoolEvaluationQuestionId = $arraIds[1];
    //         $objClinicalEval->evaluationMasterId = $retClinicalEvaluationId;
    //         $objClinicalEval->schoolEvaluationQuestionId = $schoolEvaluationQuestionId;
    //         $objClinicalEval->schoolEvaluationOptionValueA = $value[0];
    //         $objClinicalEval->schoolEvaluationOptionAnswerText = '';
    //         $objClinicalEval->comment = isset($_POST['textarea_' . $schoolEvaluationQuestionId]) ? $_POST['textarea_' . $schoolEvaluationQuestionId] : '';
    //         $getdetailId = $objDB->GetSingleColumnValueFromTable('clinicalevaluationdetails', 'evaluationDetailId', 'evaluationMasterId', $retClinicalEvaluationId, 'schoolEvaluationQuestionId', $schoolEvaluationQuestionId);
    //         if ($getdetailId) {
    //             $objDB->UpdateSingleColumnValueToTable('clinicalevaluationdetails', 'schoolEvaluationOptionValueA', $value[0], 'evaluationDetailId', $getdetailId);
    //         } else {
    //             $evaluationDetaild = $objClinicalEval->SaveClinicalEvaluationDetails($retClinicalEvaluationId);
    //         }
    //     }
    // }

    // //SAVE OPTIONS
    // foreach ($_POST as $id => $value) {

    //     if (strpos($id, 'questionoptionsB_') === 0) {
    //         $arraIds = explode("_", $id);

    //         if (!isset($arraIds[1]))
    //             continue;

    //         $schoolEvaluationQuestionId = $arraIds[1];
    //         $objClinicalEval->evaluationMasterId = $retClinicalEvaluationId;
    //         $objClinicalEval->schoolEvaluationQuestionId = $schoolEvaluationQuestionId;
    //         $objClinicalEval->schoolEvaluationOptionValueB = $value[0];
    //         $objClinicalEval->schoolEvaluationOptionAnswerText = '';
    //         $objClinicalEval->comment = isset($_POST['textarea_' . $schoolEvaluationQuestionId]) ? $_POST['textarea_' . $schoolEvaluationQuestionId] : '';
    //         $getdetailId = $objDB->GetSingleColumnValueFromTable('clinicalevaluationdetails', 'evaluationDetailId', 'evaluationMasterId', $retClinicalEvaluationId, 'schoolEvaluationQuestionId', $schoolEvaluationQuestionId);
    //         if ($getdetailId) {
    //             $objDB->UpdateSingleColumnValueToTable('clinicalevaluationdetails', 'schoolEvaluationOptionValueB', $value[0], 'evaluationDetailId', $getdetailId);
    //         } else {
    //             $evaluationDetaild = $objClinicalEval->SaveClinicalEvaluationDetails($retClinicalEvaluationId);
    //         }
    //     }
    // }

    // //SAVE OPTIONS
    // foreach ($_POST as $id => $value) {

    //     if (strpos($id, 'questionoptionsC_') === 0) {
    //         $arraIds = explode("_", $id);

    //         if (!isset($arraIds[1]))
    //             continue;

    //         $schoolEvaluationQuestionId = $arraIds[1];
    //         $objClinicalEval->evaluationMasterId = $retClinicalEvaluationId;
    //         $objClinicalEval->schoolEvaluationQuestionId = $schoolEvaluationQuestionId;
    //         $objClinicalEval->schoolEvaluationOptionValueC = $value[0];
    //         $objClinicalEval->schoolEvaluationOptionAnswerText = '';
    //         $objClinicalEval->comment = isset($_POST['textarea_' . $schoolEvaluationQuestionId]) ? $_POST['textarea_' . $schoolEvaluationQuestionId] : '';
    //         $getdetailId = $objDB->GetSingleColumnValueFromTable('clinicalevaluationdetails', 'evaluationDetailId', 'evaluationMasterId', $retClinicalEvaluationId, 'schoolEvaluationQuestionId', $schoolEvaluationQuestionId);
    //         if ($getdetailId) {
    //             $objDB->UpdateSingleColumnValueToTable('clinicalevaluationdetails', 'schoolEvaluationOptionValueC', $value[0], 'evaluationDetailId', $getdetailId);
    //         } else {
    //             $evaluationDetaild = $objClinicalEval->SaveClinicalEvaluationDetails($retClinicalEvaluationId);
    //         }
    //     }
    // }

    // foreach ($_POST as $id => $value) {

    //     if (strpos($id, 'projection_') === 0) {
    //         $arraIds = explode("_", $id);
    //         $projectionId = $value;
    //         $prjectionType = $arraIds[1];
    //         $isTechnologist = $arraIds[2];
    //         // echo '</br></br> projectionId ' . $projectionId;
    //         // echo '</br> prjectionType ' . $prjectionType;
    //         // echo '</br> isTechnologist ' . $isTechnologist;
    //         $objProjection->compId = $retClinicalEvaluationId;
    //         $objProjection->projectionId = $projectionId;
    //         $objProjection->projectionType = $prjectionType;
    //         $objProjection->isTechnologist = $isTechnologist;
    //         $objProjection->saveClinicalProjectionDetails();

    //     }
    // }

    if ($retClinicalEvaluationId > 0) {

        //Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a ID
		$action = $objLog::EDIT;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objCheckoff = new clscheckoff();
		$objCheckoff->saveCompEvaluationAuditLog($clinicalEvaluationMasterId, $userId, $userType, $action, $IsMobile, '', 0, 'finalComp');
		unset($objCheckoff);

		unset($objLog);
		
        if ($IsMobile) {
            header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=clinicalEvaluation');
            exit;
        } else {
            if ($checkoffId) {
                if ($studentId > 0) {
                    if ($IsMobile) {
                        header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=checkoff');
                        exit();
                    } else {
                        header('location:checkoffs.html?studentId=' . EncodeQueryData($studentId) . '&Type=C&status=' . $status);
                        exit;
                    }
                } else {
                    if ($IsMobile) {
                        header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=checkoff');
                        exit();
                    } else {
                        header('location:checkoffs.html?rotationId=' . EncodeQueryData($rotationId) . '&Type=R&status=' . $status);
                        exit;
                    }
                }
            } else {
                header('location:clinicalEvaluationList.html?clinicalEvaluationMasterId=' . EncodeQueryData($clinicalEvaluationMasterId) . '&clinicalRotationId=' . EncodeQueryData($clinicalRotationId) . '&status=' . $status);
                exit();
            }
        }
    } else {
        if ($IsMobile) {
            header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=clinicalEvaluation');
            exit();
        } else {
            header('location:clinicalEval.html?status=error');
        }
    }
} {
    if ($IsMobile) {
        header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=clinicalEvaluation');
        exit();
    } else {
        header('location:clinicalEvaluationList.html');
        exit();
    }
}
