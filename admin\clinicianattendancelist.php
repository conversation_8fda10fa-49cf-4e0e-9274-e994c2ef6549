<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsRotation.php');
include('../class/clsClinicianAttendance.php');
include('../class/clsAttendance.php');
include('../class/clsClinician.php');
include('../class/clsInteraction.php');
include('../class/clsClinicianRoleMaster.php');

//For Check Checkoff 
$isActiveCheckoff = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];

$selrotationId = 0;
$rotationId = 0;
$roleId = 0;
$inse = 0;
$insetting = 0;
$TotalHoursSpent = 0;
$TotalpointsAwarded = 0;
$TotalInteractionHoursSpent = 0;
$TotalApprovedTotalHours = 0;
$totalHoursTominutes = 0;
$totalOriginalHoursTominutes = 0;
$totalMinutes = 0;
$totalOriginalHoursForAllStudent = 0;
$originalTotalHours = 0;
$totalOriginalMinutes = 0;
$approvedhours = 0;
if (isset($_GET['ins'])) {
    $insetting = $_GET['ins'];
    $inse = DecodeQueryData($insetting);
}
if (isset($_GET['roleId'])) {
    $roleId = $_GET['roleId'];
    $roleId = DecodeQueryData($roleId);
}

//For All Student List
// $objClinician = new clsClinicianAttendance();
$objClinician = new clsClinician();
$rowsClinicianData = $objClinician->GetAllSchoolClinicians($currentSchoolId);
$totalStudentCount = 0;
if ($rowsClinicianData != '') {
    $totalStudentCount = mysqli_num_rows($rowsClinicianData);
}

//For Rotation
$objrotation = new clsRotation();
$rotation = $objrotation->GetAllrotation($currentSchoolId, $locationId = 0, $courseId = 0, $selrotationId);
unset($objrotation);

//For Rank List
$objClinicianRoleMaster = new clsClinicianRoleMaster();
$roles = $objClinicianRoleMaster->GetAllClinicianRolesBySchool($currentSchoolId);
unset($objClinicianRoleMaster);

$ApprovedTotalHours = '';
// $objAttendance = new clsAttendance();
// $GetAttendance = $objAttendance->GetAttendanceTotalBySchool($currentSchoolId);

// $ApprovedTotalHours=$GetAttendance['ApprovedTotalHours'];

// if($ApprovedTotalHours !='')
// {
//     $ApprovedHours = explode(':', $ApprovedTotalHours);
//     $Hours = $ApprovedHours[0];
//     $Minutes = $ApprovedHours[1];
//     $totalHoursTominutes += $Hours;
//     $totalMinutes += $Minutes;
//     $ApprovedTotalHours= $Hours.':'.$Minutes;
// }
// else 
// {
//     $ApprovedTotalHours="-";
// }

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Clinical</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    <style>
        .mt-1 {
            margin-top: 9px;
            padding-left: 58px;
        }
    </style>

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if ($inse == 1) { ?>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Clinical</li>
                    <?php } else { ?>
                        <li class="active"><a href="schoolclinicians.html">Evaluators</a></li>
                        <li class="active">Evaluator Attendance</li>
                    <?php } ?>
                </ol>
            </div>
            <div class="pull-right">
                <!-- <ol class="breadcrumb">
						<li class="active"><a class="" href="clinicianattendancelist.html">Clinician Attendance</a> </li>
				</ol> -->
            </div>
        </div>
    </div>

    <div class="container">


        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Journal added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Journal updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <div class="row">
            <div class="col-md-3 pull-right">
                <div class="form-group ">
                    <label class="col-md-4 control-label mt-1" for="cborole">Role</label>
                    <div class="col-md-8">
                        <select id="cborole" name="cborole" class="form-control select2_single">
                            <option value="" selected>Select All</option>
                            <?php
                            if ($roles != "") {
                                while ($row = mysqli_fetch_assoc($roles)) {
                                    //if($row['title'] == 'Freshman') continue;
                                    $selroleId  = $row['clinicianRoleId'];
                                    $name  = stripslashes($row['title']);

                            ?>
                                    <option value="<?php echo EncodeQueryData($selroleId); ?>" <?php if ($roleId == $selroleId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                            <?php

                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
            </div>
        </div> <br>
        <table id="attendance_datatable" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Role</th>
                    <!-- <th style="text-align:center">Dr.Interaction<br>Time Spent</th>
                    <th style="text-align:center">Dr.Interaction<br>Total Points</th> -->
                    <th style="text-align:center">Attendance<br>Approved Hours</th>
                    <th style="text-align:center;width:115px">Attendance<br>Total Hours</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>

        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>



    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();
            $('.addCommentpopup').magnificPopup({
                'type': 'ajax',
            });

        });

        $("#cborole").change(function(){
            var roleId = $(this).val();
            var inse = '<?php echo ($insetting); ?>';

            if(roleId)
            {
                window.location.href = "clinicianattendancelist.html?roleId="+roleId;
            }
            else{
                window.location.href = "clinicianattendancelist.html";
            }
        });

        var roleId = '<?php echo ($roleId); ?>';
        var inse = '<?php echo ($inse); ?>';
        var currentSchoolId = '<?php echo ($currentSchoolId); ?>';
        var isActiveCheckoff = '<?php echo ($isActiveCheckoff); ?>';

        var current_datatable = $("#attendance_datatable").DataTable({
            // "footerCallback": function(row, data, start, end, display) {
            //     var api = this.api(),
            //         data;
            //     // converting to interger to find total
            //     // var intVal = function(i) {
            //     //     return typeof i === 'string' ?
            //     //         i.replace(/[\$,]/g, '') * 1 :
            //     //         typeof i === 'number' ?
            //     //         i : 0;
            //     // };

            //     //Total Time Spend
            //     // var timeSpentTotal = api
            //     //     .column(3)
            //     //     .data()
            //     //     .reduce(function(a, b) {
            //     //         return intVal(a) + intVal(b);
            //     //     }, 0);

            //     //Total Point
            //     // var pointsTotal = api
            //     //     .column(4)
            //     //     .data()
            //     //     .reduce(function(a, b) {
            //     //         return intVal(a) + intVal(b);
            //     //     }, 0);

            //     //Total Approved Hour
            //     var approvedHourTotal = api
            //         .column(3)
            //         .data()
            //         .reduce(function(a, b) {
            //             return intVal(a) + intVal(b);
            //         }, 0);

            //     //Total Hours
            //     var hoursTotal = api
            //         .column(4)
            //         .data()
            //         .reduce(function(a, b) {
            //             return intVal(a) + intVal(b);
            //         }, 0);

            //     var ApprovedTotalHours = '<?php echo $ApprovedTotalHours; ?>';

            //     // Update footer
            //     // $( api.column( 2 ).footer() ).html('Total');
            //     // $( api.column( 3 ).footer() ).html(timeSpentTotal); 
            //     // $( api.column( 4 ).footer() ).html(pointsTotal); 
            //     // $( api.column( 5 ).footer() ).html(ApprovedTotalHours); 
            //     // $( api.column( 6 ).footer() ).html(hoursTotal); 

            // },
            "processing": true,
            "bServerSide": true,
            "aoColumns": [{
                "sWidth": "2%"
            }, {
                "sWidth": "2%"
            }, {
                "sWidth": "5%"
            }, {
                "sWidth": "2%",
                "sClass": "text-center",
                "bSortable": false
            }, {
                "sWidth": "2%",
                "sClass": "text-center",
                "bSortable": false
            }, {
                "sWidth": "2%",
                "sClass": "text-center",
                "bSortable": false

            }],
            "ajax": {
                url: "../ajax/ajax_get_clinician_list_to_admin.html", // json datasource
                type: "POST", // method  , by default get
                data: {
                    'isActiveCheckoff': isActiveCheckoff,
                    'currentSchoolId': currentSchoolId,
                    'roleId': roleId,
                    'inse': inse
                },
                // success: function(data) {
                //     console.log(data);
                // },
                error: function() { // error handling
                    $(".employee-grid-error").html("");
                    $("#attendance_datatable").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
                    $("#employee-grid_processing").css("display", "none");
                }
            }
        });
    </script>
</body>

</html>