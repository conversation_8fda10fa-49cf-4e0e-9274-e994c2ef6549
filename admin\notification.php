<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsStudentRankMaster.php');
    include('../class/clsNotification.php');
	include('../setRequest.php'); 

    $schoolId = 0;
    if(isset($_GET['schoolId'])) //Edit Mode
	{
		$schoolId = $_GET['schoolId'];
        $schoolId = DecodeQueryData($schoolId);
    }
    else
    {
        $schoolId= $currentSchoolId;
        $title ="Notification Settings - ".$currenschoolDisplayname;
    }
	
	$objNotification = new clsNotification();
	$notification = $objNotification->GetNotifications($schoolId);
	
	$student_rotation_bfore = '';
	$student_rotation_message_bfore = '';
	$student_rotation_after = '';
	$student_rotation_message_after = '';
	$student_rotation_notification = 0;

	$student_Checkoff_bfore = '';
	$student_Checkoff_message_bfore = '';
	$student_Checkoff_after = '';
	$student_Checkoff_message_after = '';
	$student_Checkoff_notification = 0;
	
	$student_interaction_bfore = '';
	$student_interaction_message_bfore = '';
	$student_interaction_after = '';
	$student_interaction_message_after = '';
	$student_interaction_notification = 0;
	
	$student_formative_bfore = '';
	$student_formative_message_bfore = '';
	$student_formative_after = '';
	$student_formative_message_after = '';
	$student_formative_notification = 0;
	
	$student_summative_bfore = '';
	$student_summative_message_bfore = '';
	$student_summative_after = '';
	$student_summative_message_after = '';
	$student_summative_notification = 0;
	
	$student_equipment_bfore = '';
	$student_equipment_message_bfore = '';
	$student_equipment_after = '';
	$student_equipment_message_after = '';
	$student_equipment_notification = 0;

	$student_absent_bfore = '';
	$student_absent_message_bfore = '';
	$student_absent_after = '';
	$student_absent_message_after = '';
	$student_absent_notification = 0;
	
	$student_immunization_bfore = '';
	$student_immunization_message_bfore = '';
	$student_immunization_after = '';
	$student_immunization_message_after = '';
	$student_immunization_notification = 0;

	// Student End
	/*--------------------------*/


	// Clinician Start //
	$clinician_rotation_bfore = '';
	$clinician_rotation_message_bfore = '';
	$clinician_rotation_after = '';
	$clinician_rotation_message_after = '';
	$clinician_rotation_notification = 0;
	
	$clinician_Checkoff_bfore = '';
	$clinician_Checkoff_message_bfore = '';
	$clinician_Checkoff_after = '';
	$clinician_Checkoff_message_after = '';
	$clinician_Checkoff_notification = 0;
	
	$clinician_interaction_bfore = '';
	$clinician_interaction_message_bfore = '';
	$clinician_interaction_after = '';
	$clinician_interaction_message_after = '';
	$clinician_interaction_notification = 0;
	
	$clinician_formative_bfore = '';
	$clinician_formative_message_bfore = '';
	$clinician_formative_after = '';
	$clinician_formative_message_after = '';
	$clinician_formative_notification = 0;
	
	$clinician_summative_bfore = '';
	$clinician_summative_message_bfore = '';
	$clinician_summative_after = '';
	$clinician_summative_message_after = '';
	$clinician_summative_notification = 0;
	
	$clinician_equipment_bfore = '';
	$clinician_equipment_message_bfore = '';
	$clinician_equipment_after = '';
	$clinician_equipment_message_after = '';
	$clinician_equipment_notification = 0;

	$clinician_absent_bfore = '';
	$clinician_absent_message_bfore = '';
	$clinician_absent_after = '';
	$clinician_absent_message_after = '';
	$clinician_absent_notification = 0;
	
	$clinician_immunization_bfore = '';
	$clinician_immunization_message_bfore = '';
	$clinician_immunization_after = '';
	$clinician_immunization_message_after = '';
	$clinician_immunization_notification = 0;
	
	/*--------- Clinician End -------- */

	$admin_rotation_bfore = '';
	$admin_rotation_message_bfore = '';
	$admin_rotation_after = '';
	$admin_rotation_message_after = '';
	$admin_rotation_notification = 0;
	
	$admin_Checkoff_bfore = '';
	$admin_Checkoff_message_bfore = '';
	$admin_Checkoff_after = '';
	$admin_Checkoff_message_after = '';
	$admin_Checkoff_notification = 0;
	
	$admin_interaction_bfore = '';
	$admin_interaction_message_bfore = '';
	$admin_interaction_after = '';
	$admin_interaction_message_after = '';
	$admin_interaction_notification = 0;
	
	$admin_formative_bfore = '';
	$admin_formative_message_bfore = '';
	$admin_formative_after = '';
	$admin_formative_message_after = '';
	$admin_formative_notification = 0;
	
	$admin_summative_bfore = '';
	$admin_summative_message_bfore = '';
	$admin_summative_after = '';
	$admin_summative_message_after = '';
	$admin_summative_notification = 0;
	
	$admin_equipment_bfore = '';
	$admin_equipment_message_bfore = '';
	$admin_equipment_after = '';
	$admin_equipment_message_after = '';
	$admin_equipment_notification = 0;

	$admin_absent_bfore = '';
	$admin_absent_message_bfore = '';
	$admin_absent_after = '';
	$admin_absent_message_after = '';
	$admin_absent_notification = 0;

	$admin_daily_weekly_bfore = '';
	$admin_daily_weekly_message_bfore = '';
	$admin_daily_weekly_after = '';
	$admin_daily_weekly_message_after = '';
	$admin_daily_weekly_notification = 0;
	

	$userType = '';

	while($row = mysqli_fetch_assoc($notification))
	{
		$userType = $row['userType'];
		$modul = $row['modul'];
		
		if($userType == 'S')
		{
			if($modul == 'rotation')
			{
				$student_rotation_bfore = $row['beforeDays'];
				$student_rotation_message_bfore =stripslashes($row['beforemessage']);
				$student_rotation_after = $row['afterDays'];
				$student_rotation_message_after = stripslashes($row['aftermessage']);
				$student_rotation_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'Checkoff')
			{
				$student_Checkoff_bfore = $row['beforeDays'];
				$student_Checkoff_message_bfore = stripslashes($row['beforemessage']);
				$student_Checkoff_after = $row['afterDays'];
				$student_Checkoff_message_after = stripslashes($row['aftermessage']);
				$student_Checkoff_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'interaction')
			{
				$student_interaction_bfore = $row['beforeDays'];
				$student_interaction_message_bfore = stripslashes($row['beforemessage']);
				$student_interaction_after = $row['afterDays'];
				$student_interaction_message_after = stripslashes($row['aftermessage']);
				$student_interaction_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'formative')
			{
				$student_formative_bfore = $row['beforeDays'];
				$student_formative_message_bfore = stripslashes($row['beforemessage']);
				$student_formative_after = $row['afterDays'];
				$student_formative_message_after = stripslashes($row['aftermessage']);
				$student_formative_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'summative')
			{
				$student_summative_bfore = $row['beforeDays'];
				$student_summative_message_bfore = stripslashes($row['beforemessage']);
				$student_summative_after = $row['afterDays'];
				$student_summative_message_after = stripslashes($row['aftermessage']);
				$student_summative_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'equipment')
			{
				$student_equipment_bfore = $row['beforeDays'];
				$student_equipment_message_bfore = stripslashes($row['beforemessage']);
				$student_equipment_after = $row['afterDays'];
				$student_equipment_message_after = stripslashes($row['aftermessage']);
				$student_equipment_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'absent')
			{
				$student_absent_bfore = $row['beforeDays'];
				$student_absent_message_bfore = stripslashes($row['beforemessage']);
				$student_absent_after = $row['afterDays'];
				$student_absent_message_after = stripslashes($row['aftermessage']);
				$student_absent_notification = $row['isActiveNotification'];
			}
		}
		
		if($userType == 'C')
		{
			if($modul == 'rotation')
			{
				$clinician_rotation_bfore = $row['beforeDays'];
				$clinician_rotation_message_bfore = stripslashes($row['beforemessage']);
				$clinician_rotation_after = $row['afterDays'];
				$clinician_rotation_message_after = stripslashes($row['aftermessage']);
				$clinician_rotation_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'Checkoff')
			{
				$clinician_Checkoff_bfore = $row['beforeDays'];
				$clinician_Checkoff_message_bfore = stripslashes($row['beforemessage']);
				$clinician_Checkoff_after = $row['afterDays'];
				$clinician_Checkoff_message_after = stripslashes($row['aftermessage']);
				$clinician_Checkoff_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'interaction')
			{
				$clinician_interaction_bfore = $row['beforeDays'];
				$clinician_interaction_message_bfore = stripslashes($row['beforemessage']);
				$clinician_interaction_after = $row['afterDays'];
				$clinician_interaction_message_after = stripslashes($row['aftermessage']);
				$clinician_interaction_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'formative')
			{
				$clinician_formative_bfore = $row['beforeDays'];
				$clinician_formative_message_bfore = stripslashes($row['beforemessage']);
				$clinician_formative_after = $row['afterDays'];
				$clinician_formative_message_after = stripslashes($row['aftermessage']);
				$clinician_formative_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'summative')
			{
				$clinician_summative_bfore = $row['beforeDays'];
				$clinician_summative_message_bfore = stripslashes($row['beforemessage']);
				$clinician_summative_after = $row['afterDays'];
				$clinician_summative_message_after = stripslashes($row['aftermessage']);
				$clinician_summative_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'equipment')
			{
				$clinician_equipment_bfore = $row['beforeDays'];
				$clinician_equipment_message_bfore = stripslashes($row['beforemessage']);
				$clinician_equipment_after = $row['afterDays'];
				$clinician_equipment_message_after = stripslashes($row['aftermessage']);
				$clinician_equipment_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'absent')
			{
				$clinician_absent_bfore = $row['beforeDays'];
				$clinician_absent_message_bfore = stripslashes($row['beforemessage']);
				$clinician_absent_after = $row['afterDays'];
				$clinician_absent_message_after = stripslashes($row['aftermessage']);
				$clinician_absent_notification = $row['isActiveNotification'];
			}
		}
		
		if($userType == 'A')
		{
			if($modul == 'rotation')
			{
				$admin_rotation_bfore = $row['beforeDays'];
			    $admin_rotation_message_bfore = stripslashes($row['beforemessage']);
				$admin_rotation_after = $row['afterDays'];
				$admin_rotation_message_after = stripslashes($row['aftermessage']);
				$admin_rotation_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'Checkoff')
			{
				$admin_Checkoff_bfore = $row['beforeDays'];
				$admin_Checkoff_message_bfore = stripslashes($row['beforemessage']);
				$admin_Checkoff_after = $row['afterDays'];
				$admin_Checkoff_message_after = stripslashes($row['aftermessage']);
				$admin_Checkoff_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'interaction')
			{
				$admin_interaction_bfore = $row['beforeDays'];
				$admin_interaction_message_bfore = stripslashes($row['beforemessage']);
				$admin_interaction_after = $row['afterDays'];
				$admin_interaction_message_after = stripslashes($row['aftermessage']);
				$admin_interaction_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'formative')
			{
				$admin_formative_bfore = $row['beforeDays'];
				$admin_formative_message_bfore = stripslashes($row['beforemessage']);
				$admin_formative_after = $row['afterDays'];
				$admin_formative_message_after = stripslashes($row['aftermessage']);
				$admin_formative_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'summative')
			{
				$admin_summative_bfore = $row['beforeDays'];
				$admin_summative_message_bfore = stripslashes($row['beforemessage']);
				$admin_summative_after = $row['afterDays'];
				$admin_summative_message_after = stripslashes($row['aftermessage']);
				$admin_summative_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'equipment')
			{
				$admin_equipment_bfore = $row['beforeDays'];
				$admin_equipment_message_bfore = stripslashes($row['beforemessage']);
				$admin_equipment_after = $row['afterDays'];
				$admin_equipment_message_after = stripslashes($row['aftermessage']);
				$admin_equipment_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'absent')
			{
				$admin_absent_bfore = $row['beforeDays'];
				$admin_absent_message_bfore = stripslashes($row['beforemessage']);
				$admin_absent_after = $row['afterDays'];
				$admin_absent_message_after = stripslashes($row['aftermessage']);
				$admin_absent_notification = $row['isActiveNotification'];
			}
			elseif($modul == 'dailyweekly')
			{
				$admin_daily_weekly_bfore = $row['beforeDays'];
				$admin_daily_weekly_message_bfore = stripslashes($row['beforemessage']);
				$admin_daily_weekly_after = $row['afterDays'];
				$admin_daily_weekly_message_after = stripslashes($row['aftermessage']);
				$admin_daily_weekly_notification = $row['isActiveNotification'];
			}
		}
		
		if($userType == 'SI')
		{
			if($modul == 'Student Immunization')
			{
				$student_immunization_bfore = $row['beforeDays'];
				$student_immunization_message_bfore = stripslashes($row['beforemessage']);
				$student_immunization_after = $row['afterDays'];
				$student_immunization_message_after = stripslashes($row['aftermessage']);
				$student_immunization_notification = $row['isActiveNotification'];
			}
		}
		
		if($userType == 'CI')
		{
			if($modul == 'Evaluator Immunization')
			{
				$clinician_immunization_bfore = $row['beforeDays'];
				$clinician_immunization_message_bfore = stripslashes($row['beforemessage']);
				$clinician_immunization_after = $row['afterDays'];
				$clinician_immunization_message_after = stripslashes($row['aftermessage']);
				$clinician_immunization_notification = $row['isActiveNotification'];
				
			}
		}
		
		
	}
	
	
	
    $page_title ="Notification Settings";
    $rankId = 0;
    $title = '';
	$sordOrder ='';
    $bedCrumTitle = 'Add';
    if(isset($_GET['id'])) //Edit Mode
	{
        $rankId = DecodeQueryData($_GET['id']);
	    $page_title ="Edit Notification";
        $bedCrumTitle = 'Edit';

        //For All Student Rank 
        $objStudentRankMaster = new clsStudentRankMaster();
		$row = $objStudentRankMaster->GetAllStudentRankMaster($schoolId,$rankId);
        unset($objStudentRankMaster);
        if($row=='')
        {
            header('location:addranking.html');
            exit;
        }

        $title  = stripslashes($row['title']);
        $sordOrder  = $row['sordOrder'];
	}
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    <?php include('includes/headercss.php');?>


</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html?id=<?php echo(EncodeQueryData($currentSchoolId)); ?>">Settings</a></li>
                    <li class="active">Notification</li>
                    <li class="active"><?php echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">
		
		 <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in row" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×
                            </button>Notification request sent successfully.
                </div>
				<?php }
				} ?>
		<form id="frmnotification" data-parsley-validate class="form-horizontal" method="POST" action="notificationsubmit.html" >

			<div class="row">
			<h4>Student</h4>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th  style="text-align: left">Module</th>                            
                            <th>Before (days)</th>
                            <th>Before Message</th>
                            <th>After (days)</th>
                            <th>After Message</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                            <tr>
                                <td>Rotation</td>
                                <td><input name="student_rotation_bfore"  id="student_rotation_bfore" type="text" value="<?php if($student_rotation_bfore != 0){echo ($student_rotation_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="student_rotation_message_bfore"  id="student_rotation_message_bfore" type="text" value="<?php echo htmlspecialchars($student_rotation_message_bfore); ?>"  data-parsley-maxlength="125" style="width: 300px;"></td>
                                <td><input name="student_rotation_after" id="student_rotation_after" type="text" value="<?php if($student_rotation_after != 0){echo ($student_rotation_after);}else{echo ('');}  ?>"  style="width: 100px;"></td>
								<td><input name="student_rotation_message_after" id="student_rotation_message_after" type="text" value="<?php echo htmlspecialchars($student_rotation_message_after); ?>"  data-parsley-maxlength="125" style="width: 300px;"></td> 
								<td class="text-center">
									<input type="checkbox" value="1" name="student_rotation_notification" id="student_rotation_notification" <?php if($student_rotation_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary">
								</td>
								
                            </tr>
							<tr>
                                <td>Checkoff</td>
                                <td><input name="student_Checkoff_bfore" id="student_Checkoff_bfore" type="text" value="<?php if($student_Checkoff_bfore != 0){echo ($student_Checkoff_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="student_Checkoff_message_bfore" id="student_Checkoff_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_Checkoff_message_bfore); ?>" style="width: 300px;"></td>
								<td><input name="student_Checkoff_after" id="student_Checkoff_after" type="text" value="<?php if($student_Checkoff_after != 0){echo ($student_Checkoff_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
                                <td><input name="student_Checkoff_message_after" id="student_Checkoff_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_Checkoff_message_after); ?>" style="width: 300px;" ></td></td>  
								<td class="text-center">
									<input type="checkbox" value="1" name="student_Checkoff_notification" id="student_Checkoff_notification" <?php if($student_Checkoff_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary">
								</td>								
                            </tr>
							<tr>
                                <td>Interaction</td>
                                <td><input name="student_interaction_bfore" id="student_interaction_bfore" type="text" value="<?php if($student_interaction_bfore != 0){echo ($student_interaction_bfore);}else{echo ('');}  ?>" style="width: 100px;"> </td>
								<td><input name="student_interaction_message_bfore" id="student_interaction_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_interaction_message_bfore); ?>" style="width: 300px;" > </td>
                                <td><input name="student_interaction_after" id="student_interaction_after" type="text" value="<?php if($student_interaction_after != 0){echo ($student_interaction_after);}else{echo ('');}  ?>" style="width: 100px;"> </td>
								<td><input name="student_interaction_message_after" id="student_interaction_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_interaction_message_after); ?>" style="width: 300px;"></td>
								<td class="text-center">
									<input type="checkbox" value="1" name="student_interaction_notification" id="student_interaction_notification" <?php if($student_interaction_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>		
                            </tr> 
							
							<tr>
                                <td>Formative</td>
                                <td><input name="student_formative_bfore" id="student_formative_bfore" type="text" value="<?php if($student_formative_bfore != 0){echo ($student_formative_bfore);}else{echo ('');}  ?>" style="width: 100px;" ></td>
								<td><input name="student_formative_message_bfore" id="student_formative_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_formative_message_bfore); ?>" style="width: 300px;"></td>
								<td><input name="student_formative_after" id="student_formative_after" type="text" value="<?php if($student_formative_after != 0){echo ($student_formative_after);}else{echo ('');}  ?>" style="width: 100px;"> </td>
                                <td><input name="student_formative_message_after" id="student_formative_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_formative_message_after); ?>" style="width: 300px;"></td> 
								<td class="text-center">
									<input type="checkbox" value="1" name="student_formative_notification" id="student_formative_notification" <?php if($student_formative_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>									
                            </tr> 
							<tr>
                                <td>Summative</td>
                                <td><input name="student_summative_bfore" id="student_summative_bfore" type="text" value="<?php if($student_summative_bfore != 0){echo ($student_summative_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="student_summative_message_bfore" id="student_summative_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_summative_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="student_summative_after" id="student_summative_after" type="text" value="<?php if($student_summative_after != 0){echo ($student_summative_after);}else{echo ('');}  ?>" style="width: 100px;" ></td>
								<td><input name="student_summative_message_after" id="student_summative_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_summative_message_after); ?>" style="width: 300px;"></td>  
								<td class="text-center">
									<input type="checkbox" value="1" name="student_summative_notification" id="student_summative_notification" <?php if($student_summative_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>		
                            </tr> 
							<tr>
                                <td>Equipment</td>
                                <td><input name="student_equipment_bfore" id="student_equipment_bfore" type="text" value="<?php if($student_equipment_bfore != 0){echo ($student_equipment_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="student_equipment_message_bfore" id="student_equipment_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_equipment_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="student_equipment_after" id="student_equipment_after" type="text" value="<?php if($student_equipment_after != 0){echo ($student_equipment_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="student_equipment_message_after" id="student_equipment_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_equipment_message_after); ?>" style="width: 300px;"></td> 
								<td class="text-center">
									<input type="checkbox" value="1" name="student_equipment_notification" id="student_equipment_notification" <?php if($student_equipment_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>		
							</tr> 
							<tr>
                                <td>Absent</td>
                                <td><input name="student_absent_bfore" id="student_absent_bfore" type="text" value="<?php if($student_absent_bfore != 0){echo ($student_absent_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="student_absent_message_bfore" id="student_absent_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_absent_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="student_absent_after" id="student_absent_after" type="text" value="<?php if($student_absent_after != 0){echo ($student_absent_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="student_absent_message_after" id="student_absent_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_absent_message_after); ?>" style="width: 300px;"></td> 
								<td class="text-center">
									<input type="checkbox" value="1" name="student_absent_notification" id="student_absent_notification" <?php if($student_absent_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>		
							</tr> 

                    </tbody>
                </table>
            </div>	
			<div class="row">
				<h4>Evaluator</h4>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th  style="text-align: left">Module</th>                            
                            <th>Before (days)</th>
                            <th>Before Message</th>
                            <th>After (days)</th>
                            <th>After Message</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                            <tr>
                                <td>Rotation</td>
                                <td><input name="clinician_rotation_bfore" id="clinician_rotation_bfore" type="text" value="<?php if($clinician_rotation_bfore != 0){echo ($clinician_rotation_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_rotation_message_bfore" id="clinician_rotation_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_rotation_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="clinician_rotation_after" id="clinician_rotation_after" type="text" value="<?php if($clinician_rotation_after != 0){echo ($clinician_rotation_after);}else{echo ('');}  ?>" style="width: 100px;"></td> 
								<td><input name="clinician_rotation_message_after" id="clinician_rotation_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_rotation_message_after); ?>" style="width: 300px;"></td>   
								<td class="text-center">
									<input type="checkbox" value="1" name="clinician_rotation_notification" id="clinician_rotation_notification" <?php if($clinician_rotation_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>									
                            </tr>
							 <tr>
                                <td>Checkoff</td>
                                <td><input name="clinician_Checkoff_bfore" id="clinician_Checkoff_bfore" type="text" value="<?php if($clinician_Checkoff_bfore != 0){echo ($clinician_Checkoff_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_Checkoff_message_bfore" id="clinician_Checkoff_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_Checkoff_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="clinician_Checkoff_after" id="clinician_Checkoff_after" type="text" value="<?php if($clinician_Checkoff_after != 0){echo ($clinician_Checkoff_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_Checkoff_message_after" id="clinician_Checkoff_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_Checkoff_message_after); ?>" style="width: 300px;"></td>
								<td class="text-center">
									<input type="checkbox" value="1" name="clinician_Checkoff_notification" id="clinician_Checkoff_notification" <?php if($clinician_Checkoff_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>								
                            </tr>
							 <tr>
                                <td>Interaction</td>
                                <td><input name="clinician_interaction_bfore" id="clinician_interaction_bfore" type="text" value="<?php if($clinician_interaction_bfore != 0){echo ($clinician_interaction_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_interaction_message_bfore" id="clinician_interaction_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_interaction_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="clinician_interaction_after" id="clinician_interaction_after" type="text" value="<?php if($clinician_interaction_after != 0){echo ($clinician_interaction_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_interaction_message_after" id="clinician_interaction_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_interaction_message_after); ?>" style="width: 300px;"></td>
								<td class="text-center">
									<input type="checkbox" value="1" name="clinician_interaction_notification" id="clinician_interaction_notification" <?php if($clinician_interaction_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>								
                            </tr>
							 <tr>
                                <td>Formative</td>
                                <td><input name="clinician_formative_bfore" id="clinician_formative_bfore" type="text" value="<?php if($clinician_formative_bfore != 0){echo ($clinician_formative_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_formative_message_bfore" id="clinician_formative_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_formative_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="clinician_formative_after" id="clinician_formative_after" type="text" value="<?php if($clinician_formative_after != 0){echo ($clinician_formative_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_formative_message_after" id="clinician_formative_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_formative_message_after); ?>" style="width: 300px;"></td> 
								<td class="text-center">
									<input type="checkbox" value="1" name="clinician_formative_notification" id="clinician_formative_notification" <?php if($clinician_formative_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>									
                            </tr>
							 <tr>
                                <td>Summative</td>
                                <td><input name="clinician_summative_bfore" id="clinician_summative_bfore" type="text" value="<?php if($clinician_summative_bfore != 0){echo ($clinician_summative_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_summative_message_bfore" id="clinician_summative_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_summative_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="clinician_summative_after" id="clinician_summative_after" type="text" value="<?php if($clinician_summative_after != 0){echo ($clinician_summative_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_summative_message_after" id="clinician_summative_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_summative_message_after); ?>" style="width: 300px;"></td>
								<td class="text-center">
									<input type="checkbox" value="1" name="clinician_summative_notification" id="clinician_summative_notification" <?php if($clinician_summative_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>									
                            </tr>
							 <tr>
                                <td>Equipment</td>
                                <td><input name="clinician_equipment_bfore" id="clinician_equipment_bfore" type="text" value="<?php if($clinician_equipment_bfore != 0){echo ($clinician_equipment_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_equipment_message_bfore" id="clinician_equipment_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_equipment_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="clinician_equipment_after" id="clinician_equipment_after" type="text" value="<?php if($clinician_equipment_after != 0){echo ($clinician_equipment_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_equipment_message_after" id="clinician_equipment_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_equipment_message_after); ?>" style="width: 300px;"></td>
								<td class="text-center">
									<input type="checkbox" value="1" name="clinician_equipment_notification" id="clinician_equipment_notification" <?php if($clinician_equipment_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>									
                            </tr>
							<tr>
                                <td>Absent</td>
                                <td><input name="clinician_absent_bfore" id="clinician_absent_bfore" type="text" value="<?php if($clinician_absent_bfore != 0){echo ($clinician_absent_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_absent_message_bfore" id="clinician_absent_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_absent_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="clinician_absent_after" id="clinician_absent_after" type="text" value="<?php if($clinician_absent_after != 0){echo ($clinician_absent_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_absent_message_after" id="clinician_absent_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($clinician_absent_message_after); ?>" style="width: 300px;"></td>
								<td class="text-center">
									<input type="checkbox" value="1" name="clinician_absent_notification" id="clinician_absent_notification" <?php if($clinician_absent_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>									
                            </tr>
                    </tbody>
                </table>
            </div>
			<div class="row">
			<h4>Admin</h4>
                  <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th  style="text-align: left">Module</th>                            
                            <th>Before (days)</th>
                            <th>Before Message</th>
                            <th>After (days)</th>
                            <th>After Message</th>
                            <th>Action</th>
                        </tr>
                    </thead>
                    <tbody>
                            <tr>
                                <td>Rotation</td>
                                <td><input name="admin_rotation_bfore" id="admin_rotation_bfore" type="text" value="<?php if($admin_rotation_bfore != 0){echo ($admin_rotation_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_rotation_message_bfore" id="admin_rotation_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($admin_rotation_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="admin_rotation_after" id="admin_rotation_after" type="text" value="<?php if($admin_rotation_after != 0){echo ($admin_rotation_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_rotation_message_after" id="admin_rotation_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($admin_rotation_message_after); ?>" style="width: 300px;"></td>  
								<td class="text-center">
									<input type="checkbox" value="1" name="admin_rotation_notification" id="admin_rotation_notification" <?php if($admin_rotation_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>
                            </tr>
							 <tr>
                                <td>Checkoff</td>
                                <td><input name="admin_Checkoff_bfore" id="admin_Checkoff_bfore" type="text" value="<?php if($admin_Checkoff_bfore != 0){echo ($admin_Checkoff_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_Checkoff_message_bfore" id="admin_Checkoff_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($admin_Checkoff_message_bfore); ?>" style="width: 300px;"> </td>
                                <td><input name="admin_Checkoff_after" id="admin_Checkoff_after" type="text" value="<?php if($admin_Checkoff_after != 0){echo ($admin_Checkoff_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_Checkoff_message_after" id="admin_Checkoff_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($admin_Checkoff_message_after); ?>" style="width: 300px;"></td>   
								<td class="text-center">
									<input type="checkbox" value="1" name="admin_Checkoff_notification" id="admin_Checkoff_notification" <?php if($admin_Checkoff_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>									
                            </tr>
							 <tr>
                                <td>Interaction</td>
                                <td><input name="admin_interaction_bfore" id="admin_interaction_bfore" type="text" value="<?php if($admin_interaction_bfore != 0){echo ($admin_interaction_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_interaction_message_bfore" id="admin_interaction_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($admin_interaction_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="admin_interaction_after" id="admin_interaction_after" type="text" value="<?php if($admin_interaction_after != 0){echo ($admin_interaction_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_interaction_message_after" id="admin_interaction_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($admin_interaction_message_after); ?>" style="width: 300px;"></td>  
								<td class="text-center">
									<input type="checkbox" value="1" name="admin_interaction_notification" id="admin_interaction_notification" <?php if($admin_interaction_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>									
                            </tr>
							 <tr>
                                <td>Formative</td>
                                <td><input name="admin_formative_bfore" id="admin_formative_bfore" type="text" value="<?php if($admin_formative_bfore != 0){echo ($admin_formative_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_formative_message_bfore" id="admin_formative_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($admin_formative_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="admin_formative_after" id="admin_formative_after" type="text" value="<?php if($admin_formative_after != 0){echo ($admin_formative_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_formative_message_after" id="admin_formative_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($admin_formative_message_after); ?>" style="width: 300px;"></td>     
								<td class="text-center">
									<input type="checkbox" value="1" name="admin_formative_notification" id="admin_formative_notification" <?php if($admin_formative_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>									
                            </tr>
							 <tr>
                                <td>Summative</td>
                                <td><input name="admin_summative_bfore" id="admin_summative_bfore" type="text" value="<?php if($admin_summative_bfore != 0){echo ($admin_summative_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_summative_message_bfore" id="admin_summative_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($admin_summative_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="admin_summative_after" id="admin_summative_after" type="text" value="<?php if($admin_summative_after != 0){echo ($admin_summative_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_summative_message_after" id="admin_summative_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($admin_summative_message_after); ?>" style="width: 300px;"></td>    
								<td class="text-center">
									<input type="checkbox" value="1" name="admin_summative_notification" id="admin_summative_notification" <?php if($admin_summative_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>								
                            </tr>
							<tr>
                                <td>Equipment</td>
                                <td><input name="admin_equipment_bfore" id="admin_equipment_bfore" type="text" value="<?php if($admin_equipment_bfore != 0){echo ($admin_equipment_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_equipment_message_bfore" id="admin_equipment_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($admin_equipment_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="admin_equipment_after" id="admin_equipment_after" type="text" value="<?php if($admin_equipment_after != 0){echo ($admin_equipment_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_equipment_message_after" id="admin_equipment_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($admin_equipment_message_after); ?>" style="width: 300px;"></td>  
								<td class="text-center">
									<input type="checkbox" value="1" name="admin_equipment_notification" id="admin_equipment_notification" <?php if($admin_equipment_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>									
                            </tr>
                            <tr>
                                <td>Student Immunization</td>
                                <td><input name="student_immunization_bfore" id="student_immunization_bfore" type="text" value="<?php if($student_immunization_bfore != 0){echo ($student_immunization_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="student_immunization_message_bfore" id="student_immunization_message_bfore" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_immunization_message_bfore); ?>" style="width: 300px;"></td>
                                <td><input name="student_immunization_after" id="student_immunization_after" type="text" value="<?php if($student_immunization_after != 0){echo ($student_immunization_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="student_immunization_message_after" id="student_immunization_message_after" type="text" data-parsley-maxlength="125" value="<?php echo htmlspecialchars($student_immunization_message_after); ?>" style="width: 300px;"></td>
								<td class="text-center">
									<input type="checkbox" value="1" name="student_immunization_notification" id="student_immunization_notification" <?php if($student_immunization_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>								
                            </tr>

                            <tr>
                                <td>Evaluator Immunization</td>
                                <td><input name="clinician_immunization_bfore" id="clinician_immunization_bfore" type="text" value="<?php if($clinician_immunization_bfore != 0){echo ($clinician_immunization_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_immunization_message_bfore" id="clinician_immunization_message_bfore" value="<?php echo htmlspecialchars($clinician_immunization_message_bfore); ?>" type="text" data-parsley-maxlength="125" style="width: 300px;"></td>
                                <td><input name="clinician_immunization_after" id="clinician_immunization_after" type="text" value="<?php if($clinician_immunization_after != 0){echo ($clinician_immunization_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="clinician_immunization_message_after" id="clinician_immunization_message_after" value="<?php echo htmlspecialchars($clinician_immunization_message_after); ?>" type="text" data-parsley-maxlength="125" style="width: 300px;"></td>  
								<td class="text-center">
									<input type="checkbox" value="1" name="clinician_immunization_notification" id="clinician_immunization_notification" <?php if($clinician_immunization_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>								
                            </tr>

							<tr>
                                <td>Absent</td>
                                <td><input name="admin_absent_bfore" id="admin_absent_bfore" type="text" value="<?php if($admin_absent_bfore != 0){echo ($admin_absent_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_absent_message_bfore" id="admin_absent_message_bfore" value="<?php echo htmlspecialchars($admin_absent_message_bfore); ?>" type="text" data-parsley-maxlength="125" style="width: 300px;"></td>
                                <td><input name="admin_absent_after" id="admin_absent_after" type="text" value="<?php if($admin_absent_after != 0){echo ($admin_absent_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_absent_message_after" id="admin_absent_message_after" value="<?php echo htmlspecialchars($admin_absent_message_after); ?>" type="text" data-parsley-maxlength="125" style="width: 300px;"></td>  
								<td class="text-center">
									<input type="checkbox" value="1" name="admin_absent_notification" id="admin_absent_notification" <?php if($admin_absent_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>								
                            </tr>

							<tr>
                                <td>Daily/Weekly</td>
                                <td><input name="admin_daily_weekly_bfore" id="admin_daily_weekly_bfore" type="text" value="<?php if($admin_daily_weekly_bfore != 0){echo ($admin_daily_weekly_bfore);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_daily_weekly_message_bfore" id="admin_daily_weekly_message_bfore" value="<?php echo htmlspecialchars($admin_daily_weekly_message_bfore); ?>" type="text" data-parsley-maxlength="125" style="width: 300px;"></td>
                                <td><input name="admin_daily_weekly_after" id="admin_daily_weekly_after" type="text" value="<?php if($admin_daily_weekly_after != 0){echo ($admin_daily_weekly_after);}else{echo ('');}  ?>" style="width: 100px;"></td>
								<td><input name="admin_daily_weekly_message_after" id="admin_daily_weekly_message_after" value="<?php echo htmlspecialchars($admin_daily_weekly_message_after); ?>" type="text" data-parsley-maxlength="125" style="width: 300px;"></td>  
								<td class="text-center">
									<input type="checkbox" value="1" name="admin_daily_weekly_notification" id="admin_daily_weekly_notification" <?php if($admin_daily_weekly_notification == 1){?> checked <?php }?>class="filled-in chk-col-primary" id="md_checkbox_22">
								</td>								
                            </tr>
                           
                    </tbody>
                </table>
            </div>	
            <div class="form-group">
						<div class="col-md-12">                       
                            <button style="margin: 0 0 0 -13px;" id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                            <!----------a type="button" href="#" class="btn btn-default">Cancel</a----------->
						</div>
            </div>		
		</form>
    </div>

    <?php include('includes/footer.php');?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript">
 
        $(window).load(function(){

             $('#frmnotification').parsley().on('field:validated', function() {
			    var ok = $('.parsley-error').length === 0;
            })
            .on('form:submit', function() {
                ShowProgressAnimation();									  
                return true;
            });
        });
    </script>
</body>

</html>