<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsJuniorMidtermPerformanceEval.php');
include('../class/clsQuestionOption.php');

$studentJrMidtermEvalId = 0;
$evaluationDate = date('Y-m-d');
// echo '<pre>';
// print_r($_POST);
// exit;
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
	$rotationId = 0;

	if (isset($_GET['studentJrMidtermEvalId'])) {
		$studentJrMidtermEvalId = $_GET['studentJrMidtermEvalId'];
		$studentJrMidtermEvalId = DecodeQueryData($studentJrMidtermEvalId);
	}
	if (isset($_GET['rotationId'])) {
		$rotationId = $_GET['rotationId'];
		$rotationId = DecodeQueryData($rotationId);
	}

	$studentJrMidtermEvalId = isset($_GET['studentJrMidtermEvalId']) ? DecodeQueryData($_GET['studentJrMidtermEvalId']) : 0;
	$status = ($studentJrMidtermEvalId > 0) ? 'updated' : 'added';

	$cboclinician  = isset($_POST['cboclinician']) ? $_POST['cboclinician'] : 0;
	$rotationId  = isset($_POST['cboTerm']) ? $_POST['cboTerm'] : 0;
	$cbohospitalsites  = isset($_POST['cboclinicianassignment']) ? $_POST['cboclinicianassignment'] : 0;
	$cboStudent  = isset($_POST['cboStudent']) ? $_POST['cboStudent'] : 0;
	$studentsignitureDate  = isset($_POST['studentsignitureDate']) ? $_POST['studentsignitureDate'] : 0;
	// $preceptorsignitureDate  = isset($_POST['preceptorSignitureDate']) ? $_POST['preceptorSignitureDate'] : 0;
	$preceptorsignature  = isset($_POST['preceptorsignature']) ? $_POST['preceptorsignature'] : '';
	$evaluationDate = GetDateStringInServerFormat($_POST['evaluationDate']);
	// $preceptorsignitureDate = GetDateStringInServerFormat($_POST['preceptorsignitureDate']);
	// $preceptorsignitureDate = str_replace('00:00:00', '12:00 PM', $preceptorsignitureDate);
	// $preceptorsignitureDate = date('Y-m-d H:i', strtotime($preceptorsignitureDate));

	$studentsignitureDate = GetDateStringInServerFormat($_POST['studentSignitureDate']);
	$studentsignitureDate = str_replace('00:00:00', '12:00 PM', $studentsignitureDate);
	$studentsignitureDate = date('Y-m-d H:i', strtotime($studentsignitureDate));
	$firstSectionAvg  = $_POST['firstSectionAvg'];
	$secondSectionAvg  = $_POST['secondSectionAvg'];
	$thirdSectionAvg  = $_POST['thirdSectionAvg'];
	$fourthSectionAvg  = $_POST['fourthSectionAvg'];
	$totalPercentage  = $_POST['totalPercentage'];
	$totalAvg  = $_POST['totalAvg'];
	$radioTerms  = $_POST['radioTerms'];
	$studentcomments  = $_POST['studentcomments'];
	$summarycomments  = $_POST['summarycomments'];

	$objJrMidtermEval = new clsJuniorMidtermPerformanceEval();
	$objJrMidtermEval->rotationId = $rotationId;
	$objJrMidtermEval->clinicianId = $cboclinician;
	$objJrMidtermEval->hospitalSiteId = $cbohospitalsites;
	$objJrMidtermEval->studentId = $cboStudent;
	$objJrMidtermEval->studentId = $cboStudent;
	$objJrMidtermEval->schoolId = $currentSchoolId;
	$objJrMidtermEval->evaluationDate = $evaluationDate;
	$objJrMidtermEval->firstSectionAvg = $firstSectionAvg;
	$objJrMidtermEval->secondSectionAvg = $secondSectionAvg;
	$objJrMidtermEval->thirdSectionAvg = $thirdSectionAvg;
	$objJrMidtermEval->fourthSectionAvg = $fourthSectionAvg;
	$objJrMidtermEval->dateOfPreceptorSignature = $preceptorsignitureDate;
	$objJrMidtermEval->preceptorsignature = $preceptorsignature;
	$objJrMidtermEval->dateOfStudentSignature = $studentsignitureDate;
	$objJrMidtermEval->totalAvg = $totalAvg;
	$objJrMidtermEval->totalPercentage = $totalPercentage;
	$objJrMidtermEval->radioTerms = $radioTerms;
	$objJrMidtermEval->studentcomments = $studentcomments;
	$objJrMidtermEval->summarycomments = $summarycomments;

	$objJrMidtermEval->createdBy = $_SESSION['loggedUserId'];;
	$retDailyEvalId = $objJrMidtermEval->SaveClinicianJrMidtermEval($studentJrMidtermEvalId);
	if ($retDailyEvalId > 0) {
		$objJrMidtermEval->DeleteStudentDailyDetails($retDailyEvalId);

		foreach ($_POST as $id => $value) {

			if (strpos($id, 'questionoptionst_') === 0) {
				//$id = explode("_", $id)[1];
				$id =	str_replace('questionoptionst_', '', $id);
				$objJrMidtermEval->studentJrMidtermEvalId = $retDailyEvalId;
				$objJrMidtermEval->studentQuestionId = $id;
				$objJrMidtermEval->studentoptionvalue = '';
				$objJrMidtermEval->studentOptionAnswerText = $value[0];
				$studentDailyDetailId = $objJrMidtermEval->SaveJrMidtermDetails($retDailyEvalId);
			}
		}


		foreach ($_POST as $id => $value) {

			if (strpos($id, 'questionoptions_') === 0) {
				//$id = explode("_", $id)[1];
				$id =	str_replace('questionoptions_', '', $id);
				$objJrMidtermEval->studentJrMidtermEvalId = $retDailyEvalId;
				$objJrMidtermEval->studentQuestionId = $id;
				$objJrMidtermEval->studentoptionvalue = $value[0];
				$objJrMidtermEval->studentOptionAnswerText = '';
				$studentDailyDetailId = $objJrMidtermEval->SaveJrMidtermDetails($retDailyEvalId);
			}
		}
	}
	unset($objJrMidtermEval);

	if ($retDailyEvalId > 0) {
		if (isset($_GET['studentJrMidtermEvalId']))
			header('location:midtermperformanceList.html?rotationId=' . EncodeQueryData($rotationId) . '&studentJrMidtermEvalId=' . EncodeQueryData($studentJrMidtermEvalId) . '&status=' . $status);
		else
			header('location:midtermperformanceList.html?rotationId=' . EncodeQueryData($rotationId) . '&status=' . $status);
		exit();
	} else {
		header('location:addMidtermperformance.html?status=error');
	}
} {
	header('location:midtermperformanceList.html');
	exit();
}
