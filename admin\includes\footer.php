
<div  id="loading-div-background">
    <div id="loading-div" class="ui-corner-all">
        <img style="height:31px;width:31px;margin:30px;" src="<?php echo ($dynamicOrgUrl); ?>/assets/images/loader.gif" alt="Loading.." /><br>PROCESSING. PLEASE WAIT...
    </div>
</div>
<div id="divBottomRightLoader" style="display:none;">Working...</div>

<script type="text/javascript" >
//-------------------------------------------------------------------------------------
//----Global variables from server-----------------------------------------------------
//-------------------------------------------------------------------------------------
 var applicationName = '<?php echo $currenschoolDisplayname; ?>';
//-------------------------------------------------------------------------------------
</script>


<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.min.js" type="text/javascript"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/bootstrap.min.js" type="text/javascript"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/common.js" type="text/javascript"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.userTimeout.js"></script>
<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/mask/jquery.maskedinput.js"></script> 

<script type="text/javascript" >

	jQuery(function($){
		$('.dateInputFormat').mask("99/99/9999");
	});

	var isSuperAdmin = '<?php echo $isCurrentSchoolSuperAdmin; ?>';
	document.getElementById("notification-icon").onclick = function () {

		if(isSuperAdmin == 1)
			location.href = "./superadminnotification.html";
		else
			location.href = "./adminnotification.html";
	};

	$(document).userTimeout({

	  // ULR to redirect to, to log user out
	  logouturl: 'logout.html',          

	  // URL Referer - false, auto or a passed URL     
	  referer: false,            

	  // Name of the passed referal in the URL
	  refererName: 'refer',        

	  // Toggle for notification of session ending
	  notify: true,                      

	  // Toggle for enabling the countdown timer
	  timer: true,             

	  // 4 hrs in Milliseconds, then notification of logout
	  session: 14400000,                   
	  //session: 120000,                   

	  // 1 Minutes in Milliseconds, then logout
	  force: 60000,       

	  // Model Dialog selector (auto, bootstrap, jqueryui)              
	  ui: 'auto',                        

	  // Shows alerts
	  debug: false,            

	  modalTitle: 'Session Timeout',     

	  // Modal Body
	  modalBody: 'You\'re being timed out due to inactivity. Please choose to stay signed in or to logoff. Otherwise, you will logged off automatically.',

	  // Modal log off button text
	  modalLogOffBtn: 'Log Off',  

	  // Modal stay logged in button text        
	  modalStayLoggedBtn: 'Stay Logged In'  
		
	});

$(window).bind("pageshow", function(event) {
    if (event.originalEvent.persisted) {
        window.location.reload(); 
    }
});
$(document).ready(function(){
    
    
   
    SetPageTitle();
	
	 $('body').click(function(e){	
			if ( e.target.id != 'notification-icon'){
				$("#notification-latest").hide();
			}
		});
	});	
	
	
	function displayNotificationCount()     {  	
		var schoolId = '<?php echo $currentSchoolId; ?>';
		
		var isSuperAdmin = '<?php echo $isCurrentSchoolSuperAdmin; ?>';
			
		if(isSuperAdmin == 1)
			ajaxUrl = "<?php echo ($dynamicOrgUrl); ?>/ajax/superadmin_noti_count.html";
		else
			ajaxUrl = "<?php echo ($dynamicOrgUrl); ?>/ajax/admin_noti_count.html";
		
		
		$.ajax({ 
				type: "POST",
				url: ajaxUrl,
				data: { schoolId:schoolId },
			
				//dataType: "json",
				success: function(totalUnreadCount)
				{
				    
					//alert(totalUnreadCount);
					if(totalUnreadCount >0)
					{
						//$('#notification-count').text(totalUnreadCount); 
						$('.notification-icon').append("<span id='notification-count' style='color:white' class='badge badge-custom'>"+totalUnreadCount+"</span>");
					}
					else
					{
						//alert('');
						//$('.notification-icon').append("<span id='notification-count' style='color:white' class='badge badge-custom'>""</span>");

					}
					
				   //$('.notification-icon').append("<span id='notification-count' style='color:white' class='badge badge-custom'>"+totalUnreadCount.notificationCount+"</span>");

				}
		});
	}    
			displayNotificationCount(); 



</script>

