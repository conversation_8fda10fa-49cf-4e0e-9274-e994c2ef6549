<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');      
    include('../class/clsUsafMasterCheckoffQuestion.php');      
    include('../setRequest.php'); 	
	
	$sectionId=0;
	$defaultTopicId=0;
	$objQuestionMaster = new clsUsafMasterCheckoffQuestion();
	$rowsQuestionData=$objQuestionMaster->GetUsafCheckoffQuestions($sectionId);
	$totalCount = 0;
	if($rowsQuestionData !='')
	{
		$totalCount = mysqli_num_rows($rowsQuestionData);
	}	
	unset($objQuestionMaster);
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Military Comps Steps</title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>
						<li><a href="settings.html">Setting	</a></li>
                        <li class="active">Military Comps Steps</li>
                    </ol>
                </div>              
            </div>
        </div>

        <div class="container">

                <div id="divTopLoading" >Loading...</div>
				
					<form name="checkoffquestion" id="checkoffquestion" data-parsley-validate method="POST" action="usafquestionsubmit.html?questionId=<?php echo $defaultTopicId;  ?>">
						<div class="row">
						<div class="col-md-10  margin_bottom_ten"></div>
						<div class="col-md-2  margin_bottom_ten">
						
								<div class="form-group">
									<!--button id="btncoarcrequest" name="btncoarcrequest" class="btn btn-success">Send CoARC Survey</button-->
								</div>
						</div>
					
					</div>	
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
							
                           <th style="text-align:center">Steps</th>                                                   
                           <th>Comps Steps Title</th>                                                   
                           <th style="text-align: center">Action</th>                                                   
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalCount > 0)
                        {
                            while($row = mysqli_fetch_array($rowsQuestionData))
                            {								
                                $defaultTopicId = ($row[0]);					
                                $schoolQuestionFullTitle = stripslashes($row['questionTitle']);
                                $CheckdQueId =($row['CheckdQueId']);
                                $QueId =($row['QueId']);
                                $sortOrder =($row['sortOrder']);
								if($CheckdQueId > 0)
								{
									$actiontype="false";
								}
								else
								{
									$actiontype="true";
								}
								
								$shortTitlelen=strlen($schoolQuestionFullTitle);
								
								if($shortTitlelen > 80)
								{
								   
								    $questionTitle=substr($schoolQuestionFullTitle,0,80);
									$questionTitle .= '...';
							      
								}else{
								    $questionTitle=$schoolQuestionFullTitle;
								}
								
                               ?>
                            <tr>
								
								<td style="text-align:center"><?php echo ($sortOrder); ?></td>
                                <td title="<?php echo ($schoolQuestionFullTitle); ?>">
                                    <?php echo($questionTitle); 				
									?>
                                </td>
								<td style="text-align: center">
								<?php if($CheckdQueId > 0)
									{ ?>
										<a href="usafmastereditquestion.html?questionId=<?php echo $defaultTopicId;  ?>">Edit </a>|
										<a id="warningAjax" class="text-muted" href="javascript:void(0);" QuestionId="<?php echo EncodeQueryData($defaultTopicId); ?>" QuestionTitle="<?php echo($questionTitle); ?>"  >Delete</a>
										
									<?php } 
									else 
									{ ?>
									<a href="usafmastereditquestion.html?questionId=<?php echo $defaultTopicId;  ?>">Edit </a>|
									<a href="javascript:void(0);" class="deleteAjaxRow" QuestionId="<?php echo EncodeQueryData($defaultTopicId); ?>" QuestionTitle="<?php echo($questionTitle); ?>">Delete</a>		
								<?php } ?>
								</td>
								
								

                            </tr>
                            <?php
							}
                        }
                    ?>
					</tbody>
                </table>
				</form>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
		
     
	
        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";        
               
			$(window).load(function(){
                $("#divTopLoading").addClass('hide');				
				  });	
                 var current_datatable = $("#datatable-responsive").DataTable({
					 "aoColumns": [{
                    "sWidth": "2%"
                },{
                    "sWidth": "50%"
				  },{
                    "sWidth": "2%",
					"bSortable": false					
                }],
                'iDisplayLength': 250
				 }); 
		
		
			
			
				$(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var QuestionId = $(this).attr('QuestionId');
                var QuestionTitle = $(this).attr('QuestionTitle');
                
                alertify.confirm('Question Name: '+QuestionTitle, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: QuestionId,
                            type: 'UsafMasterPEFQuestion'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });
			
			
			$(document).on('click', '#warningAjax', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var QuestionId = $(this).attr('QuestionId');
                var QuestionTitle = $(this).attr('QuestionTitle');
                
                alertify.confirm('Warning!', 'This question already assigned, you cant delete it!', function(){
				 }, function() {});
            });
        </script>
    </body>
    </html>