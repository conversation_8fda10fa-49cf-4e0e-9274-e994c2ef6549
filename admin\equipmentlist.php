<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsStudent.php');
include('../class/clsRotation.php');
include('../class/clsEquipment.php');
include('../class/clsLocations.php');

include('../setRequest.php');


$equipmentrotationid = 0;
$schoolId = 0;
$currentstudentId  = 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';

$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$schoolId = $currentSchoolId;
$transchooldisplayName = $currenschoolDisplayname;
if (isset($_GET['equipmentrotationid'])) //Edit Mode
{
    $equipmentrotationid = $_GET['equipmentrotationid'];
    $equipmentrotationid = DecodeQueryData($equipmentrotationid);
}
//For Student Site
if (isset($_GET['studentId'])) {
    $currentstudentId = $_GET['studentId'];
    $currentstudentId = DecodeQueryData($currentstudentId);
}



$title = "Equipment List| " . $transchooldisplayName;

//For All Equipment List 
$objEquipment = new clsEquipment();
$getEquipmentdetails = $objEquipment->GetAllEquipment($equipmentrotationid, $currentstudentId);
$totalEquipmentCount = 0;
if ($getEquipmentdetails != '') {
    $totalEquipmentCount = mysqli_num_rows($getEquipmentdetails);
}
unset($objEquipment);

//For Rotation Title
$objRotation = new clsRotation();
$RotationName = $objRotation->GetrotationDetails($equipmentrotationid, $schoolId);
$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] :'';

//For Student Full Name
$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname = $Rowstudent ? ($Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']) : '';
unset($objStudent);

$status = isset($_GET["status"]) ? $_GET["status"] : '';
$rotationStatus = checkRotationStatus($equipmentrotationid);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if (isset($_GET['studentId'])) { ?> <li><a href="clinical.html">Clinical</a></li>
                        <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>

                    <?php } else { ?>
                        <li><a href="rotations.html">Rotations</a></li>
                        <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                    <?php } ?>
                    <li class="active">Equipment List</li>

                </ol>
            </div>

            <?php if ($rotationStatus == 0) { ?>
                <div class="pull-right">
                    <?php if (isset($_GET['studentId'])) { ?>
                        <a class="btn btn-link" href="equipment.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Add</a>

                    <?php } else { ?>
                        <a class="btn btn-link" href="equipment.html?equipmentrotationid=<?php echo EncodeQueryData($equipmentrotationid); ?>">Add</a>
                    <?php } ?>
                </div>
            <?php } ?>


        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Equipment added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Equipment updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Equipment deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Rotation</th>
                    <th style="text-align: center">Evaluation Date</th>

                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalEquipmentCount > 0) {
                    while ($row = mysqli_fetch_array($getEquipmentdetails)) {

                        $rotationId = $row['rotationId'];
                        $Rotationtitle = $row['title'];
                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $studentName = $firstName . ' ' . $lastName;
                        $studentEquipmentMasterId = $row['EquipmentMasterId'];
                        $equipmentUsageDate = stripslashes($row['equipmentUsageDate']);

                        $parentRotationId = $row['parentRotationId'];
                        $rotationLocationId  = ($row['rotationLocationId']);
                        $courselocationId = $row['locationId'];

                        $locationId = 0;
                        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                            if (!$rotationLocationId)
                                $locationId = $objRotation->GetLocationByRotation($rotationId);
                            else
                                $locationId  = $rotationLocationId;
                        } else {
                            $locationId  = $courselocationId;
                        }

                        //Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);

                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];


                        $equipmentUsageDate = converFromServerTimeZone($equipmentUsageDate, $TimeZone);
                        $equipmentUsageDate = date("m/d/Y", strtotime($equipmentUsageDate));
                        $totalUserCount = 0;
                ?>
                        <tr>
                            <td><?php echo ($firstName); ?></td>
                            <td><?php echo ($lastName); ?></td>
                            <td><?php echo ($Rotationtitle); ?></td>
                            <td style="text-align: center"><?php echo ($equipmentUsageDate); ?></td>

                            <td style="text-align: center">
                            <?php
                                $rotationStatus = checkRotationStatus($rotationId);
                                if($rotationStatus) {
                                ?>
                                <a href="equipment.html?studentEquipmentMasterId=<?php echo (EncodeQueryData($studentEquipmentMasterId)); ?>
									&equipmentrotationid=<?php echo (EncodeQueryData($equipmentrotationid)); ?>&view=V">View</a> |
                                <?php } elseif (isset($_GET['studentId'])) { ?>
                                    <a href="equipment.html?studentEquipmentMasterId=<?php echo (EncodeQueryData($studentEquipmentMasterId)); ?>
									&equipmentrotationid=<?php echo (EncodeQueryData($equipmentrotationid)); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Edit</a> |
                                <?php } else { ?>
                                    <a href="equipment.html?studentEquipmentMasterId=<?php echo (EncodeQueryData($studentEquipmentMasterId)); ?>
									&equipmentrotationid=<?php echo (EncodeQueryData($equipmentrotationid)); ?>">Edit</a> |
                                <?php } ?>
                                <a href="javascript:void(0);" class="deleteAjaxRow" studentEquipmentMasterId="<?php echo EncodeQueryData($studentEquipmentMasterId); ?>" studentName="<?php echo ($studentName); ?>">Delete</a>
                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objRotation);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "ordering": true,
            "order": [
                [3, "desc"]
            ],

            "aoColumns": [{
                "sWidth": "15%"
            }, {
                "sWidth": "15%"
            }, {
                "sWidth": "20%"
            }, {
                "sWidth": "15%"
            }, {
                "sWidth": "15%",
                "sClass": "alignCenter",
                "bSortable": false
            }]
        });

        // ajax call for deleteAjaxRow

        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentEquipmentMasterId = $(this).attr('studentEquipmentMasterId');
            var title = $(this).attr('studentName');
            var UserId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1;
        

            alertify.confirm('Student Equipment: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentEquipmentMasterId,
                        userId: UserId,
                        isUser: isUser,
                        type: 'studentEquipment'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });
    </script>


</body>

</html>