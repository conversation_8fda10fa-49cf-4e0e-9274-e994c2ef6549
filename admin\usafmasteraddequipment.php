<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php'); 
    include('../class/clsusafEquiment.php');     
	include('../setRequest.php'); 

    $schoolId = 0;
    $equipmentId = 0;
    $questionType = 0;
	$equipmentTitle='';
    $schoolId= $currentSchoolId;
    $title ="Add Equipment - ".$currenschoolDisplayname;
 

    $page_title ="Add Equipment";
    $equipmentId = 0;
    $title = '';
	$sordOrder ='';
    $bedCrumTitle = 'Add';
    if(isset($_GET['equipmentId'])) //Edit Mode
	{

		$equipmentId = ($_GET['equipmentId']);
		$objusafEquipment = new clsusafEquiment();
		$singleEquipment = $objusafEquipment->GetSingleUsafEquipment($equipmentId);
		$equipmentTitle=$singleEquipment['title'];

	    $page_title ="Edit Equipment";
        $bedCrumTitle = 'Edit';        
	}
	
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
	<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">


    

</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
                    <li><a href="dashboard.html">Setting</a></li>
                    <li><a href="usafmasterviewequipment.html">Military Equipment List</a></li>                 
                    <li class="active"><?php echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

		<form id="frmaddequipment" name="frmaddequipment" data-parsley-validate class="form-horizontal" method="POST" action="usafmasteraddequipmentsubmit.html?equipmentId=<?php echo EncodeQueryData($equipmentId); ?>" >

			<div class="row">
				<div class="col-md-6">
				<div class="form-group">
					<label class="col-md-12 control-label" for="equipmentTitle">Equipment Title : </label>
					<div class="col-md-12">
						<input id="equipmentTitle"  name="equipmentTitle" class="form-control input-md required-input" value="<?php echo $equipmentTitle; ?>" required>

					</div>
				</div>
			</div>
			</div>
			
			<div class="form-group">
				<!-- <label class="col-md-2 control-label"></label> -->
				<div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
				<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
				<a type="button" href="usafmasterviewequipment.html"class="btn btn-default">Cancel</a>
			</div>	
			</div>

		</form>
    </div>

    <?php include('includes/footer.php');?>
   
	
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>	
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript">
		$(".select2_single").select2();
		$('#select2-cboQuestionType-container').addClass('required-select2');
		$(window).load(function(){
				$('#frmaddequipment').parsley().on('field:validated', function() {
				var ok = $('.parsley-error').length === 0;
			})
			.on('form:submit', function() {
				ShowProgressAnimation();									  
				return true; // Don't submit form for this demo
			});
		});

    </script>
</body>
</html>