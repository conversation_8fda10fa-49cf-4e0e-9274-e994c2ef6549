<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include('../class/clsStudent.php');
include('../class/clsLocations.php');
include('../class/clsStudentRankMaster.php');
include('../class/clsSystemUserRoleMaster.php');
include_once('../class/clsCountryStateMaster.php');


$Id = '';
$schoolId = '';
$firstName = '';
$middleName  = '';
$lastName = '';
$address1  = '';
$address2  = '';
$city  = '';
$stateId  = $CurrentSchoolStateId;
$defaultStudentImagePath = '';
$defaultImageName = '';
$zip  = '';
$email  = '';
$phone  = '';
$cellPhone  = '';
$username  = '';
$locationId = '';
$rankId = '';
$studentId  = 0;
$displayName  = '';
$dbStateId = '';
$recordIdNumber = '';
$isEmailPassword  = 1;
$schoolId = $currentSchoolId;

//Get School Details
$objSchool = new clsSchool();
$row = $objSchool->GetSchoolDetails($schoolId);
unset($objSchool);

if ($row == '') {
    header('location:addstudent.html');
    exit;
}
$address1  = stripslashes($row['address1']);
$address2  = stripslashes($row['address2']);
$dbStateId  = ($row['stateId']);
$city  = stripslashes($row['city']);
$zip  = stripslashes($row['zip']);

$title = "Add Student";
$bedCrumTitle = 'Add';
$type = isset($_GET['type']) ? DecodeQueryData($_GET['type']) : 0;
if (isset($_GET['id'])) //Edit Mode
{
    $studentId = $_GET['id'];
    $studentId = DecodeQueryData($studentId);
    $title = "Edit Student";
    $bedCrumTitle = 'Edit';
    $objStudent = new clsStudent();
    $row = $objStudent->GetStudentDetails($studentId, $schoolId);
    $firstName  = stripslashes($row['firstName']);
    unset($objStudent);

    if ($row == '') {
        header('location:student.html');
        exit;
    }

    $recordIdNumber  = $row['recordIdNumber'] ? $row['recordIdNumber'] : '';
    $firstName  = stripslashes($row['firstName']);
    $middleName  = stripslashes($row['middleName']);
    $lastName  = stripslashes($row['lastName']);
    $address1  = stripslashes($row['address1']);
    $address2  = stripslashes($row['address2']);
    $city  = stripslashes($row['city']);
    $zip  = stripslashes($row['zip']);
    $email  = ($row['email']);
    $phone  = ($row['phone']);
    $cellPhone  = ($row['cellPhone']);
    $locationId  = ($row['locationId']);
    $rankId  = ($row['rankId']);
    $username  = stripslashes($row['username']);
    $passwordHash  = stripslashes($row['passwordHash']);
    $isEmailPassword  = stripslashes($row['isEmailPassword']);
    $dbStateId  = ($row['stateId']);
    $defaultImageName = stripslashes($row['profilePic']);
}
$defaultStudentImagePath = GetStudentImagePath($studentId, $schoolId, $defaultImageName);
//CountryState
$objCountryStateMaster = new clsCountryStateMaster();
$countries = $objCountryStateMaster->GetAllCountry();
$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($dbStateId);
unset($objCountryStateMaster);
//Locations
$objLocations = new clsLocations();
$locations = $objLocations->GetAlllocation($schoolId);
unset($objLocations);
//StudentRank
$objStudentRankMaster = new clsStudentRankMaster();
$ranks = $objStudentRankMaster->GetAllStudentRankBySchool($schoolId);
unset($objStudentRankMaster);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/cropper.css">
    <style>
        input:read-only {
            background-color: transparent !important;
        }

        .formSubHeading {
            margin-left: 15px !important;
            margin-right: 15px !important;
            width: -webkit-fill-available;
        }

        .school-logo-section {
            display: flex;
            align-items: center;
        }

        .logo-image-section {
            margin-right: 20px
        }

        .img-thumbnail {
            width: 120px;
            max-width: 120px;
            border-radius: 10px;
            padding: 6px;
        }

        input[type="file"] {
            background-color: #fff !important;
        }

        @media screen and (max-width: 500px) {
            .formSubHeading {
                font-size: 16px;
                margin-bottom: 12px;
                padding-bottom: 0;
            }

            .breadcrumb-bg {
                margin-bottom: 5px;
            }

            .school-logo-section {
                align-items: start;
                flex-direction: column;
            }

            .logo-image-section {
                margin-right: 0;
                margin-bottom: 20px;
            }
        }
    </style>

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="schoolstudents.html?id=<?php echo (EncodeQueryData($schoolId)); ?>">Students</a></li>
                    <li class="active"><?php echo ($bedCrumTitle); ?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <div class="pageheading"></div>

        <form id="frmSystemStudent" data-parsley-validate class="form-horizontal" method="POST" action="addstudentsubmit.html?id=<?php echo (EncodeQueryData($studentId)); ?>&schoolId=<?php echo (EncodeQueryData($schoolId)); ?>" enctype="multipart/form-data">
            <div class="row">
                <div class="formSubHeading">Student Information</div>

                <div class="col-md-6">

                    <!-- Text input-->
                    <input type="hidden" name="hiddenType" id="hiddenType" value="<?php echo $type ?>">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="recordIdNumber">Student Id</label>
                        <div class="col-md-12">
                            <input id="recordIdNumber" name="recordIdNumber" value="<?php echo ($recordIdNumber); ?>" type="text" placeholder="" class="form-control input-md validateOnlynumbers" maxlength="18" onchange="validateData('recordId');">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">


                <div class="col-md-6">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtFirstName">First Name</label>
                        <div class="col-md-12">
                            <input id="txtFirstName" name="txtFirstName" value="<?php echo ($firstName); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">
                        </div>
                    </div>
                </div>
                <div class="col-md-6">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtMiddleName">Middle Name</label>
                        <div class="col-md-12">
                            <input id="txtMiddleName" name="txtMiddleName" value="<?php echo ($middleName); ?>" type="text" placeholder="" class="form-control input-md ">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">

                    <!-- Select Basic -->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtLastName">Last Name</label>
                        <div class="col-md-12">
                            <input id="txtLastName" name="txtLastName" value="<?php echo ($lastName); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">
                        </div>
                    </div>
                </div>
                <div class="col-md-6">



                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtEmail">Email</label>
                        <div class="col-md-12">
                            <input id="txtEmail" name="txtEmail" value="<?php echo ($email); ?>" required type="email" placeholder="" class="form-control input-md required-input" onblur="updateUsername(this.value)">

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtPhone">Preferred Phone</label>
                        <div class="col-md-12">
                            <input id="txtPhone" maxlength="12" data-inputmask-alias="************" name="txtPhone" maxlength="12" type="text" placeholder="" value="<?php echo ($phone); ?>" required class="form-control input-md required-input" onchange="validateData('mobileNo');">
                            <!-- <input class="form-control  input-md " data-inputmask-alias="************" name="txtworkPhone" id="txtworkPhone" value=""  > -->

                        </div>
                    </div>
                </div>
                <div class="col-md-6">


                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtcellPhone">Alternate Phone</label>
                        <div class="col-md-12">
                            <input id="txtcellPhone" data-inputmask-alias="************" maxlength="12" name="txtcellPhone" type="text" placeholder="" value="<?php echo ($cellPhone); ?>" class="form-control input-md">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="margin-top-5" style="display: flex; align-items: start;margin-top: 5px;margin-bottom: 10px;">
                        <input type="checkbox" checked id="chkSmsOptIn" name="chkSmsOptIn" value="1" required>
                         <label for="chkSmsOptIn" style="margin-left: 7px; margin-bottom: 0;">By checking this box, I acknowledge I am opting in to receiving SMS messages on behalf of my Clinical Trac™ clinical management tool. I agree to the <a href="https://clinicaltrac.com/privacy.html" target="_blank"> Privacy Policy </a> and <a href="https://clinicaltrac.com/termsandconditions.html" target="_blank"> Terms and Conditions. </a></label>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="formSubHeading">Address Information</div>
                <div class="col-md-6">
                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtAddress1">Address 1</label>
                        <div class="col-md-12">
                            <input id="txtAddress1" name="txtAddress1" type="text" value="<?php echo ($address1); ?>" class="form-control input-md required-input" required>

                        </div>
                    </div>
                </div>
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtAddress2">Address 2</label>
                        <div class="col-md-12">
                            <input id="txtAddress2" name="txtAddress2" type="text" placeholder="" value="<?php echo ($address2); ?>" class="form-control input-md">

                        </div>
                    </div>
                </div>

                <div class="col-md-6">


                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cboCountry">Country</label>
                        <div class="col-md-12 flex-col-reverse">
                            <select id="cboCountry" name="cboCountry" class="form-control step1 input-md  select2_single" required>
                                <option value="" selected>Select</option>
                                <?php
                                if ($countries != "") {
                                    while ($row = mysqli_fetch_assoc($countries)) {
                                        $location_id  = $row['location_id'];
                                        $name  = stripslashes($row['name']);

                                ?>
                                        <option value="<?php echo ($location_id); ?>" <?php if ($dbCountryId == $location_id) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <!-- Select Basic -->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtCity">City</label>
                        <div class="col-md-12">
                            <input id="txtCity" name="txtCity" type="text" required value="<?php echo ($city); ?>" class="form-control input-md required-input">
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <!-- Select Basic -->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cboState">State</label>
                        <div class="col-md-12 flex-col-reverse">
                            <select id="cboState" name="cboState" class="form-control step2 input-md select2_single" required>
                                <option value="" selected>Select</option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">

                    <!-- Text input-->
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtZipCode">Zip Code</label>
                        <div class="col-md-12">
                            <input id="txtZipCode" name="txtZipCode" type="text" placeholder="" value="<?php echo ($zip); ?>" class="form-control input-md">
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="formSubHeading">Access Information</div>
                <div class="col-md-6">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtUsername">Username</label>
                        <div class="col-md-12">
                            <input id="txtUsername" name="txtUsername" value="<?php echo ($username); ?>" required type="text" placeholder="" class="form-control input-md required-input" readonly>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <?php if (!isset($_GET['id'])) { ?>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="txtpassword">Password</label>
                            <div class="col-md-12">
                                <input id="txtpassword" name="txtpassword" value="" required type="password" placeholder="" class="form-control input-md required-input ">
                            </div>
                        </div>
                    <?php } ?>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="chkemailtopassword">Email Password</label>
                        <div class="col-md-12">
                            <input id="chkemailtopassword" name="chkemailtopassword" value="1" type="checkbox" class="input-md" <?php if ($isEmailPassword) echo 'checked'; ?> onchange="updateEmailToPassword(this)"> E-mail password to student.
                        </div>
                    </div>
                    <?php if (isset($_GET['id'])) { ?>
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="changepassword"></label>
                            <div class="col-md-12">
                                <a class="addCommentpopup " title="Change Password" href="rechangestudentpassword.html?id=<?php echo EncodeQueryData($studentId);  ?>">Change Password</a>
                            </div>
                        </div>
                    <?php } ?>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cbolocation">Location</label>
                        <div class="col-md-12">
                            <select id="cbolocation" name="cbolocation" class="form-control input-md required-input select2_single" required>
                                <!--option value="" selected>Select</option-->
                                <?php
                                if ($locations != "") {
                                    while ($row = mysqli_fetch_assoc($locations)) {
                                        $sellocationId  = $row['locationId'];
                                        $name  = stripslashes($row['title']);

                                ?>
                                        <option value="<?php echo ($sellocationId); ?>" <?php if ($locationId == $sellocationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cborank">Rank</label>
                        <div class="col-md-12">
                            <select id="cborank" name="cborank" class="form-control input-md required-input select2_single" required>
                                <!--option value="" selected>Select</option-->
                                <?php
                                if ($ranks != "") {
                                    while ($row = mysqli_fetch_assoc($ranks)) {
                                        $selrankId  = $row['rankId'];
                                        $name  = stripslashes($row['title']);

                                ?>
                                        <option value="<?php echo ($selrankId); ?>" <?php if ($rankId == $selrankId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="fileLogo">Student Photo</label>
                        <div class="col-md-12 school-logo-section">
                            <!-- Hidden field to store cropped image data -->
                            <input type="hidden" id="fileLogo" name="fileLogo" value="">
                            <input type="hidden" id="hasCroppedImage" name="hasCroppedImage" value="0">

                            <section class="upload-section" id="uploadSection">
                                <div class="upload-area" id="uploadArea">
                                    <input style="visibility: hidden;" type="file" id="fileInput" accept="image/*" hidden>
                                    <div class="upload-content">
                                        <?php if ($defaultStudentImagePath != '') { ?>
                                            <div style="display: flex; flex-direction: column; align-items: center; gap: 10px;">
                                                <img src="<?php echo ($defaultStudentImagePath . "?randId=" . $randormRefreshId) ?>"
                                                    style="max-width: 150px; max-height: 150px; border-radius: 8px; object-fit: contain; box-shadow: 0 4px 8px rgba(0,0,0,0.1);"
                                                    alt="Student Photo Preview">
                                                <div style="text-align: center;">
                                                    <p style="color: green; font-weight: bold; margin: 5px 0; font-size: 14px;">✓ Current student photo</p>
                                                    <span class="file-types" style="color: #666; font-size: 12px;">Click to select a different image if needed</span>
                                                </div>
                                            </div>
                                        <?php } else { ?>
                                            <div style="display: flex; flex-direction: column; align-items: center; gap: 10px;">
                                                <svg class="upload-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                                                </svg>
                                                <p>Click to browse or drag and drop an image</p>
                                                <span class="file-types">Supports: JPG, PNG, GIF, WebP</span>
                                            </div>
                                        <?php } ?>
                                    </div>
                                </div>
                            </section>
                        </div>
                    </div>
                </div>
            </div>

            <section class="cropper-section" id="cropperSection" style="display: none;">
                <div class="controls">
                    <div class="control-group">
                        <div class="mode-buttons">
                            <button type="button" class="mode-btn active" data-mode="rectangle">Rectangle</button>
                            <button type="button" class="mode-btn" data-mode="square">Square</button>
                        </div>
                    </div>
                    <div class="control-group">
                        <button type="button" class="action-btn" id="resetBtn">Reset</button>
                        <button type="button" class="action-btn" id="backBtn">Back</button>
                        <button type="button" class="action-btn primary" id="cropBtn" style="display: none;">Crop & Save</button>
                    </div>
                    <div class="control-group">
                        <label>Zoom:</label>
                        <button type="button" class="zoom-btn" id="zoomOutBtn">-</button>
                        <input type="range" id="zoomSlider" min="20" max="300" value="100" class="zoom-slider">
                        <button type="button" class="zoom-btn" id="zoomInBtn">+</button>
                        <span id="zoomValue">100%</span>
                    </div>
                    <div class="control-group">
                        <label>
                            <input type="checkbox" id="removeBackgroundCheckbox"> Remove Background
                        </label>
                    </div>
                </div>
                <div style="display: flex; gap: 20px; align-items: flex-start;">
                    <div class="image-container" id="imageContainer">
                        <img id="previewImage" src="" alt="Preview">
                        <div class="crop-overlay" id="cropOverlay">
                            <div class="crop-selection" id="cropSelection"></div>
                        </div>
                    </div>

                    <div class="preview-section">
                        <h4>Cropped Preview</h4>
                        <canvas id="previewCanvas"></canvas>
                    </div>
                </div>
            </section>

            <div class="row">
                <div class="form-group m-0 mt-10">
                    <!-- <label class="col-md-2 control-label"></label> -->
                    <div class="col-md-12" style="display: flex; justify-content: center; margin: 0 0 20px 0;gap: 15px;">
                        <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                        <?php if ($type) { ?>
                            <a type="button" href="studentIdsList.html" class="btn btn-default">Cancel</a>
                        <?php } else { ?>
                            <a type="button" href="schoolstudents.html" class="btn btn-default">Cancel</a>
                        <?php } ?>

                    </div>
                </div>
        </form>
    </div>

    <?php include('includes/footer.php'); ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/jquery.inputmask.bundle.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/inputmask/inputmask.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/cropper.js"></script>


    <script type="text/javascript">
        // Initialize the image cropper when the page loads
        $(document).ready(function() {
            new ImageCropper();

            // Check if default student image exists and display it
            const defaultImagePath = '<?php echo $defaultStudentImagePath; ?>';
            if (defaultImagePath && defaultImagePath !== '') {
                const $uploadArea = $('#uploadArea');

                // Add cropped state class to indicate we have an image
                $uploadArea.addClass('cropped-state');
            }
        });

        $('#inputId').attr('readonly', true);

        $('.addCommentpopup').magnificPopup({
            'type': 'ajax'
        });
        <?php if ($studentId == 0) { ?>
            //  $('#txtFirstName').blur(function(){
            // 	 var autousername = $('#txtFirstName').val()+"<?php echo ($schoolId); ?>";


            // 	 $.ajax({
            // 			type: "POST",			
            // 			url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_check_duplicate_username.html",
            // 			data: {userId:'<?php echo (EncodeQueryData($studentId)); ?>',userName:autousername,type:'student',schoolId:'<?php echo (EncodeQueryData($currentSchoolId)); ?>'},
            // 			success:function(responseData)
            // 			{
            // 				if(responseData==1)
            // 				{
            // 					autousername = $('#txtFirstName').val()+"_"+$('#txtLastName').val()+"_"+"<?php echo ($randormRefreshId); ?>";
            // 					$("#txtUsername").val(autousername);
            // 				}
            // 				else{
            // 					$("#txtUsername").val(autousername);
            // 				}
            // 			}
            // 	}); 


            //  });
        <?php } ?>


        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {

            $(".select2_single").select2();
            $('#select2-cboCountry-container').addClass('required-select2');
            $('#select2-cboState-container').addClass('required-select2');
            $('#select2-cbolocation-container').addClass('required-select2');
            $('#select2-cborank-container').addClass('required-select2');

            // $('#frmSystemStudent').parsley().on('field:validated', function() {
            //         var ok = $('.parsley-error').length === 0;
            //     })
            //     .on('form:submit', function() {
            //         var isrecordId = validateData('recordId');
            //         var ismobileNo = validateData('mobileNo');
            //         console.log('isrecordId' + isrecordId);
            //         console.log('ismobileNo' + ismobileNo);
            //         // ShowProgressAnimation();									  
            //         return false; // Don't submit form for this demo
            //     });

            // $('#frmSystemStudent').parsley().on('field:validated', function() {
            //     var ok = $('.parsley-error').length === 0;
            // }).on('form:submit', function(event) {
            //     event.preventDefault(); // Prevent default form submission

            //     $('#frmSystemStudent').parsley().whenValidate().done(function() {
            //         var isRecordIdValidated = validateData('recordId');
            //         var isMobileNoValidated = validateData('mobileNo');

            //         Promise.all([isRecordIdValidated, isMobileNoValidated]).then(function(values) {
            //             var isRecordIdValid = values[0];
            //             var isMobileNoValid = values[1];

            //             if (isRecordIdValid && isMobileNoValid) {
            //                 // Both validations passed, submit the form
            //                 $('#frmSystemStudent')[0].submit();
            //             } else {
            //                 // At least one validation failed
            //                 console.log('Validation failed. Please check the form inputs.');
            //             }
            //         }).catch(function(error) {
            //             console.log('Validation error:', error);
            //         });
            //     });
            // });

            $('#frmSystemStudent').parsley().on('field:validated', function() {
                var ok = $('.parsley-error').length === 0;
            }).on('form:submit', function(event) {
                var isRecordIdValidated = validateData('recordId');
                var isMobileNoValidated = validateData('mobileNo');

                Promise.all([isRecordIdValidated, isMobileNoValidated]).then(function(values) {
                    var isRecordIdValid = values[0];
                    var isMobileNoValid = values[1];

                    if (isRecordIdValid && isMobileNoValid) {
                        // Both validations passed, submit the form
                        $('#frmSystemStudent')[0].submit();
                    } else {
                        // At least one validation failed
                        console.log('Validation failed. Please check the form inputs.');
                        return false; // Prevent form submission
                    }
                }).catch(function(error) {
                    console.log('Validation error:', error);
                });

                return false; // Prevent form submission
            });


            // $('#frmSystemStudent').parsley().on('field:validated', function() {
            //     var ok = $('.parsley-error').length === 0;
            // }).on('form:submit', function(event) {

            //     var isRecordIdValidated = validateData('recordId');
            //     var isMobileNoValidated = validateData('mobileNo');

            //     Promise.all([isRecordIdValidated, isMobileNoValidated]).then(function(values) {
            //     var isRecordIdValid = values[0];
            //     var isMobileNoValid = values[1];

            //     // Check the Promise result values
            //     console.log('isRecordIdValid:', isRecordIdValid);
            //     console.log('isMobileNoValid:', isMobileNoValid);

            //     if (isRecordIdValid && isMobileNoValid) {
            //         // Both validations passed, submit the form
            //         $('#frmSystemStudent')[0].submit();
            //     } else {
            //         // At least one validation failed
            //         console.log('Validation failed. Please check the form inputs.');
            //         return false;
            //     }
            //     }).catch(function(error) {
            //     console.log('Validation error:', error);
            //     });


            //     // return false;
            // });


            <?php
            if ($dbCountryId == 0) {
            ?>
                $('#cboCountry').val('224').trigger('change');
            <?php
            }
            ?>

            $('#frmSystemStudent').cascadingDropdown({
                selectBoxes: [{
                        selector: '.step1',
                        selected: '<?php echo ($dbCountryId); ?>'
                    },
                    {
                        selector: '.step2',
                        selected: '<?php echo ($dbStateId); ?>',
                        requires: ['.step1'],
                        requireAll: true,
                        source: function(request, response) {

                            $.getJSON('<?php echo ($dynamicOrgUrl); ?>/ajax/getStates.html', request, function(data) {
                                response($.map(data, function(item, index) {
                                    return {
                                        label: item['StateName'],
                                        value: item['StateId']
                                    };
                                }));
                            });
                        }
                    }
                ]

            });


            $("#txtUsername").change(function() {
                var currentUsername = $(this).val();
                $.ajax({
                    type: "POST",

                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_check_duplicate_username.html",
                    data: {
                        userId: '<?php echo (EncodeQueryData($studentId)); ?>',
                        userName: currentUsername,
                        type: 'student',
                        schoolId: '<?php echo (EncodeQueryData($currentSchoolId)); ?>'
                    },
                    success: function(responseData) {
                        if (responseData == 1) {
                            alertify.error("Username available.");
                            $("#txtUsername").val('').focus();
                        }
                    }
                });

            });

            $('.image-preview').magnificPopup({
                type: 'image'
            });


        });

        // Onchange of email get username	  
        function updateUsername(val) {
            document.getElementById('txtUsername').value = val;
        }

        function updateEmailToPassword(checkbox) {
            var studentId = '<?php echo ($studentId); ?>'
            var isEmailPassword = (checkbox.checked) ? 1 : 0;
            if (studentId > 0) {
                $.ajax({
                    type: "POST",

                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_updateEmailToPassword.html",
                    data: {
                        userId: '<?php echo (EncodeQueryData($studentId)); ?>',
                        isEmailPassword: isEmailPassword
                    },
                    success: function(responseData) {
                        // if(responseData==1)
                        // {
                        //     alertify.success("Updated");
                        // }
                    }
                });
            }

        }

        // function validateData(type) {
        //     if (type == 'recordId')
        //         var value = $('#recordIdNumber').val();
        //     else
        //         var value = $('#txtPhone').val();
        //     var schoolId = '<?php echo $schoolId; ?>';
        //     $.ajax({
        //         url: '<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_validate_student.html',
        //         type: 'POST',
        //         data: {
        //             value: value,
        //             type: type,
        //             schoolId: schoolId

        //         },
        //         dataType: 'json',
        //         success: function(response) {
        //             console.log(response);
        //             var status = response['status'];
        //             var userId = response['studentId'];

        //             if (status == 1) {
        //                 if (type == 'recordId') {

        //                     var responseStatus = (function() {
        //                         alertify.error("Record Id is already exist");
        //                         return status;
        //                     })();
        //                     console.log('responseStatus'+responseStatus);
        //                     // var responseStatus =
        //                     //     alertify.error("Record Id is already exist");
        //                     // // $('#recordIdNumber').val('');
        //                     return responseStatus;
        //                 } else {
        //                     var responseStatus = (function() {
        //                         alertify.error("Phone number is already exist");

        //                         return status;
        //                     })();
        //                     // alertify.error("Phone number is already exist");
        //                     // // $('#txtPhone').val('');
        //                     console.log('responseStatus'+responseStatus);
        //                     return responseStatus;
        //                 }
        //             }
        //             // return status;
        //         },
        //         error: function(xhr) {
        //             // Handle error
        //             console.log(xhr.responseText);
        //             reject();
        //         }
        //     });

        // }

        // function validateData(type) {
        //     return new Promise(function(resolve, reject) {
        //         if (type == 'recordId')
        //             var value = $('#recordIdNumber').val();
        //         else
        //             var value = $('#txtPhone').val();
        //         var schoolId = '<?php echo $schoolId; ?>';
        //         $.ajax({
        //             url: '<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_validate_student.html',
        //             type: 'POST',
        //             data: {
        //                 value: value,
        //                 type: type,
        //                 schoolId: schoolId
        //             },
        //             dataType: 'json',
        //             success: function(response) {
        //                 console.log(response);
        //                 var status = response['status'];
        //                 var userId = response['studentId'];

        //                 if (status == 1) {
        //                     if (type == 'recordId') {
        //                         alertify.error("Record Id is already exist");
        //                         resolve(false);
        //                     } else {
        //                         alertify.error("Phone number is already exist");
        //                         resolve(false);
        //                     }
        //                 } else {
        //                     resolve(true);
        //                 }
        //             },
        //             error: function(xhr) {
        //                 // Handle error
        //                 console.log(xhr.responseText);
        //                 reject();
        //             }
        //         });
        //     });
        // }

        // $('#frmSystemStudent').parsley().on('field:validated', function() {
        //     var ok = $('.parsley-error').length === 0;
        // }).on('form:submit', function(event) {
        //     event.preventDefault(); // Prevent default form submission

        //     var isRecordIdValidated = validateData('recordId');
        //     var isMobileNoValidated = validateData('mobileNo');
        //     console.log('isRecordIdValidated'+isRecordIdValidated);
        //     console.log('isMobileNoValidated'+isMobileNoValidated);
        //     alert('isRecordIdValidated'+isRecordIdValidated);
        //     return false;
        //     Promise.all([isRecordIdValidated, isMobileNoValidated]).then(function(values) {
        //         var isRecordIdValid = values[0];
        //         var isMobileNoValid = values[1];

        //         if (isRecordIdValid && isMobileNoValid) {
        //             // Both validations passed, submit the form
        //             // $('#frmSystemStudent')[0].submit();
        //             return false;
        //         } else {
        //             // At least one validation failed
        //             console.log('Validation failed. Please check the form inputs.');
        //         }
        //     }).catch(function(error) {
        //         console.log('Validation error:', error);
        //     });
        // });

        function validateData(type) {
            return new Promise(function(resolve, reject) {
                if (type == 'recordId')
                    var value = $('#recordIdNumber').val();
                else
                    var value = $('#txtPhone').val();
                var schoolId = '<?php echo $schoolId; ?>';
                var studentId = '<?php echo ($studentId); ?>';
                $.ajax({
                    url: '<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_validate_student.html',
                    type: 'POST',
                    data: {
                        value: value,
                        type: type,
                        schoolId: schoolId,
                        studentId: studentId
                    },
                    dataType: 'json',
                    success: function(response) {
                        // console.log(response);
                        var status = response['status'];
                        var userId = response['studentId'];

                        if (status == 1 && studentId != userId) {
                            if (type == 'recordId') {
                                alertify.error("Student Id is already exist");
                                if (studentId == 0)
                                    $('#recordIdNumber').val('');

                                resolve(false);
                            } else {
                                alertify.error("Phone number is already exist");
                                if (studentId == 0)
                                    $('#txtPhone').val('');
                                resolve(false);
                            }
                        } else {
                            resolve(true);
                        }
                    },
                    error: function(xhr) {
                        // Handle error
                        console.log(xhr.responseText);
                        reject(xhr.responseText);
                    }
                });
            });
        }
    </script>
</body>

</html>