<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
	include('../includes/commonfun.php');
    include('../class/clsDB.php');
    include('../class/clsSchool.php');	
    include('../class/clsClinician.php');
    include('../class/clsClinicianHospitalSite.php');
    include('../setRequest.php');			
	include('../class/clsClinicianRoleMaster.php');
	
    $objClinician = new clsClinician();
    $totalClinicians = 0;
    $loggedUserId = $_SESSION['loggedUserId'];
// 	$rowsSchoolClinician = $objClinician->GetAllSchoolClinicians($currentSchoolId);
// 	if($rowsSchoolClinician !='')
// 	{
// 		$totalClinicians = mysqli_num_rows($rowsSchoolClinician);
// 	}
/* 10052021 */
if (isset($_GET['view'])) {
    $rowsSchoolClinician = $objClinician->GetAllSchoolClinicians($currentSchoolId);
    if ($rowsSchoolClinician != '') {
        $totalClinicians = mysqli_num_rows($rowsSchoolClinician);
    }
} else {
    $rowsSchoolClinician = $objClinician->GetAllSchoolCliniciansActive($currentSchoolId);
    if ($rowsSchoolClinician != '') {
        $totalClinicians = mysqli_num_rows($rowsSchoolClinician);
    }
}
/* 10052021 */
    $loggedUserRoleType='';
    $loggedUserRoleType = $_SESSION["loggedUserRoleType"];
	
?>

    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Evaluators </title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>

        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">


        <style>
             @media screen and (max-width: 767px) {
                .col-sm-12{
                    overflow-x: auto;
                }
}
        </style>
    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li class="active">Evaluators</li>
                    </ol>
                </div>
                <div class="pull-right">
					<a class="btn btn-link" href="clinicianattendancelist.html">Evaluator Attendance </a>|  
                    <a class="btn btn-link" href="addclinician.html">Add</a>
                </div>
            </div>
        </div>

        <div class="container">

            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="Added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Evaluator added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Evaluator updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="StatusUpdated")
					{
				?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Evaluator status updated successfully.
                    </div>
                <?php
					}
					 else if($_GET["status"] =="blockStatusUpdated")
					{
				?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Evaluator lock status updated successfully.
                    </div>
                <?php
					}
                    else if($_GET["status"] =="Error")
					{
						?>
                    <div class="alert alert-danger alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Error occurred.
                    </div>
                <?php 
					}
				}
              ?>

                <div id="divTopLoading" >Loading...</div>
                <!-- 10052021 -->
        <div class="row">
            <div class="col-md-12 " style="overflow: auto;">
                <a href="schoolclinicians.html?view=all" class="btn btn-success pull-right">Display All</a>
                <br>
                <br>
                <br>
            </div>
        </div>
        <!-- 10052021 -->
         
                <table id="datatable-responsive"  class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>First Name </th>
                            <th>Last Name </th>
							<th>Contact Info</th>							
							<th>Hospital Sites</th> 
							<th>Login Info</th>
                            <th>Location</th>                         
                            <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody  >
                        <?php
                        if($totalClinicians>0)
                        {
							$objClinicianHospitalSite = new clsClinicianHospitalSite();		
                            while($row = mysqli_fetch_array($rowsSchoolClinician))
                            {

                                $clinicianId = $row['clinicianId'];					
                                $firstName = stripslashes($row['firstName']);
                                $lastName = stripslashes($row['lastName']);
                                $fullName = $firstName.' '.$lastName;
                                $email = stripslashes($row['email']);
                                $phone = stripslashes($row['phone']);
                                $cellPhone = stripslashes($row['cellPhone']);
								//$cellPhone =$cellPhone ? $cellPhone : '-';
                                $userName = stripslashes($row['username']);
								$location = stripslashes($row['location']);
                                $role = stripslashes($row['role']);	
                                //For Hospital Site							
								$clinicianHospitalsites = $objClinicianHospitalSite->GetClinicianHopitalSites($clinicianId);
								$arrClinicianHospitalSiteNames = GetSingleFieldArrayFromResultSet($clinicianHospitalsites, 'title');
                                $arrClinicianHospitalSiteNames = (implode(', ', $arrClinicianHospitalSiteNames));

                                //For Check rotation assign to clinician
                                $totalrotationAssign=0;
                                $rotationAssign=$objClinician->GetRotationAssignedtoclinician($clinicianId);
                                $totalrotationAssign=mysqli_num_rows($rotationAssign);
                                

                                $IsPublished= $row['isActive'];
                                $isBlocked= $row['isBlocked'];
                                $displayStatus ="";
                                $updateStatus = "0";
								$displayBlockStatus ="";
                                $updateBlockStatus = "0";
                                $buttoncss = "btn-primary";

                                $shortTitlelen=strlen($arrClinicianHospitalSiteNames);

                                if($shortTitlelen > 40)
                                {
                                    
                                    $ClinicianHospitalSiteNames=substr($arrClinicianHospitalSiteNames,0,40);
                                    $ClinicianHospitalSiteNames .= '...';
                                    
                                }else{
                                    $ClinicianHospitalSiteNames=$arrClinicianHospitalSiteNames;
                                }

                                if($IsPublished =="1")
                                {
                                    $displayStatus="Active";
                                    $updateStatus = "0";
                                    $buttoncss = "text-primary";
									$isShowemail = 1;
                                }   
                                else
                                {
                                    $displayStatus="Inactive";
                                    $updateStatus = "1";
                                    $buttoncss = "text-warning";
									$isShowemail = 0;
                                }
								
								 if($isBlocked =="0")
									{
										$displayBlockStatus="Unlocked";
										$updateBlockStatus = "1";
										$buttoncss = "text-primary";
									  
										
									}
									else
									{
										$displayBlockStatus="Locked";
										$updateBlockStatus = "0";
										$buttoncss = "text-warning";										
									}
								
                                ?>
                            <tr>
                                <td>
                                    <?php echo($firstName); ?>
                                </td>
								<td>
                                    <?php echo($lastName); ?>
                                </td>
                                <td>
                                    Email: <a href="mailto:<?php echo($email); ?>">
                                        <?php echo($email); ?>
                                    </a><br>
									Mobile No: <a href="tel:<?php echo($phone); ?>">
                                        <?php echo($phone); ?>
                                    </a>
									<?php if($cellPhone) { ?>
                                    <br>Alternate No: <?php echo($cellPhone); } ?>
                                    
                                </td>

								<td title="<?php echo ($arrClinicianHospitalSiteNames); ?>" ><?php echo $ClinicianHospitalSiteNames; ?><br>
									Role: <?php echo($role); ?>
                                </td>
								<td>
									Username: <?php echo($userName);?>
                                    <br>       
                                    Status: <?php echo($displayBlockStatus);?>
								</td>
                                <td><?php echo($location); ?></td>	
                                                     
                                    <td style="text-align: center">
                                    <a href="clinicianCertificationLog.html?clinicianId=<?php echo EncodeQueryData($clinicianId);?>">Certification Log</a>|
                                    <a href="singleclinicianimmunization.html?clinicianId=<?php echo EncodeQueryData($clinicianId); ?>" class="Immunization" >Immunization</a>|
                                    <a href="clinicianDocuments.html?clinicianId=<?php echo EncodeQueryData($clinicianId); ?>&Type=C">Documents</a>|
                                    <a class="<?php echo($buttoncss); ?>" href="schoolclinicianstransubmit.html?id=<?php echo($clinicianId); ?>&newStatus=<?php echo($updateStatus); ?>&userId=<?php echo $_SESSION['loggedUserId']; ?>&isUser=<?php echo "1"; ?>&type=status">
                                        <?php echo($displayStatus); ?>
                                    </a> | 
									 <a class="<?php echo($buttoncss); ?>" href="schoolclinicianstransubmit.html?id=<?php echo($clinicianId); ?>&newblockStatus=<?php echo($updateBlockStatus); ?>&userId=<?php echo $_SESSION['loggedUserId']; ?>&isUser=<?php echo "1"; ?>&type=block">
                                        <?php echo($displayBlockStatus); ?>
                                    </a>
									<?php if($isShowemail==1){?>
                                    | 
                                    <a href="javascript:void(0);" clinicianId="<?php echo($clinicianId); ?>" currentSchoolId="<?php echo($currentSchoolId); ?>" class="loginEmailAjaxRow" ClinicianFullName="<?php echo($fullName); ?>" >Send email</a>
                                    <?php } ?>
                                    <?php if($loggedUserRoleType !='H') {  ?>
								   | <a href="javascript:void(0)" class="loginAsSchoolUser" clinicianId="<?php echo EncodeQueryData($clinicianId); ?>"   ClinicianFullName="<?php echo($fullName); ?>" schoolId="<?php echo($currentSchoolId); ?>" >Login As Evaluator</a>
                                    <?php } ?>
                                    | <a href="addclinician.html?id=<?php echo(EncodeQueryData($clinicianId)); ?>">Edit</a>

                                    <?php if($loggedUserRoleType !='H') {  ?>

                                    <?php if($totalrotationAssign > 0 AND $displayStatus == "Active") { ?>   
                                    | <a onclick="javascript:ShowCountMessage();" href="javascript:void(0);" >Delete</a>
                                    <?php } else { ?>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow"
                                    clinicianId="<?php echo EncodeQueryData($clinicianId); ?>" ClinicianFullName="<?php echo($fullName); ?>">Delete</a>
                                    <?php } ?>
                                    <?php } ?>   
                                </td>
                            </tr>
                            <?php


                            }
							unset($objClinicianHospitalSite);
                        }
                        unset($objClinician);
                    ?>



                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            });


            var current_datatable = $("#datatable-responsive").DataTable({
                "aoColumns": [{
                    "sWidth": "10%"
                }, {
                    "sWidth": "10%"
                },
				{
                    "sWidth": "10%"
                    
                }, {
                    "sWidth": "10%"
                },
					{
                    "sWidth": "10%"
                },
				{
                    "sWidth": "10%"
                },
                {
                    "sWidth": "20%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, ]
            });

            function ShowCountMessage()
            {
                alertify.alert('Warning','This Evaluator is already assigned to a Rotation. You can’t delete this.');
            }   


            $(document).on('click', '.loginAsSchoolUser', function() {

                var schoolId = $(this).attr('schoolId');
                var clinicianId = $(this).attr('clinicianId');
                var ClinicianFullName = $(this).attr('ClinicianFullName');

                alertify.confirm('Login Confirmation', 'Continue with login as '+ClinicianFullName+ '?', function() {
                    window.location.href='loginasschoolclinicians.html?schoolId='+schoolId+'&userId='+clinicianId;
                }, function() {});

            });

            $(document).on('click', '.loginEmailAjaxRow', function() {

                var clinicianId = $(this).attr('clinicianId');
                var ClinicianFullName = $(this).attr('ClinicianFullName');
                var currentSchoolId = $(this).attr('currentSchoolId');
                

                alertify.confirm('Evaluator: '+ ClinicianFullName, 'Continue with send login detail\'s email?', function() {

                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_login_email_to_school_user.html",
                        data: {
                            id: clinicianId,
                            schoolId: currentSchoolId,
                            type:'clinician'
                        },
                        success: function() {
                            alertify.success('Sent');
                        }
                    });
                }, function() {});
            });


             $(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var clinicianId = $(this).attr('clinicianId');
                var ClinicianFullName = $(this).attr('ClinicianFullName');
				var SchoolId='<?php echo ($currentSchoolId); ?>';
                var UserId = '<?php echo EncodeQueryData($loggedUserId); ?>';
                var isUser = 1;

                alertify.confirm('Evaluator: '+ClinicianFullName, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: clinicianId,
                            SchoolId: SchoolId,
                            UserId: UserId,
                            isUser: isUser,
                            type: 'clinician'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });

        </script>


    </body>

    </html>