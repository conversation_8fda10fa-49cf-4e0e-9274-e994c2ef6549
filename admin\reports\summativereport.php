<?php
$objSummative = new clsSummative();				
$individual_student = unserialize($individual_student);
$rowsSummative = $objSummative->GetStudentSummativeDetailsForreport($currentSchoolId,$rotationId,$individual_student,$student_rank,$evaluator,$school_location,$hospital_site,$startDate,$endDate,$AscDesc,$sordorder,$cbosemester,$subcborotation);
						$objPHPExcel = new \PHPExcel();
						
						// Set document properties
						$objPHPExcel->getProperties()->setCreator($schoolname)
													 ->setLastModifiedBy('JCC')
													 ->setTitle('Reports')
													 ->setSubject('School Report')
													 ->setDescription('All School Reports');
													 
						//Active Sheet
						$objPHPExcel->setActiveSheetIndex(0);
						$objPHPExcel->getActiveSheet()->setTitle('Summative Reports');				
						
						//Print Heading	
						$headerstyleArray = array('font'  => array('bold'  => true,'size'  => 16));
						
						$objPHPExcel->getActiveSheet()->mergeCells("B2:L2");
						$objPHPExcel->getActiveSheet()->setCellValue('B2', $schoolname);
						$objPHPExcel->getActiveSheet()->getStyle('B2')->applyFromArray($headerstyleArray);
						$objPHPExcel->getActiveSheet()->getStyle('B2')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()
									 ->getStyle('B2')
									 ->getFill()
									 ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
									 ->getStartColor()
									 ->setRGB('E0E0E0');
							
						$styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));
						$objPHPExcel->getActiveSheet()->getStyle('B2:L2')->applyFromArray($styleBorderArray);
						
						
						$styleArray = array('font'  => array('bold'  => true,'size'  => 12));
						
						$objPHPExcel->getActiveSheet()->mergeCells("B4:L4");
						$objPHPExcel->getActiveSheet()->setCellValue('B4', 'Summative Report');
						$objPHPExcel->getActiveSheet()->getStyle('B4')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('B4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()
									 ->getStyle('B4')
									 ->getFill()
									 ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
									 ->getStartColor()
									 ->setRGB('E0E0E0');
									
						$styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));
						$objPHPExcel->getActiveSheet()->getStyle('B4:L4')->applyFromArray($styleBorderArray);
						
						//Make Table Heading
						$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
						
						
						$objPHPExcel->getActiveSheet()->setCellValue('B6', 'First Name');
						$objPHPExcel->getActiveSheet()->getStyle('B6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('B6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						$objPHPExcel->getActiveSheet()->getDefaultColumnDimension('B6')->setWidth('25');
						
						
						$objPHPExcel->getActiveSheet()->setCellValue('C6', 'Last Name');
						$objPHPExcel->getActiveSheet()->getStyle('C6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('C6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('D6', 'Rank');
						$objPHPExcel->getActiveSheet()->getStyle('D6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('D6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('E6', 'Signature');
						$objPHPExcel->getActiveSheet()->getStyle('E6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('E6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('F6', 'Rotation');
						$objPHPExcel->getActiveSheet()->getStyle('F6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('F6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('G6', 'Evaluator');
						$objPHPExcel->getActiveSheet()->getStyle('G6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('G6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('H6', 'Eval Date');
						$objPHPExcel->getActiveSheet()->getStyle('H6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('H6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('I6', 'Grade');
						$objPHPExcel->getActiveSheet()->getStyle('I6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('I6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('J6', 'Unsatisfactory');
						$objPHPExcel->getActiveSheet()->getStyle('J6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('J6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('K6', 'Unsatisfactory Behavior Comments');
						$objPHPExcel->getActiveSheet()->getStyle('K6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('K6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->setCellValue('L6', 'Suggestions');
						$objPHPExcel->getActiveSheet()->getStyle('L6')->applyFromArray($styleArray);
						$objPHPExcel->getActiveSheet()->getStyle('L6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$objPHPExcel->getActiveSheet()->getStyle('B6:L6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
						
						$printStartRowCounter = 7;
						 if($rowsSummative)
							{
							   
							   $styleArray = array('font'  => array('size'  => 10));
								
												while($row = mysqli_fetch_array($rowsSummative))
												{
													$studentId='';
													$studentId = $row[0];											
													$studentSummativeMasterId = stripslashes($row['studentSummativeMasterId']);
													$firstName = stripslashes($row['firstName']);
													$lastName = stripslashes($row['lastName']);																																		
													$rank = stripslashes($row['title']);  
													$rotationname = stripslashes($row['rotationname']);  
													$studentSignature = stripslashes($row['dateOfStudentSignature']);
													$studentSignature = date('m/d/Y', strtotime($studentSignature));
													if ($studentSignature != '' && $studentSignature != '01/01/1970' && $studentSignature != '11/30/-0001') 
													$studentSignature = $studentSignature;
												else $studentSignature =''; 

													$clinicianfname = stripslashes($row['clinicianfname']);  
													$clinicianlname = stripslashes($row['clinicianlname']);
													$clinicianfullname=$clinicianfname . ' ' .$clinicianlname;
													$evaluationDate = stripslashes($row['evaluationDate']);  
													
													$getSummativeScore=$objSummative->GetSummativeScore($studentSummativeMasterId);
													$avgTotalScore=$getSummativeScore['EvaluationScore'];

													$getUnsatisfyBehaviourComments=$objSummative->GetUnsatisfyComments($studentSummativeMasterId);
													$UnsatisfactoryBehaviorComments=$getUnsatisfyBehaviourComments['schoolSummativeOptionAnswerText'];

													$getSuggestionsComments=$objSummative->GetSuggestionsComments($studentSummativeMasterId);
													$SuggestionsComment=$getSuggestionsComments['SuggestionsComment'];
													
													$preceptorId = $row['preceptorId'];
													$isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];
													if ($preceptorId > 0) {
														$objExternalPreceptors = new clsExternalPreceptors();
														$externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
														$preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
														$preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
														$preceptorNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
														$preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
														$preceptorInfo = ' Name: ' . $preceptorFullName . PHP_EOL .' Phone: ' . $preceptorNum;
														if ($isPreceptorCompletedStatus) 
															$preceptorInfo .= PHP_EOL .' Status: Completed';
														else
															$preceptorInfo .= PHP_EOL .' Status: Pending';
											
														// $clinicianfullname = $preceptorInfo;
														unset($objExternalPreceptors);
													}	
													if ($preceptorId) {
														$clinicianInfo = $preceptorInfo;
													}
													else{
														$clinicianInfo = $clinicianfullname;
													}

								$objPHPExcel->getActiveSheet()->setCellValue('B'.$printStartRowCounter, $firstName);
								$objPHPExcel->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
								$objPHPExcel->getActiveSheet()->getStyle('B'.$printStartRowCounter)->applyFromArray($styleArray);
								
								$objPHPExcel->getActiveSheet()->setCellValue('C'.$printStartRowCounter, $lastName);
								$objPHPExcel->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
								$objPHPExcel->getActiveSheet()->getStyle('C'.$printStartRowCounter)->applyFromArray($styleArray);
								
										 
								$objPHPExcel->getActiveSheet()->setCellValue('D'.$printStartRowCounter, $rank);
								$objPHPExcel->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
								$objPHPExcel->getActiveSheet()->getStyle('D'.$printStartRowCounter)->applyFromArray($styleArray);
								
								
								
								$objPHPExcel->getActiveSheet()->setCellValue('E'.$printStartRowCounter, $studentSignature);
								$objPHPExcel->getActiveSheet()->getStyle('E'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
								$objPHPExcel->getActiveSheet()->getStyle('E'.$printStartRowCounter)->applyFromArray($styleArray);
								
								
								$objPHPExcel->getActiveSheet()->setCellValue('F'.$printStartRowCounter, $rotationname);
								$objPHPExcel->getActiveSheet()->getStyle('F'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
								$objPHPExcel->getActiveSheet()->getStyle('F'.$printStartRowCounter)->applyFromArray($styleArray);
								
								$objPHPExcel->getActiveSheet()->setCellValue('G'.$printStartRowCounter, $clinicianInfo);
								$objPHPExcel->getActiveSheet()->getStyle('G'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
								$objPHPExcel->getActiveSheet()->getStyle('G'.$printStartRowCounter)->applyFromArray($styleArray);
								
								$objPHPExcel->getActiveSheet()->setCellValue('H'.$printStartRowCounter, $evaluationDate);
								$objPHPExcel->getActiveSheet()->getStyle('H'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
								$objPHPExcel->getActiveSheet()->getStyle('H'.$printStartRowCounter)->applyFromArray($styleArray);
								
								$objPHPExcel->getActiveSheet()->setCellValue('I'.$printStartRowCounter, $avgTotalScore);
								$objPHPExcel->getActiveSheet()->getStyle('I'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
								$objPHPExcel->getActiveSheet()->getStyle('I'.$printStartRowCounter)->applyFromArray($styleArray);
								
								$objPHPExcel->getActiveSheet()->setCellValue('J'.$printStartRowCounter, ' ');
								$objPHPExcel->getActiveSheet()->getStyle('J'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
								$objPHPExcel->getActiveSheet()->getStyle('J'.$printStartRowCounter)->applyFromArray($styleArray);
								
								$objPHPExcel->getActiveSheet()->setCellValue('K'.$printStartRowCounter, $UnsatisfactoryBehaviorComments);
								$objPHPExcel->getActiveSheet()->getStyle('K'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
								$objPHPExcel->getActiveSheet()->getStyle('K'.$printStartRowCounter)->applyFromArray($styleArray);
								
								$objPHPExcel->getActiveSheet()->setCellValue('L'.$printStartRowCounter, $SuggestionsComment);
								$objPHPExcel->getActiveSheet()->getStyle('L'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
								$objPHPExcel->getActiveSheet()->getStyle('L'.$printStartRowCounter)->applyFromArray($styleArray);
								
								
								$printStartRowCounter++;
							}
						}
						// exit;
												  
						//Make Border
						$printStartRowCounter--;
						$styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));
						$objPHPExcel->getActiveSheet()->getStyle('B6:L6'.$printStartRowCounter)->applyFromArray($styleBorderArray);
						
						// Auto size columns for each worksheet
						foreach(range('B','D') as $columnID)
						{
							$objPHPExcel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(false);
						}
						$reportname='SummativeReport_';
?>