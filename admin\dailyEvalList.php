<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsDaily.php');
include('../class/clsRotation.php');
include('../class/clsCourses.php');
include('../class/clsExternalPreceptors.php');
include('../class/clsClinician.php');


$midtermrotationid = 0;
$schoolId = 0;
$studentId = 0;

$currentSchoolId;
$transchooldisplayName = '';
$rotationId = 0;
$Type = '';
$courseId = 0;
$display_from_date = '';
$display_to_date = '';

$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0;
$canvasStatus = isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';
$viewAll = isset($_GET['view']) ? $_GET['view'] : '';
$loggedAsBackUserId = isset($_SESSION["loggedAsBackUserId"]) ? $_SESSION["loggedAsBackUserId"] : 0;

$type = isset($_GET['type']) ? $_GET['type'] : '';
if ($type == 'canvas')
    $canvasStatus = 1;

if (isset($_GET['Type'])) {
    $Type = ($_GET['Type']);
}

$encodedCourseId = 0;
if (isset($_GET['courseId'])) {
    $encodedCourseId = $_GET['courseId'];
    $courseId = DecodeQueryData($_GET['courseId']);
}


//For Rotation Site
if (isset($_GET['rotationId'])) {
    $rotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($rotationId);
} elseif (isset($_GET['studentId'])) {
    $studentId = $_GET['studentId'];
    $studentId = DecodeQueryData($studentId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

// echo '<pre>';
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSearch'])) {
    $canvasStatus = isset($_POST['canvasStatus']) ? $_POST['canvasStatus'] : '';
    $courseId = (isset($_POST['cboCourse'])) ? DecodeQueryData($_POST['cboCourse']) : 0;
    $rotationId = $_POST['cboRotation'];
    $rotationId = DecodeQueryData($rotationId);
    $display_from_date = (isset($_POST['fromDate']) && !empty($_POST['fromDate'])) ? date("Y-m-d", strtotime($_POST['fromDate'])) : '';
    $display_to_date = (isset($_POST['toDate']) && !empty($_POST['toDate'])) ? date("Y-m-d", strtotime($_POST['toDate'])) : '';
    // $viewAll = isset($_POST['hideViewAll']) ? $_POST['hideViewAll'] : '';
}
$title = "Daily/Weekly Evaluation |" . $transchooldisplayName;

//For Daily Weekly List
$objDailyEval = new clsDaily();
$getDailydetails = $objDailyEval->GetAllDailyweeklyEvalForAdmin($rotationId, $currentSchoolId, $courseId, $studentId, $canvasStatus, $viewAll, $display_from_date, $display_to_date);
$totalDailyCount = 0;
if ($getDailydetails != '') {
    $totalDailyCount = mysqli_num_rows($getDailydetails);
}
unset($objDailyEval);


//For Rotation Name
$objRotation = new clsRotation();
$rotation = $objRotation->GetRotationBySchool($currentSchoolId, $studentId);
//  echo mysqli_num_rows($rotation);
$RotationName = $objRotation->GetrotationDetails($rotationId, $currentSchoolId);
$rotationtitle = $RotationName['title'];
unset($objRotation);

//Get Course List for dropdown
$objCourses = new clsCourses();
$courseList = $objCourses->GetAllCoursesBySchool($currentSchoolId);
unset($objCourses);

//For Student Name
$objStudent = new clsStudent();
$rowsStudents = $objStudent->GetSingleStudent($currentSchoolId, $studentId);
$studentfullname = ($rowsStudents['firstName'] . ' ' . $rowsStudents['lastName']);
unset($objStudent);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link href="https://cdn.datatables.net/fixedcolumns/3.2.4/css/fixedColumns.bootstrap4.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <?php if ($type == 'canvas') { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Daily/Weekly Evaluation</li>
                    <?php } else { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <?php
                        if ($rotationId != '' && $Type == 'A') { ?>
                            <li><a href="rotations.html">Rotations</a></li>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                        <?php } elseif ($studentId != '' && $Type == 'C') { ?>
                            <li><a href="clinical.html">Clinical</a></li>
                            <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                        <?php } ?>
                        <li class="active">Daily/Weekly Evaluation</li>
                    <?php } ?>
                </ol>
            </div>

            <?php if ($type != 'canvas') { ?>
                <div class="pull-right">
                    <?php
                    if ($rotationId > 0) {
                        $rotationStatus = checkRotationStatus($rotationId);
                        if($rotationStatus == 0){
                        ?>
                        <a class="btn btn-link" href="dailyEval.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>&Type=<?php echo $Type; ?>">Add</a>
                    <?php } } elseif ($studentId > 0) { ?>
                        <a class="btn btn-link" href="dailyEval.html?studentId=<?php echo (EncodeQueryData($studentId)); ?>&Type=<?php echo $Type; ?>">Add</a>
                    <?php } else { ?>
                        <a class="btn btn-link" href="dailyEval.html">Add</a>
                    <?php } ?>
                </div>
            <?php } ?>

        </div>
    </div>

    <div class="custom-container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Daily/Weekly Evaluation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Daily/Weekly Evaluation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Daily/Weekly Evaluation deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>
        <div id="divTopLoading">Loading...</div>
        <?php //if ($rotationId == 0) { 
        ?>
        <div class="row margin_bottom_ten">
            <div class="col-md-8"></div>
            <div class="col-md-4">
                <div class="form-group">
                    <a href="dailyEvalList.html?view=all" class="btn btn-primary pull-right">Display All</a>

                </div>
            </div>
        </div>
        <?php //} 
        ?>
        <hr>
        <form name="listForm" id="listForm" method="POST" action="dailyEvalList.html?view=<?php echo $viewAll; ?>">
            <div class="row">
                <div class="col-md-4  margin_bottom_ten">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="fromDate" style="margin-top:8px">From Date</label>
                        <div class='input-group date' name="fromDate" id='fromDate'>
                            <input type='text' name="fromDate" id="fromDate" value="<?php if ($display_from_date != '') echo date('m/d/Y', strtotime($display_from_date)); ?>" class="dateInputFormat form-control input-md r rotation_date" data-parsley-errors-container="#error-txtDate" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        <div id="error-txtDate"></div>
                    </div>
                </div>
                <div class="col-md-4  margin_bottom_ten">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="toDate" style="margin-top:8px">To Date</label>

                        <div class='input-group date' id='toDate'>

                            <input type='text' name="toDate" id="toDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php if ($display_to_date != '') echo date('m/d/Y', strtotime($display_to_date)); ?>" data-parsley-errors-container="#error-txtDate" />
                            <span class="input-group-addon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        <div id="error-txtDate"></div>
                    </div>
                </div>
                <div class="col-md-4 margin_bottom_ten">
                    <div class="form-group">
                        <label class="col-md-4 control-label margin_top_seven" for="cboCourse ">Course</label>

                        <div class="col-md-8  <?php if (!$isActiveCanvas) { ?> padding_right_zero <?php } ?>">
                            <select id="cboCourse" name="cboCourse" class="form-control input-md required-input select2_single">
                                <option value="" selected>Select</option>
                                <?php
                                if ($courseList != "") {
                                    while ($row = mysqli_fetch_assoc($courseList)) {
                                        $selcourseId  = $row['courseId'];
                                        $name  = stripslashes($row['title']);

                                ?>
                                        <option value="<?php echo (EncodeQueryData($selcourseId)); ?>" <?php if ($courseId == $selcourseId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php
                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>



            </div>
            <div class="row">
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="cbostudent" style="margin-top:8px">Rotation</label>
                        <div class="col-md-8 padding_right_zero padding_left_zero">
                            <select id="cboRotation" name="cboRotation" class="form-control input-md required-input select2_single">
                                <option value="" selected>Select</option>

                                <?php

                                if ($rotation != "") {
                                    while ($row = mysqli_fetch_assoc($rotation)) {
                                        $selrotationIdDropdown  = $row['rotationId'];
                                        $name  = stripslashes($row['title']);

                                ?>
                                        <option value="<?php echo (EncodeQueryData($selrotationIdDropdown)); ?>" <?php if ($rotationId == $selrotationIdDropdown) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }

                                ?>
                            </select>

                        </div>
                    </div>
                </div>
                <?php if ($isActiveCanvas) { ?>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-4 control-label padding_right_zero" for="" style="margin-top:8px">Canvas Status</label>
                            <div class="col-md-8 padding_right_zero padding_left_zero">
                                <select id="canvasStatus" name="canvasStatus" class="form-control input-md required-input select2_single">
                                    <option value="" selected>All</option>
                                    <option value="1" <?php if ($canvasStatus == 1) { ?> selected="true" <?php } ?>>Sent</option>
                                    <option value="0" <?php if ($canvasStatus == 0 && $canvasStatus != '') { ?> selected="true" <?php } ?>>Not Sent</option>
                                </select>
                            </div>
                        </div>
                    </div>
                <?php } ?>
                <div class="col-md-2  margin_bottom_ten">
                    <div class="form-group">
                        <button id="btnSearch" name="btnSearch" class="btn btn-success">Search</button>
                    </div>
                </div>

            </div>
        </form>
        <hr style="margin-top: 0 !important;">
        <?php if ($type != 'canvas') { ?>
            <!-- <div class="row margin_bottom_ten">
                <?php if ($rotationId == 0) { ?>
                    <?php if (!$isActiveCanvas) { ?> <div class="col-md-4"></div> <?php } ?>
                    <div class="col-md-4"></div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-4 control-label margin_top_seven" for="cboCourse ">Course:</label>

                            <div class="col-md-8  <?php if (!$isActiveCanvas) { ?> padding_right_zero <?php } ?>">
                                <select id="cboCourse" name="cboCourse" class="form-control input-md required-input select2_single">
                                    <option value="" selected>Select</option>
                                    <?php
                                    if ($courseList != "") {
                                        while ($row = mysqli_fetch_assoc($courseList)) {
                                            $selcourseId  = $row['courseId'];
                                            $name  = stripslashes($row['title']);

                                    ?>
                                            <option value="<?php echo (EncodeQueryData($selcourseId)); ?>" <?php if ($courseId == $selcourseId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                    <?php
                                        }
                                    }
                                    ?>
                                </select>
                            </div>
                        </div>
                    </div>

                <?php } ?>
                <?php if ($isActiveCanvas) {
                    if ($rotationId != 0) {
                        echo "<div class='col-md-8'> </div>";
                    }
                ?>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="col-md-4 control-label padding_right_zero" for="" style="margin-top:8px">Canvas Status:</label>
                            <div class="col-md-8 padding_right_zero padding_left_zero">
                                <select id="canvasStatus" name="canvasStatus" class="form-control input-md required-input select2_single" studentId="<?php echo EncodeQueryData($currentstudentId); ?>">
                                    <option value="" selected>All</option>
                                    <option value="1" <?php if ($canvasStatus == 1) { ?> selected="true" <?php } ?>>Sent</option>
                                    <option value="0" <?php if ($canvasStatus == 0 && $canvasStatus != '') { ?> selected="true" <?php } ?>>Not Sent</option>
                                </select>
                            </div>
                        </div>
                    </div>
                <?php } ?>
            </div> -->
        <?php } ?>



        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr id="filters">
                    <th>First <br>Name</th>
                    <th>Last<br> Name</th>
                    <th style="text-align: center">Evaluation<br>Date</th>
                    <th class="select-filter">Rank</th>
                    <th class="select-filter">Rotation</th>
                    <th style="text-align: center">Student<br>Signature Date</th>
                    <th style="text-align: center">Instructor/Preceptor<br>Sign Date</th>
                    <th style="text-align: center">Avg<br>Atten</th>
                    <th style="text-align: center">Avg<br>Student<br>Prep</th>
                    <th style="text-align: center">Avg<br>Profess</th>
                    <th style="text-align: center">Avg<br>Know</th>
                    <th style="text-align: center">Avg<br>Psych</th>
                    <th style="text-align: center">Avg<br>Org</th>
                    <th style="text-align: center">Total<br>Average</th>
                    <?php if ($type != 'canvas') { ?>
                        <th style="text-align: center">Action</th>
                    <?php } ?>
                    <?php if ($isActiveCanvas && $type != 'canvas') { ?>
                        <th class="text-center">Canvas Status</th>
                    <?php } ?>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalDailyCount > 0) {
                    while ($row = mysqli_fetch_array($getDailydetails)) {

                        //$title = $row['title'];
                        $rotationame = $row['title'];
                        $studentId = $row['studentId'];
                        $rotationId = $row['rotationId'];
                        $Ranktitle = $row['Ranktitle'];
                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $studentName = $firstName . ' ' . $lastName;
                        $studentDailyMasterId = $row['DailyEvalID'];
                        $evaluationDate = stripslashes($row['evaluationDate']);
                        $isPreceptorCompletedStatus = '';

                        $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                        $preceptorId = $row['preceptorId'];
                        $isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];
                        if ($preceptorId > 0) {
                            $objExternalPreceptors = new clsExternalPreceptors();
                            $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
                            $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                            $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                            $preceptorNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
                            $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                        }
                        $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                        if ($dateOfStudentSignature != "0000-00-00 00:00:00") {
                            $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                            $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                        } else {
                            $dateOfStudentSignature = "-";
                        }
                        $preceptorDetials = '';

                        $dateOfInstructorSignature = stripslashes($row['dateOfInstructorSignature']);
                        if ($preceptorId) {
                            //  echo ($dateOfInstructorSignature); 
                            $dateOfInstructorSignature = converFromServerTimeZone($dateOfInstructorSignature, $TimeZone);
                            $dateOfInstructorSignature = date("m/d/Y", strtotime($dateOfInstructorSignature));
                            $preceptorInfo = 'Name: ' . $preceptorFullName . '</br>Phone: ' . $preceptorNum;
                            if ($isPreceptorCompletedStatus)
                                $preceptorInfo .= '</br>Status: Completed';
                            else
                                $preceptorInfo .= '</br>Status: Pending';


                            $preceptorDetials = $preceptorInfo;
                            if ($dateOfInstructorSignature != '' && $dateOfInstructorSignature != '0000-00-00 00:00:00' && $dateOfInstructorSignature != '01/01/1970' && $dateOfInstructorSignature != '0000-00-00' && $dateOfInstructorSignature != '12/31/1969')
                                $preceptorInfo .= '</br>Date: ' . $dateOfInstructorSignature;

                            $dateOfInstructorSignature = $preceptorInfo;
                        } else if ($dateOfInstructorSignature != '0000-00-00 00:00:00') {
                            $dateOfInstructorSignature = converFromServerTimeZone($dateOfInstructorSignature, $TimeZone);
                            $dateOfInstructorSignature = date("m/d/Y", strtotime($dateOfInstructorSignature));
                        } else {
                            $dateOfInstructorSignature = "-";
                        }

                        //For Avg
                        $firstSectionAvg = $row['firstSectionAvg'];
                        $firstSectionAvg = number_format((float)$firstSectionAvg, 2, '.', '');
                        $secondSectionAvg = $row['secondSectionAvg'];
                        $secondSectionAvg = number_format((float)$secondSectionAvg, 2, '.', '');
                        $thirdSectionAvg = $row['thirdSectionAvg'];
                        $thirdSectionAvg = number_format((float)$thirdSectionAvg, 2, '.', '');
                        $fourthSectionAvg = $row['fourthSectionAvg'];
                        $fourthSectionAvg = number_format((float)$fourthSectionAvg, 2, '.', '');
                        $fiveSectionAvg = $row['fiveSectionAvg'];
                        $fiveSectionAvg = number_format((float)$fiveSectionAvg, 2, '.', '');
                        $sixSectionAvg = $row['sixSectionAvg'];
                        $sixSectionAvg = number_format((float)$sixSectionAvg, 2, '.', '');
                        $totalAvg = $row['totalAvg'];
                        $totalAvg = number_format((float)$totalAvg, 2, '.', '');

                        if ($preceptorId && $isPreceptorCompletedStatus == 0) {
                            $firstSectionAvg = '';
                            $secondSectionAvg = '';
                            $thirdSectionAvg = '';
                            $fourthSectionAvg = '';
                            $fiveSectionAvg = '';
                            $sixSectionAvg = '';
                            $totalAvg = '';
                        }

                        $clinicianId = $row['clinicianId'];

                        if ($clinicianId) {
                            $objClinician = new clsClinician();
                            $clinicianFullName = $objClinician->GetClinicianNameById($clinicianId);
                            $preceptorDetials = 'Name: ' . $clinicianFullName;
                            unset($objClinician);
                        }
                        // For Canvas
                        $isSendToCanvas  = isset($row['isSendToCanvas']) ? $row['isSendToCanvas'] : 0;

                        $isSendToCanvasClass = 'isSendRecordToCanvas';
                        $isSentToCanvasClass = 'hide';
                        if ($isSendToCanvas) {
                            $isSendToCanvasClass = 'hide';
                            $isSentToCanvasClass = '';
                        }

                        $isUserCanSendCompletedRecordToCanvas = 0;
                        if ($dateOfStudentSignature != '-')
                            $isUserCanSendCompletedRecordToCanvas = 1;

                        // -- End Canvas --//
                ?>
                        <tr>
                            <td><?php echo ($firstName); ?></td>
                            <td><?php echo ($lastName); ?></td>
                            <td style="text-align: center"><?php echo ($evaluationDate); ?></td>
                            <td><?php echo ($Ranktitle); ?></td>
                            <td><?php echo ($rotationame); ?></td>
                            <td style="text-align: center"><?php echo ($dateOfStudentSignature);
                                                            ?></td>
                            <td><?php
                                echo ($dateOfInstructorSignature);
                                // echo $rotationId;                                                                                                  
                                if ($isPreceptorCompletedStatus ==  0 && $preceptorId > 0) { ?>
                                    <br>
                                    <a href="javascript:void(0);" class="copyLink" preceptorId=<?php echo EncodeQueryData($preceptorId); ?> preceptornum="<?php echo $preceptorNum; ?>" evaluationId="<?php echo $studentDailyMasterId; ?>" rotationId="<?php echo ($rotationId); ?>" evaluationType="dailyEval" onclick="copyLinkUrl(this)">Click to Copy URL</a>
                                <?php } ?>
                            </td>

                            <td style="text-align: center"><?php echo $firstSectionAvg; ?></td>
                            <td style="text-align: center"><?php echo $secondSectionAvg; ?></td>
                            <td style="text-align: center"><?php echo $thirdSectionAvg; ?></td>
                            <td style="text-align: center"><?php echo $fourthSectionAvg; ?></td>
                            <td style="text-align: center"><?php echo $fiveSectionAvg; ?></td>
                            <td style="text-align: center"><?php echo $sixSectionAvg; ?></td>
                            <td style="text-align: center"><?php echo $totalAvg; ?></td>

                            <?php if ($type != 'canvas') { ?>
                                <td style="text-align: center">
                                    <?php
                                    $rotationStatus = checkRotationStatus($rotationId);
                                    ?>


                                    <?php if ($dateOfStudentSignature != '-' || $rotationStatus) { ?>
                                        <a href="dailyEval.html?studentDailyMasterId=<?php echo (EncodeQueryData($studentDailyMasterId)); ?>&view=V&isPreceptor=<?php echo (EncodeQueryData($preceptorId)); ?>">View</a>
                                        <?php
                                        if ($loggedAsBackUserId) {
                                        ?>
                                            | <a href="javascript:void(0);" class="deleteAjaxRow" studentDailyMasterId="<?php echo EncodeQueryData($studentDailyMasterId); ?>" studentName="<?php echo ($studentName); ?>">Delete</a>
                                        <?php }
                                    } elseif ($rotationId > 0 && $Type != '') { ?>
                                        <a href="dailyEval.html?studentDailyMasterId=<?php echo (EncodeQueryData($studentDailyMasterId)); ?>&rotationId=<?php echo (EncodeQueryData($rotationId)); ?>&Type=<?php echo $Type; ?>&isPreceptor=<?php echo (EncodeQueryData($preceptorId)); ?>">Edit</a>
                                        | <a href="javascript:void(0);" class="deleteAjaxRow" studentDailyMasterId="<?php echo EncodeQueryData($studentDailyMasterId); ?>" studentName="<?php echo ($studentName); ?>">Delete</a>
                                    <?php } else { ?>
                                        <a href="dailyEval.html?studentDailyMasterId=<?php echo (EncodeQueryData($studentDailyMasterId)); ?>&isPreceptor=<?php echo (EncodeQueryData($preceptorId)); ?>">Edit</a>
                                        | <a href="javascript:void(0);" class="deleteAjaxRow" studentDailyMasterId="<?php echo EncodeQueryData($studentDailyMasterId); ?>" studentName="<?php echo ($studentName); ?>">Delete</a>
                                    <?php } ?>

                                </td>
                            <?php } ?>
                            <?php if ($isActiveCanvas && $type != 'canvas') {
                                if ($loggedUserSendRecordToCanvas && $isUserCanSendCompletedRecordToCanvas) { ?>
                                    <td class="text-center">
                                        <a href="javascript:void(0);" id="isSendToCanvas_<?php echo $studentDailyMasterId; ?>" class="<?php echo $isSendToCanvasClass; ?>" evaluationDate="<?php echo $evaluationDate; ?>" rotation="<?php echo $rotationame; ?>" dateOfStudentSignature="<?php echo $dateOfStudentSignature; ?>" dateOfInstructorSignature="<?php echo $dateOfInstructorSignature; ?>" avgAttendance="<?php echo $firstSectionAvg; ?>" avgStudentPrep="<?php echo $secondSectionAvg; ?>" avgProfess="<?php echo $thirdSectionAvg; ?>" avgKnow="<?php echo $fourthSectionAvg; ?>" avgPsych="<?php echo $fiveSectionAvg; ?>" avgOrg="<?php echo $sixSectionAvg; ?>" totalAvg="<?php echo $totalAvg; ?>" dailyWeeklyId="<?php echo $studentDailyMasterId; ?>" studentId="<?php echo $studentId; ?>" studentFullName="<?php echo $studentName; ?>" preceptorInfo="<?php echo $preceptorDetials; ?>">
                                            Send to Canvas
                                        </a>
                                        <label for="" class="isSentToCanvas_<?php echo $studentDailyMasterId; ?> <?php echo $isSentToCanvasClass; ?>">Sent</label>

                                    </td>

                                <?php } else { ?>
                                    <td class="text-center"><label for="" class=""> -
                                            <?php //if($isSendToCanvas>0) { echo 'Sent'; } else { echo 'Send to canvas'; } 
                                            ?>
                                        </label></td>

                            <?php }
                            }
                            ?>
                        </tr>
                <?php
                    }
                }
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="https://cdn.datatables.net/fixedcolumns/3.2.4/js/dataTables.fixedColumns.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            var rotationId = '<?php echo $rotationId ?>';
            var viewAll = '<?php echo $viewAll ?>';
            if (rotationId == 0 && viewAll == 'all') {
                var currentURL = window.location.href;
                splitCurrentURL = currentURL.split('?');
                history.pushState({}, null, splitCurrentURL[0]);
            }

            $(function() {
                $("#fromDate").datetimepicker({
                    format: "MM/DD/YYYY"
                });
            });

            $(function() {
                $("#toDate").datetimepicker({
                    format: "MM/DD/YYYY"
                });
            });

        });



        $(".select2_single").select2();

        var current_datatable = $("#datatable-responsive").DataTable({


            responsive: false,
            "sScrollX": true,
            "ordering": true,
            scrollX: true,
            fixedColumns: {
                leftColumns: 5
            },
            "aaSorting": [],
            "aoColumns": [{
                    "sWidth": "5%"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, {
                    "sWidth": "15%",
                    "bSortable": false
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "5%"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                },
                {
                    "sWidth": "5%"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "5%",
                    "sClass": "alignCenter"
                }

                <?php if ($type != 'canvas') { ?>, {
                        "sWidth": "5%",
                        "sClass": "alignCenter",
                        "bSortable": false
                    }
                <?php } ?>

                <?php if ($isActiveCanvas && $type != 'canvas') { ?>, {
                        "sWidth": "10%"
                    }
                <?php } ?>
            ]
        });

        // ajax call for deleteAjaxRow
        $(document).on('click', '.deleteAjaxRow', function() {
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentDailyMasterId = $(this).attr('studentDailyMasterId');
            var title = $(this).attr('studentName');

            alertify.confirm('Daily/Weekly Eval: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentDailyMasterId,
                        type: 'Daily/Weekly'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});
        });

        // $("#cboCourse").change(function() {
        //     var courseId = $(this).val();
        //     var canvasStatus = '<?php echo $canvasStatus; ?>';
        //     if (courseId) {
        //         window.location.href = "dailyEvalList.html?courseId=" + courseId + "&canvasStatus=" + canvasStatus;
        //     } else {
        //         window.location.href = "dailyEvalList.html";
        //     }
        // });

        // $("#canvasStatus").change(function() {
        //     var courseId = '<?php echo $encodedCourseId; ?>';
        //     var canvasStatus = $(this).val();
        //     if (canvasStatus) {
        //         window.location.href = "dailyEvalList.html?canvasStatus=" + canvasStatus + "&courseId=" + courseId;
        //     } else {
        //         window.location.href = "dailyEvalList.html";
        //     }
        // });

        //Send Records To Canvas
        $(document).on('click', '.isSendRecordToCanvas', function() {
            var that = this;
            var evaluationDate = $(this).attr('evaluationDate');
            var rotation = $(this).attr('rotation');
            var dateOfStudentSignature = $(this).attr('dateOfStudentSignature');
            var dateOfInstructorSignature = $(this).attr('dateOfInstructorSignature');
            var avgAttendance = $(this).attr('avgAttendance');
            var avgStudentPrep = $(this).attr('avgStudentPrep');
            var avgProfess = $(this).attr('avgProfess');
            var avgKnow = $(this).attr('avgKnow');
            var avgPsych = $(this).attr('avgPsych');
            var avgOrg = $(this).attr('avgOrg');
            var totalAvg = $(this).attr('totalAvg');
            var dailyWeeklyId = $(this).attr('dailyWeeklyId');
            var studentId = $(this).attr('studentId');
            var studentfullname = $(this).attr('studentfullname');
            var schoolId = "<?php echo $currentSchoolId; ?>";
            var preceptorInfo = $(this).attr('preceptorInfo');

            alertify.confirm('Daily Journal ', 'Continue with send record to Canvas?', function() {
                $(that).text('Loading..');
                $(that).prop('disabled', true);
                $.ajax({
                    type: "POST",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_record_to_canvas.html",
                    data: {

                        evaluationDate: evaluationDate,
                        rotation: rotation,
                        dateOfStudentSignature: dateOfStudentSignature,
                        dateOfInstructorSignature: dateOfInstructorSignature,
                        avgAttendance: avgAttendance,
                        avgStudentPrep: avgStudentPrep,
                        avgProfess: avgProfess,
                        avgKnow: avgKnow,
                        avgPsych: avgPsych,
                        avgOrg: avgOrg,
                        totalAvg: totalAvg,
                        dailyWeeklyId: dailyWeeklyId,
                        studentFullName: studentfullname,
                        studentId: studentId,
                        schoolId: schoolId,
                        preceptorInfo: preceptorInfo,
                        type: 'DailyWeeklyEval'
                    },
                    success: function(response) {
                        if (response == 'Success') {
                            $(that).addClass('hide');
                            $('.isSentToCanvas_' + dailyWeeklyId).removeClass('hide')
                            alertify.success('Record Successfully Sent to Canvas.');
                        } else {
                            alertify.success(response);
                        }

                    }
                });
            }, function() {});

        });
    </script>
</body>

</html>