<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');  
    include('../class/clsCourses.php');
	include('../class/clsHospitalSite.php'); 
    include('../class/clsStudent.php');
    include('../class/clsRotation.php');
	include('../setRequest.php');
	include('../class/clsLocations.php'); 


	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		//Get input		
		$copyrotationId = isset($_GET['id']) ? $_GET['id'] : 0;		
		$status = 'Copied';
		$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

		$txtTitle  = ($_POST['txtTitle']);
		$coursesId=0;
		if(isset($_POST['cbocourses']))
		{
			$coursesId  = $_POST['cbocourses'];
		}
		else
		{
			$coursesId  = $_POST['default_courseId'];
		}

		$cbohospitalsites  = ($_POST['cbohospitalsites']); 
		$duration  = ($_POST['Inputrange']);
		$loggedUserId= $_SESSION['loggedUserId'];
		$parentRotationId= $_POST['mainrotation'];
		$parentRotationId = $parentRotationId ? $parentRotationId : 0;
		$startDate = ($_POST['startDate']);
		$startDate = converToServerTimeZone($startDate,$TimeZone);
		$startDate  = GetDateStringInServerFormat($startDate);
		$endDate = ($_POST['endDate']);
		$endDate = converToServerTimeZone($endDate,$TimeZone);
		$endDate = GetDateStringInServerFormat($endDate);


		$startDateTimestamp = converToServerTimeZone($_POST['startDate'].' '.$_POST['startTime'],$TimeZone);
		$endDateTimestamp = converToServerTimeZone($_POST['endDate'].' '.$_POST['endTime'],$TimeZone);
		$startDate = GetDateStringInServerFormat($startDateTimestamp);
		$endDate  = GetDateStringInServerFormat($endDateTimestamp);

		//Save data		
		$objRotation = new clsRotation();
		$objRotation->title = $txtTitle;	
		$objRotation->schoolId = $currentSchoolId;
		$objRotation->parentRotationId = $parentRotationId;
		$objRotation->hospitalSiteId = $cbohospitalsites;
		$objRotation->courseId = $coursesId;
		$objRotation->startDate = $startDate;	
		$objRotation->endDate = $endDate;
		$objRotation->createdBy = $loggedUserId;
		$objRotation->duration = $duration;	
		$retrotationId = $objRotation->CopyOfOldRotations($copyrotationId);		
		
		unset($objRotation);

		if($retrotationId > 0)
		{
				header('location:rotations.html?rotationId='.EncodeQueryData($copyrotationId).'&status='.$status);			
			
		}
		else
		{
			
			header('location:rotations.html?rotationId='.EncodeQueryData($copyrotationId).'&status='.$status);
			exit();
		}
	}
	else
	{
		header('location:rotations.html');
		exit();
	}
?>