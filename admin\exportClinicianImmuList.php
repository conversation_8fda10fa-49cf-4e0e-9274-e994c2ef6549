<?php

require '../vendor/autoload.php'; // Include the Composer autoloader

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

// Include necessary files
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsClinicianImmunization.php');	
include('../class/clsClinician.php'); 
include('../setRequest.php');

$studentImmunizationId = 0;
$currentSchoolId = $_GET['currentSchoolId'];
$currentSchoolId = DecodeQueryData($currentSchoolId);

$clinicianId = $_GET['clinicianId'];
$clinicianId = DecodeQueryData($clinicianId);

$objimmunization = new clsClinicianImmunization();
$rowsimmunization = $objimmunization->GetAllSingleClinicianImumunizationDetails($studentImmunizationId, $clinicianId);

// For Clinician Details
$objClinician = new clsClinician();
$clinicianDetail = $objClinician->GetClinicianDetails($clinicianId);
$firstName = isset($clinicianDetails['firstName']) ? $clinicianDetail['firstName'] : '';
$lastName = isset($clinicianDetail['lastName']) ? $clinicianDetail['lastName'] : '';
$fullName = $firstName . ' ' . $lastName;
unset($objClinician);

$title = 'Clinician Immunization List';
date_default_timezone_set('Asia/Kolkata');
$today = date('m/d/Y, H:i A');

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnStudentExport'])) {
    $reportType = $_POST['cboreporttype'];
}

switch ($reportType) {
    case "ClinicianImmulizationReport":

        $spreadsheet = new Spreadsheet();
        $sheet = $spreadsheet->getActiveSheet();

        // Set document properties
        $spreadsheet->getProperties()
            ->setCreator('Schools')
            ->setLastModifiedBy('JCC')
            ->setTitle('Reports')
            ->setSubject('Clinician Immunization List')
            ->setDescription('All School Reports');

        // Print Heading
        $sheet->mergeCells("B2:E2");
        $sheet->setCellValue('B2', $title);
        $sheet->getStyle('B2')->getFont()->setBold(true)->setSize(16);
        $sheet->getStyle('B2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('B2')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('E0E0E0');
        $sheet->getStyle('B2:E2')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        // Clinician Name
        $sheet->mergeCells("B4:E4");
        $sheet->setCellValue('B4', $fullName);
        $sheet->getStyle('B4')->getFont()->setBold(true)->setSize(12);
        $sheet->getStyle('B4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('B4')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('E0E0E0');
        $sheet->getStyle('B4:E4')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        // Table Headings
        $sheet->setCellValue('B6', 'Immunization');
        $sheet->setCellValue('C6', 'Date');
        $sheet->setCellValue('D6', 'Notification Date');
        $sheet->setCellValue('E6', 'Expiry Date');

        $sheet->getStyle('B6:E6')->getFont()->setBold(true)->setSize(10);
        $sheet->getStyle('B6:E6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
        $sheet->getStyle('B6:E6')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('E0E0E0');
        $sheet->getStyle('B6:E6')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

        // Populate Data
        $printStartRowCounter = 7;
        if ($rowsimmunization) {
            while ($row = mysqli_fetch_array($rowsimmunization)) {
                $expiryDays = $row['expiryDays'];
                $shortName = $row['shortName'];
                $immunizationDate = date("m/d/Y", strtotime($row['immunizationDate']));
                $immunizationNotificationDate = ($expiryDays == 0) ? 'N/A' : date("m/d/Y", strtotime($row['immunizationNotificationDate']));
                $ExpiryDate = ($expiryDays == 0) ? 'N/A' : date("m/d/Y", strtotime($row['expiryDate']));

                // Add data to cells
                $sheet->setCellValue('B' . $printStartRowCounter, $shortName);
                $sheet->setCellValue('C' . $printStartRowCounter, $immunizationDate);
                $sheet->setCellValue('D' . $printStartRowCounter, $immunizationNotificationDate);
                $sheet->setCellValue('E' . $printStartRowCounter, $ExpiryDate);

                $sheet->getStyle("B{$printStartRowCounter}:E{$printStartRowCounter}")->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);
                $printStartRowCounter++;
            }
        }

        // Auto size columns
        foreach (range('B', 'E') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }
        $sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows


        // Export File
        $reportname = 'ClinicianImmunizationListReport_';
        $currentDate = date('m_d_Y_h_i');
        header('Content-Type: application/vnd.ms-excel');
        header("Content-Disposition: attachment; filename={$reportname}{$currentDate}.xls");
        header('Cache-Control: max-age=0');

        $writer = new Xls($spreadsheet);
        $writer->save('php://output');
        exit;

    default:
        echo "<b>Please Select Valid Type.</b>";
        break;
}
?>
