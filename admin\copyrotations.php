<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsCourses.php');
include('../class/clsLocations.php');
include('../class/clsHospitalSite.php');
include('../class/clsRotation.php');

$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$loggedUserLocationId = '';
$loggedUserLocationId = $_SESSION["loggedUserLocation"];
$page_title = "Copy Rotation";
$rotationId = 0;
$hospitalSiteId = 0;
$parentRotationId = 0;
$title = '';
$currentDate = date("m/d/Y");
$nextDate = date("m/d/Y", strtotime("+1 days"));
$startDate = date("m/d/Y");
$endDate = date("m/d/Y", strtotime("+7 days"));
$subRotationStartTime = date('h:i A');
$subRotationEndTime =  date('h:i A', strtotime("+1 hours"));
$courseId = 0;
$durationTime  = 0;
$id = '';
$copyrotationId = 0;
$bedCrumTitle = 'Copy';
$default_courseId = 0;
$requiredRotationClass = 'required';
$sellocationId = 0;
$locationId = 0;
$objRotation = new clsRotation();
if (isset($_GET['id'])) //Edit Mode
{
	$rotationId = DecodeQueryData($_GET['id']);
	$copyrotationId = DecodeQueryData($_GET['copyrotationId']);
	$page_title = "Copy Rotation";
	$bedCrumTitle = 'Copy';

	$row = $objRotation->GetrotationDetails($rotationId, $currentSchoolId);

	if ($row == '') {
		header('location:addrotations.html');
		exit;
	}
	$title  = stripslashes($row['title']);
	$title = 'Copy of ' . $title;
	$courseId  = ($row['courseId']);
	$rotationId  = ($row['rotationId']);
	$parentRotationId = $row['parentRotationId'];
	$hospitalSiteId = $row['hospitalSiteId'];
	$courselocationId = $row['locationId'];
	$rotationLocationId = stripslashes($row['rotationLocationId']);

	$locationId = 0;
	if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
		if ($parentRotationId > 0) {
			if (!$rotationLocationId)
				$locationId = $objRotation->GetLocationByRotation($rotationId);
			else
				$locationId  = $rotationLocationId;
		}
	} else {
		$locationId  = $courselocationId;
	}

	//Get Time Zone By Rotation 
	$objLocation = new clsLocations();
	$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
	unset($objLocation);
	if ($TimeZone == '')
		$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

	$startDateTime = converFromServerTimeZone($row['startDate'], $TimeZone);
	$startTime = date('h:i A', strtotime($startDateTime));
	$startDate = date('m/d/Y', strtotime($startDateTime));

	$endDateTime = converFromServerTimeZone($row['endDate'], $TimeZone);
	$endTime = date('h:i A', strtotime($endDateTime));
	$endDate = date('m/d/Y', strtotime($endDateTime));

	$durationTime = ($row['duration']);
	//-------------------------------------------------------------------
	//This is sub rotation
	//-------------------------------------------------------------------
	$parentStartDate = '';
	$parentEndDate = '';
	if ($parentRotationId > 0) {
		$requiredRotationClass = '';
		$page_title = "Edit SubRotation";
		$bedCrumTitle = 'Edit';
		$parentStartDate = converFromServerTimeZone($row['parentStartDate'], $TimeZone);
		$parentStartDate = date('m/d/Y h:i A', strtotime($parentStartDate));
		$parentEndDate = converFromServerTimeZone($row['parentEndDate'], $TimeZone);
		$parentEndDate = date('m/d/Y h:i A', strtotime($parentEndDate));
	}
} else if (isset($_GET['perrotationId'])) {
	$page_title = "Copy SubRotation";
	$bedCrumTitle = 'Copy';
	$parentRotationId = $_GET['perrotationId'];
	$parentRotationId = DecodeQueryData($parentRotationId);
	$row = $objRotation->GetrotationDetails($parentRotationId, $currentSchoolId);

	if ($row == '') {
		header('location:addrotations.html');
		exit;
	}

	$parentStartDate = converFromServerTimeZone($row['startDate'], $TimeZone);
	$parentStartDate = date('m/d/Y h:i A', strtotime($parentStartDate));
	$parentEndDate = converFromServerTimeZone($row['endDate'], $TimeZone);
	$parentEndDate = date('m/d/Y h:i A', strtotime($parentEndDate));
	$parentlocationId =  stripslashes($row['locationId']);
}
//----------------------------------------------------------------------------------------
//Get parent rotations
//----------------------------------------------------------------------------------------
$parentRotations = $objRotation->GetParentRotations($currentSchoolId, $loggedUserLocationId, $parentRotationId);
//----------------------------------------------------------------------------------------
//Get Course Details
//----------------------------------------------------------------------------------------
if (isset($_GET['courseId']))
	$default_courseId = DecodeQueryData($_GET['courseId']);
$objCourses = new clsCourses();
$courses = $objCourses->GetCousesByLocation($currentSchoolId, $loggedUserLocationId, $default_courseId);
unset($objCourses);
//----------------------------------------------------------------------------------------
//Get Hospitals
//----------------------------------------------------------------------------------------
$objHospitalSite = new clsHospitalSite();
$hospitalSite = $objHospitalSite->GetAllHospitalSite($currentSchoolId);
unset($objHospitalSite);
unset($objRotation);

$objLocations = new clsLocations();
$locations = $objLocations->GetAlllocation($currentSchoolId);
unset($objLocations);
?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($page_title); ?></title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">


	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
					<?php
					$queryparams = "";
					if (isset($_GET['courseId'])) { ?>
						<li><a href="courses.html">Courses</a></li>
						<li><a href="rotations.html?courseId=<?php echo (EncodeQueryData($default_courseId)); ?>">Rotation</a></li>
					<?php
						$queryparams = "?courseId=" . $_GET['courseId'];
					} else if (isset($_GET['perrotationId']) || isset($_GET['perrotationId'])) { ?>
						<li><a href="rotations.html">Rotation</a></li>
						<li><a href="addsubrotation.html?rotationId=<?php echo (EncodeQueryData($parentRotationId)); ?>">Subrotation</a></li>


					<?php } else { ?>
						<li><a href="rotations.html<?php echo ($queryparams); ?>">Rotations</a></li>
					<?php  } ?>
					<li class="active"><?php echo ($bedCrumTitle); ?></li>
				</ol>
			</div>

		</div>
	</div>

	<div class="container">

		<?php
		if (isset($_GET["status"])) {
			if ($_GET["status"] == "error") {
		?>
				<div class="alert alert-danger alert-dismissible fade in" role="alert">
					<button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
					</button> Error occurred.
				</div>
		<?php   }
		} ?>

		<form id="frmRotations" data-parsley-validate class="form-horizontal" method="POST" action="copyrotationsubmit.html?id=<?php echo ($copyrotationId); ?>">
			<div class="row">

				<!-- Text input-->

				<div class="form-group mx-0">
					<!-- <label class="col-md-4 control-label" for="txtTitle"></label> -->
					<div class="col-md-12">
						<?php if (isset($_GET['id'])) //Edit Mode
						{
							if ($parentRotationId > 0) { ?>
								<label class="radio-inline">
									<input id="subrotation" class="type checkrotation" value="subrotation" name="type" type="radio">
									Hospital Site
								</label>
							<?php } else { ?>
								<label class="radio-inline col-md-4 control-label">
									<input id="rotation" class="type checkrotation" value="rotation" name="type" type="radio" checked>
									Rotation
								</label>
							<?php }
						} else {
							if (isset($_GET['perrotationId']) || isset($_GET['perrotationId'])) { ?>

								<label class="radio-inline">
									<input id="subrotation" class="type checkrotation" value="subrotation" name="type" type="radio">
									Hospital Site
								</label>

							<?php } else { ?>
								<label class="radio-inline col-md-4 control-label">
									<input id="rotation" class="type checkrotation" value="rotation" name="type" type="radio" checked>
									Rotation
								</label>
								<label class="radio-inline">
									<input id="subrotation" class="type checkrotation" value="subrotation" name="type" type="radio">
									Hospital Site
								</label>
						<?php }
						} ?>
					</div>

				</div>

				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="txtTitle">Title</label>
						<div class="col-md-12">
							<input id="txtTitle" name="txtTitle" value="<?php echo ($title); ?>" required type="text" placeholder="" class="form-control input-md required-input" required="">

						</div>

					</div>
				</div>
				<div class="col-md-6">
					<span class="row margin_zero" id="SubRotationcourse" <?php
																			if (isset($_GET['perrotationId']) || isset($_GET['perrotationId'])) { ?>style="display:none" <?php } ?>>
						<div class="form-group">
							<input type="hidden" name="default_courseId" id="default_courseId" value="<?php echo $default_courseId; ?>">
							<label class="col-md-12 control-label" for="cbocourses">Courses</label>
							<div class="col-md-12">
								<select id="cbocourses" name="cbocourses" class="form-control input-md required-input select2_single" required>
									<option value="" selected>Select</option>
									<?php
									if ($courses != "") {
										while ($row = mysqli_fetch_assoc($courses)) {
											$selcourseId  = $row['courseId'];
											$name  = stripslashes($row['title']);
											$courseEndDate  = stripslashes($row['courseEndDate']);

									?>
											<option value="<?php echo ($selcourseId);  ?>" <?php if ($courseId == $selcourseId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>



										<?php } ?>

									<?php }
									?>
								</select>
							</div>
						</div>
					</span>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<input type="hidden" name="default_courseId" id="default_courseId" value="<?php echo $default_courseId; ?>">
						<label class="col-md-12 control-label" for="cbohospitalsites">Hospital Site</label>
						<div class="col-md-12">
							<select id="cbohospitalsites" name="cbohospitalsites" class="form-control input-md required-input select2_single" required>
								<option value="" selected>Select</option>
								<?php
								if ($hospitalSite != "") {
									while ($row = mysqli_fetch_assoc($hospitalSite)) {
										$selhospitalSiteId  = $row['hospitalSiteId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($selhospitalSiteId); ?>" <?php if ($hospitalSiteId == $selhospitalSiteId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

								<?php

									}
								}
								?>
							</select>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<span class=" margin_zero" id="SubRotationSection">
						<div class="form-group">
							<input type="hidden" name="perrotationId" id="perrotationId" value="<?php echo ($parentRotationId); ?>">

							<label class="col-md-12 control-label" for="mainrotation">Rotations</label>
							<div class="col-md-12">
								<select id="mainrotation" name="mainrotation" class="form-control input-md  select2_single">
									<option value="" selected>Select Rotation</option>
									<?php
									while ($row = mysqli_fetch_assoc($parentRotations)) {
										$selrotationId  = $row['rotationId'];
										$name  = stripslashes($row['title']);

									?>
										<option value="<?php echo ($selrotationId); ?>" <?php if ($selrotationId == $parentRotationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

									<?php

									}
									?>
								</select>
								<?php if (isset($_GET['perrotationId'])) { ?>
									<input type="hidden" name="mainrotation" id="mainrotation" value="<?php echo $parentRotationId; ?>">
								<?php } ?>
							</div>
						</div>
						<div class="form-group">

							<label class="col-md-12 control-label" for="cbolocation">Location</label>
							<div class="col-md-12">
								<select id="cbolocation" name="cbolocation" class="form-control input-md  select2_single parentlocationId">
									<!--option value="<?php echo ($sellocationId); ?>" selected>Select</option-->
									<?php
									if ($locations != "") {
										while ($row = mysqli_fetch_assoc($locations)) {
											$sellocationId  = $row['locationId'];
											$name  = stripslashes($row['title']);

									?>
											<?php if (isset($_GET['perrotationId'])) { ?>
												<option value="<?php echo ($sellocationId); ?>" <?php if ($sellocationId == $parentlocationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
											<?php } else { ?>
												<option value="<?php echo ($sellocationId); ?>" <?php if ($locationId == $sellocationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
											<?php } ?>
									<?php

										}
									}
									?>
								</select>
							</div>
						</div>

					</span>
				</div>

				<div class="col-md-6">
					<div class="form-group sectionRotation">
						<label class="col-md-12 control-label" for="startDate">Start Date</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full relative' id='startDate'>
								<input type='text' name="startDate" id="startDate" class="form-control input-md required-input rotation_date dateInputFormat" value="<?php echo ($startDate); ?>" <?php echo $requiredRotationClass; ?> data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group sectionRotation">
						<label class="col-md-12 control-label" for="startTime">Start Time</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full relative' id='startTime'>
								<input type='text' name="startTime" id="startTime" class="form-control input-md required-input rotation_date" value="<?php echo date('h:i A', strtotime($startTime)); ?>" <?php echo $requiredRotationClass; ?> data-parsley-errors-container="#error-txtDate" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>

				<div class="col-md-6">
					<div class="form-group sectionRotation">
						<label class="col-md-12 control-label" for="endDate">End Date </label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full relative' id='endDate'>
								<input type='text' name="endDate" id="endDate" class="form-control input-md required-input rotation_date dateInputFormat" value="<?php echo ($endDate); ?>" <?php echo $requiredRotationClass; ?> data-parsley-errors-container="#error-txtendDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtendDate"></div>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group sectionRotation">
						<label class="col-md-12 control-label" for="endTime">End Time</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full relative' id='endTime'>
								<input type='text' name="endTime" id="endTime" class="form-control input-md required-input rotation_date" value="<?php echo date('h:i A', strtotime($endTime)); ?>" <?php echo $requiredRotationClass; ?> data-parsley-errors-container="#error-txtendDate" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtendDate"></div>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="duration">Duration In Hours.</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group Duration w-full' id='Duration'>
								<input style="display:none;" type="range" value="<?php echo ($durationTime); ?>" name="duration" id="duration" min="0" max="100" onchange="updateTextInput(this.value);">
								<input readonly class="form-control input-md w-full required-input" type="text" id="Inputrange" name="Inputrange" value="<?php echo ($durationTime); ?>">
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="row">

				<div class="form-group">
					<label class="col-md-12 control-label"></label>
					<div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px">
						<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Copy</button>
						<?php if (isset($_GET['rotationId'])) { ?>
							<a type="button" href="addsubrotation.html?rotationId=<?php echo EncodeQueryData($subrotationId); ?>" class="btn btn-default">Cancel</a>
						<?php } elseif (isset($_GET['courseId'])) { ?>
							<a type="button" href="rotations.html?courseId=<?php echo EncodeQueryData($default_courseId); ?>" class="btn btn-default">Cancel</a>

						<?php } else { ?>
							<a type="button" href="rotations.html" class="btn btn-default">Cancel</a>
						<?php  } ?>
					</div>
				</div>

			</div>

		</form>
	</div>

	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>


	<script type="text/javascript">
		$(window).load(function() {
			var TotalCheckboxCount = $('input[name="repeatdays[]"]').length;
			var CheckedCheckboxCount = $('input[name="repeatdays[]"]:checked').length;
			if (TotalCheckboxCount == CheckedCheckboxCount) {
				$('.selectall').prop('checked', true);
			} else {
				$('.selectall').prop('checked', false);
			}

			// for course drop down disabled 
			<?php if (isset($_GET['courseId'])) { ?>
				$('#cbocourses').val('<?php echo (DecodeQueryData($_GET['courseId'])); ?>').trigger('change');
				$('#cbocourses').prop('disabled', true);
			<?php } ?>

			<?php if (isset($_GET['perrotationId'])) { ?>
				$('#mainrotation').prop('disabled', true);
			<?php  } ?>


			<?php
			if ($parentRotationId > 0) {
			?>
				$('#subrotation').attr('checked', true);
				$('#subrotation').trigger('click');

			<?php } ?>


			$('#startDate').datetimepicker({
				useCurrent: false,
				inline: true,
				sideBySide: true,
				format: 'MM/DD/YYYY'

			});

			$('#endDate').datetimepicker({
				useCurrent: false,
				inline: true,
				sideBySide: true,
				format: 'MM/DD/YYYY'
			});
			$('#startTime').datetimepicker({
				format: 'hh:mm A',

			});

			$('#endTime').datetimepicker({
				format: 'hh:mm A',

			});

			<?php if ($rotationId > 0) { ?>
				var yearDate = new Date();
				yearDate.setFullYear(yearDate.getFullYear() + 1);
				$('#endDate').val(yearDate);
			<?php }
			if ($parentRotationId  > 0) { ?>

				$('#startDate').data('DateTimePicker').minDate('<?php echo $parentStartDate; ?>');
				$('#startDate').data('DateTimePicker').maxDate('<?php echo $parentEndDate; ?>');

				$('#endDate').data('DateTimePicker').minDate('<?php echo $parentStartDate ?>');
				$('#endDate').data('DateTimePicker').maxDate('<?php echo $parentEndDate ?>');

			<?php
			}
			?>

			//Set default date
			$(".select2_single").select2();
			$('#select2-cbocourses-container').addClass('required-select2');
			$('#select2-mainrotation-container').addClass('required-select2');
			$('#select2-cbohospitalsites-container').addClass('required-select2');


			$('#frmRotations').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {


					ShowProgressAnimation();
					return true; // Don't submit form for this demo
				});

			//Sub Rotation
			$('.type').trigger("change");

		});

		function updateTextInput(val) {
			document.getElementById('Inputrange').value = val;
		}

		$(".type").change(function() {

			if ($("#subrotation").prop("checked")) {

				document.getElementById("cbocourses").required = false;
				$('#SubRotationSection').removeClass('hide');
				$('#mainrotation').prop('required', true);
				// FOR HIDE COURSE DD AT SUBROTATION TIME
				$('#SubRotationcourse').addClass('hide');
				$('#SubRotationLocation').removeClass('hide');
				$('#mainrotation').removeAttr('required', true);

			} else if ($("#rotation").is(":checked")) {

				$('#mainrotation').removeAttr('required');
				$('#SubRotationSection').addClass('hide');
				// FOR SHOW COURSE DD AT ROTATION TIME
				$('#SubRotationcourse').removeClass('hide');
				$('#SubRotationLocation').addClass('hide');
				$('#mainrotation').removeAttr('required', true);
			}

		});

		<?php
		if (isset($_GET['perrotationId'])) {	?>
			document.getElementById("cbocourses").required = false;
			$('#mainrotation').prop('disabled', true);
		<?php }
		?>

		$(function() {

			$('#startDate').datetimepicker({
				format: 'MM/DD/YYYY'
			}).on('dp.change', function(e) {

				var incrementDay = moment(new Date(e.date));
				$('#endDate').data('DateTimePicker').minDate(incrementDay);
				$(this).data("DateTimePicker");

			});

			$('#endDate').datetimepicker({
				format: 'MM/DD/YYYY'
			}).on('dp.change', function(e) {
				var decrementDay = moment(new Date(e.date));
				decrementDay.subtract(1, 'days');
				$('#startDate').data('DateTimePicker').maxDate(decrementDay);
				$(this).data("DateTimePicker");

			});

			$('#startTime').datetimepicker({
				format: 'hh:mm A'
			}).on('dp.change', function(e) {
				calculateDuration();
			});

			$('#endTime').datetimepicker({
				format: 'hh:mm A'
			}).on('dp.change', function(e) {
				calculateDuration();
			});



		});


		//DONE BY Asharani 24 March 2021 Wednesday
		function calculateDuration() {

			start_date = new Date($('#startTime').data("DateTimePicker").date());
			date_ob = new Date(start_date);

			end_date = new Date($('#endTime').data("DateTimePicker").date());
			date_ob_end = new Date(end_date);

			// year as 4 digits (YYYY)
			var year = date_ob.getFullYear();

			// month as 2 digits (MM)
			var month = ("0" + (date_ob.getMonth() + 1)).slice(-2);

			// date as 2 digits (DD)
			var date = ("0" + date_ob.getDate()).slice(-2);

			// hours as 2 digits (hh)
			var hours = ("0" + date_ob.getHours()).slice(-2);

			// minutes as 2 digits (mm)
			var minutes = ("0" + date_ob.getMinutes()).slice(-2);

			// seconds as 2 digits (ss)
			var seconds = ("0" + date_ob.getSeconds()).slice(-2);

			var dateFuture = year + "/" + month + "/" + date + " " + hours + ":" + minutes + ":" + seconds;
			console.log(dateFuture);
			// End date

			// year as 4 digits (YYYY)
			var year = date_ob_end.getFullYear();

			// month as 2 digits (MM)
			var month = ("0" + (date_ob_end.getMonth() + 1)).slice(-2);

			// date as 2 digits (DD)
			var date1 = ("0" + date_ob_end.getDate()).slice(-2);
			var date = +date + +1;

			// hours as 2 digits (hh)
			var hours = ("0" + date_ob_end.getHours()).slice(-2);

			// minutes as 2 digits (mm)
			var minutes = ("0" + date_ob_end.getMinutes()).slice(-2);

			// seconds as 2 digits (ss)
			var seconds = ("0" + date_ob_end.getSeconds()).slice(-2);

			// date & time as YYYY-MM-DD hh:mm:ss format: 
			var dateNow = year + "/" + month + "/" + date + " " + hours + ":" + minutes + ":" + seconds;

			console.log(dateNow);

			function timeDiffCalc(dateNow, dateFuture) {
				let diffInMilliSeconds = Math.abs(dateFuture - dateNow) / 1000;

				// calculate days
				const days = Math.floor(diffInMilliSeconds / 86400);
				diffInMilliSeconds -= days * 86400;
				console.log('calculated days', days);

				// calculate hours
				const hours = Math.floor(diffInMilliSeconds / 3600) % 24;
				diffInMilliSeconds -= hours * 3600;
				console.log('calculated hours', hours);

				// calculate minutes
				const minutes = Math.floor(diffInMilliSeconds / 60) % 60;
				diffInMilliSeconds -= minutes * 60;
				console.log('minutes', minutes);

				let difference = '';
				if (days > 0) {
					difference += (days === 1) ? `${days} day, ` : `${days} days, `;
				}

				$('#Inputrange').val(hours + ":" + minutes);

				difference += (hours === 0 || hours === 1) ? `${hours} hour, ` : `${hours} hours, `;

				difference += (minutes === 0 || hours === 1) ? `${minutes} minutes` : `${minutes} minutes`;

				return difference;
			}

			console.log(timeDiffCalc(new Date(dateNow), new Date(dateFuture)));



		}

		$('#selectall').click(function() {
			//alert('');
			if ($(this).is(':checked')) {
				$('.uncheck').prop('checked', true);
			} else {
				$('.uncheck').removeAttr('checked');

			}
		});

		$('.checkedcount').click(function() {

			var TotalCheckboxCount = $('input[name="repeatdays[]"]').length;

			var CheckedCheckboxCount = $('input[name="repeatdays[]"]:checked').length;

			if (TotalCheckboxCount == CheckedCheckboxCount) {
				$('.selectall').prop('checked', true);
			} else {
				$('.selectall').prop('checked', false);
			}

		});
	</script>
</body>

</html>