<?php
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\IOFactory;

// $originalId = unserialize($individual_student);
$objprocedurecount = new clsProcedureCount();			
						$rowsprocedure = $objprocedurecount->GetProcedureDetailsForreport($currentSchoolId,$rotationId,$individual_student,$student_rank,$evaluator,$school_location,$hospital_site,$startDate,$endDate,$AscDesc,$sordorder);
						// $spreadsheet = new Spreadsheet();
						
						// Set document properties
						$spreadsheet->getProperties()->setCreator('Jacson Community College')
													 ->setLastModifiedBy('JCC')
													 ->setTitle('Reports')
													 ->setSubject('School Report')
													 ->setDescription('All School Reports');
													 
						//Active Sheet
						$spreadsheet->setActiveSheetIndex(0);
						$spreadsheet->getActiveSheet()->setTitle('Procedure Count Details Reports');				
						
						//Print Heading	
						
						$headerstyleArray = array('font'  => array('bold'  => true,'size'  => 16));
						
						$spreadsheet->getActiveSheet()->mergeCells("B2:K2");
						$spreadsheet->getActiveSheet()->setCellValue('B2', $schoolname);
						$spreadsheet->getActiveSheet()->getStyle('B2')->applyFromArray($headerstyleArray);
						$spreadsheet->getActiveSheet()->getStyle('B2')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						 $spreadsheet->getActiveSheet()
									 ->getStyle('B2')
									 ->getFill()
									 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
									 ->getStartColor()
									 ->setRGB('E0E0E0');
							
						$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];	
						$spreadsheet->getActiveSheet()->getStyle('B2:K2')->applyFromArray($styleBorderArray);
						
						$styleArray = array('font'  => array('bold'  => true,'size'  => 12));
						
						$spreadsheet->getActiveSheet()->mergeCells("B4:K4");
						$spreadsheet->getActiveSheet()->setCellValue('B4', 'Procedure Count Report');
						$spreadsheet->getActiveSheet()->getStyle('B4')->applyFromArray($styleArray);
						$spreadsheet->getActiveSheet()->getStyle('B4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						
						$spreadsheet->getActiveSheet()
									 ->getStyle('B4')
									 ->getFill()
									 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
									 ->getStartColor()
									 ->setRGB('E0E0E0');
									
						$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];	
						$spreadsheet->getActiveSheet()->getStyle('B4:K4')->applyFromArray($styleBorderArray);
						
						
						//Make Table Heading
						$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
						
						$spreadsheet->getActiveSheet()->setCellValue('B6', 'Student Name');
						$spreadsheet->getActiveSheet()->getStyle('B6')->applyFromArray($styleArray);
						$spreadsheet->getActiveSheet()->getStyle('B6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B6')->setWidth('25');
						
						$spreadsheet->getActiveSheet()->setCellValue('C6', 'Hospital Name');
						$spreadsheet->getActiveSheet()->getStyle('C6')->applyFromArray($styleArray);
						$spreadsheet->getActiveSheet()->getStyle('C6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
						$spreadsheet->getActiveSheet()->getDefaultColumnDimension('C6')->setWidth('25');
						
						$rowCount = 6;
						$columnNum = 'D';
						
						$rowsprocedureName = $objprocedurecount->GetTotalProcedures();
						
					   while($row = mysqli_fetch_array($rowsprocedureName))
						{
						 
						
						//Make Table Heading
						$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
						
						
						$spreadsheet->getActiveSheet()->setCellValue($columnNum.$rowCount, $row['procedures']); 
						//$spreadsheet->getActiveSheet()->getStyle('B6:K6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');

						$spreadsheet->getActiveSheet()->getStyle($columnNum.$rowCount)->applyFromArray($styleArray);
						
					
						
						$columnNum++;
						}	

						$spreadsheet->getActiveSheet()->getStyle('B6:DX6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
						$printStartRowCounter = 7;
						$rowsprocedurecount = ($rowsprocedure != '') ? mysqli_num_rows($rowsprocedure):0;

						if($rowsprocedurecount)
								{
									while($row = mysqli_fetch_array($rowsprocedure))
									{
									    
									    $studentId=$row['studentId'];
    									$firstName=$row['firstName'];
    									$lastName=$row['lastName'];
    									$fullName=$firstName.' '.$lastName; 
    									$hospitalTitle=$row['hospitalTitle']; 
    									
    									
        								$spreadsheet->getActiveSheet()->setCellValue('B'.$printStartRowCounter, $fullName);
        								$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
        								$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('C'.$printStartRowCounter, $hospitalTitle);
        								$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
        								$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$rowTotalIsol = $objprocedurecount->GetTotalProcedureCountForEquipClean($studentId);
										
										//FIRST
        							    $TotalO2Tx= $rowTotalIsol['TotalO2Tx'];
										$TotalO2Check= $rowTotalIsol['TotalO2Check'];
										$TotalBlandAer= $rowTotalIsol['TotalBlandAer'];
										$TotalNTSx= $rowTotalIsol['TotalNTSx'];
										$TotalNMT= $rowTotalIsol['TotalNMT'];
										$TotalMDI= $rowTotalIsol['TotalMDI'];
										$TotalDPI= $rowTotalIsol['TotalDPI'];
										$TotalIS= $rowTotalIsol['TotalIS'];
										$TotalSxInline= $rowTotalIsol['TotalSxInline'];

										//SECOND
										$TotalCPT= $rowTotalIsol['TotalCPT'];
										$TotalPEP= $rowTotalIsol['TotalPEP'];
										$TotalIPPB= $rowTotalIsol['TotalIPPB'];
										$TotalSxETT= $rowTotalIsol['TotalSxETT'];
										$TotalCuffChk= $rowTotalIsol['TotalCuffChk'];
										$TotalCPRBag= $rowTotalIsol['TotalCPRBag'];
										$TotalCPRComp= $rowTotalIsol['TotalCPRComp'];
										$TotalPEF= $rowTotalIsol['TotalPEF'];
										$TotalContAer= $rowTotalIsol['TotalContAer'];
										$TotalSxCSS= $rowTotalIsol['TotalSxCSS'];

										//THIRD
										$TotalTrach= $rowTotalIsol['TotalTrach'];
										$TotalVestThera= $rowTotalIsol['TotalVestThera'];
										$TotalMetaNeb= $rowTotalIsol['TotalMetaNeb'];
										$TotalPO= $rowTotalIsol['TotalPO'];
										$TotalABG= $rowTotalIsol['TotalABG'];
										$TotalALine= $rowTotalIsol['TotalALine'];
										$TotalRun= $rowTotalIsol['TotalRun'];
										$TotalAnaly= $rowTotalIsol['TotalAnaly'];
										$TotalParams= $rowTotalIsol['TotalParams'];
										$TotalEKG= $rowTotalIsol['TotalEKG'];

										//FORTH
										$TotalPFT= $rowTotalIsol['TotalPFT'];
										$TotalCapnog= $rowTotalIsol['TotalCapnog'];
										$TotalCXR= $rowTotalIsol['TotalCXR'];
										$TotalBronch= $rowTotalIsol['TotalBronch'];
										$TotalSleep= $rowTotalIsol['TotalSleep'];
										$TotalLungScan= $rowTotalIsol['TotalLungScan'];
										$TotalCTScan= $rowTotalIsol['TotalCTScan'];
										$TotalMRI= $rowTotalIsol['TotalMRI'];
										$TotalCathLab= $rowTotalIsol['TotalCathLab'];
										$TotalHolter= $rowTotalIsol['TotalHolter'];

										//FIFTH
										$TotalEEG= $rowTotalIsol['TotalEEG'];
										$TotalStressTest= $rowTotalIsol['TotalStressTest'];
										$TotalVentChk= $rowTotalIsol['TotalVentChk'];
										$TotalVentCircChg= $rowTotalIsol['TotalVentCircChg'];
										$TotalVentInit= $rowTotalIsol['TotalVentInit'];
										$TotalCPAP= $rowTotalIsol['TotalCPAP'];
										$TotalStatComp= $rowTotalIsol['TotalStatComp'];
										$TotalCritCareTrans= $rowTotalIsol['TotalCritCareTrans'];
										$TotalVentWean= $rowTotalIsol['TotalVentWean'];
										$TotalBiPAPNIPPV= $rowTotalIsol['TotalBiPAPNIPPV'];

										//SIX
										$TotalPSV= $rowTotalIsol['TotalPSV'];
										$TotalExtub= $rowTotalIsol['TotalExtub'];
										$TotalSecureETT= $rowTotalIsol['TotalSecureETT'];
										$TotalPMW= $rowTotalIsol['TotalPMW'];
										$TotalManualETT= $rowTotalIsol['TotalManualETT'];
										$TotalIntub= $rowTotalIsol['TotalIntub'];
										$TotalVentChg= $rowTotalIsol['TotalVentChg'];
										$TotalSBT= $rowTotalIsol['TotalSBT'];
										$TotalHFNC= $rowTotalIsol['TotalHFNC'];
										$TotalInlineNMT= $rowTotalIsol['TotalInlineNMT'];

										//SEVEN

										$TotalInlineMDI= $rowTotalIsol['TotalInlineMDI'];
										$TotalBiPAP= $rowTotalIsol['TotalBiPAP'];
										$TotalVent= $rowTotalIsol['TotalVent'];
										$TotalCBG= $rowTotalIsol['TotalCBG'];
										$TotalOxyhood= $rowTotalIsol['TotalOxyhood'];
										$TotalBubCPAP= $rowTotalIsol['TotalBubCPAP'];
										$TotalHFJV= $rowTotalIsol['TotalHFJV'];
										$TotalOscillator= $rowTotalIsol['TotalOscillator'];
										$TotalNitricOx= $rowTotalIsol['TotalNitricOx'];
										$TotalECMO= $rowTotalIsol['TotalECMO'];

										//EIGTH
										$TotalNHFNC = $rowTotalIsol['TotalNHFNC'];
										$TotalUAC= $rowTotalIsol['TotalUAC'];
										$TotalNABG= $rowTotalIsol['TotalNABG'];
										$TotalNAnaly= $rowTotalIsol['TotalNAnaly'];
										$TotalNALine= $rowTotalIsol['TotalNALine'];
										$TotalNO2= $rowTotalIsol['TotalNO2'];
										$TotalNBlandAer= $rowTotalIsol['TotalNBlandAer'];
										$TotalNNMT= $rowTotalIsol['TotalNNMT'];
										$TotalNNMTBB= $rowTotalIsol['TotalNNMTBB'];
										$TotalNInlineNMT= $rowTotalIsol['TotalNInlineNMT'];

										//NINE
										$TotalNInlineMDI= $rowTotalIsol['TotalNInlineMDI'];
										$TotalNMDI= $rowTotalIsol['TotalNMDI'];
										$TotalNCPT= $rowTotalIsol['TotalNCPT'];
										$TotalNDPI= $rowTotalIsol['TotalNDPI'];
										$TotalNEquipCln= $rowTotalIsol['TotalNEquipCln'];
										$TotalNPtAssess= $rowTotalIsol['TotalNPtAssess'];
										$TotalPPtAssess= $rowTotalIsol['TotalPPtAssess'];
										$TotalNBPtAssess= $rowTotalIsol['TotalNBPtAssess'];
										$TotalNVitals= $rowTotalIsol['TotalNVitals'];
										$TotalNIS= $rowTotalIsol['TotalNIS'];

										//TEN
										$TotalNPEP= $rowTotalIsol['TotalNPEP'];
										$TotalNTrach= $rowTotalIsol['TotalNTrach'];
										$TotalNSecureETT= $rowTotalIsol['TotalNSecureETT'];
										$TotalNNTS= $rowTotalIsol['TotalNNTS'];
										$TotalNSxInline= $rowTotalIsol['TotalNSxInline'];
										$TotalPCuffChk= $rowTotalIsol['TotalPCuffChk'];
										$TotalNIntub= $rowTotalIsol['TotalNIntub'];
										$TotalNExtub= $rowTotalIsol['TotalNExtub'];
										$TotalTcO2= $rowTotalIsol['TotalTcO2'];
										$TotalTcCO2= $rowTotalIsol['TotalTcCO2'];

										//ELEVAN
										$TotalSurfAdm= $rowTotalIsol['TotalSurfAdm'];
										$TotalPPD= $rowTotalIsol['TotalPPD'];
										$TotalSGT= $rowTotalIsol['TotalSGT'];
										$TotalFCN= $rowTotalIsol['TotalFCN'];
										$TotalNCPR= $rowTotalIsol['TotalNCPR'];
										$TotalPCPR= $rowTotalIsol['TotalPCPR'];
										$TotalNBCPR= $rowTotalIsol['TotalNBCPR'];
										$TotalCPAP= $rowTotalIsol['TotalCPAP'];
										$TotalNBiPAP= $rowTotalIsol['TotalNBiPAP'];
										$TotalNVentChk= $rowTotalIsol['TotalNVentChk'];

										//TWELVE
										$TotalNVentCircChg= $rowTotalIsol['TotalNVentCircChg'];
										$TotalNVentInit= $rowTotalIsol['TotalNVentInit'];
										$TotalNVentChg= $rowTotalIsol['TotalNVentChg'];
										$TotalNCritCareTrans= $rowTotalIsol['TotalNCritCareTrans'];
										$TotalPCritCareTrans= $rowTotalIsol['TotalPCritCareTrans'];

										//THIRTEEN
										$TotalIsol= $rowTotalIsol['TotalIsol'];
										$TotalEquipClean= $rowTotalIsol['TotalEquipClean'];
										$TotalO2Trans= $rowTotalIsol['TotalO2Trans'];
										$TotalHandWsh= $rowTotalIsol['TotalHandWsh'];
										$TotalMedRec= $rowTotalIsol['TotalMedRec'];
										$TotalO2Analy= $rowTotalIsol['TotalO2Analy'];
										$TotalApical= $rowTotalIsol['TotalApical'];
										$TotalPeriph= $rowTotalIsol['TotalPeriph'];
										$TotalBP= $rowTotalIsol['TotalBP'];
										$TotalPtAssess= $rowTotalIsol['TotalPtAssess'];
										$TotalPtVitals= $rowTotalIsol['TotalPtVitals'];

										
										//FIRST
										$spreadsheet->getActiveSheet()->setCellValue('D'.$printStartRowCounter, $TotalABG);
        								$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('E'.$printStartRowCounter, $TotalALine);
        								$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('F'.$printStartRowCounter, $TotalAnaly);
        								$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('G'.$printStartRowCounter, $TotalApical);
        								$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('H'.$printStartRowCounter, $TotalBiPAP);
        								$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('I'.$printStartRowCounter, $TotalBiPAPNIPPV);
        								$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('J'.$printStartRowCounter, $TotalBlandAer);
        								$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('K'.$printStartRowCounter, $TotalBP);
        								$spreadsheet->getActiveSheet()->getStyle('K'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('K'.$printStartRowCounter)->applyFromArray($styleArray);
        								
										$spreadsheet->getActiveSheet()->setCellValue('L'.$printStartRowCounter, $TotalBronch);
        								$spreadsheet->getActiveSheet()->getStyle('L'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('L'.$printStartRowCounter)->applyFromArray($styleArray);
										
										//SECOND

										$spreadsheet->getActiveSheet()->setCellValue('M'.$printStartRowCounter, $TotalBubCPAP);
        								$spreadsheet->getActiveSheet()->getStyle('M'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('M'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('N'.$printStartRowCounter, $TotalCapnog);
        								$spreadsheet->getActiveSheet()->getStyle('N'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('N'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('O'.$printStartRowCounter, $TotalCathLab);
        								$spreadsheet->getActiveSheet()->getStyle('O'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('O'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('P'.$printStartRowCounter, $TotalCBG);
        								$spreadsheet->getActiveSheet()->getStyle('P'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('P'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('Q'.$printStartRowCounter, $TotalContAer);
        								$spreadsheet->getActiveSheet()->getStyle('Q'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('Q'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('R'.$printStartRowCounter, $TotalCPAP);
        								$spreadsheet->getActiveSheet()->getStyle('R'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('R'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('S'.$printStartRowCounter, $TotalCPAP);
        								$spreadsheet->getActiveSheet()->getStyle('S'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('S'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('T'.$printStartRowCounter, $TotalCPRBag);
        								$spreadsheet->getActiveSheet()->getStyle('T'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('T'.$printStartRowCounter)->applyFromArray($styleArray);
        								
										$spreadsheet->getActiveSheet()->setCellValue('U'.$printStartRowCounter, $TotalCPRComp);
        								$spreadsheet->getActiveSheet()->getStyle('U'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('U'.$printStartRowCounter)->applyFromArray($styleArray);
										
										$spreadsheet->getActiveSheet()->setCellValue('V'.$printStartRowCounter, $TotalCPT);
        								$spreadsheet->getActiveSheet()->getStyle('V'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('V'.$printStartRowCounter)->applyFromArray($styleArray);
        								
										//THIRD

										$spreadsheet->getActiveSheet()->setCellValue('W'.$printStartRowCounter, $TotalCritCareTrans);
        								$spreadsheet->getActiveSheet()->getStyle('W'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('W'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('X'.$printStartRowCounter, $TotalCTScan);
        								$spreadsheet->getActiveSheet()->getStyle('X'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('X'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('Y'.$printStartRowCounter, $TotalCuffChk);
        								$spreadsheet->getActiveSheet()->getStyle('Y'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('Y'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('Z'.$printStartRowCounter, $TotalCXR);
        								$spreadsheet->getActiveSheet()->getStyle('Z'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('Z'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('AA'.$printStartRowCounter, $TotalDPI);
        								$spreadsheet->getActiveSheet()->getStyle('AA'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('AA'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('AB'.$printStartRowCounter, $TotalECMO);
        								$spreadsheet->getActiveSheet()->getStyle('AB'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('AB'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('AC'.$printStartRowCounter, $TotalEEG);
        								$spreadsheet->getActiveSheet()->getStyle('AC'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('AC'.$printStartRowCounter)->applyFromArray($styleArray);
        								
        								$spreadsheet->getActiveSheet()->setCellValue('AD'.$printStartRowCounter, $TotalEKG);
        								$spreadsheet->getActiveSheet()->getStyle('AD'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('AD'.$printStartRowCounter)->applyFromArray($styleArray);
        								
										$spreadsheet->getActiveSheet()->setCellValue('AE'.$printStartRowCounter, $TotalEquipClean);
        								$spreadsheet->getActiveSheet()->getStyle('AE'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AE'.$printStartRowCounter)->applyFromArray($styleArray);
										
										$spreadsheet->getActiveSheet()->setCellValue('AF'.$printStartRowCounter, $TotalExtub);
        								$spreadsheet->getActiveSheet()->getStyle('AF'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
        								$spreadsheet->getActiveSheet()->getStyle('AF'.$printStartRowCounter)->applyFromArray($styleArray);

										//FORTH

										$spreadsheet->getActiveSheet()->setCellValue('AG'.$printStartRowCounter, $TotalFCN);
										$spreadsheet->getActiveSheet()->getStyle('AG'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AG'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AH'.$printStartRowCounter, $TotalHandWsh);
										$spreadsheet->getActiveSheet()->getStyle('AH'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AH'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AI'.$printStartRowCounter, $TotalHFJV);
										$spreadsheet->getActiveSheet()->getStyle('AI'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AI'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AJ'.$printStartRowCounter, $TotalHFNC);
										$spreadsheet->getActiveSheet()->getStyle('AJ'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AJ'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AK'.$printStartRowCounter, $TotalHolter);
										$spreadsheet->getActiveSheet()->getStyle('AK'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AK'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AL'.$printStartRowCounter, $TotalInlineMDI);
										$spreadsheet->getActiveSheet()->getStyle('AL'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AL'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AM'.$printStartRowCounter, $TotalInlineNMT);
										$spreadsheet->getActiveSheet()->getStyle('AM'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AM'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AN'.$printStartRowCounter, $TotalIntub);
										$spreadsheet->getActiveSheet()->getStyle('AN'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AN'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AO'.$printStartRowCounter, $TotalIPPB);
										$spreadsheet->getActiveSheet()->getStyle('AO'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AO'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AP'.$printStartRowCounter, $TotalIS);
										$spreadsheet->getActiveSheet()->getStyle('AP'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AP'.$printStartRowCounter)->applyFromArray($styleArray);


										//FIFTH

										$spreadsheet->getActiveSheet()->setCellValue('AQ'.$printStartRowCounter, $TotalIsol);
										$spreadsheet->getActiveSheet()->getStyle('AQ'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AQ'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AR'.$printStartRowCounter, $TotalLungScan);
										$spreadsheet->getActiveSheet()->getStyle('AR'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AR'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AS'.$printStartRowCounter, $TotalManualETT);
										$spreadsheet->getActiveSheet()->getStyle('AS'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AS'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AT'.$printStartRowCounter, $TotalMDI);
										$spreadsheet->getActiveSheet()->getStyle('AT'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AT'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AU'.$printStartRowCounter, $TotalMedRec);
										$spreadsheet->getActiveSheet()->getStyle('AU'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AU'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AV'.$printStartRowCounter, $TotalMetaNeb);
										$spreadsheet->getActiveSheet()->getStyle('AV'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AV'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AW'.$printStartRowCounter, $TotalMRI);
										$spreadsheet->getActiveSheet()->getStyle('AW'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AW'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AX'.$printStartRowCounter, $TotalNABG);
										$spreadsheet->getActiveSheet()->getStyle('AX'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AX'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AY'.$printStartRowCounter, $TotalNALine);
										$spreadsheet->getActiveSheet()->getStyle('AY'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AY'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('AZ'.$printStartRowCounter, $TotalNAnaly);
										$spreadsheet->getActiveSheet()->getStyle('AZ'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('AZ'.$printStartRowCounter)->applyFromArray($styleArray);

										//SIXTH

										$spreadsheet->getActiveSheet()->setCellValue('BA'.$printStartRowCounter, $TotalNBiPAP);
										$spreadsheet->getActiveSheet()->getStyle('BA'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BA'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BB'.$printStartRowCounter, $TotalNBlandAer);
										$spreadsheet->getActiveSheet()->getStyle('BB'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BB'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BC'.$printStartRowCounter, $TotalNCPR);
										$spreadsheet->getActiveSheet()->getStyle('BC'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BC'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BD'.$printStartRowCounter, $TotalNCPT);
										$spreadsheet->getActiveSheet()->getStyle('BD'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BD'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BE'.$printStartRowCounter, $TotalNCritCareTrans);
										$spreadsheet->getActiveSheet()->getStyle('BE'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BE'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BF'.$printStartRowCounter, $TotalNDPI);
										$spreadsheet->getActiveSheet()->getStyle('BF'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BF'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BG'.$printStartRowCounter, $TotalNEquipCln);
										$spreadsheet->getActiveSheet()->getStyle('BG'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BG'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BH'.$printStartRowCounter, $TotalNExtub);
										$spreadsheet->getActiveSheet()->getStyle('BH'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BH'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BI'.$printStartRowCounter, $TotalNHFNC);
										$spreadsheet->getActiveSheet()->getStyle('BI'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BI'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BJ'.$printStartRowCounter, $TotalNInlineMDI);
										$spreadsheet->getActiveSheet()->getStyle('BJ'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BJ'.$printStartRowCounter)->applyFromArray($styleArray);

									
										//SEVEN

										$spreadsheet->getActiveSheet()->setCellValue('BK'.$printStartRowCounter, $TotalNInlineNMT);
										$spreadsheet->getActiveSheet()->getStyle('BK'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BK'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BL'.$printStartRowCounter, $TotalNIntub);
										$spreadsheet->getActiveSheet()->getStyle('BL'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BL'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BM'.$printStartRowCounter, $TotalNIS);
										$spreadsheet->getActiveSheet()->getStyle('BM'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BM'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BN'.$printStartRowCounter, $TotalNMDI);
										$spreadsheet->getActiveSheet()->getStyle('BN'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BN'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BO'.$printStartRowCounter, $TotalNNMT);
										$spreadsheet->getActiveSheet()->getStyle('BO'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BO'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BP'.$printStartRowCounter, $TotalNNMTBB);
										$spreadsheet->getActiveSheet()->getStyle('BP'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BP'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BQ'.$printStartRowCounter, $TotalNNTS);
										$spreadsheet->getActiveSheet()->getStyle('BQ'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BQ'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BR'.$printStartRowCounter, $TotalNO2);
										$spreadsheet->getActiveSheet()->getStyle('BR'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BR'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BS'.$printStartRowCounter, $TotalNPEP);
										$spreadsheet->getActiveSheet()->getStyle('BS'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BS'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BT'.$printStartRowCounter, $TotalNPtAssess);
										$spreadsheet->getActiveSheet()->getStyle('BT'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BT'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BU'.$printStartRowCounter, $TotalNSecureETT);
										$spreadsheet->getActiveSheet()->getStyle('BU'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BU'.$printStartRowCounter)->applyFromArray($styleArray);


										//EIGTH

										$spreadsheet->getActiveSheet()->setCellValue('BV'.$printStartRowCounter, $TotalNSxInline);
										$spreadsheet->getActiveSheet()->getStyle('BV'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BV'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BW'.$printStartRowCounter, $TotalNTrach);
										$spreadsheet->getActiveSheet()->getStyle('BW'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BW'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BX'.$printStartRowCounter, $TotalNVentChg);
										$spreadsheet->getActiveSheet()->getStyle('BX'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BX'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BY'.$printStartRowCounter, $TotalNVentChk);
										$spreadsheet->getActiveSheet()->getStyle('BY'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BY'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('BZ'.$printStartRowCounter, $TotalNVentCircChg);
										$spreadsheet->getActiveSheet()->getStyle('BZ'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('BZ'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CA'.$printStartRowCounter, $TotalNVentInit);
										$spreadsheet->getActiveSheet()->getStyle('CA'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CA'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CB'.$printStartRowCounter, $TotalNVitals);
										$spreadsheet->getActiveSheet()->getStyle('CB'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CB'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CC'.$printStartRowCounter, $TotalNBCPR);
										$spreadsheet->getActiveSheet()->getStyle('CC'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CC'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CD'.$printStartRowCounter, $TotalNBPtAssess);
										$spreadsheet->getActiveSheet()->getStyle('CD'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CD'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CE'.$printStartRowCounter, $TotalNitricOx);
										$spreadsheet->getActiveSheet()->getStyle('CE'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CE'.$printStartRowCounter)->applyFromArray($styleArray);

										//NINE

										$spreadsheet->getActiveSheet()->setCellValue('CF'.$printStartRowCounter, $TotalNMT);
										$spreadsheet->getActiveSheet()->getStyle('CF'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CF'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CG'.$printStartRowCounter, $TotalNTSx);
										$spreadsheet->getActiveSheet()->getStyle('CG'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CG'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CH'.$printStartRowCounter, $TotalO2Analy);
										$spreadsheet->getActiveSheet()->getStyle('CH'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CH'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CI'.$printStartRowCounter, $TotalO2Check);
										$spreadsheet->getActiveSheet()->getStyle('CI'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CI'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CJ'.$printStartRowCounter, $TotalO2Trans);
										$spreadsheet->getActiveSheet()->getStyle('CJ'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CJ'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CK'.$printStartRowCounter, $TotalO2Tx);
										$spreadsheet->getActiveSheet()->getStyle('CK'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CK'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CL'.$printStartRowCounter, $TotalOscillator);
										$spreadsheet->getActiveSheet()->getStyle('CL'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CL'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CM'.$printStartRowCounter, $TotalOxyhood);
										$spreadsheet->getActiveSheet()->getStyle('CM'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CM'.$printStartRowCounter)->applyFromArray($styleArray);


										//TEN

										$spreadsheet->getActiveSheet()->setCellValue('CN'.$printStartRowCounter, $TotalPCPR);
										$spreadsheet->getActiveSheet()->getStyle('CN'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CN'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CO'.$printStartRowCounter, $TotalPCritCareTrans);
										$spreadsheet->getActiveSheet()->getStyle('CO'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CO'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CP'.$printStartRowCounter, $TotalPCuffChk);
										$spreadsheet->getActiveSheet()->getStyle('CP'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CP'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CQ'.$printStartRowCounter, $TotalPPtAssess);
										$spreadsheet->getActiveSheet()->getStyle('CQ'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CQ'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CR'.$printStartRowCounter, $TotalParams);
										$spreadsheet->getActiveSheet()->getStyle('CR'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CR'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CS'.$printStartRowCounter, $TotalPEF);
										$spreadsheet->getActiveSheet()->getStyle('CS'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CS'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CT'.$printStartRowCounter, $TotalPEP);
										$spreadsheet->getActiveSheet()->getStyle('CT'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CT'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CU'.$printStartRowCounter, $TotalPeriph);
										$spreadsheet->getActiveSheet()->getStyle('CU'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CU'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CV'.$printStartRowCounter, $TotalPFT);
										$spreadsheet->getActiveSheet()->getStyle('CV'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CV'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CW'.$printStartRowCounter, $TotalPMW);
										$spreadsheet->getActiveSheet()->getStyle('CW'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CW'.$printStartRowCounter)->applyFromArray($styleArray);

										//ELEVENTH

										$spreadsheet->getActiveSheet()->setCellValue('CX'.$printStartRowCounter, $TotalPO);
										$spreadsheet->getActiveSheet()->getStyle('CX'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CX'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CY'.$printStartRowCounter, $TotalPPD);
										$spreadsheet->getActiveSheet()->getStyle('CY'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CY'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('CZ'.$printStartRowCounter, $TotalPSV);
										$spreadsheet->getActiveSheet()->getStyle('CZ'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('CZ'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DA'.$printStartRowCounter, $TotalPtAssess);
										$spreadsheet->getActiveSheet()->getStyle('DA'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DA'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DB'.$printStartRowCounter, $TotalPtVitals);
										$spreadsheet->getActiveSheet()->getStyle('DB'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DB'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DC'.$printStartRowCounter, $TotalRun);
										$spreadsheet->getActiveSheet()->getStyle('DC'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DC'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DD'.$printStartRowCounter, $TotalSBT);
										$spreadsheet->getActiveSheet()->getStyle('DD'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DD'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DE'.$printStartRowCounter, $TotalSecureETT);
										$spreadsheet->getActiveSheet()->getStyle('DE'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DE'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DF'.$printStartRowCounter, $TotalSGT);
										$spreadsheet->getActiveSheet()->getStyle('DF'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DF'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DG'.$printStartRowCounter, $TotalSleep);
										$spreadsheet->getActiveSheet()->getStyle('DG'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DG'.$printStartRowCounter)->applyFromArray($styleArray);


										//TWELVE

										$spreadsheet->getActiveSheet()->setCellValue('DH'.$printStartRowCounter, $TotalStatComp);
										$spreadsheet->getActiveSheet()->getStyle('DH'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DH'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DI'.$printStartRowCounter, $TotalStressTest);
										$spreadsheet->getActiveSheet()->getStyle('DI'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DI'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DJ'.$printStartRowCounter, $TotalSurfAdm);
										$spreadsheet->getActiveSheet()->getStyle('DJ'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DJ'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DK'.$printStartRowCounter, $TotalSxCSS);
										$spreadsheet->getActiveSheet()->getStyle('DK'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DK'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DL'.$printStartRowCounter, $TotalSxETT);
										$spreadsheet->getActiveSheet()->getStyle('DL'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DL'.$printStartRowCounter)->applyFromArray($styleArray);

										//THIRTEEN

										$spreadsheet->getActiveSheet()->setCellValue('DM'.$printStartRowCounter, $TotalSxInline);
										$spreadsheet->getActiveSheet()->getStyle('DM'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DM'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DN'.$printStartRowCounter, $TotalTcCO2);
										$spreadsheet->getActiveSheet()->getStyle('DN'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DN'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DO'.$printStartRowCounter, $TotalTcO2);
										$spreadsheet->getActiveSheet()->getStyle('DO'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DO'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DP'.$printStartRowCounter, $TotalTrach);
										$spreadsheet->getActiveSheet()->getStyle('DP'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DP'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DQ'.$printStartRowCounter, $TotalUAC);
										$spreadsheet->getActiveSheet()->getStyle('DQ'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DQ'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DR'.$printStartRowCounter, $TotalVent);
										$spreadsheet->getActiveSheet()->getStyle('DR'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DR'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DS'.$printStartRowCounter, $TotalVentChg);
										$spreadsheet->getActiveSheet()->getStyle('DS'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DS'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DT'.$printStartRowCounter, $TotalVentChk);
										$spreadsheet->getActiveSheet()->getStyle('DT'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DT'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DU'.$printStartRowCounter, $TotalVentCircChg);
										$spreadsheet->getActiveSheet()->getStyle('DU'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DU'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DV'.$printStartRowCounter, $TotalVentInit);
										$spreadsheet->getActiveSheet()->getStyle('DV'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DV'.$printStartRowCounter)->applyFromArray($styleArray);

										$spreadsheet->getActiveSheet()->setCellValue('DW'.$printStartRowCounter, $TotalVentWean);
										$spreadsheet->getActiveSheet()->getStyle('DW'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DW'.$printStartRowCounter)->applyFromArray($styleArray);


										$spreadsheet->getActiveSheet()->setCellValue('DX'.$printStartRowCounter, $TotalVestThera);
										$spreadsheet->getActiveSheet()->getStyle('DX'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
										$spreadsheet->getActiveSheet()->getStyle('DX'.$printStartRowCounter)->applyFromArray($styleArray);


								
    								    $printStartRowCounter++;	
								    }
								}  
								
								$printStartRowCounter--;	    
						
						$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];	
						$spreadsheet->getActiveSheet()->getStyle('B6:DX6'.$printStartRowCounter)->applyFromArray($styleBorderArray);
						
						// Auto size columns for each worksheet
					    foreach(range('B','DX') as $columnID)
						{
							$spreadsheet->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
						
						}
						$reportname='ProcedureCountDetailsReport_';

	// Set headers for .xlsx file download
header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
header('Content-Disposition: attachment; filename="' . $reportname . $today . '.xlsx"');
header('Cache-Control: max-age=0');

$sheet->setSelectedCell('A1'); // Set focus to A1

// Create the writer for .xlsx format and save to output
$writer = new Xlsx($spreadsheet); // OR use IOFactory (choose one method)
// $writer->save('php://output');
exit;
?>