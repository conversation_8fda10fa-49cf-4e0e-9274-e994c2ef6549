<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsCheckoffQuestionMaster.php');
include('../class/clscheckoff.php');
include('../setRequest.php');
// echo '<pre>';
// print_r($_POST);
// exit;

$schoolQuestionId = 0;
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
	if (isset($_GET['sectionId'])) {
		$schoolSectionId = DecodeQueryData($_GET['sectionId']);
	}

	$questionType  = ($_POST['questionType']);
	$question  = ($_POST['question']);
	$txtsinglechoice  = ($_POST['txtsinglechoice']);
	$txtsinglechoicemarks  = ($_POST['txtsinglechoicemarks']);
	$answersid  = ($_POST['answers']);
	$longans  = ($_POST['longans']);
	$yesnoanswers  = ($_POST['yesnoanswers']);
	$txtyesno  = ($_POST['txtyesno']);
	$SortOrder  = ($_POST['SortOrder']);
	$Marks  = ($_POST['Marks']);
	$checkboxes  = ($_POST['checkboxes']);
	$txtcheckbox  = ($_POST['txtcheckbox']);
	$checkboxanswers  = ($_POST['checkboxanswers']);



	$objQuestionMaster = new clsCheckoffQuestionMaster();
	$objQuestionMaster->schoolQuestionTitle = $question;
	$objQuestionMaster->schoolQuestionType = $questionType;
	$objQuestionMaster->schoolId = $currentSchoolId;
	$objQuestionMaster->sortOrder = $SortOrder;
	$objQuestionMaster->marks = $Marks;

	$RetQuestionId = $objQuestionMaster->SaveNewQuestionsMaster($schoolQuestionId);

	switch ($questionType) {
		case "2":
			foreach ($txtsinglechoice as $key => $value) {

				$objQuestionMaster->schoolQuestionId = $RetQuestionId;
				$objQuestionMaster->schoolOptionText = $value;
				$objQuestionMaster->schoolOptionValue = $answersid[$key];
				$objQuestionMaster->choiceAnswer = $txtsinglechoicemarks[$key];
				$QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
			}
			break;
		case "1":
			foreach ($txtyesno as $keys => $value) {

				$objQuestionMaster->schoolQuestionId = $RetQuestionId;
				$objQuestionMaster->schoolOptionText = $value;
				$objQuestionMaster->schoolOptionValue = $yesnoanswers[$keys];
				$objQuestionMaster->choiceAnswer = 0;
				$QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
			}
			break;
		case "3":
			foreach ($txtcheckbox as $key => $value) {

				$objQuestionMaster->schoolQuestionId = $RetQuestionId;
				$objQuestionMaster->schoolOptionText = $value;
				$objQuestionMaster->schoolOptionValue = $checkboxanswers[$key];
				$objQuestionMaster->choiceAnswer = 0;
				$QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
			}
			break;
		case "4":
			// foreach ($txtcheckbox as $key => $value) {

			$objQuestionMaster->schoolQuestionId = $RetQuestionId;
			$objQuestionMaster->schoolOptionText = '';
			$objQuestionMaster->schoolOptionValue = 1;
			$objQuestionMaster->choiceAnswer = 0;
			$QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
			// }
			break;
		case "6":
			// foreach ($txtcheckbox as $key => $value) {

			$objQuestionMaster->schoolQuestionId = $RetQuestionId;
			$objQuestionMaster->schoolOptionText = '';
			$objQuestionMaster->schoolOptionValue = 0;
			$objQuestionMaster->choiceAnswer = 0;
			$QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
			// }
			break;
	}

	unset($objQuestionMaster);

	if ($RetQuestionId > 0) {
		//Audit Log
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "step";
		$action = $objLog::ADD;
		$isSuperAdmin = isset($_SESSION['isCurrentSchoolSuperAdmin']) ? ($_SESSION['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;


		$objCheckoff = new clscheckoff();
		$objCheckoff->saveCompEvaluationAuditLog($RetQuestionId, $_SESSION['loggedUserId'], $userType, $action,  0, $type, $isSuperAdmin);
		unset($objCheckoff);

		unset($objLog);

		header('location:questions.html?sectionId=' . EncodeQueryData($schoolSectionId) . '&status=added');
		//header('location:questions.html?sectionId='.EncodeQueryData($schoolSectionId).'&status='.$status);	
		exit();
	} else {
		header('location:addquestion.html?status=error');
	}
}
