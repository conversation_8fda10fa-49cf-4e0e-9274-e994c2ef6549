<?php
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Fill;

$objMasteryEval = new clsMasteryEval();
$individual_student = unserialize($individual_student);
$rowsMastery = $objMasteryEval->GetAllMasteryEvalForExportReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder);

$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('Mastery Reports');

// Set document properties
$spreadsheet->getProperties()
    ->setCreator($schoolname)
    ->setLastModifiedBy('JCC')
    ->setTitle('Reports')
    ->setSubject('School Report')
    ->setDescription('All School Reports');

// Print Heading
$headerStyleArray = [
    'font' => [
        'bold' => true,
        'size' => 16,
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'E0E0E0'],
    ],
];
$borderStyleArray = [
    'borders' => [
        'allBorders' => [
            'borderStyle' => Border::BORDER_THIN,
        ],
    ],
];

$sheet->mergeCells("B2:M2");
$sheet->setCellValue('B2', $schoolname);
$sheet->getStyle('B2')->applyFromArray($headerStyleArray);
$sheet->getStyle('B2:M2')->applyFromArray($borderStyleArray);

$sheet->mergeCells("B4:M4");
$sheet->setCellValue('B4', 'Mastery Report');
$sheet->getStyle('B4')->applyFromArray($headerStyleArray);
$sheet->getStyle('B4:M4')->applyFromArray($borderStyleArray);

// Make Table Heading
$tableHeaderStyle = [
    'font' => [
        'bold' => true,
        'size' => 10,
    ],
    'alignment' => [
        'horizontal' => Alignment::HORIZONTAL_CENTER,
    ],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'E0E0E0'],
    ],
];

$columns = [
    'B' => 'First Name',
    'C' => 'Last Name',
    'D' => 'Rank',
    'E' => 'Student Signature',
    'F' => 'Rotation',
    'G' => 'Evaluator',
    'H' => 'Evaluator Signature',
    'I' => 'CPAP',
    'J' => 'Delivery Of Neonate',
    'K' => 'HFOV',
    'L' => 'Tracheostomy Care',
    'M' => 'Total Average',
];

foreach ($columns as $column => $title) {
    $sheet->setCellValue($column . '6', $title);
    $sheet->getStyle($column . '6')->applyFromArray($tableHeaderStyle);
    $sheet->getColumnDimension($column)->setAutoSize(true);
}

$printStartRowCounter = 7;
if ($rowsMastery) {
    $contentStyleArray = [
        'font' => ['size' => 10],
        'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT],
    ];

    while ($row = mysqli_fetch_array($rowsMastery)) {
        $sheet->setCellValue('B' . $printStartRowCounter, stripslashes($row['firstName']));
        $sheet->setCellValue('C' . $printStartRowCounter, stripslashes($row['lastName']));
        $sheet->setCellValue('D' . $printStartRowCounter, stripslashes($row['Ranktitle']));
        $sheet->setCellValue('E' . $printStartRowCounter, date('m/d/Y', strtotime($row['dateOfStudentSignature'])));
        $sheet->setCellValue('F' . $printStartRowCounter, stripslashes($row['rotationname']));
        $sheet->setCellValue('G' . $printStartRowCounter, stripslashes($row['clinicianFirstName'] . ' ' . $row['clinicianLastName']));
        $sheet->setCellValue('H' . $printStartRowCounter, date('m/d/Y', strtotime($row['evaluationDate'])));
        $sheet->setCellValue('I' . $printStartRowCounter, stripslashes($row['firstSectionAvg']));
        $sheet->setCellValue('J' . $printStartRowCounter, stripslashes($row['secondSectionAvg']));
        $sheet->setCellValue('K' . $printStartRowCounter, stripslashes($row['thirdSectionAvg']));
        $sheet->setCellValue('L' . $printStartRowCounter, stripslashes($row['fourthSectionAvg']));
        $sheet->setCellValue('M' . $printStartRowCounter, stripslashes($row['totalAvg']));

        $printStartRowCounter++;
    }
}

$printStartRowCounter--;
$sheet->getStyle('B6:M' . $printStartRowCounter)->applyFromArray($borderStyleArray);
$sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows

// Save the file
$reportname = 'MasteryReport_';


?>
