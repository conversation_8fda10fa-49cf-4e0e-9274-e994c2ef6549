<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsStudent.php');
include('../class/clsRotation.php');
include('../class/clsInteraction.php');
include('../class/clsLocations.php');
include('../class/clsStudentIncident.php');
include('../setRequest.php');


$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0;
$canvasStatus = isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';
$type = isset($_GET['type']) ? $_GET['type'] : '';
$loggedUserId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;

if ($type == 'canvas')
    $canvasStatus = 1;

$schoolId = 0;
$studentId = 0;
$rotationtitle = '';
$transchooldisplayName = '';
$DefaultrotationId = 0;


if (isset($_GET['schoolId'])) {
    $schoolId = $_GET['schoolId'];
    $schoolId = DecodeQueryData($schoolId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

$title = "Incident  ";

//For Rotation Site
$encodedRotationId = '';
if (isset($_GET['rotationId'])) {
    $encodedRotationId = $_GET['rotationId'];
    $DefaultrotationId = $_GET['rotationId'];
    $DefaultrotationId = DecodeQueryData($DefaultrotationId);
}

//For Clinical Site
$encodedStudentId = '';
if (isset($_GET['studentId'])) {
    $studentId = $_GET['studentId'];
    $encodedStudentId = $_GET['studentId'];
    $studentId = DecodeQueryData($studentId);
}

//For Rotation Title
$objrotation = new clsRotation();
$rowsrotation = $objrotation->GetrotationTitleForInteraction($DefaultrotationId, $schoolId);
$rotationtitle = $rowsrotation ? $rowsrotation['title'] : '';


//For Display All Incident List
$totalincident = 0;
$objIncident = new clsStudentIncident();
$rowsincident = $objIncident->GetAllStudentsiteIncidentMaster($DefaultrotationId, $studentId, $canvasStatus, $schoolId);
if ($rowsincident != '') {
    $totalincident = mysqli_num_rows($rowsincident);
}
unset($objIncident);

//For Student Name
$objStudent = new clsStudent();
$StudentName = $objStudent->GetSingleStudent($currentSchoolId, $studentId);
$studentfullname = $StudentName ? ($StudentName['firstName'] . ' ' . $StudentName['lastName']) : '';
unset($objStudent);

$status = isset($_GET["status"]) ? $_GET["status"] : '';
$rotationStatus = checkRotationStatus($DefaultrotationId);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <?php if ($type == 'canvas') { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Incident</li>
                    <?php } else { ?>

                        <li><a href="dashboard.html">Home</a></li>
                        <?php if ($studentId > 0) { ?>
                            <li class="active"><a href="clinical.html">Clinical</a></li>
                            <li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                            <li class="active">Incident</li>

                        <?php } else { ?>
                            <li class="active"><a href="rotations.html">Rotations</a></li>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                            <li class="active">Incident</li>

                        <?php } ?>
                    <?php } ?>

                </ol>
            </div>

            <?php if ($type != 'canvas' && $rotationStatus == 0) { ?>
                <div class="pull-right">
                    <?php if ($studentId > 0) {  ?>
                        <a class="btn btn-link" href="addincident.html?studentId=<?php echo EncodeQueryData($studentId); ?>">Add</a>
                    <?php } else { ?>
                        <a class="btn btn-link" href="addincident.html?rotationId=<?php echo EncodeQueryData($DefaultrotationId); ?>">Add</a>
                    <?php } ?>
                </div>
            <?php } ?>


        </div>
    </div>

    <div class="container">
        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Incident added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Incident updated successfully.
                </div>

            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Interaction deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }    ?>


        <div id="divTopLoading">Loading...</div>

        <?php if ($type != 'canvas') { ?>
            <div class="row margin_bottom_ten">
                <div class="col-md-8"></div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="col-md-6 control-label text-right" for="" style="margin-top:8px;text-align: end !important;">Canvas Status:</label>
                        <div class="col-md-6 padding_right_zero padding_left_zero">
                            <select id="canvasStatus" name="canvasStatus" class="form-control input-md required-input select2_single" studentId="<?php echo EncodeQueryData($currentstudentId); ?>">
                                <option value="" selected>All</option>
                                <option value="1" <?php if ($canvasStatus == 1) { ?> selected="true" <?php } ?>>Sent</option>
                                <option value="0" <?php if ($canvasStatus == 0 && $canvasStatus != '') { ?> selected="true" <?php } ?>>Not Sent</option>
                            </select>
                        </div>
                    </div>
                </div>

            </div>
        <?php } ?>

        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th style="text-align:left">Incident Date </th>
                    <th>Title </th>
                    <?php if ($type == 'canvas') { ?>
                        <th>Rotation</th>
                        <th>First Name</th>
                        <th>Last Name</th>
                    <?php } else if ($studentId > 0) { ?>
                        <th>Rotation</th>
                    <?php } else { ?>
                        <th>First Name</th>
                        <th>Last Name</th>
                    <?php } ?>
                    <th>Evaluator</th>
                    <?php if ($type != 'canvas') { ?>
                        <th style="text-align:center">Action</th>
                    <?php } ?>
                    <?php if ($isActiveCanvas && $type != 'canvas') { ?>
                        <th class="text-center">Canvas Status</th>
                    <?php } ?>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalincident > 0) {
                    while ($row = mysqli_fetch_array($rowsincident)) {
                        $studentSiteIncidentMasterId = $row['studentSiteIncidentMasterId'];
                        $incidentDate = $row['incidentDate'];
                        $rotationId = stripslashes($row['rotationId']);
                        $courselocationId = $row['locationId'];
                        $parentRotationId = stripslashes($row['parentRotationId']);
                        $rotationLocationId = stripslashes($row['rotationLocationId']);

                        $locationId = 0;
                        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                            if ($parentRotationId > 0) {
                                if (!$rotationLocationId)
                                    $locationId = $objrotation->GetLocationByRotation($rotationId);
                                else
                                    $locationId  = $rotationLocationId;
                            }
                        } else {
                            $locationId  = $courselocationId;
                        }

                        //Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);
                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
                        $incidentDate = converFromServerTimeZone($incidentDate, $TimeZone);
                        $incidentDate = date("m/d/Y", strtotime($incidentDate));
                        $title = $row['title'];
                        $rotationname = $row['rotationname'];
                        $DBstudentId = $row['studentId'];
                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $clinicianfullName = $firstName . '  ' . $lastName;
                        $studentfirstName = $row['studentfirstName'];
                        $studentlastName = $row['studentlastName'];
                        $studentfullName = $studentfirstName . '  ' . $studentlastName;

                        // For Canvas
                        $isSendToCanvas  = isset($row['isSendToCanvas']) ? $row['isSendToCanvas'] : 0;

                        $isSendToCanvasClass = 'isSendRecordToCanvas';
                        $isSentToCanvasClass = 'hide';
                        if ($isSendToCanvas) {
                            $isSendToCanvasClass = 'hide';
                            $isSentToCanvasClass = '';
                        }

                        // $isUserCanSendCompletedRecordToCanvas = 0;
                        // if($interactionClinicianDate != '-' || $interactionSchoolDate !='-')
                        $isUserCanSendCompletedRecordToCanvas = 1;

                        // -- End Canvas --//

                ?>
                        <tr>
                            <td>
                                <?php echo ($incidentDate); ?>
                            </td>

                            <td style="text-align:left">
                                <?php echo $title; ?>
                            </td>
                            <?php if ($type == 'canvas') { ?>
                                <td style="text-align: left"><?php echo $rotationname;  ?></td>
                                <td style="text-align: left"><?php echo $studentfirstName;  ?></td>
                                <td style="text-align: left"><?php echo $studentlastName;  ?></td>
                            <?php } elseif ($studentId > 0) { ?>
                                <td style="text-align: left"><?php echo $rotationname;  ?></td>
                            <?php } else { ?>
                                <td style="text-align: left"><?php echo $studentfirstName;  ?></td>
                                <td style="text-align: left"><?php echo $studentlastName;  ?></td>
                            <?php } ?>
                            <td><?php echo $clinicianfullName;  ?></td>

                            <?php if ($type != 'canvas') { ?>
                                <td style="text-align: center">
                                    <?php
                                    $rotationStatus = checkRotationStatus($rotationId);
                                    if ($rotationStatus) { ?>
                                        <a href="addincident.html?incidentId=<?php echo (EncodeQueryData($studentSiteIncidentMasterId)); ?>&rotationId=<?php echo (EncodeQueryData($DefaultrotationId)); ?> &view=V">View</a>

                                    <?php } elseif ($studentId > 0) { ?>
                                        <a href="addincident.html?incidentId=<?php echo (EncodeQueryData($studentSiteIncidentMasterId)); ?>&studentId=<?php echo (EncodeQueryData($studentId)); ?>">Edit</a>
                                    <?php } else { ?>
                                        <a href="addincident.html?incidentId=<?php echo (EncodeQueryData($studentSiteIncidentMasterId)); ?>&rotationId=<?php echo (EncodeQueryData($DefaultrotationId)); ?>">Edit</a>
                                    <?php } ?>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow" studentSiteIncidentMasterId="<?php echo EncodeQueryData($studentSiteIncidentMasterId); ?>">Delete</a>
                                </td>
                            <?php } ?>

                            <?php if ($isActiveCanvas && $type != 'canvas') {
                                if ($loggedUserSendRecordToCanvas && $isUserCanSendCompletedRecordToCanvas) { ?>
                                    <td class="text-center">
                                        <a href="javascript:void(0);" id="isSendToCanvas_<?php echo $studentSiteIncidentMasterId; ?>" class="<?php echo $isSendToCanvasClass; ?>" incidentDate="<?php echo $incidentDate; ?>" rotation="<?php echo $rotationname; ?>" incidentTitle="<?php echo $title; ?>" clinicianfullName="<?php echo $clinicianfullName; ?>" studentSiteIncidentMasterId="<?php echo $studentSiteIncidentMasterId; ?>" studentId="<?php echo $DBstudentId; ?>" studentFullName="<?php echo $studentfullName; ?>">
                                            Send to Canvas
                                        </a>
                                        <label for="" class="isSentToCanvas_<?php echo $studentSiteIncidentMasterId; ?> <?php echo $isSentToCanvasClass; ?>">Sent</label>

                                    </td>

                                <?php } else { ?>
                                    <td class="text-center"><label for="" class=""> -
                                            <?php //if($isSendToCanvas>0) { echo 'Sent'; } else { echo 'Send to canvas'; } 
                                            ?>
                                        </label></td>

                            <?php }
                            }
                            ?>
                        </tr>
                <?php
                    }
                }
                unset($objrotation);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();
        });

        <?php if (isset($_GET['rotationId'])) { ?>
            var current_datatable = $("#datatable-responsive").DataTable({
                "ordering": true,
                "order": [
                    [0, "desc"]
                ],

                "aoColumns": [{
                        "sWidth": "10%"
                    }, {
                        "sWidth": "20%"
                    }, {
                        "sWidth": "15%"
                    }, {
                        "sWidth": "15%",
                        "sClass": "alignCenter",

                    }, {
                        "sWidth": "20%",
                        "sClass": "alignCenter"

                    }, {
                        "sWidth": "20%",
                        "sClass": "alignCenter",
                        "bSortable": false
                    }
                    <?php if ($isActiveCanvas) { ?>, {
                            "sWidth": "10%"
                        }
                    <?php } ?>
                ]

            });
        <?php } else { ?>
            var current_datatable = $("#datatable-responsive").DataTable({
                "ordering": true,
                "order": [
                    [0, "desc"]
                ],

                "aoColumns": [{
                        "sWidth": "15%"
                    }, {
                        "sWidth": "25%"
                    }, {
                        "sWidth": "25%"
                    }, {
                        "sWidth": "20%",
                        "sClass": "alignCenter"

                    }, {
                        "sWidth": "15%",
                        "sClass": "alignCenter",
                        "bSortable": false
                    }
                    <?php if ($type == 'canvas') { ?>, {
                            "sWidth": "15%",
                            "sClass": "alignCenter",
                            "bSortable": false
                        }
                    <?php } ?>
                    <?php if ($isActiveCanvas && $type != 'canvas') { ?>, {
                            "sWidth": "10%"
                        }
                    <?php } ?>
                ]

            });

        <?php } ?>



        // ajax call for deleteAjaxRow


        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentSiteIncidentMasterId = $(this).attr('studentSiteIncidentMasterId');
            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1; //for Admin


            alertify.confirm('Incident: ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentSiteIncidentMasterId,
                        userId: userId,
                        isUser: isUser,
                        type: 'Incident'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $(document).on('click', '.isSendRecordToCanvas', function() {
            var that = this;
            var incidentDate = $(this).attr('incidentDate');
            var rotation = $(this).attr('rotation');
            var incidentTitle = $(this).attr('incidentTitle');
            var clinicianfullName = $(this).attr('clinicianfullName');
            var studentSiteIncidentMasterId = $(this).attr('studentSiteIncidentMasterId');
            var studentId = $(this).attr('studentId');
            var studentFullName = $(this).attr('studentFullName');
            var schoolId = "<?php echo $currentSchoolId; ?>";

            alertify.confirm('Incident', 'Continue with send record to Canvas?', function() {
                $(that).text('Loading..');
                $(that).prop('disabled', true);

                $.ajax({
                    type: "POST",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_record_to_canvas.html",
                    data: {

                        incidentDate: incidentDate,
                        rotation: rotation,
                        incidentTitle: incidentTitle,
                        clinicianfullName: clinicianfullName,
                        studentSiteIncidentMasterId: studentSiteIncidentMasterId,
                        studentFullName: studentFullName,
                        studentId: studentId,
                        schoolId: schoolId,
                        type: 'Incident'
                    },
                    success: function(response) {
                        if (response == 'Success') {
                            $(that).addClass('hide');
                            $('.isSentToCanvas_' + studentSiteIncidentMasterId).removeClass('hide')
                            alertify.success('Record Successfully Sent to Canvas.');
                        } else {
                            alertify.success(response);
                        }

                    }
                });
            }, function() {});

        });

        $("#canvasStatus").change(function() {
            var encodedRotationId = '<?php echo $encodedRotationId; ?>';
            var encodedStudentId = '<?php echo $encodedStudentId; ?>';

            var canvasStatus = $(this).val();
            if (encodedRotationId != '') {
                if (canvasStatus)
                    window.location.href = "incident.html?canvasStatus=" + canvasStatus + "&rotationId=" + encodedRotationId;
                else
                    window.location.href = "incident.html?rotationId=" + encodedRotationId;
            } else if (encodedStudentId != '') {
                if (canvasStatus)
                    window.location.href = "incident.html?canvasStatus=" + canvasStatus + "&studentId=" + encodedStudentId;
                else
                    window.location.href = "incident.html?studentId=" + encodedStudentId;
            }
        });
    </script>


</body>

</html>