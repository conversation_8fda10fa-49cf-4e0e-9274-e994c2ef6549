<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsSchool.php');
include('../setRequest.php');

require '../vendor/autoload.php';  // Include PhpSpreadsheet's autoloader

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;

$currentSchoolId = $_GET['currentSchoolId'];
$currentSchoolId = DecodeQueryData($currentSchoolId);
$objSchool = new clsSchool();
$GetSchoolName = $objSchool->GetSchoolNames($currentSchoolId);
$schoolname = $GetSchoolName['displayName'];

$active = (isset($_GET['active'])) ? DecodeQueryData($_GET['active']) : 0;
date_default_timezone_set('Asia/Kolkata');
$today = (date('m/d/Y, H:i A'));

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSchoolExport']))
    $reportType = $_POST['cboreporttype'];

switch ($reportType) {
    case "School_Report":
        $objSchool = new clsSchool();
        $rowsSchool = $objSchool->GetAllSchools();
        $spreadsheet = new Spreadsheet();

        // Set document properties
        $spreadsheet->getProperties()->setCreator('Schools')
            ->setLastModifiedBy('JCC')
            ->setTitle('Reports')
            ->setSubject('School Report')
            ->setDescription('All School Reports');

        // Active Sheet
        $sheet = $spreadsheet->getActiveSheet();
        $sheet->setTitle($schoolname);

        // Print Heading
        $headerStyleArray = ['font' => ['bold' => true, 'size' => 16]];

        $sheet->mergeCells("B2:G2");
        $sheet->setCellValue('B2', $schoolname);
        $sheet->getStyle('B2')->applyFromArray($headerStyleArray);
        $sheet->getStyle('B2')->getAlignment()->setHorizontal('center');

        $sheet->getStyle('B2')->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setRGB('E0E0E0');

        $borderStyleArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
        $sheet->getStyle('B2:G2')->applyFromArray($borderStyleArray);

        $styleArray = ['font' => ['bold' => true, 'size' => 12]];

        $sheet->mergeCells("B4:G4");
        $sheet->setCellValue('B4', 'All Schools Report');
        $sheet->getStyle('B4')->applyFromArray($styleArray);
        $sheet->getStyle('B4')->getAlignment()->setHorizontal('center');

        $sheet->getStyle('B4')->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setRGB('E0E0E0');

        $sheet->getStyle('B4:G4')->applyFromArray($borderStyleArray);

        // Make Table Heading
        $tableHeaderStyleArray = ['font' => ['bold' => true, 'size' => 10]];

        $headers = ['School Name', 'Contact Person', 'Email', 'Phone', 'Address1', 'Address2'];
        $columns = range('B', 'G');
        foreach ($columns as $index => $column) {
            $sheet->setCellValue($column . '6', $headers[$index]);
            $sheet->getStyle($column . '6')->applyFromArray($tableHeaderStyleArray);
            $sheet->getStyle($column . '6')->getAlignment()->setHorizontal('center');
        }

        $sheet->getStyle('B6:G6')->getFill()
            ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
            ->getStartColor()->setRGB('E0E0E0');

        $printStartRowCounter = 7;
        if ($rowsSchool) {
            $styleArray = ['font' => ['size' => 10]];
            while ($row = mysqli_fetch_array($rowsSchool)) {
                $sheet->setCellValue('B' . $printStartRowCounter, stripslashes($row['title']));
                $sheet->setCellValue('C' . $printStartRowCounter, stripslashes($row['contactPerson']));
                $sheet->setCellValue('D' . $printStartRowCounter, stripslashes($row['email']));
                $sheet->setCellValue('E' . $printStartRowCounter, stripslashes($row['phone']));
                $sheet->setCellValue('F' . $printStartRowCounter, stripslashes($row['address1']));
                $sheet->setCellValue('G' . $printStartRowCounter, stripslashes($row['address2']));

                $printStartRowCounter++;
            }
        }

        // Add Border
        $printStartRowCounter--;
        $sheet->getStyle('B6:G' . $printStartRowCounter)->applyFromArray($borderStyleArray);

        // Auto size columns for each worksheet
        foreach (range('B', 'G') as $columnID) {
            $sheet->getColumnDimension($columnID)->setAutoSize(true);
        }
        $reportname = 'SchoolReport_';
        break;

    default:
        echo "<b>Please Select Valid Type.</b>";
        exit;
}
$sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows

$currentDate = date('m_d_Y_h_i');
header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="' . $reportname . $today . '.xls"');
header("Cache-Control: max-age=0");

$writer = IOFactory::createWriter($spreadsheet, 'Xls');
$writer->save('php://output');
exit;
?>