<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsPEF.php');
include('../class/clsRotation.php');
include('../class/clsCourses.php');

$rotationId = 0;
$schoolId = 0;
$currentstudentId = 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$rotationId = 0;
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0;
$canvasStatus = isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';

//For Check Checkoff 
$isActiveCheckoff = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];

$activebtn = '';
$clsBtnActiveFloor = '';
$clsBtnActiveAdult = '';
$clsBtnActive = '';
if (isset($_GET['active']))
    $activebtn = $_GET['active'];

if ($activebtn == 'pef1')
    $clsBtnActiveFloor = "active";
elseif ($activebtn == 'pef2')
    $clsBtnActiveAdult = "active";
else
    $clsBtnActive = "active";


$courseId = 0;
//For Rotation 
$encodedRotationId = '';
$encodedStudentId = '';
if (isset($_GET['rotationId'])) {
    $encodedRotationId = $_GET['rotationId'];
    $rotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($rotationId);
} elseif (isset($_GET['studentId'])) {
    $encodedStudentId = $_GET['studentId'];
    $currentstudentId = $_GET['studentId'];
    $currentstudentId = DecodeQueryData($currentstudentId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

$directionType = isset($_GET['type']) ? $_GET['type'] : '';
if ($directionType == 'canvas')
    $canvasStatus = 1;

$title = "PEF Evaluation |" . $transchooldisplayName;

//For Case Study List
$objPEF = new clsPEF();
if ($activebtn == 'pef1')
    $getPEFdetails = $objPEF->GetAllPEF($schoolId, $rotationId, $currentstudentId, 'pef1');
elseif ($activebtn == 'pef2')
    $getPEFdetails = $objPEF->GetAllPEF($schoolId, $rotationId, $currentstudentId, 'pef2');
else
    $getPEFdetails = $objPEF->GetAllPEF($schoolId, $rotationId, $currentstudentId, '');

$totalPEF = 0;
if ($getPEFdetails != '') {
    $totalPEF = mysqli_num_rows($getPEFdetails);
}
unset($objPEF);

//For Student Name
$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname = $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'];
unset($objStudent);

//For Rotation Name
$objRotation = new clsRotation();
$RotationName = $objRotation->GetrotationDetails($rotationId, $schoolId);
$rotationtitle = $RotationName['title'];
unset($objRotation);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
</head>

<body>
    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <?php if ($directionType == 'canvas') { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">PEF Evaluation</li>
                    <?php } else { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <?php if ($rotationId != '') { ?>
                            <li><a href="rotations.html">Rotations</a></li>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                        <?php } else if ($currentstudentId > 0 && $isActiveCheckoff != 2) { ?>
                            <li><a href="clinical.html">clinical</a></li>
                            <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                        <?php } ?>
                        <li class="active">PEF Evaluation</li>
                    <?php } ?>
                </ol>
            </div>
        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> PEF Evaluation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> PEF Evaluation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Case Study deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <?php if ($directionType != 'canvas') { ?>
            <div class="row margin_bottom_ten margin_right_zero">
                <div class="btn-group pull-right" role="group" aria-label="First group">
                    <?php if ($rotationId > 0) { ?>
                        <a role="button" class="btn btn-primary <?php echo $clsBtnActive; ?>" href="pefList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">All</a>
                        <a role="button" class="btn btn-primary <?php echo $clsBtnActiveFloor; ?>" href="pefList.html?active=pef1&rotationId=<?php echo EncodeQueryData($rotationId); ?>"> PEF I </a>
                        <a role="button" class="btn btn-primary  <?php echo $clsBtnActiveAdult; ?>" href="pefList.html?active=pef2&rotationId=<?php echo EncodeQueryData($rotationId); ?>">PEF II</a>
                    <?php } else { ?>
                        <a role="button" class="btn btn-primary <?php echo $clsBtnActive; ?>" href="pefList.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">All</a>
                        <a role="button" class="btn btn-primary <?php echo $clsBtnActiveFloor; ?>" href="pefList.html?active=pef1&studentId=<?php echo EncodeQueryData($currentstudentId); ?>"> PEF I </a>
                        <a role="button" class="btn btn-primary  <?php echo $clsBtnActiveAdult; ?>" href="pefList.html?active=pef2&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">PEF II</a>
                    <?php } ?>
                </div>
            </div>
        <?php } ?>

        <div class="row margin_bottom_ten ">
            <?php if ($activebtn == 'pef1') {
                if ($currentstudentId > 0)
                    $hrefLink = 'addPEFEvaluation.html?studentId=' . EncodeQueryData($currentstudentId) . '&isPEFType=' . EncodeQueryData(0);
                else
                    $hrefLink = 'addPEFEvaluation.html?rotationId=' . EncodeQueryData($rotationId) . '&isPEFType=' . EncodeQueryData(0);
            ?>
                <a class="showall btn btn-success pull-right margin_right_fifteen" href="<?php echo $hrefLink; ?>"> Add </a>
            <?php } else if ($activebtn == 'pef2') {

                if ($currentstudentId > 0)
                    $hrefLink = 'addPEFEvaluation.html?studentId=' . EncodeQueryData($currentstudentId) . '&isPEFType=' . EncodeQueryData(1);
                else
                    $hrefLink = 'addPEFEvaluation.html?rotationId=' . EncodeQueryData($rotationId) . '&isPEFType=' . EncodeQueryData(1);
            ?>

                <a class="showall btn btn-success pull-right margin_right_fifteen" href="<?php echo $hrefLink; ?>"> Add </a>
            <?php } ?>
        </div>

        <div id="divTopLoading">Loading...</div>

        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Rotation</th>
                    <th class="text-center">PEF</th>
                    <th class="text-center">Student Signture Date</th>
                    <th class="text-center">Score</th>
                    <th class="text-center">Status</th>
                    <?php if ($directionType != 'canvas') { ?>
                        <th style="text-align: center">Action</th>
                    <?php } ?>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalPEF > 0) {
                    while ($row = mysqli_fetch_array($getPEFdetails)) {

                        $isType = $row['isType'];
                        if ($isType == 1)
                            $isPEF = 'PEF II';
                        else
                            $isPEF = 'PEF I';

                        $DBStudentId = $row['studentId'];
                        $isStatus = $row['isStatus'];
                        $score = $row['score'];
                        $studentPEFMasterId = $row['studentPEFMasterId'];
                        $studentfirstName = $row['studentfirstName'];
                        $studentlastName = $row['studentlastName'];
                        $studentfullName = $studentfirstName . ' ' . $studentlastName;

                        $evaluationDate = $row['evaluationDate'];
                        $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
                        $evaluationDate = date("m/d/Y", strtotime($evaluationDate));

                        $dateOfStudentSignature = $row['dateOfStudentSignature'];
                        if ($dateOfStudentSignature != '0000-00-00 00:00:00') {
                            $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                            $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                        } else {
                            $dateOfStudentSignature = "-";
                        }

                        $rotationName = $row['rotationName'];

                ?>
                        <tr>
                            <td><?php echo ($evaluationDate); ?></td>
                            <td><?php echo ($studentfirstName); ?></td>
                            <td><?php echo ($studentlastName); ?></td>
                            <td><?php echo ($rotationName); ?></td>
                            <td class="text-center"><?php echo ($isPEF); ?></td>
                            <td class="text-center"><?php echo ($dateOfStudentSignature); ?></td>
                            <td class="text-center"><?php echo ($score); ?></td>
                            <td class="text-center"><?php echo ($isStatus); ?></td>
                            <?php if ($directionType != 'canvas') { ?>
                                <td>
                                    <?php
                                    $rotationStatus = checkRotationStatus($rotationId);

                                    if ($rotationStatus) { ?>
                                        <a href="addPEFEvaluation.html?studentPEFMasterId=<?php echo EncodeQueryData($studentPEFMasterId); ?>&rotationId=<?php echo (EncodeQueryData($rotationId)); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>&isPEFType=<?php echo EncodeQueryData($isType) ?>&view=1">View</a>
                                    <?php } else { ?>

                                        <a href="addPEFEvaluation.html?studentPEFMasterId=<?php echo EncodeQueryData($studentPEFMasterId); ?>&rotationId=<?php echo (EncodeQueryData($rotationId)); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>&isPEFType=<?php echo EncodeQueryData($isType) ?>">Edit</a>
                                    <?php } ?>

                                    | <a href="javascript:void(0);" class="deleteAjaxRow" studentPEFMasterId="<?php echo EncodeQueryData($studentPEFMasterId); ?>">Delete</a>
                                </td>
                            <?php } ?>

                        </tr>
                <?php

                    }
                }
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });

        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentPEFMasterId = $(this).attr('studentPEFMasterId');


            alertify.confirm('PEF I: ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentPEFMasterId,
                        type: 'pef1Evalution'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });


        $(".select2_single").select2();

        var current_datatable = $("#datatable-responsive").DataTable({
            "scrollX": true,
            responsive: false,
            "ordering": true,
            "order": [
                [0, "desc"]
            ],
            "aoColumns": [{
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "30%"
                },
                {
                    "sWidth": "10%"
                },
                <?php if ($directionType != 'canvas') { ?>, {
                        "sWidth": "10%",
                        "bSortable": false
                    }
                <?php } ?>
                <?php if ($isActiveCanvas && $directionType != 'canvas') { ?>, {
                        "sWidth": "10%"
                    }
                <?php } ?>
            ]
        });

        $(document).on('click', '.isSendRecordToCanvas', function() {

            var that = this;
            var caseStudydate = $(this).attr('caseStudydate');
            var rotation = $(this).attr('rotation');
            var caseStudyName = $(this).attr('caseStudyName');
            var caseChiefComplaint = $(this).attr('caseChiefComplaint');
            var clinicianApprove = $(this).attr('clinicianApprove');
            var schoolApprove = $(this).attr('schoolApprove');
            var caseStudyId = $(this).attr('caseStudyId');
            var caseStudyType = $(this).attr('caseStudyType');
            var studentId = $(this).attr('studentId');
            var studentFullName = $(this).attr('studentFullName');
            var schoolId = "<?php echo $currentSchoolId; ?>";

            alertify.confirm('Case Study', 'Continue with send record to Canvas?', function() {
                $(that).text('Loading..');
                $(that).prop('disabled', true);
                $.ajax({
                    type: "POST",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_record_to_canvas.html",
                    data: {

                        caseStudydate: caseStudydate,
                        rotation: rotation,
                        caseStudyName: caseStudyName,
                        caseChiefComplaint: caseChiefComplaint,
                        clinicianApprove: clinicianApprove,
                        schoolApprove: schoolApprove,
                        caseStudyId: caseStudyId,
                        caseStudyType: caseStudyType,
                        studentFullName: studentFullName,
                        studentId: studentId,
                        schoolId: schoolId,
                        type: 'CaseStudy'
                    },
                    success: function(response) {
                        if (response == 'Success') {
                            $(that).addClass('hide');
                            $('.isSentToCanvas_' + caseStudyId).removeClass('hide')
                            alertify.success('Record Successfully Sent to Canvas.');
                        } else {
                            alertify.success(response);
                        }

                    }
                });
            }, function() {});

        });

        $("#canvasStatus").change(function() {
            var canvasStatus = $(this).val();
            var encodedRotationId = '<?php echo $encodedRotationId; ?>';
            var encodedStudentId = '<?php echo $encodedStudentId; ?>';


            if (encodedRotationId != '') {
                if (canvasStatus)
                    window.location.href = "interaction.html?canvasStatus=" + canvasStatus + "&rotationId=" + encodedRotationId;
                else
                    window.location.href = "interaction.html?rotationId=" + encodedRotationId;
            } else if (encodedStudentId != '') {
                if (canvasStatus)
                    window.location.href = "interaction.html?canvasStatus=" + canvasStatus + "&studentId=" + encodedStudentId;
                else
                    window.location.href = "interaction.html?studentId=" + encodedStudentId;
            }

        });
    </script>


</body>

</html>