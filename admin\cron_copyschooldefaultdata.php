<?php

// $ServerRootPath	= '/www/wwwroot/rt.clinicaltrac.net';
$ServerRootPath    = 'C:/xampp/htdocs/clinicaltrac';

include($ServerRootPath . '/includes/config.php');
include($ServerRootPath . '/includes/commonfun.php');
include($ServerRootPath . '/class/clsDB.php');
include($ServerRootPath . '/class/clsSchool.php');
include($ServerRootPath . '/class/clsStudent.php');
include($ServerRootPath . '/class/clsLocations.php');
include($ServerRootPath . '/class/clsSystemUserRoleMaster.php');
include($ServerRootPath . '/class/clsStudentRankMaster.php');
include($ServerRootPath . '/class/clsClinicianRoleMaster.php');
include($ServerRootPath . '/class/clsSemester.php');
include($ServerRootPath . '/class/clsHospitalSite.php');
include($ServerRootPath . '/class/clsSystemUser.php');
include($ServerRootPath . '/class/clsTopic.php');
include($ServerRootPath . '/class/clsFormative.php');
include($ServerRootPath . '/class/clsCoarc.php');
include($ServerRootPath . '/class/clsGraduateCoarc.php');
include($ServerRootPath . '/class/clsPersonnelCoarc.php');
include($ServerRootPath . '/class/clsEmployerCoarc.php');
include($ServerRootPath . '/class/clsSummative.php');
include($ServerRootPath . '/class/clsMidterm.php');
include($ServerRootPath . '/class/clsDaily.php');
include($ServerRootPath . '/class/clsCIevaluation.php');
include($ServerRootPath . '/class/clsPEvaluation.php');
include($ServerRootPath . '/class/clsSiteevaluation.php');
include($ServerRootPath . '/class/clsEquipment.php');
include($ServerRootPath . '/class/clsMasteryEval.php');
include($ServerRootPath . '/class/clsJuniorMidtermPerformanceEval.php');
include($ServerRootPath . '/class/clsSrMidtermProfessionalEval.php');
include($ServerRootPath . '/class/clsImmunizationMaster.php');
include($ServerRootPath . '/class/clsStudentIncident.php');
include($ServerRootPath . '/class/clsschoolclinicalsiteunit.php');
include_once($ServerRootPath . '/class/clsSendEmails.php');
include($ServerRootPath . "/class/PasswordHash.php");
include($ServerRootPath . "/class/clsSMTPSettings.php");
include($ServerRootPath . '/class/clsPerformance.php');
include($ServerRootPath . '/class/clsOrientationChecklist.php');
include($ServerRootPath . '/class/clsClinicalEval.php');



$objTopic = new clsTopic();
$objTopic->CopyDefaultTopicMaster(142, 1);
unset($objTopic);
exit;
//Get Default Copy  Schools
$objSchool = new clsSchool();
$rowSchoolList = $copiedSchoolIdList = $objSchool->GetDefaultCopySchools();

if ($rowSchoolList != "") {
	$objDB = new clsDB();
	while ($rowSchool = mysqli_fetch_array($rowSchoolList)) {

		$schoolId = $rowSchool['schoolId'];
		$isActiveCheckoff = $rowSchool['isActiveCheckoffForStudent'];

		//Start Copy Process			
		$objDB->UpdateSingleColumnValueToTable('schools', 'isDefaultCopyProcessing', '1', 'schoolId', $schoolId);


		if ($rowSchool['code'] == '') {
			$code =  create_school_code($schoolId);
			$objSchool->UpdateSchoolCode($schoolId, $code);
		}

		//Contact Name
		$cFirstName = '';
		$cLastName = '';
		$arryContactPerson = explode(' ', $rowSchool['contactPerson']);
		if (isset($arryContactPerson[0])) {
			$cFirstName = $arryContactPerson[0];
		}
		if (isset($arryContactPerson[1])) {
			$cLastName = $arryContactPerson[1];
		}

		//Generate Username
		$cUsername = $cFirstName;
		$arryEmail = explode('@', $rowSchool['contactEmail']);
		if (isset($arryEmail[0])) {
			$cUsername = $arryEmail[0];
		}
		//--------------------------------------------------------------------
		//Add Default Role
		//--------------------------------------------------------------------
		$objSystemUserRoleMaster = new clsSystemUserRoleMaster();

		$objSystemUserRoleMaster->title = "P.D.";
		$objSystemUserRoleMaster->description = "Program director";
		$objSystemUserRoleMaster->isPrimaryRole = 1;
		$objSystemUserRoleMaster->schoolId = $schoolId;
		$objSystemUserRoleMaster->sortOrder = '2';
		$objSystemUserRoleMaster->type = 'Pd';
		$objSystemUserRoleMaster->createdBy = 0;
		$systemUserRoleMasterId = $objSystemUserRoleMaster->SaveSystemUserRole();

		$objSystemUserRoleMaster->title = "D.C.E.";
		$objSystemUserRoleMaster->description = "Director Clinician Education";
		$objSystemUserRoleMaster->isPrimaryRole = 1;
		$objSystemUserRoleMaster->schoolId = $schoolId;
		$objSystemUserRoleMaster->sortOrder = '3';
		$objSystemUserRoleMaster->type = 'D';
		$objSystemUserRoleMaster->createdBy = 0;
		$systemUserRoleMasterId = $objSystemUserRoleMaster->SaveSystemUserRole();

		$objSystemUserRoleMaster->title = "Coordinator ";
		$objSystemUserRoleMaster->description = "Coordinator ";
		$objSystemUserRoleMaster->isPrimaryRole = 1;
		$objSystemUserRoleMaster->schoolId = $schoolId;
		$objSystemUserRoleMaster->sortOrder = '4';
		$objSystemUserRoleMaster->type = 'C';
		$objSystemUserRoleMaster->createdBy = 0;
		$systemUserRoleMasterId = $objSystemUserRoleMaster->SaveSystemUserRole();

		$objSystemUserRoleMaster->title = "Preceptor";
		$objSystemUserRoleMaster->description = "Preceptor";
		$objSystemUserRoleMaster->isPrimaryRole = 1;
		$objSystemUserRoleMaster->schoolId = $schoolId;
		$objSystemUserRoleMaster->sortOrder = '5';
		$objSystemUserRoleMaster->type = 'P';
		$objSystemUserRoleMaster->createdBy = 0;
		$systemUserRoleMasterId = $objSystemUserRoleMaster->SaveSystemUserRole();

		$objSystemUserRoleMaster->title = "Administrator";
		$objSystemUserRoleMaster->description = "Administrator";
		$objSystemUserRoleMaster->isPrimaryRole = 1;
		$objSystemUserRoleMaster->schoolId = $schoolId;
		$objSystemUserRoleMaster->sortOrder = '1';
		$objSystemUserRoleMaster->type = 'A';
		$objSystemUserRoleMaster->createdBy = 0;
		$systemUserRoleMasterId = $objSystemUserRoleMaster->SaveSystemUserRole();

		unset($objSystemUserRoleMaster);
		//--------------------------------------------------------------------					
		//Add default Location
		//--------------------------------------------------------------------					
		$objLocation =  new clsLocations();
		$objLocation->schoolId = $schoolId;
		$objLocation->title = $rowSchool['title'];
		$objLocation->address1 = $rowSchool['address1'];
		$objLocation->address2 = $rowSchool['address2'];
		$objLocation->city = $rowSchool['city'];
		$objLocation->stateId = $rowSchool['stateId'];
		$objLocation->zip = $rowSchool['zip'];
		$objLocation->phone = $rowSchool['phone'];
		$objLocation->email = $rowSchool['contactEmail'];
		$objLocation->contactPerson = $rowSchool['contactPerson'];
		$objLocation->createdBy = '';
		$objLocation->createdDate = (date('Y-m-d H:i:s'));
		$defaultLocationId = $objLocation->SaveLocation(0);

		unset($objLocation);
		//--------------------------------------------------------------------										
		//Add default studnet rank
		//--------------------------------------------------------------------					
		$defaultrankId = 0;
		$objStudentRankMaster = new clsStudentRankMaster();
		foreach (unserialize(DEFAULT_RANK) as $Key => $value) {
			$objStudentRankMaster->title = $Key; // DEFAULT TITLE
			$objStudentRankMaster->schoolId = $schoolId;
			$objStudentRankMaster->sordOrder = $value;
			$defaultrankId = $objStudentRankMaster->SaveStudentRank(0);
		}
		unset($objStudentRankMaster);
		//--------------------------------------------------------------------					
		//Add default clincian role
		//--------------------------------------------------------------------					
		$defaultroleId = 0;
		$objclinicianrolemaster = new clsClinicianRoleMaster();

		foreach (unserialize(DEFAULT_CLINCIAN_ROLE) as $value) {

			$objclinicianrolemaster->title = $value[0]; // DEFAULT TITLE
			$objclinicianrolemaster->schoolId = $schoolId;
			$objclinicianrolemaster->sortOrder = $value[1];
			$objclinicianrolemaster->type = $value[2];
			$defaultroleId = $objclinicianrolemaster->SaveClinicianRole(0);
		}
		unset($objclinicianrolemaster);
		//--------------------------------------------------------------------										
		//Add default Semester
		//--------------------------------------------------------------------					
		$defaultSemesterId = 0;
		$objSemester = new clsSemester();
		foreach (unserialize(DEFAULT_SEMESTER) as $Key => $value) {
			$objSemester->title = $Key; // DEFAULT TITLE
			$objSemester->schoolId = $schoolId;
			$objSemester->sortOrder = $value;
			$defaultSemesterId = $objSemester->SaveSemester(0);
		}
		unset($objSemester);
		//--------------------------------------------------------------------
		//Add default hospital
		//--------------------------------------------------------------------
		$objHospitalSite =  new clsHospitalSite();
		$objHospitalSite->schoolId = $schoolId;
		$objHospitalSite->title = $rowSchool['title'];
		$objHospitalSite->address1 = $rowSchool['address1'];
		$objHospitalSite->address2 = $rowSchool['address2'];
		$objHospitalSite->city = $rowSchool['city'];
		$objHospitalSite->stateId = $rowSchool['stateId'];
		$objHospitalSite->zip = $rowSchool['zip'];
		$objHospitalSite->phone = $rowSchool['phone'];
		$objHospitalSite->email = $rowSchool['contactEmail'];
		$objHospitalSite->contactPerson = $rowSchool['contactPerson'];
		$objHospitalSite->createdBy = 0;
		$objHospitalSite->createdDate = (date('Y-m-d H:i:s'));
		$defaultHospitalSiteId = $objHospitalSite->SaveHospitalSite(0);

		unset($objHospitalSite);
		//--------------------------------------------------------------------

		//--------------------------------------------------------------------
		//Add Peer To Peer hospital site only for standard school - Done by Asharani - Date(24/04/2021)
		//--------------------------------------------------------------------
		if ($isActiveCheckoff == 1) {
			$objHospitalSite =  new clsHospitalSite();
			$objHospitalSite->schoolId = $schoolId;
			$objHospitalSite->title = "Peer To Peer";
			$objHospitalSite->address1 = $rowSchool['address1'];
			$objHospitalSite->address2 = $rowSchool['address2'];
			$objHospitalSite->city = $rowSchool['city'];
			$objHospitalSite->stateId = $rowSchool['stateId'];
			$objHospitalSite->zip = $rowSchool['zip'];
			$objHospitalSite->phone = $rowSchool['phone'];
			$objHospitalSite->email = $rowSchool['contactEmail'];
			$objHospitalSite->contactPerson = $rowSchool['contactPerson'];
			$objHospitalSite->createdBy = 0;
			$objHospitalSite->isPeerToPeer = 1;
			$objHospitalSite->createdDate = (date('Y-m-d H:i:s'));
			$defaultHospitalSiteId = $objHospitalSite->SaveHospitalSite(0);

			unset($objHospitalSite);
		}
		//--------------------------------------------------------------------

		//Add default system User
		//Generate Password					
		$password = GenerateRandomAlphaNumericNumber(6);
		$passwordHash = PasswordHash::hash($password);

		$objSystemUser = new clsSystemUser();
		$objSystemUser->firstName = $cFirstName;
		$objSystemUser->lastName = $cLastName;
		$objSystemUser->schoolId = $schoolId;
		$objSystemUser->username = $cUsername;
		$objSystemUser->passwordHash = $passwordHash;
		$objSystemUser->email = $rowSchool['contactEmail'];
		$objSystemUser->phone = $rowSchool['phone'];
		$objSystemUser->cellPhone = $rowSchool['cellPhone'];
		$objSystemUser->address1 = $rowSchool['address1'];
		$objSystemUser->address2 = $rowSchool['address2'];
		$objSystemUser->stateId = $rowSchool['stateId'];
		$objSystemUser->city = $rowSchool['city'];
		$objSystemUser->zip = $rowSchool['zip'];
		$objSystemUser->isActive = 1;
		$objSystemUser->isPrimaryUser = 1;
		$objSystemUser->systemUserRoleMasterId = $systemUserRoleMasterId;
		$objSystemUser->createdBy = 0;
		$objSystemUser->createdDate = date('Y-m-d H:i:s');
		$objSystemUser->SaveSystemUser();

		unset($objSystemUser);
		//--------------------------------------------------------------------
		//Add Default SMTP 
		//--------------------------------------------------------------------					
		$objSmtp = new clsSMTPSettings();

		$objSmtp->schoolId = $schoolId;
		//$objSmtp->SMTPHost ='server.clinicaltrac.net';
		$objSmtp->SMTPHost = '***************';
		$objSmtp->SMTPUserName = '<EMAIL>';
		$objSmtp->SMTPPassword = '153237#154237';
		$objSmtp->SMTPPort = '587';
		$objSmtp->SMTPFromName = 'Support';
		$objSmtp->SMTPFromEmail = '<EMAIL>';
		$objSmtp->type = 'S';

		$objSmtp->SaveSMTPSetting();
		unset($objSmtp);
		//--------------------------------------------------------------------
		//Add default Student
		//--------------------------------------------------------------------
		/*$objStudent= new clsStudent();
				$objStudent->schoolId = $schoolId;
				$objStudent->rankId = $defaultrankId;
				$objStudent->firstName = "James";
				$objStudent->lastName = "Doe";
				$objStudent->username = "student";
				$passwordHash = PasswordHash::hash(GenerateRandomAlphaNumericNumber(6));
				$objStudent->passwordHash = $passwordHash;
				$objStudent->email = $rowSchool['contactEmail'];
				$objStudent->phone = $rowSchool['phone'];
				$objStudent->address1 = $rowSchool['address1'];
				$objStudent->address2 = $rowSchool['address2'];
				$objStudent->stateId = $rowSchool['stateId'];
				$objStudent->city = $rowSchool['city'];
				$objStudent->zip = $rowSchool['zip'];
				$objStudent->isActive = "1";
				$objStudent->locationId = $defaultLocationId;
				$objStudent->createdBy = 0;
				$objStudent->createdDate = date('Y-m-d H:i:s');
				$objStudent->SaveStudent(0);

				unset($objStudent);*/
		//--------------------------------------------------------------------
		//Add default section
		//--------------------------------------------------------------------
		$objTopic = new clsTopic();
		$objTopic->CopyDefaultTopicMaster($schoolId, $isActiveCheckoff);
		unset($objTopic);
		//--------------------------------------------------------------------
		//--------------------------------------------------------------------					
		//Add default School Formative Question
		//--------------------------------------------------------------------
		$objFormative = new clsFormative();
		$objFormative->CopyAllFormativeQuestionMaster($schoolId);

		unset($objFormative);

		//--------------------------------------------------------------------					
		//Add default School Summative Question
		//--------------------------------------------------------------------
		$objSummative = new clsSummative();
		$objSummative->CopyAllSummativeQuestionMaster($schoolId);
		unset($objSummative);
		//--------------------------------------------------------------------

		//--------------------------------------------------------------------					
		//Add default School Daily/Weekly Question
		//--------------------------------------------------------------------
		$objDaily = new clsDaily();
		$objDaily->CopyAllDailyQuestionMaster($schoolId);
		unset($objDaily);
		//--------------------------------------------------------------------

		//--------------------------------------------------------------------					
		//Add default School Mastery Question
		//--------------------------------------------------------------------
		if ($isActiveCheckoff == 0) {
			$objMasteryEval = new clsMasteryEval();
			$objMasteryEval->CopyAllMasteryQuestionMaster($schoolId);
			unset($objMasteryEval);
		}
		//--------------------------------------------------------------------

		//--------------------------------------------------------------------					
		//Add default School Mastery Question
		//--------------------------------------------------------------------
		// if ($isActiveCheckoff == 0) {
		$objMasteryEval = new clsJuniorMidtermPerformanceEval();
		$objMasteryEval->CopyAllJuniorMidtermPerformanceEvaluationQuestionMaster($schoolId);
		unset($objMasteryEval);
		// }
		//--------------------------------------------------------------------

		//--------------------------------------------------------------------					
		//Add dSrMidtermProfessionalEval Question
		//--------------------------------------------------------------------
		$objSrMidtermProfessionalEval = new clsSrMidtermProfessionalEval();
		$objSrMidtermProfessionalEval->CopyAllSeniorMidtermProfessionalEvaluationQuestionMaster($schoolId);
		unset($objSrMidtermProfessionalEval);
		// }
		//--------------------------------------------------------------------

		//Add default School CIevaluation Question
		//--------------------------------------------------------------------				 
		$objCIevaluation = new clsCIevaluation();
		$objCIevaluation->CopyAllCIEvalQuestionMaster($schoolId, $isActiveCheckoff);
		unset($objCIevaluation);
		//--------------------------------------------------------------------	

		//Add default School Pevaluation Question
		//--------------------------------------------------------------------				 
		$objPevaluation = new clsPEvaluation();
		$objPevaluation->CopyAllPEvalQuestionMaster($schoolId, $isActiveCheckoff);
		unset($objPevaluation);
		//--------------------------------------------------------------------	

		//Add default School Siteevaluation Question
		//--------------------------------------------------------------------				
		$objSiteevaluation = new clsSiteevaluation();
		$objSiteevaluation->CopyAllSiteEvalQuestionMaster($schoolId);
		unset($objSiteevaluation);
		//--------------------------------------------------------------------					
		//Add default School Equipment Question
		//--------------------------------------------------------------------				
		$objEquipment = new clsEquipment();
		$objEquipment->CopyAllEquipmentQuestionMaster($schoolId);
		unset($objEquipment);
		//--------------------------------------------------------------------					
		//Add default School Incident Question
		//--------------------------------------------------------------------		

		$objIncident = new clsStudentIncident();
		$objIncident->CopyAllIncedentQuestionMaster($schoolId);
		unset($objIncident);
		//--------------------------------------------------------------------				
		//Add Default Clinical Site Unit
		//--------------------------------------------------------------------
		$objschoolclinicalsiteunit = new clsschoolclinicalsiteunit();
		$objschoolclinicalsiteunit->title = $rowSchool['title'];
		$objschoolclinicalsiteunit->CopyAllDefaultClinicalSiteUnit($schoolId);
		unset($objschoolclinicalsiteunit);
		//--------------------------------------------------------------------				
		//Add Default Coarc
		//--------------------------------------------------------------------
		$objCoarc = new clsCoarc();
		$objCoarc->CopyDefaultCoarcTopicMaster($schoolId);
		unset($objCoarc);
		//--------------------------------------------------------------------				
		//Add Default PersonnelCoarc
		//--------------------------------------------------------------------
		$objPersonnelCoarc = new clsPersonnelCoarc();
		$objPersonnelCoarc->CopyDefaultPersonnelCoarcQuestionMaster($schoolId);
		unset($objPersonnelCoarc);
		//--------------------------------------------------------------------
		//--------------------------------------------------------------------				
		//Add Default GraduatelCoarc
		//--------------------------------------------------------------------
		$objGraduateCoarc = new clsGraduateCoarc();
		$objGraduateCoarc->CopyDefaultgraduateCoarcQuestionMaster($schoolId);
		unset($objGraduateCoarc);
		//--------------------------------------------------------------------				
		//Add Default EmployerCoarc
		//--------------------------------------------------------------------
		$objEmployerCoarc = new clsEmployerCoarc();
		$objEmployerCoarc->CopyDefaultEmployerCoarcQuestionMaster($schoolId);
		unset($objEmployerCoarc);
		//--------------------------------------------------------------------					
		//Add default School Midterm Question
		//--------------------------------------------------------------------
		$objMidterm = new clsMidterm();
		$objMidterm->CopyAllMidtermQuestionMaster($schoolId);

		unset($objMidterm);

		//--------------------------------------------------------------------
		//Add Default Immunization
		//--------------------------------------------------------------------

		$objImmunizationMaster = new clsImmunizationMaster();
		$objImmunizationMaster->CopyAllDefaultImmunization($schoolId);
		unset($objImmunizationMaster);

		//--------------------------------------------------------------------
		//Add School Performance Evaluation Questions
		//--------------------------------------------------------------------
		$objPerformance = new clsPerformance();
		$objPerformance->CopyAllperformanceEvaluationQuestionMaster($schoolId);
		unset($objPerformance);

		//--------------------------------------------------------------------
		//clinical evaluation questions
		//--------------------------------------------------------------------
		$objClinicalEval = new clsClinicalEval();
		$objClinicalEval->CopyAllClinicalEvaluationQuestionMaster($schoolId);
		unset($objClinicalEval);

		//--------------------------------------------------------------------
		//Add Orientation Checklist Question
		//--------------------------------------------------------------------
		$objOrientation = new clsOrientationChecklist();
		$objOrientation->CopyAllOrientationChecklistQuestionMasterForNewSchool($schoolId);
		unset($objOrientation);

		//--------------------------------------------------------------------
		//Complete Copy Process
		//--------------------------------------------------------------------
		$objDB->UpdateSingleColumnValueToTable('schools', 'isDefaultCopyProcessing', '0', 'schoolId', $schoolId);
		$objDB->UpdateSingleColumnValueToTable('schools', 'isCopiedDefaultData', '1', 'schoolId', $schoolId);
		//--------------------------------------------------------------------	

		//YOUR SCHOOL SET IS READY EMAIL
		//--------------------------------------------------------------------
		$objSendEmails = new clsSendEmails($schoolId);
		$objSendEmails->SendWelcomeEmailtoAdmin($password);
		unset($objSendEmails);
		//--------------------------------------------------------------------

		//Copy Personal coarc survey options
		$objPersonnelCoarc = new clsPersonnelCoarc();
		$Coarcquestion = $objPersonnelCoarc->GetAllSurveyQuestionBySchoolId($schoolId);
		unset($objPersonnelCoarc);
	}


	unset($objDB);
	unset($objSchool);
}

$objSchool = new clsSchool();
$rowSchools = $copiedSchoolIdList = $objSchool->GetpersonnalSchools();
if ($rowSchools != "") {
	$objDB = new clsDB();
	while ($rowSchool = mysqli_fetch_array($rowSchools)) {
		$schoolId =  $rowSchool["schoolId"];
		$objPersonnelCoarc = new clsPersonnelCoarc();
		$Coarcquestion = $objPersonnelCoarc->GetAllSurveyQuestionBySchoolId($schoolId);
		unset($objPersonnelCoarc);
	}
}
unset($objSchool);
