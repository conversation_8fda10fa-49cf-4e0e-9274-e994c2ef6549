<?php
	include('includes/validateUserLogin.php'); 	
    include('../includes/config.php'); 
	include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsClinician.php'); 
    include('../class/clsSchool.php');	
	include('../setRequest.php');

	if( isset($_GET['userId']))
	{
		$schoolId=$currentSchoolId;
		$clinicianId=DecodeQueryData($_GET['userId']);
		$_SESSION["loggedAsClinicianBackUserId"]=$_SESSION["loggedUserId"];
		
		//Get User Details
		$objClinician = new clsClinician();
		$rowClinician =  $objClinician->GetClinicianDetails($clinicianId);
		
		

		if($rowClinician !="")
		{

			//------------------------------------------------------------------------------------------
			// Get school Details
			//------------------------------------------------------------------------------------------
			$objSchool = new clsSchool();
			$schoolDetails = $objSchool->GetschoolDetails($schoolId);
			$schoolSlug = stripslashes($schoolDetails['slug']);
			unset($objSchool);

			$dynamicLoginURL = BASE_PATH.'/school/'.$schoolSlug.'/clinician/dashboard.html';
			
			//------------------------------------------------------------------------------------------
			// check isSystemUserRole Primary


			$clinicianId = $rowClinician['clinicianId'];

			//Get System User profile Image
			$profileImageName = stripslashes($rowClinician['smallProfilePic']);
			$defaultProfileImagePath = GetClinicianImagePath($clinicianId,$currentSchoolId,$profileImageName);

			$profileLargeImageName = stripslashes($rowClinician['profilePic']);
			$_SESSION['loggedClinicianLocationId']=($rowClinician['locationId']);
			$defaultProfileLargeImagePath = GetClinicianImagePath($clinicianId,$currentSchoolId,$profileLargeImageName);


			$clinicianTimeZone = $objClinician->GetClinicianTimeZoneByClinicianId($clinicianId);
			if($clinicianTimeZone == '')
				$timezone = $rowClinician['timezone'];
			else
				$timezone = $clinicianTimeZone;

			//Start Session
			//-----------------------------------
			@session_start();
			$_SESSION["loggedClinicianId"] = $clinicianId;
			$_SESSION["loggedClinicianName"] = stripslashes($rowClinician['username']);
			$_SESSION["loggedClinicianFirstName"] =  stripslashes($rowClinician['firstName']);
			$_SESSION["loggedClinicianLastName"] = stripslashes($rowClinician['lastName']);
			$_SESSION["loggedClinicianSchoolId"] = stripslashes($rowClinician['schoolId']);
			$_SESSION["isActiveCheckoff"] = stripslashes($rowClinician['isActiveCheckoff']);
			$_SESSION["loggedClinicianSchoolTimeZone"] = $timezone;
			$_SESSION["loggedClinicianType"] = ($rowClinician['loggedClinicianType']);
			$_SESSION["loggedClinicianProfileImagePath"] = $defaultProfileImagePath;
			$_SESSION["loggedClinicianProfileLargeImagePath"] = $defaultProfileLargeImagePath;
			$_SESSION["isDefaultCiEval"] = ($rowClinician['isDefaultCiEval']);
		

			//------------------------------------------------------------------------------------------

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = $objLog::LOGINAS;	
			$userType = isset($_SESSION['loggedAsBackUserId']) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to ADMIN
			$uId = isset($_SESSION['loggedAsBackUserId']) ?  $_SESSION['loggedAsBackUserId'] : $_SESSION['loggedAsClinicianBackUserId'];
			$IsMobile = 0;

			$objClinician = new clsClinician();
			$objClinician->saveClinicianAuditLog($clinicianId, $uId, $userType, $action, $IsMobile);

			unset($objLog);
			//Audit Log End

			header("location:".$dynamicLoginURL);
			exit();
		}
		else
		{
			header('location:schoolusers.html?Error=7');
		}

		unset($objClinician);
	}
	else
	{
		header('location:schoolusers.html?Error=2');
	}
	exit();
?>