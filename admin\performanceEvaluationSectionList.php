<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsPerformance.php');
include('../setRequest.php');

$currentSchoolId;
//For CI Section
$totalSection = 0;
$performanceSection = '';

$loggedUserId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;

$objPerformance = new clsPerformance();   
if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
$performanceSection = $objPerformance->GetAllDefaultSections();
else // For School Admin
$performanceSection = $objPerformance->GetSectionsSetting($currentSchoolId);

$totalSection = ($performanceSection != '') ? mysqli_num_rows($performanceSection)  : 0;
$loggedAsBackUserId = isset($_SESSION["loggedAsBackUserId"]) ? $_SESSION["loggedAsBackUserId"] : 0;
$loggedUserIsRolePrimary = isset($_SESSION["loggedUserIsRolePrimary"]) ? $_SESSION["loggedUserIsRolePrimary"] : 0;

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Performance Evaluation Section</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">Performance Evaluation</li>
                    <li class="active">Sections</li>
                </ol>
            </div>
            <?php
            if ($loggedAsBackUserId || $currentSchoolId == 1) {
            ?>
                <div class="pull-right">
                    <ol class="breadcrumb">
                        <li><a href="addPerformanceEvalSection.html">Add</a></li>
                    </ol>
                </div>
            <?php
            }
            ?>

        </div>
    </div>

    <div class="container">
        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Performance Evaluation Section added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Performance Evaluation Section updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
            <?php
            } else if ($_GET["status"] == "StatusUpdated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Status updated successfully.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th style="text-align: center">Sort Order</th>
                    <th>Section Title</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalSection > 0) {
                    while ($row = mysqli_fetch_array($performanceSection)) {
                        $sectionMasterId = $row['sectionId'];
                        $title = $row['title'];
                        $sortOrder = $row['sortOrder'];

                        if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
                            $questionCount = $objPerformance->GetPerformanceEvalQuestionCountbySections($sectionMasterId);
                        else // For School Admin 
                            $questionCount = $objPerformance->GetSchoolPerformanceEvalQuestionCountbySections($currentSchoolId, $sectionMasterId);

                        $isActive = isset($row['isActive']) ? $row['isActive'] : 0;

                        $displayStatus = "";
                        $updateStatus = "0";
                        $buttoncss = "btn-primary";

                        if ($isActive == "1") {
                            $displayStatus = "Active";
                            $updateStatus = "0";
                            $buttoncss = "text-primary";
                        } else {
                            $displayStatus = "Inactive";
                            $updateStatus = "1";
                            $buttoncss = "text-warning";
                        }

                ?>
                        <tr>
                            <td style="text-align: center"><?php echo ($sortOrder); ?></td>
                            <td><?php echo ($title); ?></td>
                            <td style="text-align: center">
                                <a class="<?php echo ($buttoncss); ?>" href="updateallevaluationsectionstatus.html?isEvalType=PEval&id=<?php echo (EncodeQueryData($sectionMasterId)); ?>&newStatus=<?php echo ($updateStatus); ?>&type=status&userId=<?php echo $loggedUserId; ?>&isUser=1">
                                    <?php echo ($displayStatus); ?>
                                </a>
                                |
                                <a href="performanceEvaluationQuestionList.html?sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>">Steps</a>
                                <span class="badge"><?php echo ($questionCount); ?></span>
                                |
                                <a href="addPerformanceEvalSection.html?editid=<?php echo EncodeQueryData($sectionMasterId); ?>">Edit</a>
                                <?php if (isset($_SESSION["loggedAsBackUserId"])) { ?>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow" sectionMasterId="<?php echo EncodeQueryData($sectionMasterId); ?>" title="<?php echo ($title); ?>">Delete</a>

                                <?php } ?>
                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objJrPerformanceEval);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $('.cliniciandetails').magnificPopup({
            'type': 'ajax',
        });

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();
        });

        var current_datatable = $("#datatable-responsive").DataTable({});

        //Delete Section
        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var sectionMasterId = $(this).attr('sectionMasterId');
            var title = $(this).attr('title');
            var isCurrentSchoolSuperAdmin = '<?php echo $isCurrentSchoolSuperAdmin; ?>';
            var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';
            var isUser = 1; //for Admin


            alertify.confirm('Performance Evaluation Section: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: sectionMasterId,
                        type: 'PEval',
                        isCurrentSchoolSuperAdmin: isCurrentSchoolSuperAdmin,
                        userId: userId,
                        isUser: isUser

                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });
    </script>
</body>

</html>