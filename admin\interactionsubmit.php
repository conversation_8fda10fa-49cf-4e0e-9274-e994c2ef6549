<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsInteraction.php');   
	include('../setRequest.php'); 
	
	
	
		$interactionId ='';		
		if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
		{
		
			if(isset($_GET['interactionId'])) 
			{
				$interactionId = $_GET['interactionId'];
				$interactionId = DecodeQueryData($interactionId);
			}
			if(isset($_GET['studentId']))
			{
				$currentstudentId= DecodeQueryData($_GET['studentId']);	
			}
		
			if(isset($_GET['rotationId']))
			{
				$selrotationId= DecodeQueryData($_GET['rotationId']);		
			}
			$status = ($interactionId > 0) ? 'Updated' : 'Added';	
			
			$loggedUserId= $_SESSION['loggedUserId'];
			$interactionDate=($_POST['StartDate']);
			
			$interactionSchoolDate  = GetDateStringInServerFormat($_POST['SchoolDate']);
			$interactionSchoolDate = str_replace('00:00:00','12:00 PM',$interactionSchoolDate);
			$interactionSchoolDate = date('Y-m-d H:i',strtotime($interactionSchoolDate));
			$loggedUserId  = $loggedUserId;
			$timeSpent  = ($_POST['txtTimeSpent']);
			$pointsAwarded  = ($_POST['txtPointsAwarded']);		
			$schoolSummary  = ($_POST['School_Response']);
			
			

			$objInteraction = new clsInteraction();		
			$objInteraction->timeSpent = $timeSpent;
			$objInteraction->pointsAwarded = $pointsAwarded;	
			$objInteraction->schoolSummary = $schoolSummary;		
			$objInteraction->interactionSchoolDate = $interactionSchoolDate;
			$objInteraction->interactionClinicianDate = $interactionClinicianDate;
			$objInteraction->createdBy = $loggedUserId;		
			$retinteractionId = $objInteraction->SaveAdminInteraction($interactionId); 
			unset($objInteraction);
		
	
	   if($retinteractionId > 0)
		{  
	            if ($currentstudentId) 
				{
					
				 header('location:interaction.html?studentId='.EncodeQueryData($currentstudentId).'&status='.$status);
				}
				else
				{
				   header('location:interaction.html?rotationId='.EncodeQueryData($selrotationId).'&status='.$status);	
				}		
		        exit;
		}
		else
		{
			header('location:addinteraction.html?status=error');
		}
		 
	}
	{
		header('location:addinteraction.html');
		exit();
	}
	
	
	
?>