<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsStudent.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsSectionStudentName.php');
include('../class/clsSiteevaluation.php');
include('../class/clsHospitalSite.php');
include('../class/clsschoolclinicalsiteunit.php');
include('../class/clsCIevaluation.php');



$schoolId = 0;
$rotationId = 0;
$studentId = 0;
$currentstudentId = 0;
$csEvaluationMasterId = 0;
$siteevaluationrotationid = 0;
$schoolClinicalSiteUnitId = 0;
$hospitalSiteId = 0;
$display_to_date = date('m/d/Y');
$schoolId = $currentSchoolId;
$patientCareAdultAreaId = 0;
$patientCarePediatricAreaId = 0;
$patientCareNeonatalaAreaId = 0;
$evaluationDate = '';
$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

if (isset($_GET['siteevaluationrotationid'])) {
	$siteevaluationrotationid = DecodeQueryData($_GET['siteevaluationrotationid']);
}
if (isset($_GET['studentId'])) {
	$currentstudentId = DecodeQueryData($_GET['studentId']);
	$studentId = $currentstudentId;
}
if (isset($_GET['rotationId'])) {
	$rotationId = DecodeQueryData($_GET['rotationId']);
	$siteevaluationrotationid = $rotationId;
}
//For Edit Site Evaluation
$objSiteevaluation = new clsSiteevaluation();
$objRotation = new clsRotation();
if (isset($_GET['csEvaluationMasterId']) && ($_GET['siteevaluationrotationid'])) {
	$csEvaluationMasterId = DecodeQueryData($_GET['csEvaluationMasterId']);
	$schoolId = $currentSchoolId;
	$page_title = "Edit Site Evaluation ";
	$bedCrumTitle = 'Edit';

	//For Site Evalution Details

	$rowCSevaluation = $objSiteevaluation->GetCSEvaluationDetails($csEvaluationMasterId);

	if ($rowCSevaluation == '') {
		header('location:siteevaluation.html');
		exit;
	}
	$rotationId = ($rowCSevaluation['rotationId']);
	$schoolClinicalSiteUnitId = ($rowCSevaluation['schoolClinicalSiteUnitId']);
	$evaluationDate = ($rowCSevaluation['evaluationDate']);
	$courselocationId = $rowCSevaluation['locationId'];
	$parentRotationId = stripslashes($rowCSevaluation['parentRotationId']);
	$rotationLocationId = stripslashes($rowCSevaluation['rotationLocationId']);

	$locationId = 0;
	if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
		if ($parentRotationId > 0) {
			if (!$rotationLocationId)
				$locationId = $objRotation->GetLocationByRotation($rotationId);
			else
				$locationId  = $rotationLocationId;
		}
	} else {
		$locationId  = $courselocationId;
	}

	//Get Time Zone By Rotation 
	$objLocation = new clsLocations();
	$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
	unset($objLocation);
	if ($TimeZone == '')
		$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
	$evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
	$studentId = ($rowCSevaluation['studentId']);
	$hospitalSiteId = ($rowCSevaluation['hospitalSiteId']);
	$patientCareAdultAreaId = ($rowCSevaluation['patientCareAdultAreaId']);
	$patientCarePediatricAreaId = ($rowCSevaluation['patientCarePediatricAreaId']);
	$patientCareNeonatalaAreaId = ($rowCSevaluation['patientCareNeonatalaAreaId']);
} else {

	$page_title = "Add Site Evaluation";
	$bedCrumTitle = 'Add';
}

//For Student List
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($schoolId, $siteevaluationrotationid);
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname = $Rowstudent ? ($Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']) : '';
unset($objStudent);


//For Evalution Section
$totalSection = 0;

$CSevaluationSection = $objSiteevaluation->GetSections($schoolId);
if ($CSevaluationSection != '') {
	$totalSection = mysqli_num_rows($CSevaluationSection);
}

//For Hospital Site Unit
$objSchoolClinicalSiteUnit = new clsschoolclinicalsiteunit();
$hospitalSite = $objSchoolClinicalSiteUnit->GetAllClinicalSiteUnit($currentSchoolId);
unset($objSchoolClinicalSiteUnit);

//For Hospital Site
$objHospitalSite = new clsHospitalSite();
$Hospitals = $objHospitalSite->GetAllHospitalSite($currentSchoolId);
unset($objHospitalSite);

//For Rotation Name

$RotationName = $objRotation->GetrotationDetails($siteevaluationrotationid, $schoolId);
$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] : '';
unset($objRotation);
$objCIevaluation = new clsCIevaluation();
$PatientCareAdultArea = $objCIevaluation->GetPatientCareAdultArea($currentSchoolId);
$PatientCarePediatricArea = $objCIevaluation->GetPatientCarePediatricArea($currentSchoolId);
$PatientCareNeonatalArea = $objCIevaluation->GetPatientCareNeonatalArea($currentSchoolId);
unset($objCIevaluation);

$bedCrumTitle = (isset($_GET['view']) && $_GET['view'] == '1') ? 'View' : $bedCrumTitle;

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($page_title); ?></title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

	<style>
		/* Style for the collapsible content */
		.panel-collapse {
			display: none;
			/* Hidden by default */
			/* padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9; */
		}

		/* Style for the collapsible button */
		.collapsible {
			/* background-color: #3498db;
            color: white;
            padding: 10px;
            border: none; */
			width: 100%;
			cursor: pointer;
			display: flex;
			justify-content: space-between;
			/* Align content horizontally */
		}

		.panel-heading {
			width: 100%;
		}

		/* Style for the arrow icons */
		.arrow-icon {
			transition: transform 0.3s;
		}

		.collapsible.expanded .arrow-icon i {
			transform: rotate(180deg);
		}

		#hospitalerror{
			padding-bottom: 0 !important;
		}

		@media screen and (max-width: 500px) {
			.panel-body ol {
				padding-left: 20px;
			}

			.panel-default>.panel-heading {
				padding-left: 5px;
			}

			.panel,
			.form-group {
				margin-bottom: 5px;
			}
		}

		@media screen and (max-width: 767px) {
			#sectionHeader {
				display: none !important;
				/* Use !important to override any existing display styles */
			}

			/* Additional mobile optimizations for the evaluation form */
			.panel-body {
				padding: 10px 5px;
			}

			.nav.navbar-nav.list-inline {
				margin-left: 0;
				padding-left: 0;
			}

			.nav.navbar-nav.list-inline li {
				display: block;
				margin-bottom: 10px;
				text-align: left;
			}

			/* Improve spacing for form elements on mobile */
			.form-group {
				margin-bottom: 15px;
			}

			.input-group {
				margin-bottom: 10px;
			}

			/* Ensure radio buttons are easier to tap on mobile */


			/* Make text more readable on small screens */
			.panel-title {
				font-size: 16px;
				line-height: 1.4;
			}

			input[type="radio"] {
				margin: 2px 0 0;
			}

		}

		@media screen and (max-width: 575px) {

			.boxli ul li {
				width: 100%;
			}

			.mobile-thead {
				display: block;
			}

			.desktop-thead {
				display: none;
			}

			.navbar-nav {
				margin-top: 0;
			}

			.panel-group .panel {
				padding: 10px;
			}

		}
	</style>

</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
					<?php if ($currentstudentId > 0) { ?>
						<li><a href="clinical.html">Clinical</a></li>
						<li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
						<li><a href="siteevaluationlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Site Evaluation</a></li>
					<?php } else { ?>
						<li><a href="rotations.html">Rotation</a></li>
						<li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
						<li><a href="siteevaluationlist.html?siteevaluationrotationid=<?php echo EncodeQueryData($siteevaluationrotationid); ?>">Site Evaluation</a></li>
					<?php } ?>
					<li class="active"><?php echo ($bedCrumTitle); ?></li>
				</ol>
			</div>

		</div>
	</div>

	<div class="container">

		<form id="frmsiteevaluation" data-parsley-validate class="form-horizontal" method="POST" <?php if ($currentstudentId > 0) { ?> action="siteevaluationsubmit.html?csEvaluationMasterId=<?php echo (EncodeQueryData($csEvaluationMasterId)); ?>&studentId=<?php echo (EncodeQueryData($currentstudentId)); ?>" <?php } else { ?> action="siteevaluationsubmit.html?csEvaluationMasterId=<?php echo (EncodeQueryData($csEvaluationMasterId)); ?>
																									&siteevaluationrotationid=<?php echo (EncodeQueryData($siteevaluationrotationid)); ?>" <?php } ?>>

			<div class="row">


				<div class="col-md-6">

					<div class="form-group">
						<label class="col-md-12 control-label" for="cbostudent">Student</label>
						<div class="col-md-12 flex-col-reverse">
							<select id="cbostudent" name="cbostudent" class="form-control input-md required-input select2_single" required>
								<option value="" selected>Select</option>
								<?php
								if ($Student != "") {
									while ($row = mysqli_fetch_assoc($Student)) {

										$selstudentId  = $row['studentId'];
										$firstName  = stripslashes($row['firstName']);
										$lastName  = stripslashes($row['lastName']);
										$name =	$firstName . ' ' . $lastName;
								?>
										<option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

								<?php

									}
								}
								?>
							</select>
							<input type="hidden" name="rotation" value="<?php echo ($siteevaluationrotationid); ?>">
						</div>
					</div>
					<!-- ROTATION DD END -->
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="cbohospitalsites">Hospital Sites</label>
						<div class="col-md-12 flex-col-reverse">
							<select id="cbohospital" name="cbohospital" class="form-control input-md required-input select2_single" placeholder="Select Hospital site" required="true">
								<option value="" selected>Select</option>
								<?php
								if ($Hospitals != "") {
									while ($row = mysqli_fetch_assoc($Hospitals)) {
										$selhospitalSiteId  = $row['hospitalSiteId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($selhospitalSiteId); ?>" <?php if ($hospitalSiteId == $selhospitalSiteId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php

									}
								}
								?>
							</select>
							<!-- <p id="hospitalerror" style="color:#E74C3C; border: 1px solid #E8544;"></p> -->
						</div>
					</div>
				</div>


			</div>



			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="evaluationDate">Evaluation Date</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full relative' id='evaluationDate'>

								<input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date" value="<?php echo ($evaluationDate); ?>" required data-parsley-errors-container="#error-evaluationDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-evaluationDate"></div>
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="cbohospitalsites">Hospital Sites Unit</label>
						<div class="col-md-12 flex-col-reverse">
							<select id="cbohospitalsites" name="cbohospitalsites" class="form-control input-md required-input select2_single" placeholder="Select Hospital site" required="true">
								<option value="" selected>Select</option>
								<?php
								if ($hospitalSite != "") {
									while ($row = mysqli_fetch_assoc($hospitalSite)) {
										$selschoolClinicalSiteUnitId  = $row['schoolClinicalSiteUnitId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo ($selschoolClinicalSiteUnitId); ?>" <?php if ($schoolClinicalSiteUnitId == $selschoolClinicalSiteUnitId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php

									}
								}
								?>
							</select>
							<!-- <p id="hospitalerror" style="color:#E74C3C; border: 1px solid #E8544;"></p> -->
						</div>
					</div>
				</div>

			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="Adult">Patient Care Adult Area</label>
						<div class="col-md-12">
							<select id="Adult" name="Adult" class="form-control input-md  select2_single">
								<option value="" selected>select</option>
								<?php
								if ($PatientCareAdultArea != "") {
									while ($row = mysqli_fetch_assoc($PatientCareAdultArea)) {
										$selpatientCareAdultAreaId  = $row['patientCareAdultAreaId'];
										$name  = stripslashes($row['patientCareAdultAreaName']);

								?>
										<option value="<?php echo ($selpatientCareAdultAreaId); ?>" <?php if ($patientCareAdultAreaId == $selpatientCareAdultAreaId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php

									}
								}
								?>
							</select>
						</div>
					</div>

				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="Pediatric">Patient Care Pediatric Area</label>
						<div class="col-md-12">
							<select id="Pediatric" name="Pediatric" class="form-control input-md  select2_single">
								<option value="" selected>select</option>
								<?php
								if ($PatientCarePediatricArea != "") {
									while ($row = mysqli_fetch_assoc($PatientCarePediatricArea)) {
										$selpatientCarePediatricAreaId  = $row['patientCarePediatricAreaId'];
										$name  = stripslashes($row['patientCarePediatricAreaName']);

								?>
										<option value="<?php echo ($selpatientCarePediatricAreaId); ?>" <?php if ($patientCarePediatricAreaId == $selpatientCarePediatricAreaId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php

									}
								}
								?>
							</select>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="Neonatal">Patient Care Neonatal Area</label>
						<div class="col-md-12">
							<select id="Neonatal" name="Neonatal" class="form-control input-md  select2_single">
								<option value="" selected>select</option>
								<?php
								if ($PatientCareNeonatalArea != "") {
									while ($row = mysqli_fetch_assoc($PatientCareNeonatalArea)) {
										$selpatientCareNeonatalaAreaId  = $row['patientCareNeonatalAreaId'];
										$name  = stripslashes($row['patientCareNeonatalAreaName']);

								?>
										<option value="<?php echo ($selpatientCareNeonatalaAreaId); ?>" <?php if ($patientCareNeonatalaAreaId == $selpatientCareNeonatalaAreaId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php

									}
								}
								?>
							</select>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="panel panel-default border-14">
								<div class="panel-body">
									<p>The clinical site ratings are based on the following categories. The rating scale for each category is<br>
										<b>1-Strongly Disagree, 2-Disagree, 3-Neutral/Acceptable, 4-Agree, 5-Strongly Agree.</b>
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!--- 1st SECTION div start -------->
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="panel-group" id="posts">
								<div class="panel panel-default">
									<a class="collapsible" style="color: #000; text-decoration: none;" href="#CSevaluationSection" data-toggle="collapse" data-parent="#posts" id="collapse-link">
										<div class="panel-heading" style="display: flex; justify-content: space-between; align-items: center;">
											<h4 class="panel-title">
												<b>Student Clinical Site Evaluation</b> 
											</h4>
											<span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
										</div>
									</a>
									<div id="CSevaluationSection" class="panel-collapse panel-body">

										<?php
										if ($CSevaluationSection) {
											while ($row = mysqli_fetch_array($CSevaluationSection)) {
												$sectionMasterId = $row['sectionMasterId'];
												$title = $row['title'];
												//$firstName = $row['firstName'];

										?>
												<div class=""><?php echo '<b>' . $title . '</b>'; ?></div>
												<?php

												$totalCSevaluation = 0;
												$CSevaluationquestion = $objSiteevaluation->GetAllCSEvaluationQuestionMaster($schoolId, $sectionMasterId);

												if ($CSevaluationquestion != '') {
													$totalCSevaluation = mysqli_num_rows($CSevaluationquestion);
												}

												if ($totalCSevaluation > 0) {
													while ($row = mysqli_fetch_array($CSevaluationquestion)) {
														if (isset($_GET['csEvaluationMasterId'])) {
															$csEvaluationMasterId = DecodeQueryData($_GET['csEvaluationMasterId']);
														} else {
															$csEvaluationMasterId = 0;
														}

														$schoolCSEvaluationQuestionId = $row['schoolCSEvaluationQuestionId'];
														$questionText = $row['questionText'];
														$schoolCSEvaluationQuestionType = $row['schoolCSEvaluationQuestionType'];
														$isPosition = $row['isPosition'];
														$qhtml = GetCSEvaluationQuestionHtml($schoolCSEvaluationQuestionId, $schoolCSEvaluationQuestionType, $csEvaluationMasterId, $currentSchoolId);

												?>
														<div class="panel-body">
															<b>
																<?php echo ($questionText); ?> </b><br /><br />
															<?php echo $qhtml; ?>
														</div>
										<?php
													}
												}
											}
										}
										?>
									</div>
								</div>

							</div>

						</div>

					</div>
				</div>
			</div>

			<div class="row">
				<div class="form-group">
					<!-- <label class="col-md-2 control-label"></label> -->
					<div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                         
						<?php
						$rotationStatus = checkRotationStatus($siteevaluationrotationid);
						if ($rotationStatus == 0) {
						?>
							<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
						<?php } ?>
						<?php if ($currentstudentId > 0) { ?>
							<a type="button" href="siteevaluationlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>" class="btn btn-default">Cancel</a>
						<?php } else { ?>
							<a type="button" href="siteevaluationlist.html?siteevaluationrotationid=<?php echo EncodeQueryData($siteevaluationrotationid); ?>" class="btn btn-default">Cancel</a>
						<?php } ?>
					</div>
				</div>
			</div>

		</form>


	</div>

	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>




	<script type="text/javascript">
		$(window).load(function() {


			$('#frmsiteevaluation').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {
					ShowProgressAnimation();
					return true; // Don't submit form for this demo
				});

			$('#evaluationDate').datetimepicker({
				format: 'MM/DD/YYYY'

			});


			//for searching dropdown
			$(".select2_single").select2();
			$('#select2-cbostudent-container').addClass('required-select2');
			$('#select2-cbohospitalsites-container').addClass('required-select2');
			$('#select2-cbohospital-container').addClass('required-select2');

			<?php if (isset($_GET['csEvaluationMasterId']) && ($_GET['siteevaluationrotationid'])) { ?>
				$('#cbostudent').prop('disabled', false);
			<?php } ?>
		});
	</script>
	<script>
		// Get all collapsible button elements
		var buttons = document.querySelectorAll(".collapsible");
		var contents = document.querySelectorAll(".panel-collapse");

		// Add click event listeners to all buttons
		buttons.forEach(function(button, index) {
			button.addEventListener("click", function() {
				// Check if the content is currently expanded
				var isExpanded = contents[index].style.display === "block";

				// Close all sections
				contents.forEach(function(content) {
					content.style.display = "none";
				});

				// Reset the "expanded" class for all buttons
				buttons.forEach(function(btn) {
					btn.classList.remove("expanded");
				});

				// Toggle the content for the clicked section
				if (!isExpanded) {
					contents[index].style.display = "block";
					button.classList.add("expanded");
				}
			});
		});
	</script>
</body>

</html>