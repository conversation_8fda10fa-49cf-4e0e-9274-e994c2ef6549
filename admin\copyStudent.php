<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');
	include('../setRequest.php');  
	include('../class/clsStudent.php');
	    
   
	$rotationId=0;
	$clinicianId=0;
	$topicId = 0;
	$schoolId = 0;
	$loggedClinicianType='';
	
	$studentId=0;
	$isActiveCheckoff =$_SESSION["isActiveCheckoff"];

	if(isset($_GET['studentId'])) //Edit Mode
	{
		$studentId = DecodeQueryData($_GET['studentId']);
	}

	if(isset($_GET['schoolId'])) //Edit Mode
	{
		$schoolId = DecodeQueryData($_GET['schoolId']);
	}

	
	$studentSearch = isset($_GET['studentSearch']) ? $_GET['studentSearch'] : '';

	
	$objSchool = new clsSchool();
	$rowSchool = $objSchool->GetAllSchoolsToCopyStudent($schoolId);


	 //For Student Name
	 $objStudent=new clsStudent();
	 $Rowstudent=$objStudent->GetSingleStudent($currentSchoolId,$studentId);
	 $studentfullname= $Rowstudent ? ($Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']) : '';
	 $schoolName= $Rowstudent ? $Rowstudent['displayName'] : '';
	 
	 unset($objStudent);
	
    ?> 
	
	<div class="" tabindex="-1" role="dialog">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
				<h4 class="modal-title">Student Name: <?php echo $studentfullname; ?></h4>
			</div>
			<div class="modal-body">
			<div class="modal-body">
				
			<form id="frmCheckoff" data-parsley-validate class="form-horizontal" method="POST" action="copyStudentSubmit.html?studentId=<?php echo EncodeQueryData($studentId); ?>&studentSearch=<?php echo $studentSearch; ?>" >
				
			<div class="form-group">
					<label  class="col-md-4 control-label">Copy From School:</label>
					<label class="col-md-8 pull-left" style="padding-top:5px;"><?php echo $schoolName; ?></label> 
					       
				</div>
				
				<div class="form-group">
					<label for="copySchool" class="col-md-4 control-label">Copy To School:</label>
					<div class="col-md-8">
						<select id="copySchool" name="copySchool" class="copySchool input-md required-input select2_single form-control" required >
							<option value="" selected>Select</option>
								<?php
								if($rowSchool!="")
								{
									while($row = mysqli_fetch_assoc($rowSchool))
									{
										$selschoolId  = $row['schoolId'];
										$name  = stripslashes($row['displayName']);

										?>
										<option rotationId="<?php echo($selschoolId); ?>" value="<?php echo($selschoolId); ?>" ><?php echo($name); ?></option>
										<?php

									}
								}
							?>
						</select> 
					</div>        
				</div>	
				
				<div class="row">
					<button id="btnSubmit" name="btnSubmit" class="btn btn-primary pull-right" style="margin-right: 15px;">Copy</button>
				</div>
			</form>
			
		</div>
		
		<div class="modal-footer">
		
		</div><!-- /.modal-content -->
		</div><!-- /.modal-dialog -->
	</div><!-- /.modal -->

<script>

		$('.close').click(function(){  
  
			$.magnificPopup.close();
		});
		
</script>