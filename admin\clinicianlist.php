<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsIrr.php');
include('../setRequest.php');

$selrotationId = 0;
$rotationId = 0;
$from_date = '';
$to_date = '';
$display_from_date = date('m/d/Y', strtotime('-15 days'));
$display_to_date = date('m/d/Y');
$currentSchoolId;
$irrMasterId = 0;
if (isset($_GET['irrMasterId'])) {
	$irrMasterId = DecodeQueryData($_GET['irrMasterId']);
}
$objIrr = new clsIrr();
$rowsClinicianData = $objIrr->GetClinicianByIrr($irrMasterId);
$totalClinicianCount = 0;
if ($rowsClinicianData != '') {
	$totalClinicianCount = mysqli_num_rows($rowsClinicianData);
}
$GetIrrTitle = $objIrr->GetIrrAssignmentTitle($irrMasterId);
$IrrTitle = $GetIrrTitle['title'];
unset($objIrr);
?>

<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title>IRR Assignments</title>
	<style>
		.modal-backdrop {
			display: none;
		}
	</style>
</head>
<?php include('includes/headercss.php'); ?>
<?php include("includes/datatablecss.php") ?>

<body>


	<div class="container">
		<div class="" tabindex="-1" role="dialog">
			<div class="modal-dialog" role="document" id="myModal">
				<div class="modal-content">
					<div class="modal-header">
						<!-- <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button> -->
						<button title="Close (Esc)" type="button" class="mfp-close" style="font-size: 150%!important;" data-dismiss="modal">×</button>
						<h4 class="modal-title">IRR <?php echo ($GetIrrTitle['title']); ?> : Clinicians / Preceptor</h4>
					</div>
					<div class="modal-body">
						<div class="modal-body">
						<span id="genrateLink" style="display: none; float: left; margin-bottom: 15px;"> </span>
							<table id="clinicianlist_table" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
								<thead>
									<tr>
										<th>First Name</th>
										<th>Last Name</th>
										<th>Hospital Site/ Preceptor Info</th>
										<th>Status</th>

									</tr>
								</thead>
								<tbody>
									<?php
									if ($totalClinicianCount > 0) {
										while ($row = mysqli_fetch_array($rowsClinicianData)) {
											$irrMasterId = ($row['irrMasterId']);
											$irrDetailId = ($row['irrDetailId']);
											$Irrtitle = ($row['title']);
											$clinicianId = stripslashes($row['clinicianId']);
											$firstName = stripslashes($row['firstName']);
											$lastName = stripslashes($row['lastName']);
											$ClinicianName = $firstName . ' ' . $lastName;
											$HospitalName = stripslashes($row['HospitalName']);
											$clinicianCompletionDate = stripslashes($row['clinicianCompletionDate']);
											$schoolTopicId = stripslashes($row['schoolTopicId']);
											$preceptorId = stripslashes($row['preceptorId']);
											$preceptorFName = stripslashes($row['preceptorFName']);
											$preceptorLName = stripslashes($row['preceptorLName']);
											$preceptorNum = stripslashes($row['mobile_num']);
											$isPreceptorCompletedStatus = stripslashes($row['isPreceptorCompletedStatus']);
											if ($preceptorId) {
												$firstName = $preceptorFName;
												$lastName .= $preceptorLName;
												if ($HospitalName != '')
													$HospitalName .= '</br>' . $preceptorNum;
												else
													$HospitalName .= $preceptorNum;
												if ($isPreceptorCompletedStatus == 0 && $preceptorId > 0) {
													$HospitalName .= '</br><a href="javascript:void(0)" class="resendSms" irrDetailId = "' . $irrDetailId . '" >Resend SMS</a> ';
													$HospitalName .= '| <a href="javascript:void(0)" class ="copyLink" schoolTopicId="' . $schoolTopicId . '" irrDetailId="' . $irrDetailId . '" irrMasterId="' . $irrMasterId . '" preceptorNum="' . $preceptorNum . '" preceptorId = "' . EncodeQueryData($preceptorId) . '" evaluationType = "irr">Click to Copy URL</a>';
													
												}
											}

											if ($clinicianCompletionDate != '' && $clinicianCompletionDate != '0000-00-00') {
												$status = 'Completed';
											} else {
												$status = 'Pending';
											}


									?>
											<tr>
												<td style="background:white"><?php echo ($firstName); ?></td>
												<td style="background:white"><?php echo ($lastName); ?></td>
												<td style="white-space: normal; word-wrap: break-word; background:white"><?php echo ($HospitalName); ?></td>
												<td style="background:white"><?php echo ($status); ?></td>
											</tr>
									<?php
										}
									}
									?>
								</tbody>
							</table>

						</div>
					</div>
				</div>
			</div>
		</div>
	</div>

	<?php include('includes/footer.php'); ?>
	<?php include("includes/datatablejs.php") ?>
	<script>
		var current_clinicianlist_table = $("#clinicianlist_table").DataTable({


			responsive: false,
			"aoColumns": [{
				"sWidth": "10%"
			}, {
				"sWidth": "10%"
			}, {
				"sWidth": "10%"
			}, {
				"sWidth": "10%"
			}]
		});


		
	</script>
</body>

</html>