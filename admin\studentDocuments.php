<?php
    ini_set('display_errors',1);
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php');
	include('../includes/commonfun.php');
    include('../class/clsDB.php');
	include('../setRequest.php');  
    include('../class/clsStudent.php');
    include('../class/clsStudentRankMaster.php');
    include('../class/clsImmunization.php');
    
	  
    $totalstudentDocument = 0;
    $studentId=0;
    $studentImmunizationId=0;
    $loggedUserId = $_SESSION["loggedUserId"]; 
	$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
    $schoolId = $currentSchoolId;	
    $objStudents = new clsStudent(); 
    $objimmunization = new clsImmunization(); 
   
    
	 if(isset($_GET['studentId'])) 
	{
		$studentId= DecodeQueryData($_GET['studentId']);
          
    }
    
    if(isset($_GET['Type'])) 
	{
		$Type= ($_GET['Type']);
          
    }
    
    if(isset($_GET['studentImmunizationId'])) 
	{
		$studentImmunizationId= DecodeQueryData($_GET['studentImmunizationId']);
         
    }
    if($Type =='I')
    {
        $title ='Immulization Documents'; 
    }
    else{
        $title ='Student Documents';
    }
    //For immunization name 
    $immunization = $objimmunization->GetSingleImmulization($studentImmunizationId);
    $immunizationName= isset($immunization['shortName']) ? $immunization['shortName'] : '';
    
    //For Documents Details
    if($Type =='I')
    {
    $studentDocument=$objStudents->studentImmulizationDocumentDetails($studentId,$Type,$studentImmunizationId);
    }
    elseif($Type =='S')
    {
        $studentDocument=$objStudents->studentDocumentDetails($studentId,'S');
    }
    else
    {
        $studentDocument=$objStudents->studentDocumentDetails($studentId,'',1);
    }
    if($studentDocument !='')
    {
        $totalstudentDocument = mysqli_num_rows($studentDocument);
    }

    $objimmunization = new clsImmunization();
   
   
    //For Student Details
    $studentDetail = $objStudents->GetStudentDetails($studentId);
    $firstName=$studentDetail['firstName'];
    $lastName=$studentDetail['lastName'];
    $fullName=$firstName.' '.$lastName;
    
    unset($objimmunization);

?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title> <?php echo $title; ?></title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
 <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">  
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">


        <style>
           .mt-1 {
                    margin-top: 10px;
                    padding-left: 55px;
                }
        </style>

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <?php if($Type == 'S') { ?>
                            <li><a href="settings.html">Settings</a></li>
                        <?php } else { ?>
                            <li><a href="schoolstudents.html">Student </a></li>
                        <?php } ?>
                        <?php if($Type == 'S') { ?>
                        <li><a href="javascript:void(0);"><?php echo $fullName; ?> </a></li>
                        <?php } else {?>
                            <li><a href="schoolstudents.html?studentId=<?php echo(EncodeQueryData($studentId)); ?>"><?php echo $fullName; ?> </a></li>
                        <?php } ?>
                       <?php if($Type == 'I') 
                        { ?>
                        <li><a href="singlestudentimmunization.html?studentId=<?php echo(EncodeQueryData($studentId)); ?>">Immunization</a></li>
                        <?php } ?>
                        <li class="active">Documents</li>
                    </ol>
                </div>
                <div class="pull-right">
                    <?php if($Type !='S') { ?>
                        <a class="btn btn-link" href="documentUpload.html?studentId=<?php echo(EncodeQueryData($studentId)); ?>&Type=<?php echo $Type; ?>&studentImmunizationId=<?php echo(EncodeQueryData($studentImmunizationId)); ?>">Upload</a>
                    <?php } ?>
                </div>
            </div>
        </div>

        <div class="container"> 

            <?php
		
				if (isset($_GET["status"]))
				{
					
					if($_GET["status"] =="added")
					{
						
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Student added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Student updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="StatusUpdated")
					{
				?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Student status updated successfully.
                    </div>
                <?php
					}
                    else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}
					 else if($_GET["status"] =="datanotfound")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Certification log not available.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
                <?php if($studentImmunizationId > 0) 
	            { ?>
				<div class="formSubHeading"><?php echo $immunizationName; ?></div>
                <?php } ?>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Uploaded Date</th>
                            <?php if($Type !='I') { ?> <th class="text-center">Uploaded By</th> <?php } ?>
                            <th style="text-align:center">Action</th>
							
                        </tr>
                    </thead>
                    <tbody>
                    <?php
                        if($totalstudentDocument > 0)
                        {
                            while($row = mysqli_fetch_array($studentDocument))
                            {
                                $studentDocumentId=$row['studentDocumentId'];
                                $documetype=$row['documetype'];
                                if($documetype == 'S')
                                    $uploadedBy = 'Student';
                                else
                                    $uploadedBy = 'Admin';

                                $fileTitle = ($row['fileTitle']);
                                $uploadedDate = ($row['uploadedDate']);
                                $uploadedDate = converFromServerTimeZone($uploadedDate,$TimeZone);
                                $documentDetails =$objStudents->singleDocument($studentDocumentId);
                                $fileTitle=$documentDetails['fileTitle'];
                                $studentDocumentId =$documentDetails['studentDocumentId'];
                                
                                $documentPath = GetStudentDocumentPath($schoolId,$studentId,$studentDocumentId,$fileTitle);
                             
                     ?>
                            <tr>
                                
                                <td><?php echo($fileTitle); ?></td>                                
                                <td><?php echo ($uploadedDate) ;?></td>
                                <?php if($Type !='I') { ?><td class="text-center"><?php echo ($uploadedBy) ;?></td><?php } ?>
                                <td style="text-align:center">
                                   <!--<a href="downloadDocument.html?studentId=<?php //echo(EncodeQueryData($studentId)); ?>&studentDocumentId=<?php //echo(EncodeQueryData($studentDocumentId)); ?>" >Download</a>|-->
                                   <a href="<?php echo $documentPath;?>" target="_blank">View</a>
                                    <?php if($Type !='S') { ?>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow"  studentDocumentId="<?php echo EncodeQueryData($studentDocumentId); ?>" fileTitle="<?php echo($fileTitle); ?>">Delete</a>
                                    <?php } ?>
                                </td>
                            </tr>
                            <?php


                            }
                        }
                    ?>



                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
         <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>    
        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

             $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            	$(".select2_tags").select2({'placeholder':'Select'}); //for multiple selection
				$(".select2_single").select2();
			});

            var current_datatable = $("#datatable-responsive").DataTable({
                "aoColumns": [
                    {"sWidth": "40%"}, 
                    {"sWidth": "25%"},
                    <?php if($Type !='I') { ?> {"sWidth": "15%"},  <?php } ?>
                    { "sWidth": "20%","sClass": "alignCenter","bSortable": false
                } ]
            });

            $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentDocumentId = $(this).attr('studentDocumentId');
            var fileTitle = $(this).attr('fileTitle');

            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1; //for Admin

            alertify.confirm('Document: '+fileTitle, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentDocumentId,
                        userId: userId,
                        isUser: isUser,
                        type: 'Document'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

            });
          

        </script>
    </body>
    </html>