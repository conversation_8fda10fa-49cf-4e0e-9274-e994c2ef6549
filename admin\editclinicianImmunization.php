<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsClinicianImmunization.php');


$immunizationDate  = "";
$reimmunizationMId  = "";
$immunizationNote = "";
$clinicianIdImmunizationId = "";
$shortName = "";
$ExpiryDate = null;
$immunDate = null;

$title = "Add Immunization";
if (isset($_GET['schoolId'])) //Edit Mode
{
	$schoolId = $_GET['schoolId'];
	$schoolId = DecodeQueryData($schoolId);
} else {
	$schoolId = $currentSchoolId;
	//$tranSchoolDisplayname = $currenschoolDisplayname;
}

$bedCrumTitle = 'Add';
if (isset($_GET['clinicianId'])) //Edit Mode
{
	$clinicianId = $_GET['clinicianId'];
	$clinicianId = DecodeQueryData($clinicianId);
}
if (isset($_GET['clinicianIdImmunizationId'])) //Edit Mode
{

	$clinicianIdImmunizationId = $_GET['clinicianIdImmunizationId'];
	$clinicianIdImmunizationId = DecodeQueryData($clinicianIdImmunizationId);
	$title = "Edit Immunization";
	$bedCrumTitle = 'Edit';

	$objimmunization = new clsClinicianImmunization();
	$row = $objimmunization->GetClincianImmunization($clinicianIdImmunizationId, $clinicianId);
	unset($objimmunization);

	$immunizationNotificationDate = $row['immunizationNotificationDate'];
	$immunizationNotificationDate = date("m/d/Y", strtotime($immunizationNotificationDate));
	$reimmunizationMId  = stripslashes($row['immunizationMId']);
	$MasterImmunizationMId  = stripslashes($row['MasterImmunizationMId']);
	$immunizationNote  = stripslashes($row['immunizationNote']);
	$immunizationName  = stripslashes($row['shortName']);
	$firstName  = stripslashes($row['firstName']);
	$lastName  = stripslashes($row['lastName']);
	$clinicianId  = stripslashes($row['clinicianId']);
	$fullName  = $firstName . ' ' . $lastName;
	$expiryDays  = $row['expiryDays'];
	$immunizationDate  = date("Y/m/d", strtotime($row['immunizationDate']));
	$immunDate = date("m/d/Y", strtotime($immunizationDate));
	$ExpiryDate  = date("Y/m/d", strtotime($row['expiryDate']));
	$ExpiryDate = date("m/d/Y", strtotime($ExpiryDate));
}

$title;



$objStudent = new clsStudent();
$totalstudent = 0;
$rowsstudent = $objStudent->GetAllSchoolStudents($schoolId);
// print_r($rowsAttendance);exit;
if ($rowsstudent != '') {
	$totalstudent = mysqli_num_rows($rowsstudent);
}

unset($objStudent);
//Get Immulization Before Days



unset($objimmunization);

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($title); ?></title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
					<li><a href="schoolclinicians.html">Evaluator</a></li>
					<li><a href="schoolclinicians.html?clinicianId=<?php echo EncodeQueryData($clinicianId); ?>"><?php echo $fullName; ?></a></li>
					<li><a href="singleclinicianimmunization.html?clinicianId=<?php echo EncodeQueryData($clinicianId); ?>">Immunization</a></li>
					<li class="active"><?php echo ($bedCrumTitle); ?></li>
				</ol>
			</div>

		</div>
	</div>

	<div class="container">


		<form id="frmimmunization" data-parsley-validate class="form-horizontal" method="POST" action="editclinicianimmunizationsubmit.html?clinicianIdImmunizationId=<?php echo EncodeQueryData($clinicianIdImmunizationId) ?>">

			<div class="formSubHeading"> Edit Evaluator Immunization</div>
			<div class="row">
				<div class="col-md-6">
					<!-- Text input-->
					<div class="form-group">
						<label class="col-md-12 control-label" for="SchoolDate">Immunization</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date ' id='EndDate'>
								<label for="SchoolDate" style="margin-top:8px"><?php echo ($immunizationName); ?></label>

								<input type="hidden" name="txtreimmunizationMId" id="txtreimmunizationMId" value="<?php echo ($reimmunizationMId); ?>">
							</div>
							<!-- <div id="error-txtDate"></div> -->
						</div>
					</div>
				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="clinicianname">Evaluator</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group' id='clinicianname'>
								<label for="clinicianname" style="margin-top:8px"> <?php echo ($fullName); ?> </label>

								<input type="hidden" name="txtclinicianname" id="txtclinicianname" value="<?php echo ($clinicianId); ?>">
							</div>
							<!-- <div id="error-txtDate"></div> -->
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="StartDate">Immunization Date</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date calender-input w-full' id='StartDate'>

								<input type='text' name="StartDate" id="StartDate" class="form-control input-md required-input rotation_date dateInputFormat" value="<?php echo (date("m/d/Y", strtotime($immunizationDate))); ?>" required data-parsley-errors-container="#error-StartDate" placeholder="MM-DD-YYYY" />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-StartDate"></div>
						</div>
					</div>
				</div>
				<?php if ($expiryDays != 0) { ?>
					<div class="col-md-6">
						<div class="form-group">

							<label class="col-md-12 control-label" for="ExpireDate">Immunization Expiration Date</label>
							<div class="col-md-12 col-sm-12 col-xs-12">
								<div class='input-group date calender-input w-full' id='ExpiryDate'>
									<input type='text' name="ExpiryDate" id="ExpiryDate" class="form-control input-md required-input rotation_date dateInputFormat"
										value=" <?php
												if ($ExpiryDate != '' && $ExpiryDate != '0000-00-00' &&  $ExpiryDate != '01/01/1970' && $ExpiryDate != '11/30/-0001') {
													echo ($ExpiryDate);
												}
												?>" data-parsley-errors-container="#error-ExpiryDate" placeholder="MM-DD-YYYY" required />
									<span class="input-group-addon calender-icon">
										<span class="glyphicon glyphicon-calendar"></span>
									</span>
								</div>
								<div id="error-ExpiryDate"></div>
							</div>
						</div>
					</div>
			</div>
			<div class="row">
				<div class="col-md-6">

					<div class="form-group">
						<label class="col-md-12 control-label" for="ImmunizationNotification">Expiration Notification Date</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date calender-input w-full ' id='ImmunizationNotification'>

								<input type='text' name="ImmunizationNotification" id="ImmunizationNotificationId" class="form-control input-md required-input dateInputFormat" value="<?php if ($immunizationNotificationDate != '' && $immunizationNotificationDate != '01/01/1970' && $immunizationNotificationDate != '11/30/-0001') {
																																															echo ($immunizationNotificationDate);
																																														} ?>" required data-parsley-errors-container="#error-txtDate" readonly />
								<span class="input-group-addon calender-icon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>
			<?php } ?>
			<div class="col-md-6">
				<div class="form-group">
					<label class="col-md-12 control-label" for="SchoolDate">Note</label>
					<div class="col-md-12 col-sm-12 col-xs-12">
						<div class='input-group date' id='EndDate'>

							<textarea name="immunization_note" id="immunization_note " class="form-control input-md" rows="4" cols="100"><?php echo ($immunizationNote) ?></textarea>

						</div>
						<div id="error-txtDate"></div>
					</div>
				</div>

			</div>
			</div>
			<!-- <div class="col-md-12 col-sm-12 col-xs-12 pull-center"> -->
			<div class="form-group m-0">
				<!-- <label class="col-md-2 control-label"></label> -->
				<div class="col-md-12" style="display: flex; justify-content: center; margin: 0 0 20px 0;gap: 15px;">
					<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
					<a type="button" href="singleclinicianimmunization.html?clinicianIdImmunizationId=<?php echo EncodeQueryData($clinicianIdImmunizationId); ?>&clinicianId=<?php echo EncodeQueryData($clinicianId); ?>" class="btn btn-default">Cancel</a>
				</div>
				<!-- </div> -->

			</div>
		</form>


	</div>

	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>




	<script type="text/javascript">
		alertify.defaults.transition = "slide";
		alertify.defaults.theme.ok = "btn btn-success";
		alertify.defaults.theme.cancel = "btn btn-danger";
		alertify.defaults.theme.input = "form-control";

		$(window).load(function() {

			$(".select2_single").select2();
			$('#select2-cboCountry-container').addClass('required-select2');
			$('#select2-cboState-container').addClass('required-select2');



			$('#StartDate').datetimepicker({
				format: 'MM/DD/YYYY'

			});




			$(function() {
				$('#StartDate,#ExpiryDate').datetimepicker({
					useCurrent: false,

					format: 'MM/DD/YYYY'
				});
			});
			$(function() {

				$('#ImmunizationNotificationId').datetimepicker({
					format: 'MM/DD/YYYY'

				});
				$('#ExpiryDate').datetimepicker({
					format: 'MM/DD/YYYY',

				});

				$('#ExpiryDate').datetimepicker().on('dp.change', function(e) {
					var incrementDay = moment(new Date(e.date));
					var expiryDays = <?php echo ($expiryDays); ?>;
					incrementDay.subtract(expiryDays, 'days');
					var incrementDay1 = incrementDay.format("MM/DD/YYYY");
					$('#ImmunizationNotificationId').val(incrementDay1);
				});

				/*$('#ImmunizationNotification').datetimepicker().on('dp.change', function (e) {
					var decrementDay = moment(new Date(e.date));
					
					decrementDay.subtract(1, 'days');
					$('#ExpiryDate').data('DateTimePicker').maxDate(decrementDay);
						$(this).data("DateTimePicker").hide();
				});*/

			});

		});




		$(function() {
			$("#selectall").click(function() {
				$('.checkbox').attr('checked', this.checked);
			});

			$("selectall").click(function() {

				if ($(".checkbox").length == $(".checkbox:checked").length) {
					$("#selectall").attr("checked", "checked");
				} else {
					$("#selectall").removeAttr("checked");
				}

			});
		});
		<?php if (isset($_GET['clinicianIdImmunizationId'])) { ?>
			$('#cboImmunization').prop('disabled', true);
			var assignedStudentChkCount = $('.checkbox:checked').length;
			var allStudentChkCount = $('.checkbox').length;
			if (allStudentChkCount == assignedStudentChkCount)
				$('#selectall').prop('checked', true);
		<?php  } ?>
	</script>

</body>

</html>