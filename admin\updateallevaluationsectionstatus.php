<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../class/clsSystemUser.php');
include('../class/clsSchool.php');
include('../class/clsFormative.php');
include('../class/clsMidterm.php');
include('../class/clsSiteevaluation.php');
include('../class/clsCIevaluation.php');
include('../class/clsSummative.php');
include('../includes/commonfun.php');
include('../class/clsSrMidtermProfessionalEval.php');
include('../class/clsJuniorMidtermPerformanceEval.php');
include('../class/clsOrientationChecklist.php');
include('../class/clsPerformance.php');


include('../setRequest.php');


if (isset($_GET['id'])) //Check for Updates Status
{
	$sectionMasterId = DecodeQueryData($_GET['id']);
	$type = $_GET['type'];
	$isEvalType = $_GET['isEvalType'];
	if ($type == 'status') {
		if ($isEvalType == 'F') {
			//Create object
			$objFormative = new clsFormative();
			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				$objFormative->SetDefaultFormativeEvaluationStatus($sectionMasterId, $newStatus);
			else // For School Admin 
				$objFormative->SetFormativeEvaluationStatus($sectionMasterId, $newStatus);
			unset($objFormative);

			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				header('location:formativeEvaluationSectionList.html?status=StatusUpdated');
			else // For School Admin 
				header('location:formativeEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}
		if ($isEvalType == 'M') {
			//Create object
			$objMidterm = new clsMidterm();
			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				$objMidterm->SetDefaultMidtermEvaluationStatus($sectionMasterId, $newStatus);
			else // For School Admin
				$objMidterm->SetMidtermEvaluationStatus($sectionMasterId, $newStatus);

			unset($objMidterm);
			header('location:midtermEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}
		if ($isEvalType == 'site') {
			//Create object
			$objSiteevaluation = new clsSiteevaluation();
			$objDB = new clsDB();
			$newStatus = $_GET['newStatus'];

			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
			{
				$objDB->UpdateSingleColumnValueToTable('defaultsitesectionmaster', 'isActive', $newStatus, 'sectionMasterId', $sectionMasterId);
				// header('location:siteEvaluationSectionList.html?status=StatusUpdated');
			} else {
				$objSiteevaluation->SetSiteEvaluationStatus($sectionMasterId, $newStatus);
				// header('location:siteEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			}

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
			$type = "section";
			$isSuperAdmin = ($isCurrentSchoolSuperAdmin == 1) ? 1 : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admin
			$objSiteevaluation = new clsSiteevaluation();
			$objSiteevaluation->saveSiteEvalAuditLog($sectionMasterId, $sectionMasterId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);

			unset($objSiteevaluation);
			unset($objLog);

			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				header('location:siteEvaluationSectionList.html?status=StatusUpdated');
			else
				header('location:siteEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();

		}
		if ($isEvalType == 'cie') {

			//Create object
			$objCIevaluation = new clsCIevaluation();
			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				$objCIevaluation->SetDefaultCIEvaluationStatus($sectionMasterId, $newStatus);
			else // For School Admin 
				$objCIevaluation->SetCleEvaluationStatus($sectionMasterId, $newStatus);

			unset($objCIevaluation);

			header('location:ciEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}
		if ($isEvalType == 'sum') {
			//Create object
			$objSummative = new clsSummative();
			$objDB = new clsDB();

			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
			{
				$objDB->UpdateSingleColumnValueToTable('defaultsummativesectionmaster', 'isActive', $newStatus, 'sectionMasterId', $sectionMasterId);
				header('location:summativeEvaluationSectionList.html?status=StatusUpdated');
			} else {
				$objSummative->SetSummativeEvaluationStatus($sectionMasterId, $newStatus);
				header('location:summativeEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			}

			unset($objSummative);
			unset($objDB);
			exit();
		}
		if ($isEvalType == 'SPE') {
			//Create object
			$objSrMidtermProfessionalEval = new clsSrMidtermProfessionalEval();
			$objDB = new clsDB();

			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
			{
				$objDB->UpdateSingleColumnValueToTable('defaultsrmidtermprofessionalevalsectionmaster', 'isActive', $newStatus, 'dfSrmidTermProfessionalSectionId', $sectionMasterId);
			} else {
				$objSrMidtermProfessionalEval->SetSPEvaluationStatus($sectionMasterId, $newStatus);
			}
			$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
			$isSuperAdmin = isset($_GET['isSuperAdmin']) ? ($_GET['isSuperAdmin']) : 0;

			$objSPE = new clsSrMidtermProfessionalEval();
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();
			$type = 'section';
			// Determine the action type (delete) 
			if ($newStatus == "1") {
				$action = $objLog::ACTIVE;
			} else {
				$action = $objLog::INACTIVE;
			}
			$userType = ($isSuperAdmin > 0) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to ADMIN

			$objSPE->saveSPEAuditLog($sectionMasterId, $userId, $userType, $action, $isMobile = 0, $type, $isSuperAdmin);
			unset($objLog);

			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
			{
				header('location:srmidtermprofessionalEvalSectionList.html?status=StatusUpdated');
			} else {
				header('location:srmidtermprofessionalEvalSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			}

			unset($objSrMidtermProfessionalEval);
			unset($objDB);


			exit();
		}

		if ($isEvalType == 'jrprof') {
			//Create object
			$objJrProfessional = new clsJuniorMidtermPerformanceEval();
			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				$objJrProfessional->SetDefaultJrProfessionalStatus($sectionMasterId, $newStatus);
			else // For School Admin 
				$objJrProfessional->SetJrProfessionalStatus($sectionMasterId, $newStatus);
			unset($objJrProfessional);

			$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
			$isSuperAdmin = isset($_GET['isSuperAdmin']) ? ($_GET['isSuperAdmin']) : 0;

			$objJPE = new clsJuniorMidtermPerformanceEval();
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();
			$type = 'section';
			// Determine the action type (delete) 
			if ($newStatus == "1") {
				$action = $objLog::ACTIVE;
			} else {
				$action = $objLog::INACTIVE;
			}

			$userType = ($isSuperAdmin > 0) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to ADMIN

			$objJPE->saveJPEAuditLog($sectionMasterId, $userId, $userType, $action, $isMobile = 0, $type, $isSuperAdmin);
			unset($objLog);

			header('location:jrProfessionalSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}

		if ($isEvalType == 'orientation') {

			//Create object
			$objOrientationEval = new clsOrientationChecklist();
			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				$objOrientationEval->SetDefaultOrientationEvalStatus($sectionMasterId, $newStatus);
			else // For School Admin 
				$objOrientationEval->SetOrientationEvalStatus($sectionMasterId, $newStatus);


			$userId = isset($_GET['userId']) ? DecodeQueryData($_GET['userId']) : 0;
			$isSuperAdmin = isset($_GET['isSuperAdmin']) ? ($_GET['isSuperAdmin']) : 0;

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();
			$type = 'section';
			// Determine the action type (delete) 
			$action = ($newStatus > 0) ? $objLog::ACTIVE : $objLog::INACTIVE;
			$userType = ($isSuperAdmin > 0) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to ADMIN	
			$objOrientationEval->saveOrientationEvalAuditLog($sectionMasterId, $userId, $userType, $action, 0, $type, $isSuperAdmin);

			unset($objLog);
			unset($objOrientationEval);

			header('location:orientationEvalSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}
		if ($isEvalType == 'PEval') {

			//Create object
			$objPerformance = new clsPerformance();

			$newStatus = $_GET['newStatus'];
			if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
				$objPerformance->SetDefaultPerformanceEvalStatus($sectionMasterId, $newStatus);
			else // For School Admin 
				$objPerformance->SetPerformanceEvalStatus($sectionMasterId, $newStatus);

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			$userId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;
			$type = "section";
			$isSuperAdmin = ($isCurrentSchoolSuperAdmin == 1) ? 1 : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admin
			$objPerformance->savePerformanceEvaluationAuditLog($sectionMasterId, $userId, $userType, $logAction, $IsMobile = 0, $type, $isSuperAdmin);
			unset($objPerformance);
			unset($objDB);
			//Audit Log End


			header('location:performanceEvaluationSectionList.html?status=StatusUpdated&schoolId=' . $_GET['schoolId'] . '');
			exit();
		}

	}
} else {
	header('location:formativeEvaluationSectionList.html');
	exit();
}
