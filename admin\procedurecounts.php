<?php
   include('includes/validateUserLogin.php'); 
   include('../includes/config.php'); 
   include('../includes/commonfun.php'); 
   include('../class/clsDB.php'); 
   include('../class/clsProcedureCategory.php');
   include('../class/clsStudent.php');
   include('../class/clscheckoff.php');
   include('../class/clsRotation.php');
   include('../class/clsProcedureCount.php');
   include('../class/clsCheckoffTopicMaster.php');
   include('../setRequest.php'); 
   
   $schoolId = 0;
   $procedureCategoryId = 0;
   $rotationId = 0;
   $studentId=0;  
   $currentstudentId=0;
   $rotationtitle='';
   $transchooldisplayName = '';

   if(isset($_GET['schoolId'])) //Edit Mode
   {
   $schoolId = $_GET['schoolId'];
       $schoolId = DecodeQueryData($schoolId);
   }
   else
   {
       $schoolId = $currentSchoolId;
       $transchooldisplayName=$currenschoolDisplayname;
   }
   
   //For Filter by Student
   if(isset($_GET['studentId'])) 
   {
    $studentId = DecodeQueryData($_GET['studentId']);
    $currentstudentId= DecodeQueryData($_GET['studentId']);	
   }

   //For Filter by Rotation
   if(isset($_GET['rotationId'])) 
   {
		$rotationId = $_GET['rotationId'];
        $rotationId = DecodeQueryData($rotationId);
   }

   //For View 
    if(isset($_GET['view'])) 
   {
	    $view = $_GET['view'];
        $view = DecodeQueryData($view);
   }

   //For Filter by Procedure Category 
   if(isset($_GET['procedureCategoryId'])) 
   {
        $procedureCategoryId = $_GET['procedureCategoryId'];
        $procedureCategoryId = DecodeQueryData($procedureCategoryId);
   }
   $title ="Procedure Count ";
   
   
   
   //For Rotation Title
   $objrotation = new clsRotation();
   $rotation = $objrotation->GetrotationForProcedureCount($schoolId,$rotationId,$currentstudentId);
   $rowsrotation=$objrotation->GetrotationTitleForInteraction($rotationId,$currentSchoolId);
   $rotationtitle = isset($rowsrotation['title']) ? $rowsrotation['title'] :'';
   unset($objrotation);
   
   //For Student Name
   $objStudent = new clsStudent();
   $rowsStudents = $objStudent->GetSingleStudent($currentSchoolId,$currentstudentId);
   $studentfullname= $rowsStudents ? ($rowsStudents['firstName'] . ' ' . $rowsStudents['lastName']) : '';
   $Students = $objStudent->GetAllStudents($currentSchoolId);
   unset($objStudent);
   
   //For Procedure Category Name By Filter
   $objProcedureCategory = new clsProcedureCategory();
   $Category = $objProcedureCategory->GetAllCategory();
   unset($objProcedureCategory);
   
   //For Procedure
   $objcheckoff = new clscheckoff();
   $ProcedureCategory = $objcheckoff->GetProcedureCategory($currentSchoolId);
   if($ProcedureCategory !='')
   {       
     $totalSection =mysqli_num_rows($ProcedureCategory);  
   }

   ?>
<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
      <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
      <title><?php echo($title); ?></title>
      <?php include('includes/headercss.php');?>
      <?php include("includes/datatablecss.php");?>        
      <link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
      <link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
      <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
      <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
   <style>
	::-webkit-scrollbar { 
    display: none; 
	}
    /* table.dataTable.nowrap th, table.dataTable.nowrap td{
        white-space: normal !important;
    } */

    .table-datepicker .datepicker td{
        white-space: normal !important;
    }
   </style>
   </head>
   <body>
      <?php include('includes/header.php');?>
      <div class="row margin_zero breadcrumb-bg">
         <div class="container">
            <div class="pull-left">
               <ol class="breadcrumb">
                  <li><a href="dashboard.html">Home</a></li>
                  <?php if ($currentstudentId != 0) 
                     { ?>
                  <li class="active"><a href="clinical.html">Clinical</a></li>
                  <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                  <li class="active">Procedure Count</li>
                  
                  <?php } else { ?>
                  <li class="active"><a href="rotations.html"> Rotations </a></li>
                  <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                  <li class="active">Procedure Count</li>
                  
                  <?php } ?>
               </ol>
            </div>
			<div class="pull-right">      
            <ol class="breadcrumb">
            <?php if(!isset($_GET['view'])) 
                { 
                    if(isset($_GET['rotationId'])) 
                        { 
                    ?>

                    <li><a href="procedurecounts.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>&view=<?php echo EncodeQueryData('1'); ?>">View</a></li>  
                <?php
                        } 
                        else 
                        { ?>
                        <li><a href="procedurecounts.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>&view=<?php echo EncodeQueryData('1'); ?>">View</a></li> 
                    <?php
                        }
                } 
                else
                {
                    if(isset($_GET['rotationId'])) 
                     { ?>
                    <li><a href="procedurecounts.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Back</a></li>
                <?php
                     } else
                     { ?>
                        <li><a href="procedurecounts.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Back</a></li>      
                <?php   }    
                
                 } 
                ?>
                </ol>
            </div>
         </div>
      </div>
      <div id="contentText" class="container" style="width:1370px;">
         <?php
            if (isset($_GET["status"]))
            {
                
                 if($_GET["status"] =="Added")
                {
                    ?>
         <div class="alert alert-success alert-dismissible fade in" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
            </button> Procedure Count Updated successfully.
         </div>
         <?php 
            }
            
             else if($_GET["status"] =="Error")
            {
                ?>
         <div class="alert alert-danger alert-dismissible fade in" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
            </button> Error occurred.
         </div>
         <?php 
            }
            
            }
            ?>
         <div id="divTopLoading" >Loading...</div>
         
		  <div class="col-md-4 pull-right padding_zero  margin_zero" >
		 <div class="form-group">
			<div class="col-md-3">
			<label class="control-label" for="cbostudent" style="margin-top:8px">Student:</label>
			</div>
			<div class="col-md-9">
            <select studentId="<?php echo EncodeQueryData($studentId); ?>" id="cbostudent" name="cbostudent"  
               class="form-control input-md  select2_single"  >
               <option value="" selected>Select</option>
               <?php
                  if($Students!="")
                  {
                      while($row = mysqli_fetch_assoc($Students))
                      {
                           $selstudentId  = $row['studentId'];
                           $firstName  = stripslashes($row['firstName']);
                           $lastName  = stripslashes($row['lastName']);
                           $name  = $firstName . ' ' . $lastName;
                  
                           ?>
               <option value="<?php echo EncodeQueryData($selstudentId); ?>" <?php if($studentId==$selstudentId){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
               <?php
                  }
                  }
                  ?>
            </select>
			</div>
			</div>
         </div>
		 
         <div class="col-md-4 pull-right padding_zero  margin_zero" >
		 <div class="form-group">
			<div class="col-md-3">
			<label class="control-label" for="cboCategory" style="margin-top:8px">Category:</label>
			</div>
			<div class="col-md-9">
            <select studentId="<?php echo EncodeQueryData($studentId); ?>" id="cboCategory" name="cboCategory"  
               class="form-control input-md  select2_single"  >
               <!--<option value="" selected>Select</option>-->
               <?php
                  if($Category!="")
                  {
                      while($row = mysqli_fetch_assoc($Category))
                      {
                           $selCategoryId  = $row['procedureCategoryId'];
                           $name  = stripslashes($row['categoryName']);
                  
                           ?>
               <option value="<?php echo EncodeQueryData($selCategoryId); ?>" <?php if($procedureCategoryId==$selCategoryId){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
               <?php
                  }
                  }
                  ?>
            </select>
			</div>
			</div>
         </div>
         
         
         <div class="col-md-2 pull-right padding_right_zero">
            <select studentId="<?php echo EncodeQueryData($studentId); ?>" id="cborotation" name="cborotation" class="form-control input-md select2_single"  >
               <!--<option value="" selected>Select</option>-->
               <?php
                  if($rotation!="")
                  {
                      while($row = mysqli_fetch_assoc($rotation))
                      {
                           $selrotationId  = $row['rotationId'];
                           
                           $name  = stripslashes($row['title']);
                  
                           ?>
               <option value="<?php echo(EncodeQueryData($selrotationId)); ?>" <?php if($rotationId==$selrotationId){ ?>  selected="true" <?php }?>><?php echo($name); ?></option>
               <?php
                  }
                  }
                  ?>
            </select>
         </div>
         <label class=" control-label  pull-right" for="cborotation" style="margin-top:8px">Rotation:</label> <br/> <br>
         
		 
	
		 
		 
		 <form id="frmprocedurcount" data-parsley-validate class="form-horizontal" method="POST" <?php if ($currentstudentId != 0){ ?> action="addprocedurecountsubmit.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>" 
		 
            <?php  } else { ?> action="addprocedurecountsubmit.html?rotationId=<?php echo EncodeQueryData($rotationId);?>" <?php } ?>>
        <div class="row">
	   <div class="col-md-12">    
		           
				<?php  
                    $objProcedureCount=new clsProcedureCount();
                    while($rowProcedure = mysqli_fetch_array($ProcedureCategory))
                    {
                        $procedureCategoryId = $rowProcedure['procedureCategoryId'];
                        $title = $rowProcedure['categoryName'];
                    
                    ?>

                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <div class="panel-title">
                                    <a href="#divContent_<?php echo($procedureCategoryId) ?>" data-toggle="collapse" data-parent="#posts" class="collapsed" aria-expanded="false"><?php echo($title) ?></a>
                                </div>
                            </div>
                        </div>

        <div id="divContent_<?php echo($procedureCategoryId) ?>" class="panel-collapse collapse" aria-expanded="false" style="height: 0px;">
         <div class="panel-body">  

             <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover myCustomTable" cellspacing="0" width="100%">
                     <thead>
                        <tr>
                           <th>ID</th>
                           <?php if ($currentstudentId != 0){ ?>
                           <th>Rotation</th>
                           <?php } else { ?>
                           <th>First Name</th>
                           <th>Last Name</th>
                           <th>Rank</th>
                           <?php } ?> 
                           <th>Procedure Name</th>
                           
                           <th style="text-align: left">Asstd</th>
                           <th style="text-align: left">Obsvd</th>
                           <th style="text-align: center">Prfmd</th>
                           <th style="text-align: center" >Prfmd Total</th>
                           <th style="text-align: right" >Procdr Total</th>
                           <th style="text-align: center">Eva.Date</th>
                           <th style="text-align: center">Proc.Date </th>
                        </tr>
                     </thead>
                     <tbody>
                      
                        <?php 
                           $totalprocedurecount = 0;
                           $rowsprocedurecount = $objcheckoff->GetProcedurCount($studentId,$rotationId,$procedureCategoryId);
                           if($rowsprocedurecount !='')
                           {       
                           	    $totalprocedurecount =mysqli_num_rows($rowsprocedurecount);        
                           }
                           if($totalprocedurecount>0)
                           {
                                                 
                                while($row = mysqli_fetch_array($rowsprocedurecount))
                                {
                                    
                                
                                $proceduteCountTopicId = $row['proceduteCountId'];
                                $proceduteCountName = $row['proceduteCountName'];
                                $procedureCountsCode = $row['procedureCountsCode'];

                                $shortTitlelen=strlen($proceduteCountName);

                                if($shortTitlelen > 35)
                                {
                                    
                                    $schoolproceduteCountName=substr($proceduteCountName,0,35);
                                    $schoolproceduteCountName .= '...';
                                    
                                }else{
                                    $schoolproceduteCountName=$proceduteCountName;
                                }
                                
                               
                                
                                $GetProcedureCountTotal=$objProcedureCount->GetProcedureCountForView($rotationId,$proceduteCountTopicId,$studentId);
                                $total=$GetProcedureCountTotal['Total'];
                                $total=$total ? $total : 0;
                                $checkoffId=$GetProcedureCountTotal['checkoffId'];
                                $rotationname=$GetProcedureCountTotal['title'];
                                
                                $procedurePointsAssist=$GetProcedureCountTotal['procedurePointsAssist'];
                                $procedurePointsObserve=$GetProcedureCountTotal['procedurePointsObserve'];
                                $procedurePointsPerform=$GetProcedureCountTotal['procedurePointsPerform']; 
                                $studentfirstname=$GetProcedureCountTotal['studentfirstname'];
                                $studentlastName=$GetProcedureCountTotal['studentlastName'];
                                $rankname=$GetProcedureCountTotal['rankname'];
                                $procedurePointsPerformTotal= $GetProcedureCountTotal['procedurePointsPerformTotal'];
                                $procedurePointsPerformTotal=$procedurePointsPerformTotal ? $procedurePointsPerformTotal : 0;
                                $evalutionDate= $GetProcedureCountTotal['evaluationDate'];
                                $procedureDate=$GetProcedureCountTotal['procedureDate'];   
                                
                                    
                                    ?>
                       
                                <tr>
                                <td><?php echo ($procedureCountsCode); ?></td>
                                <?php if ($currentstudentId != 0)
                                    { ?>
                                <td><?php echo ($rotationname);?></td>
                                <?php } else {?>
                                <td><?php echo ($studentfirstname);?></td>
                                <td><?php echo ($studentlastName);?></td>
                                <td><?php echo ($rankname);?></td>
                                <?php } ?>
                                <td>
                                    <?php echo ($schoolproceduteCountName); ?></td>                                      
                                
                                
                                <td style="text-align: left">
                                    <input type="text" data-parsley-type="number"  class ="point form-control input-md asstd" style="text-align:right; width:60px"  name="txtAsstd_<?php echo ($checkoffId); ?>[]" value="<?php echo($procedurePointsAssist);?>"   >
                                </td>
                                <td style="text-align: left">
                                    <input type="text" data-parsley-type="number"  class ="point form-control input-md obsvd" style="text-align:right; width:60px"  name="txtobsvd_<?php echo ($checkoffId); ?>[]" value="<?php echo($procedurePointsObserve);?>"   > 
                                </td>
                                <td style="text-align: center">
                                    <input type="text" data-parsley-type="number" class ="point form-control input-md prfmd" style="text-align:right; width:60px"  name="txtprfmd_<?php echo ($checkoffId); ?>[]" value="<?php echo($procedurePointsPerform);?>"   >
                                </td>
                                <td style="text-align: center" width="10px">
                                    <span class="prfmdtotal"><?php echo ($procedurePointsPerform); ?></span> 
                                </td>
                                <td style="text-align: center">
                                    <span class="sumTotal"> <?php echo ($total); ?></span>
                                </td>
                                <td style="text-align: center">
                                    <?php 
                                    if($evalutionDate !='' )
                                    {
                                    echo(date('m/d/Y', strtotime($evalutionDate)));
                                    }
                                    else{
                                        echo "-";
                                    }
                                     ?>
                                </td>
                                <td style="text-align: center">
                                    <div class="col-md-12">
                                        <div class='input-group date' id='procedurecountDate'>
                                            <input type='text' name="procedureDate_<?php //echo ($checkoffId); ?>[]"  id="procedurecountDate" class=" procedurecountDate form-control input-md required-input procedurecountDate dateInputFormat" value="<?php echo (date('m/d/Y  H:i:s',strtotime($procedureDate))); ?>" required data-parsley-errors-container="#error-drugScreeningDate"/>
                                            <span class="input-group-addon">										
                                            <span class="glyphicon glyphicon-calendar"></span>
                                            </span>
                                        </div>
                                    </div>
                                </td>
                                </tr>
                                <?php  
                                }
                                }//end count
                                
                                
                                
                                        ?>
                            </tbody>
                        </table>

                            </div>
                        </div>

                    <?php
                    }      
                    unset($objProcedureCount);
                 ?> 

            </div>
		</div>
            <div class="form-group">
               <div class="col-md-12"> 
			    <?php  if(!isset($_GET['view'])) { ?>
                  <button id="btnSubmit" style="text-align:Center; width:130px;margin-top:10px" name="btnSubmit" class="btn btn-success">Save</button>
				<?php } ?>
			   </div>
            </div>  
      
			
			 </form>
	  </div>
      <?php include('includes/footer.php');?>
      <?php include("includes/datatablejs.php") ?>
      <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
      <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
      <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
      <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
      <script type="text/javascript">
         alertify.defaults.transition = "slide";
         alertify.defaults.theme.ok = "btn btn-success";
         alertify.defaults.theme.cancel = "btn btn-danger";
         alertify.defaults.theme.input = "form-control";
         
         $(".select2_single").select2();
         
         
             $(window).load(function(){
                 $("#divTopLoading").addClass('hide');
				 <?php  if(isset($_GET['view'])) { ?>
						$("input[type=text]").attr('disabled', true);
					<?php } ?>
         
         $('.procedurecountDate').datetimepicker({
              
                  		format: 'MM/DD/YYYY',
         defaultDate: new Date()				 
         
            			});
             });
         
          var current_datatable = $(".myCustomTable").DataTable({
            
           
           });
         
         
         $(window).load(function(){
             $("#divTopLoading").addClass('hide');
             $(".select2_single").select2();
         
         
         $("#cborotation").change(function(){
         var rotationId = $(this).val();
         var studentId = $(this).attr('studentId');
         
         if(rotationId)
         {
         window.location.href = "procedurecounts.html?rotationId="+rotationId+"&studentId="+studentId;
         }
         else{
         window.location.href = "procedurecounts.html";
         }
         });
         
         $("#cboCategory").change(function(){
			var procedureCategoryId = $(this).val();
			var studentId = "<?php echo EncodeQueryData($studentId); ?>";
         
			 if(procedureCategoryId)
			 {
				window.location.href = "procedurecounts.html?procedureCategoryId="+procedureCategoryId+"&studentId="+studentId;
			 }
			 else{
				window.location.href = "procedurecounts.html?studentId="+studentId;
			 }
         });
		 
		 
		 
		   $("#cbostudent").change(function(){
			var studentId = $(this).val();
			var procedureCategoryId = "<?php echo EncodeQueryData($procedureCategoryId); ?>";
			var rotationId = "<?php echo EncodeQueryData($rotationId); ?>";
         
			 if(procedureCategoryId)
			 {
				window.location.href = "procedurecounts.html?procedureCategoryId="+procedureCategoryId+"&studentId="+studentId+"&rotationId="+rotationId;
			 }
			 else{
				window.location.href = "procedurecounts.html?studentId="+studentId+"&rotationId="+rotationId;
			 }
         });
         
        });		
		
         //for Calculation
         $(document).ready(function(){
         
         $(".point").keyup(function(){
         
         var asstd = $(this).closest('tr').find('.asstd').val();
         asstd = parseInt(asstd);
         
         var obsvd = $(this).closest('tr').find('.obsvd').val();
         obsvd = parseInt(obsvd);
         
         var prfmd = $(this).closest('tr').find('.prfmd').val();
         prfmd = parseInt(prfmd);
         
         $(this).closest('tr').find('.prfmdtotal').text(prfmd);
         
         var sumTotal = asstd + obsvd + prfmd;
         $(this).closest('tr').find('.sumTotal').text(sumTotal);
         
         });
         });

       
				

         
         $("#cborotation").change(function(){
         var rotationId = $(this).val();
         
         if(rotationId)
         {
			window.location.href = "procedurecounts.html?rotationId="+rotationId;
         }
         else{
			window.location.href = "procedurecounts.html";
         }
         });
      </script>
   </body>
</html>