<?php
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Border;

$objJournal = new clsJournal();
$individual_student = unserialize($individual_student);

$rowsJournal = $objJournal->GetStudentJournalDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder);

$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('Journal Reports');

// Set document properties
$spreadsheet->getProperties()
    ->setCreator($schoolname)
    ->setLastModifiedBy('JCC')
    ->setTitle('Reports')
    ->setSubject('School Report')
    ->setDescription('All School Reports');

// Header styling
$headerStyle = [
    'font' => ['bold' => true, 'size' => 16],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E0E0E0']],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
];

$sheet->mergeCells('B2:I2')->setCellValue('B2', $schoolname);
$sheet->getStyle('B2:I2')->applyFromArray($headerStyle);

$sheet->mergeCells('B4:I4')->setCellValue('B4', 'Journal Report');
$sheet->getStyle('B4:I4')->applyFromArray($headerStyle);

// Column Headers
$columns = ['B' => 'First Name', 'C' => 'Last Name', 'D' => 'Rank', 'E' => 'Date', 'F' => 'Hospital Site', 'G' => 'Rotation', 'H' => 'Student Entry', 'I' => 'School Response'];
$headerStyleArray = ['font' => ['bold' => true, 'size' => 10], 'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E0E0E0']]];

$rowNum = 6;
foreach ($columns as $col => $value) {
    $sheet->setCellValue($col . $rowNum, $value);
    $sheet->getStyle($col . $rowNum)->applyFromArray($headerStyleArray);
    $sheet->getStyle($col . $rowNum)->getAlignment()->setHorizontal(Alignment::HORIZONTAL_LEFT);
}

// Data Population
$rowNum = 7;
if ($rowsJournal) {
    while ($row = mysqli_fetch_array($rowsJournal)) {
        $journalDate = date('m/d/Y', strtotime(stripslashes($row['journalDate'])));
        $data = [
            'B' => stripslashes($row['firstName']),
            'C' => stripslashes($row['lastName']),
            'D' => stripslashes($row['rank']),
            'E' => $journalDate,
            'F' => stripslashes($row['hospitalname']),
            'G' => stripslashes($row['rotationname']),
            'H' => str_replace("&nbsp;", '', strip_tags(stripslashes($row['journalStudentEntry']))),
            'I' => str_replace("&nbsp;", '', strip_tags(stripslashes($row['journalSchoolResponse'])))
        ];

        foreach ($data as $col => $value) {
            $sheet->setCellValue($col . $rowNum, $value);
            $sheet->getStyle($col . $rowNum)->getAlignment()->setHorizontal(($col == 'E') ? Alignment::HORIZONTAL_CENTER : Alignment::HORIZONTAL_LEFT);
        }
        $rowNum++;
    }
}

// Apply border to the whole table
$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]];
$sheet->getStyle("B6:I" . ($rowNum - 1))->applyFromArray($styleBorderArray);

// Set column width
foreach (range('B', 'I') as $colID) {
    $sheet->getColumnDimension($colID)->setAutoSize(true);
}
$sheet->getColumnDimension('H')->setWidth(100);
$sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows

$reportname = 'JournalReport_';
?>
