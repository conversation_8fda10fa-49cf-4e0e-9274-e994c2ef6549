<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');       
    include('../class/clsUsafMasterCheckoffSection.php');       
    include('../class/clsUsafMasterCheckoffTopic.php');       
    include('../setRequest.php'); 	
	
	
	$currentSchoolId;
	$sectionId =0;
	$encodedDefaultTopicId=0;
	$encodedDefaultSectionId=0;
	$topicid =0;
	if(isset($_GET['sectionId']))
	{
		$encodedDefaultSectionId=$_GET['sectionId'];
		$sectionId=DecodeQueryData($_GET['sectionId']);
	}
	if(isset($_GET['topicid']))
	{
		$encodedDefaultTopicId=$_GET['topicid'];
		$topicid=DecodeQueryData($_GET['topicid']);
	}
	$objCheckoffSectionMaster = new clsUsafMasterCheckoffSection();			
	$rowsCheckoffSection = $objCheckoffSectionMaster->GetUsafCheckoffSectionByCheckoffTopicMasterId($topicid,$sectionId);
	
	$totalCount = 0;
	if($rowsCheckoffSection !='')
	{
		$totalCount = mysqli_num_rows($rowsCheckoffSection);
	}
	$objCheckoffTopicMaster=new clsUsafMasterCheckoffTopic();
    $GetSingleTopicId=$objCheckoffTopicMaster->GetSingleUsafCheckoffTopicId($topicid,$sectionId);
	$title=$GetSingleTopicId['title'] ?? '';
	unset($objCheckoffTopicMaster);
	
	
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Military Comps Steps</title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
		
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

		
    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>
						<li><a href="settings.html">Setting	</a></li>
                        <li><a href="usafmastercheckofftopic.html">Military Comps</a></li>						
						<li class="active"><?php echo ($title); ?>-Comps Section</li>						
                    </ol>
                </div>
               <div class="pull-right"> 
				 <ol class="breadcrumb">
                    <a href="usafmasteraddsection.html?topicid=<?php echo EncodeQueryData($topicid); ?>">Add</a> 
                 </ol>   
               </div>
            </div>
        </div>

        <div class="container">


            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Comps Section added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Comps Section updated successfully.
                </div>
                <?php 
					}                   
                    else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
				<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
						<th >Select<!--input type="hide" id="selectall" name="selectall[0]" class="check selectall" value=""--></th>
                           <th>Section Number</th>		   
                           <th>Section Description</th>		   
                           <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalCount > 0)
                        {
                            while($row = mysqli_fetch_array($rowsCheckoffSection))
                            {								
                                $sectionId = ($row['MasterSectionId']);                               			
                                $sectionTitle = ($row['title']);                             
                                $sortOrder = ($row['sortOrder']);                             
                                $CheckdSectionId = ($row['CheckdSectionId']);
								$questionCount = ($row['questionCount']);
                                     if($CheckdSectionId > 0)
								{
									$actiontype="false";
								}
								else
								{
									$actiontype="true";
								}                    
                               ?>
                            <tr>
								<td style="text-align: center"><input sectionId="<?php echo EncodeQueryData($sectionId);?>" type="checkbox"  id="checkoffsection" name="checkoffsection[]" 
								value="" actiontype="<?php echo ($actiontype); ?>" defaultTopicId="<?php echo EncodeQueryData($topicid);?>" sectionId="<?php echo EncodeQueryData($sectionId);?>" <?php if($CheckdSectionId > 0)
								{?> checked  class="checkoffsection sendrequest chksection"<?php }else { ?> class="sendrequest chksection" <?php } ?>></td>
								
								<td style="text-align:center"><?php echo ($sortOrder); ?></td>
								<td><?php echo ($sectionTitle); ?></td>
								<td style="text-align: center">	
								<a  href="usafmasterquestion.html?topicid=<?php echo EncodeQueryData($topicid); ?>&sectionId=<?php echo EncodeQueryData($sectionId); ?>">Steps</a>
									<span class="badge"><?php echo($questionCount); ?></span>
								|
								<a  href="usafmasteraddsection.html?editid=<?php echo EncodeQueryData($sectionId); ?>&topicid=<?php echo  EncodeQueryData($topicid); ?>">Edit</a>
									<?php if($CheckdSectionId > 0) { ?>
									|<a id="warningAjax" class="text-muted" href="javascript:void(0);" SectionId="<?php echo EncodeQueryData($sectionId); ?>" sectionTitle="<?php echo($sectionTitle); ?>"  >Delete</a>
									<?php } else { ?>
								|  <a href="javascript:void(0);" class="deleteAjaxRow"
								 sectionId="<?php echo EncodeQueryData($sectionId); ?>" sectionTitle="<?php echo($sectionTitle); ?>">Delete</a>
									<?php } ?>
							   </td>
                            </tr>
                            <?php
                            }
                        }
                    ?>
					</tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
		
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
		
        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

			
            $(window).load(function(){
				
				 var TotalCheckboxCount = $('input[name="checkoffsection[]"]').length;                       
                var CheckedCheckboxCount = $('input[name="checkoffsection[]"]:checked').length;
                if(TotalCheckboxCount==CheckedCheckboxCount)
                {
                    $('.selectall').prop('checked', true);
                }else{
                    $('.selectall').prop('checked', false);
                }
									
                $("#divTopLoading").addClass('hide');
				 });
			
			
                 var current_datatable = $("#datatable-responsive").DataTable({
                    "iDisplayLength": 250,
                    "ordering": true,         
                    "order":[[2, 'asc'],[2, 'desc']],
					"aoColumns": [{
                    "sWidth": "1%",
					"bSortable": false
                        },{
                            "sWidth": "1%"
                            
                        },{
                            "sWidth": "68%",
                        },{
                            "sWidth": "30%",
                            "bSortable": false					
                        }]
				 });    

				 
				 $('#selectall').click(function() {				
				if ($(this).is(':checked')) {					
					$('input:checkbox').prop('checked', true);
					var ischeckall=1;
				} else {					
					//$('input:checkbox').prop('checked', false);
					$('input').filter(':checkbox').removeAttr('checked');
					var ischeckall=0;
				}
				
				var defaultTopicId = '<?php echo ($encodedDefaultTopicId); ?>';
				
					
				$.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/assign_usaf_master_section.html",
                        data: {							     
							  ischeckall: ischeckall,							  
							  defaultTopicId: defaultTopicId,							  
							  type: 'assign_all_usaf_sections'								  
                        },
						 success: function() {                           
                            
							if(ischeckall == 1){ //Assigned 
                            	alertify.success('Assigned');
							 }
								
							else if(ischeckall == 0) {//Removed
                            	alertify.error('Removed');
							 }
                        }
                    });
					
					
				});
				
			$('.chksection').click(function() {         
                var TotalCheckboxCount = $('input[name="checkoffsection[]"]').length;                       
                var CheckedCheckboxCount = $('input[name="checkoffsection[]"]:checked').length;
                if(TotalCheckboxCount==CheckedCheckboxCount)
                {
                    $('.selectall').prop('checked', true);
                }else{
                    $('.selectall').prop('checked', false);
                }
            });

		$(document).on('click', '#warningAjax', function() {
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            alertify.confirm('Warning!', 'This section already assigned, you cant delete it!', function(){
                }, function() {});
        });
			
            $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var sectionId = $(this).attr('sectionId');
            var sectionTitle = $(this).attr('sectionTitle');
            
            alertify.confirm('Comps Section: '+sectionTitle, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: sectionId,
                        type: 'Master_PEF_Checkoff_Section'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });
		
		//$(".sendrequest").change(function(){		
		$("#datatable-responsive").on("click", ".sendrequest", function() {		
            var action;	
            var thischeck= $(this);
            var actiontype = $(this).attr('actiontype');
            var defaultTopicId = $(this).attr('defaultTopicId');                 
            var sectionId = $(this).attr('sectionId'); 
            
            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/assign_usaf_master_section.html",
                data: {							
                        id: sectionId,
                        action : actiontype,							  
                        defaultTopicId: defaultTopicId,							  
                        type: 'assign_sections'								  
                },
                    success: function() {                           
                    //alertify.success('Assigned Question');
                    //alert(actiontype);
                    if(actiontype == 'true'){ //Assigned 
                        alertify.success('Assigned');
                    thischeck.attr('actiontype','false'); }
                        
                    else if(actiontype == 'false') {//Removed
                        alertify.error('Removed');
                    thischeck.attr('actiontype','true'); }
                }
            });
            
        });
        </script>
    </body>
    </html>