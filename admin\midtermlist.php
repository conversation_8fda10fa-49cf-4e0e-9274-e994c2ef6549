<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsLocations.php');
include('../class/clsMidterm.php');
include('../class/clsRotation.php');
include('../class/clsExternalPreceptors.php');
include('../class/clsClinician.php');

$midtermrotationid = 0;
$schoolId = 0;
$currentstudentId = 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0;
$canvasStatus = isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';
$type = isset($_GET['type']) ? $_GET['type'] : '';
if ($type == 'canvas')
    $canvasStatus = 1;

$encodedRotationId = '';
$encodedStudentId = '';

//For Student Site
if (isset($_GET['studentId'])) {
    $encodedStudentId = $_GET['studentId'];
    $currentstudentId = $_GET['studentId'];
    $currentstudentId = DecodeQueryData($currentstudentId);
}

//For Rotation Site
if (isset($_GET['midtermrotationid'])) {
    $encodedRotationId = $_GET['midtermrotationid'];
    $midtermrotationid = $_GET['midtermrotationid'];
    $midtermrotationid = DecodeQueryData($midtermrotationid);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

$title = "Admin Midterm| " . $transchooldisplayName;

//For Formative List
$objMidterm = new clsMidterm();
$getmidtermdetails = $objMidterm->GetAllMidtermEval($midtermrotationid, $currentstudentId, $canvasStatus, $schoolId);
$totalmidtermCount = 0;
if ($getmidtermdetails != '') {
    $totalmidtermCount = mysqli_num_rows($getmidtermdetails);
}

//For Student Name
$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname = $Rowstudent ? ($Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']) : '';
unset($objStudent);

//For Rotation Name
$objRotation = new clsRotation();
$RotationName = $objRotation->GetrotationDetails($midtermrotationid, $schoolId);
$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] : '';
// unset($objRotation);

$status = isset($_GET["status"]) ? $_GET["status"] : '';
$rotationStatus = checkRotationStatus($midtermrotationid);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <?php if ($type == 'canvas') { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Midterm Evaluation</li>
                    <?php } else { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <?php if (isset($_GET['studentId'])) { ?> <li><a href="clinical.html">Clinical</a></li>
                            <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                            <li class="active">Midterm Evaluation </li>

                        <?php } else { ?>
                            <li><a href="rotations.html">Rotations</a></li>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                            <li class="active">Midterm Evaluation</li>
                        <?php } ?>
                    <?php } ?>

                </ol>
            </div>
            <?php if ($type != 'canvas' && $rotationStatus == 0) { ?>
                <div class="pull-right">
                    <?php if (isset($_GET['studentId'])) { ?>
                        <a class="btn btn-link" href="midterm.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>&midtermrotationid=<?php echo EncodeQueryData($midtermrotationid); ?>">Add</a>

                    <?php } else { ?>
                        <a class="btn btn-link" href="midterm.html?midtermrotationid=<?php echo EncodeQueryData($midtermrotationid); ?>">Add</a>
                    <?php } ?>
                </div>
            <?php } ?>

        </div>
    </div>

    <div class="custom-container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Midterm Evaluation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Midterm Evaluation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Midterm Evaluation deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <?php if ($isActiveCanvas && $type != 'canvas') { ?>
            <div class="row margin_bottom_ten">
                <div class="col-md-8"></div>
                <div class="col-md-4">
                    <div class="form-group">
                        <label class="col-md-7 control-label text-right" for="" style="margin-top:8px">Canvas Status</label>
                        <div class="col-md-5 padding_right_zero padding_left_zero">
                            <select id="canvasStatus" name="canvasStatus" class="form-control input-md required-input select2_single">
                                <option value="" selected>All</option>
                                <option value="1" <?php if ($canvasStatus == 1) { ?> selected="true" <?php } ?>>Sent</option>
                                <option value="0" <?php if ($canvasStatus == 0 && $canvasStatus != '') { ?> selected="true" <?php } ?>>Not Sent</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        <?php } ?>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr id="filters">
                    <?php if ($type == 'canvas') { ?>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Rank</th>
                        <th>Rotation</th>
                    <?php } elseif ($currentstudentId < 0 || $currentstudentId == 0) { ?>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Rank</th>
                    <?php } else { ?>
                        <th>Rotation</th>
                    <?php } ?>
                    <th style="text-align: center">Evaluation Date</th>
                    <th style="text-align: center">Student <br>Signature Date</th>
                    <th style="text-align: center">Instructor <br> Signature Date</th>
                    <th style="text-align: center">Preceptor Info</th>
                    <th style="text-align: center">Average Rating</th>
                    <?php if ($type != 'canvas') { ?>
                        <th style="text-align: center">Action</th>
                    <?php } ?>
                    <?php if ($isActiveCanvas && $type != 'canvas') { ?>
                        <th class="text-center">Canvas Status</th>
                    <?php } ?>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalmidtermCount > 0) {
                    while ($row = mysqli_fetch_array($getmidtermdetails)) {

                        //$title = $row['title'];
                        $rotationame = $row['title'];
                        $Ranktitle = $row['Ranktitle'];
                        $DBstudentId = $row['studentId'];
                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $studentName = $firstName . ' ' . $lastName;
                        $studentMidtermMasterId = $row['MidtermID'];
                        $evaluationDate = stripslashes($row['evaluationDate']);
                        $rotationId = stripslashes($row['rotationId']);
                        $courselocationId = $row['locationId'];
                        $parentRotationId = stripslashes($row['parentRotationId']);
                        $rotationLocationId = stripslashes($row['rotationLocationId']);

                        $locationId = 0;
                        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                            if ($parentRotationId > 0) {
                                // $objRotation=new clsRotation();
                                if (!$rotationLocationId)
                                    $locationId = $objRotation->GetLocationByRotation($rotationId);
                                else
                                    $locationId  = $rotationLocationId;
                                // unset($objRotation);

                            }
                        } else {
                            $locationId  = $courselocationId;
                        }

                        //Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);
                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
                        $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
                        $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                        $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                        $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));

                        $dateOfInstructorSignature = stripslashes($row['dateOfInstructorSignature']);
                        $dateOfInstructorSignature = converFromServerTimeZone($dateOfInstructorSignature, $TimeZone);
                        $dateOfInstructorSignature = date("m/d/Y", strtotime($dateOfInstructorSignature));

                        if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '12/31/1969') {
                            $dateOfStudentSignature = $dateOfStudentSignature;
                        } else {
                            $dateOfStudentSignature = "-";
                        }

                        if ($dateOfInstructorSignature != '' && $dateOfInstructorSignature != '0000-00-00 00:00:00' && $dateOfInstructorSignature != '01/01/1970' && $dateOfInstructorSignature != '0000-00-00' && $dateOfInstructorSignature != '12/31/1969') {
                            $dateOfInstructorSignature =  $dateOfInstructorSignature;
                        } else {
                            $dateOfInstructorSignature =  "-";
                        }

                        // Preceptor info

                        $preceptorId = $row['preceptorId'];
                        $isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];
                        $preceptorDetials = '';
                        if ($preceptorId > 0) {
                            $objExternalPreceptors = new clsExternalPreceptors();
                            $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
                            $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                            $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                            $preceptorNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
                            $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                            $preceptorInfo = 'Name: ' . $preceptorFullName . '</br>Phone: ' . $preceptorNum;
                            if ($isPreceptorCompletedStatus)
                                $preceptorInfo .= '</br>Status: Completed';
                            else
                                $preceptorInfo .= '</br>Status: Pending';

                            $preceptorDetials = $preceptorInfo;
                        } else {
                            $preceptorInfo = "-";
                        }

                        $clinicianId = $row['clinicianId'];

                        if ($clinicianId) {
                            $objClinician = new clsClinician();
                            $clinicianFullName = $objClinician->GetClinicianNameById($clinicianId);
                            $preceptorDetials = 'Name: ' . $clinicianFullName;
                            unset($objClinician);
                        }

                        $avgScore = $objMidterm->GetMidTermEvaluationScore($studentMidtermMasterId);
                        $avgScore = number_format((float)$avgScore, 2, '.', '');

                        if ($preceptorId && $isPreceptorCompletedStatus == 0) {
                            $avgScore = '';
                        }
                        // For Canvas
                        $isSendToCanvas  = isset($row['isSendToCanvas']) ? $row['isSendToCanvas'] : 0;

                        $isSendToCanvasClass = 'isSendRecordToCanvas';
                        $isSentToCanvasClass = 'hide';
                        if ($isSendToCanvas) {
                            $isSendToCanvasClass = 'hide';
                            $isSentToCanvasClass = '';
                        }

                        $isUserCanSendCompletedRecordToCanvas = 0;
                        if ($dateOfStudentSignature != '-')
                            $isUserCanSendCompletedRecordToCanvas = 1;

                        /// End Canvas ///

                        $isActionName = 'Edit';
                        // if($preceptorId > 0 && $isPreceptorCompletedStatus == 0 )
                        //     $isActionName = 'pending';
                        // elseif($preceptorId > 0 && $isPreceptorCompletedStatus == 1 )
                        // $isActionName = 'View';


                ?>
                        <tr>
                            <?php if ($type == 'canvas') { ?>
                                <td><?php echo ($firstName); ?></td>
                                <td><?php echo ($lastName); ?></td>
                                <td><?php echo ($Ranktitle); ?></td>
                                <td><?php echo ($rotationame); ?></td>

                            <?php } elseif ($currentstudentId < 0 || $currentstudentId == 0) { ?>
                                <td><?php echo ($firstName); ?></td>
                                <td><?php echo ($lastName); ?></td>
                                <td><?php echo ($Ranktitle); ?></td>
                            <?php } else { ?>
                                <td><?php echo ($rotationame); ?></td>
                            <?php } ?>

                            <td style="text-align: center"><?php echo ($evaluationDate); ?></td>
                            <td style="text-align: center"><?php echo $dateOfStudentSignature; ?></td>
                            <td style="text-align: center"><?php echo $dateOfInstructorSignature; ?></td>
                            <td><?php echo ($preceptorInfo);
                                if ($isPreceptorCompletedStatus ==  0 && $preceptorId > 0) { ?>
                                    <br>
                                    <a href="javascript:void(0);" class="copyLink" preceptorId="<?php echo EncodeQueryData($preceptorId); ?>" preceptorNum="<?php echo ($preceptorNum); ?>" evaluationId="<?php echo ($studentMidtermMasterId); ?>" rotationId="<?php echo ($rotationId); ?>" evaluationType='midterm' onclick="copyLinkUrl(this)">Click to Copy URL</a>
                                <?php } ?>
                            </td>
                            <td style="text-align: center"><?php echo $avgScore; ?></td>
                            <?php if ($type != 'canvas') { ?>
                                <?php
                                $rotationStatus = checkRotationStatus($rotationId);
                                $viewParam = ($rotationStatus) ? '&view=V' : ''; // Add '&view=V' if $rotationStatus is true
                                $linkText = ($rotationStatus) ? 'View' : 'Edit'; // Change link text based on $rotationStatus
                                $isActionName = ($rotationStatus) ? 'View' : $isActionName; // Add '&view=V' if $rotationStatus is true
                                ?>
                                <td style="text-align: center">
                                    <a href="midterm.html?studentMidtermMasterId=<?php echo (EncodeQueryData($studentMidtermMasterId)); ?>&midtermrotationid=<?php echo (EncodeQueryData($midtermrotationid)); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>&isPreceptor=<?php echo (EncodeQueryData($preceptorId)); ?> <?php echo $viewParam; ?>"><?php echo ($isActionName); ?></a> |
                                    <a href="javascript:void(0);" class="deleteAjaxRow" studentMidtermMasterId="<?php echo EncodeQueryData($studentMidtermMasterId); ?>" studentName="<?php echo ($studentName); ?>">Delete</a>
                                </td>
                            <?php } ?>
                            <?php if ($isActiveCanvas && $type != 'canvas') {
                                if ($loggedUserSendRecordToCanvas && $isUserCanSendCompletedRecordToCanvas) { ?>
                                    <td class="text-center">
                                        <a href="javascript:void(0);" id="isSendToCanvas_<?php echo $studentMidtermMasterId; ?>" class="<?php echo $isSendToCanvasClass; ?>" evaluationDate="<?php echo $evaluationDate; ?>" dateOfStudentSignature="<?php echo $dateOfStudentSignature; ?>" dateOfInstructorSignature="<?php echo $dateOfInstructorSignature; ?>" avgRating="<?php echo $avgScore; ?>" rotation="<?php echo $rotationame; ?>" studentfullname="<?php echo $studentName; ?>" studentId="<?php echo $DBstudentId; ?>" studentMidtermMasterId="<?php echo $studentMidtermMasterId; ?>" preceptorInfo="<?php echo $preceptorDetials; ?>">
                                            Send to Canvas
                                        </a>
                                        <label for="" class="isSentToCanvas_<?php echo $studentMidtermMasterId; ?> <?php echo $isSentToCanvasClass; ?>">Sent</label>

                                    </td>

                                <?php } else { ?>
                                    <td class="text-center"><label for="" class=""> -
                                            <?php //if($isSendToCanvas>0) { echo 'Sent'; } else { echo 'Send to canvas'; } 
                                            ?>
                                        </label></td>

                            <?php }
                            }
                            ?>
                        </tr>
                <?php
                    }
                }
                unset($objMidterm);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $(".select2_single").select2();
            $("#divTopLoading").addClass('hide');
        });

        <?php if (isset($_GET['midtermrotationid'])) { ?>
            var current_datatable = $("#datatable-responsive").DataTable({

                initComplete: function() {
                    this.api().columns(2).every(function() {
                        var column = this;
                        var select = $('<select class="datatable-select-filter-box"><option value="">Select </option></select>')
                            .appendTo($("#filters").find("th").eq(column.index()))
                            .on('change', function() {
                                var val = $.fn.dataTable.util.escapeRegex(
                                    $(this).val());

                                column.search(val ? '^' + val + '$' : '', true, false)
                                    .draw();
                            });

                        console.log(select);

                        column.data().unique().sort().each(function(d, j) {
                            select.append('<option value="' + d + '">' + d + '</option>')
                        });
                    });
                },
                "ordering": true,
                responsive: false,
                scrollX: true,
                "order": [
                    [3, "desc"]
                ],
                "aoColumns": [{
                        "sWidth": "10%"
                    }, {
                        "sWidth": "10%"
                    }, {
                        "sWidth": "15%",
                        "bSortable": false
                    }, {
                        "sWidth": "10%"
                    }, {
                        "sWidth": "15%",
                        "sClass": "alignCenter"
                    }, {
                        "sWidth": "15%",
                        "sClass": "alignCenter"
                    }, {
                        "sWidth": "15%",
                        "sClass": "alignCenter"
                    },
                    {
                        "sWidth": "15%",
                        "sClass": "alignCenter"
                    }, {
                        "sWidth": "15%",
                        "sClass": "alignCenter",
                        "bSortable": false
                    }
                    <?php if ($isActiveCanvas) { ?>, {
                            "sWidth": "10%"
                        },
                    <?php } ?>
                ]
            });
        <?php } else { ?>
            var current_datatable = $("#datatable-responsive").DataTable({

                initComplete: function() {
                    this.api().columns(0).every(function() {
                        var column = this;
                        var select = $('<select class="datatable-select-filter-box"><option value="">Select </option></select>')
                            .appendTo($("#filters").find("th").eq(column.index()))
                            .on('change', function() {
                                var val = $.fn.dataTable.util.escapeRegex(
                                    $(this).val());

                                column.search(val ? '^' + val + '$' : '', true, false)
                                    .draw();
                            });

                        console.log(select);

                        column.data().unique().sort().each(function(d, j) {
                            select.append('<option value="' + d + '">' + d + '</option>')
                        });
                    });
                },

                "ordering": true,
                "order": [
                    [1, "desc"]
                ],
                "aoColumns": [{
                        "sWidth": "10%",
                        "bSortable": false
                    }, {
                        "sWidth": "10%"
                    }, {
                        "sWidth": "15%"
                    }, {
                        "sWidth": "15%",
                        "sClass": "alignCenter"
                    }, {
                        "sWidth": "15%",
                        "sClass": "alignCenter"
                    },
                    {
                        "sWidth": "15%",
                        "sClass": "alignCenter",
                        "bSortable": false
                    }
                    <?php if ($type == 'canvas') { ?>, {
                            "sWidth": "15%",
                            "sClass": "alignCenter",
                            "bSortable": false
                        }, {
                            "sWidth": "15%",
                            "sClass": "alignCenter",
                            "bSortable": false
                        }

                    <?php } ?>
                    <?php if ($isActiveCanvas && $type != 'canvas') { ?>, {
                            "sWidth": "10%"
                        },
                    <?php } ?>
                ]
            });

        <?php } ?>
        current_datatable.columns.adjust().draw();
        // ajax call for deleteAjaxRow


        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentMidtermMasterId = $(this).attr('studentMidtermMasterId');
            var title = $(this).attr('studentName');

            alertify.confirm('Midterm: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentMidtermMasterId,
                        type: 'Midterm'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        //Send Records To Canvas
        $(document).on('click', '.isSendRecordToCanvas', function() {
            var that = this;
            var evaluationDate = $(this).attr('evaluationDate');
            var dateOfStudentSignature = $(this).attr('dateOfStudentSignature');
            var dateOfInstructorSignature = $(this).attr('dateOfInstructorSignature');
            var avgRating = $(this).attr('avgRating');
            var rotation = $(this).attr('rotation');
            var schoolId = "<?php echo $currentSchoolId; ?>";
            var studentfullname = $(this).attr('studentfullname');
            var studentId = $(this).attr('studentId');
            var studentMidtermMasterId = $(this).attr('studentMidtermMasterId');
            var preceptorInfo = $(this).attr('preceptorInfo');

            alertify.confirm('Summative Evalution ', 'Continue with send record to Canvas?', function() {
                $(that).text('Loading..');
                $(that).prop('disabled', true);

                $.ajax({
                    type: "POST",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_record_to_canvas.html",
                    data: {

                        evaluationDate: evaluationDate,
                        dateOfInstructorSignature: dateOfInstructorSignature,
                        dateOfStudentSignature: dateOfStudentSignature,
                        avgRating: avgRating,
                        studentMidtermMasterId: studentMidtermMasterId,
                        rotation: rotation,
                        studentFullName: studentfullname,
                        studentId: studentId,
                        schoolId: schoolId,
                        preceptorInfo: preceptorInfo,
                        type: 'MidTermEval'
                    },
                    success: function(response) {
                        if (response == 'Success') {
                            $(that).addClass('hide');
                            $('.isSentToCanvas_' + studentMidtermMasterId).removeClass('hide')
                            alertify.success('Record Successfully Sent to Canvas.');
                        } else {
                            alertify.success(response);
                        }

                    }
                });
            }, function() {});

        });

        $("#canvasStatus").change(function() {
            var canvasStatus = $(this).val();
            var studentId = '<?php echo $encodedStudentId; ?>';
            var rotationId = '<?php echo $encodedRotationId; ?>';

            if (studentId != '') {
                if (canvasStatus != '')
                    window.location.href = "midtermlist.html?studentId=" + studentId + "&canvasStatus=" + canvasStatus;
                else
                    window.location.href = "midtermlist.html?studentId=" + studentId;
            } else if (rotationId != '') {
                if (canvasStatus != '')
                    window.location.href = "midtermlist.html?midtermrotationid=" + rotationId + "&canvasStatus=" + canvasStatus;
                else
                    window.location.href = "midtermlist.html?midtermrotationid=" + rotationId;
            }
        });
    </script>


</body>

</html>