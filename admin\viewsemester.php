<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
	include('../setRequest.php');       
	include('../class/clsSemester.php');
	include('../class/clsLocations.php');
	include('../class/clsRotation.php');
    $schoolId = 0;
    $transchooldisplayName = '';   
        $schoolId = $currentSchoolId;
        $transchooldisplayName=$currenschoolDisplayname;  
    $title ="Semester| ".$transchooldisplayName;
    $TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
    //CREATE OBJECT
	$objSemester = new clsSemester();
	 $objrotation = new clsRotation();
    $totalSemester = 0;
	$rowsSemester = $objSemester->GetAllSemester($schoolId);	
	if($rowsSemester !='')
	{
		$totalSemester =mysqli_num_rows($rowsSemester);
	}
	unset($objSemester);
?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo($title); ?></title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>

		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Semester</li>
                    </ol>
                </div>
         
               <div class="pull-right">
                     <a class="btn btn-link" href="addsemester.html">Add</a>
               </div>
         

            </div>
        </div>

        <div class="container">

            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Semester added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Semester updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Deleted")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Semester deleted successfully.
                </div>
                <?php 
					}
                     else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Sort Order</th>                                                       
                            <th>Semester</th> 
                            <th>Start Date</th>
                            <th>End Date</th>                                                      
                            <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalSemester>0)
                        {
                            while($row = mysqli_fetch_array($rowsSemester))
                            {
								
								$semesterId = $row['semesterId'];
                                $schoolId = $row['schoolId'];					
                                $title = stripslashes($row['title']);
                                $sortOrder = stripslashes($row['sortOrder']);
                                $fromDate = stripslashes($row['fromDate']);
								$rotationId = isset($row['rotationId'])  ? stripslashes($row['rotationId']) : '';
								$courselocationId = isset($row['locationId']) ?  $row['locationId'] : '';
								$parentRotationId = isset($row['parentRotationId']) ?  stripslashes($row['parentRotationId']) : '';
								$rotationLocationId = isset($row['rotationLocationId']) ? stripslashes($row['rotationLocationId']) : '';
								
								$locationId = 0;
									if($rotationLocationId != $courselocationId && $parentRotationId > 0)
								{
									if($parentRotationId > 0)
									{
										if(!$rotationLocationId)
											$locationId = $objrotation->GetLocationByRotation($rotationId);
										else
											$locationId  = $rotationLocationId;
									}
								}
								else
								{	
										$locationId  = $courselocationId;
								}
									
								//Get Time Zone By Rotation 
								$objLocation = new clsLocations();
								$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
								unset($objLocation);
								if($TimeZone =='')
									$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
                                $fromDate = converFromServerTimeZone($fromDate,$TimeZone);
                                if($fromDate !='0000-00-00' && $fromDate !='' && $fromDate !='01/01/1970' && $fromDate !='11/30/-0001 01:00 AM' && $fromDate !='11/30/-0001 12:00 AM')
                                {
                                    $fromDate = date('m/d', strtotime($fromDate));
                                } 
                                else
                                {
                                    $fromDate ="-";
                                }	
                                
                                $toDate = stripslashes($row['toDate']);
                                $toDate = converFromServerTimeZone($toDate,$TimeZone); 
                                if($toDate !='0000-00-00' && $fromDate !='' && $toDate !='01/01/1970' && $toDate !='11/30/-0001 01:00 AM' && $toDate !='11/30/-0001 12:00 AM')
                                {
                                    $toDate = date('m/d', strtotime($toDate));
                                } 
                                else
                                {
                                    $toDate ="-";
                                }
		                        
                               
                                
                                $totalUserCount = 0;
                                ?>
                            <tr>
								<td>
                                    <?php echo($sortOrder); ?>
                                </td>
                                <td>
                                    <?php echo($title); ?>
                                </td>
								<td>
                                    <?php echo($fromDate); ?>
                                </td>
                                <td>
                                    <?php echo($toDate); ?>
                                </td>
                                                                
                                <td style="text-align: center">
                                    <a href="addsemester.html?editid=<?php echo(EncodeQueryData($semesterId)); ?>">Edit</a> 
									| <a href="javascript:void(0);" class="deleteAjaxRow"
									semesterId="<?php echo EncodeQueryData($semesterId); ?>" semesterName="<?php echo($title); ?>" >Delete</a>
                                </td>
                            </tr>
                            <?php
                            }
                        }
						unset($objrotation);
                    ?>
                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

        <script type="text/javascript">

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            });
			
            var current_datatable = $("#datatable-responsive").DataTable({
                "aoColumns": [{
                    "sWidth": "5%"
                },{
                    "sWidth": "25%"
                },{
                    "sWidth": "25%"
                },{
                    "sWidth": "25%"
                }, {
                    "sWidth": "15%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, ]
            });
			
                 
             $(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var semesterId = $(this).attr('semesterId');
                var title = $(this).attr('semesterName');

                var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';
                var isUser = 1; //for Admin
               
                alertify.confirm('Semester: '+ title, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: semesterId,
                            userId: userId,
                            isUser: isUser,
                            type: 'semester'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });
        </script>


    </body>

    </html> 