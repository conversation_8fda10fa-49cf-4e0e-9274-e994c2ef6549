<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsMasteryEval.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsCourses.php');

$midtermrotationid = 0;
$schoolId = 0;
$currentstudentId = 0;
$selrotationId  = 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$schoolDate = '';
$rotationId = 0;
$courseId = 0;
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$Type = '';
$rotationtitle = '';
$studentfullname = '';
$display_from_date  = '';
$display_to_date   = '';
$schoolId = $currentSchoolId;

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSearch'])) {

    $currentstudentId = $_POST['hidenstudentid'];
    $currentstudentId = DecodeQueryData($currentstudentId);
    $selrotationId = $_POST['cboRotation'];
    $selrotationId = DecodeQueryData($selrotationId);
    $display_from_date = $_POST['fromDate'];
    $display_from_date = date("Y-m-d", strtotime($display_from_date));
    $display_to_date = $_POST['toDate'];
    $display_to_date = date("Y-m-d", strtotime($display_to_date));
}

//For Rotation Site
if (isset($_GET['rotationId'])) {
    $rotationId = $_GET['rotationId'];
    $selrotationId = DecodeQueryData($rotationId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

if (isset($_GET['selrotationId'])) {
    $selrotationIdFilter = DecodeQueryData($_GET['selrotationId']);
}


if (isset($_GET['studentId'])) {
    $currentstudentId = DecodeQueryData($_GET['studentId']);
}

$title = "Mastery Evaluation |" . $transchooldisplayName;

//For Daily Weekly List
$objMasteryEval = new clsMasteryEval();

$getDailydetails = $objMasteryEval->GetAllMasteryEvalDatewise($selrotationId, $display_from_date, $display_to_date, $currentstudentId);
$totalDailyCount = 0;
if ($getDailydetails != '') {
    $totalDailyCount = mysqli_num_rows($getDailydetails);
}
unset($objMasteryEval);

//For Student Name
$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
if ($Rowstudent != '') {
    $studentfullname = $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'];
}

unset($objStudent);

//For Rotation Name
$objRotation = new clsRotation();
$rotation = $objRotation->GetRotationBySchool($currentSchoolId);
if ($rotationId != '') {
    $rotationtitle = $objRotation->GetrotationTitle($rotationId, $schoolId);
}
unset($objRotation);
//Get Course List for dropdown
$objCourses = new clsCourses();
$courseList = $objCourses->GetAllCoursesBySchool($currentSchoolId);
unset($objCourses);


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link href="https://cdn.datatables.net/fixedcolumns/3.2.4/css/fixedColumns.bootstrap4.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <style>
        .DTFC_LeftBodyLiner {
            overflow-y: unset !important
        }

        .DTFC_RightBodyLiner {
            overflow-y: unset !important
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="clinical.html">Clinical</a></li>
                    <li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                    <li>Mastery Evaluation</li>
                </ol>
            </div>



        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Mastery Evaluation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Mastery Evaluation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Mastery Evaluation deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <div class="row">
            <form name="masteryList" id="masteryList" method="POST" action="masteryList.html?selrotationId=<?php echo (EncodeQueryData($selrotationId)); ?>">
                <div class="col-md-4  margin_bottom_ten">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="fromDate" style="margin-top:8px">From Date</label>
                        <div class='input-group date relative' name="fromDate" id='fromDate'>
                            <input type='text' name="fromDate" id="fromDate" value="<?php if ($display_from_date != '') echo date('m/d/Y', strtotime($display_from_date)); ?>" class="dateInputFormat form-control input-md r rotation_date" data-parsley-errors-container="#error-fromDate" required />
                            <span class="input-group-addon calender-icon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        <div id="error-fromDate"></div>
                    </div>
                </div>
                <div class="col-md-3  margin_bottom_ten">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="toDate" style="margin-top:8px">To Date</label>

                        <div class='input-group date relative' id='toDate'>

                            <input type='text' name="toDate" id="toDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php if ($display_to_date != '') echo date('m/d/Y', strtotime($display_to_date)); ?>" data-parsley-errors-container="#error-toDate" required />
                            <span class="input-group-addon calender-icon">
                                <span class="glyphicon glyphicon-calendar"></span>
                            </span>
                        </div>
                        <div id="error-toDate"></div>
                    </div>
                </div>

                <div class="col-md-3  margin_bottom_ten">
                    <div class="form-group">
                        <label class="col-md-4 control-label" for="cbostudent" style="margin-top:8px">Rotation</label>
                        <div class="col-md-8 pull-right padding_right_zero">
                            <select id="cboRotation" name="cboRotation" class="form-control input-md required-input select2_single">
                                <option value="" selected>Select</option>

                                <?php

                                if ($rotation != "") {
                                    while ($row = mysqli_fetch_assoc($rotation)) {
                                        $selrotationIdDropdown  = $row['rotationId'];
                                        $name  = stripslashes($row['title']);

                                ?>
                                        <option value="<?php echo (EncodeQueryData($selrotationIdDropdown)); ?>" <?php if ($selrotationId == $selrotationIdDropdown) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }

                                ?>
                            </select>

                            <input type="hidden" name="hidenstudentid" id="hidenstudentid" value="<?php echo EncodeQueryData($currentstudentId); ?>">
                        </div>

                    </div>
                </div>

                <div class="col-md-2  margin_bottom_ten">
                    <div class="form-group">
                        <button id="btnSearch" name="btnSearch" class="btn btn-success">Search</button>
                    </div>
                </div>
            </form>
        </div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>First<br>Name</th>
                    <th>Last<br>Name</th>
                    <th>Rotation</th>
                    <th style="text-align: center">Evaluation<br>Date</th>
                    <th style="text-align: center">Student<br>Sign<br>Date</th>
                    <th style="text-align: center">Instructor<br>Sign<br>Date</th>
                    <th style="text-align: center">CPAP</th>
                    <th style="text-align: center">Delivery<br>Of Neonate</th>
                    <th style="text-align: center">HFOV</th>
                    <th style="text-align: center">Tracheostomy<br>Care</th>
                    <th style="text-align: center">Total<br>Average</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalDailyCount > 0) {
                    while ($row = mysqli_fetch_array($getDailydetails)) {

                        //$title = $row['title'];
                        $rotationame = $row['title'];
                        $Ranktitle = $row['Ranktitle'];
                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $studentName = $firstName . ' ' . $lastName;
                        $studentMasteryEvalId = $row['MasteryEvalID'];
                        $evaluationDate = stripslashes($row['evaluationDate']);
                        $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                        $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                        $courselocationId = $row['locationId'];
                        $rotationId = $row['rotationId'];
                        $studentcomments = $row['studentcomments'];
                        $studentsignature = $row['studentsignature'];
                        $parentRotationId = stripslashes($row['parentRotationId']);
                        $rotationLocationId = stripslashes($row['rotationLocationId']);
                        $schoolDate = ($row['schoolDate']);
                        if($schoolDate !='')
                        $schoolDate = converFromServerTimeZone($schoolDate, $TimeZone);
                        $schoolDate = (date('m/d/Y', strtotime($schoolDate)));

                        $locationId = 0;

                        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                            if (!$rotationLocationId)
                                $locationId = $objRotation->GetLocationByRotation($rotationId);
                            else
                                $locationId  = $rotationLocationId;
                        } else {
                            $locationId  = $courselocationId;
                        }

                        //Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);
                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

                        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                        $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                        $dateOfInstructorSignature = stripslashes($row['dateOfInstructorSignature']);
                        $dateOfInstructorSignature = converFromServerTimeZone($dateOfInstructorSignature, $TimeZone);
                        $dateOfInstructorSignature = date("m/d/Y", strtotime($dateOfInstructorSignature));

                        //For Avg
                        $firstSectionAvg = $row['firstSectionAvg'];
                        $secondSectionAvg = $row['secondSectionAvg'];
                        $thirdSectionAvg = $row['thirdSectionAvg'];
                        $fourthSectionAvg = $row['fourthSectionAvg'];
                        $totalAvg = $row['totalAvg'];

                ?>
                        <tr>
                            <td><?php echo ($firstName); ?></td>
                            <td><?php echo ($lastName); ?></td>
                            <td><?php echo ($rotationame); ?></td>
                            <td style="text-align: center"><?php echo ($evaluationDate); ?></td>
                            <td style="text-align: center"><?php
                                                            if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {

                                                                echo ($dateOfStudentSignature);
                                                            } else {
                                                                echo "-";
                                                            }
                                                            ?></td>
                            <td style="text-align: center"><?php
                                                            if ($dateOfInstructorSignature != '' && $dateOfInstructorSignature != '12/31/1969' && $dateOfInstructorSignature != '01/01/1970' && $dateOfInstructorSignature != '0000-00-00') {

                                                                echo ($dateOfInstructorSignature);
                                                            } else {
                                                                echo "-";
                                                            }
                                                            ?></td>
                            <td style="text-align: center"><?php echo number_format((float)$firstSectionAvg, 2, '.', ''); ?></td>
                            <td style="text-align: center"><?php echo number_format((float)$secondSectionAvg, 2, '.', ''); ?></td>
                            <td style="text-align: center"><?php echo number_format((float)$thirdSectionAvg, 2, '.', ''); ?></td>
                            <td style="text-align: center"><?php echo number_format((float)$fourthSectionAvg, 2, '.', ''); ?></td>

                            <td style="text-align: center"><?php echo number_format((float)$totalAvg, 2, '.', ''); ?></td>
                            <td style="text-align: center">
                            <?php
                            $rotationStatus = checkRotationStatus($rotationId);
                               
                                if ($rotationStatus) { ?>
                                    <a href="addMastery.html?studentMasteryEvalId=<?php echo (EncodeQueryData($studentMasteryEvalId)); ?>&rotationId=<?php echo EncodeQueryData($rotationId); ?>&view=V">View</a>
                                <?php }
                                elseif ($studentcomments != '' && $studentsignature != '' && $dateOfStudentSignature != '11/30/-0001' && $dateOfStudentSignature != '0000-00-00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969' && $schoolDate != '11/30/-0001' && $schoolDate != '0000-00-00' && $schoolDate != '01/01/1970' && $schoolDate != '12/31/1969') { ?>
                                    <a href="addMastery.html?studentMasteryEvalId=<?php echo (EncodeQueryData($studentMasteryEvalId)); ?>&view=V">View</a>

                                <?php } else { ?>
                                    <a href="addMastery.html?studentMasteryEvalId=<?php echo (EncodeQueryData($studentMasteryEvalId)); ?>">Edit</a>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow" studentMasteryEvalId="<?php echo EncodeQueryData($studentMasteryEvalId); ?>" studentName="<?php echo ($studentName); ?>">Delete</a>
                                <?php } ?>

                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objRotation);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

    <script src="https://cdn.datatables.net/fixedcolumns/3.2.4/js/dataTables.fixedColumns.min.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();
            $(function() {
                $("#fromDate").datetimepicker({
                    format: "MM/DD/YYYY"
                });
            });

            $(function() {
                $("#toDate").datetimepicker({
                    format: "MM/DD/YYYY"
                });
            });

        });


        var current_datatable = $("#datatable-responsive").DataTable({

            responsive: false,
            scrollX: true,
            fixedColumns: {
                leftColumns: 4
            },

            "ordering": true,
            "order": [
                [0, "desc"]
            ],
            "aoColumns": [{
                "sWidth": "20%"
            }, {
                "sWidth": "20%",

            }, {
                "sWidth": "30%",

            }, {
                "sWidth": "25%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "25%"
            }, {
                "sWidth": "25%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "20%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "25%"
            }, {
                "sWidth": "25%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "25%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "20%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "15%",
                "sClass": "alignCenter",
                "bSortable": false
            }]
        });

        // ajax call for deleteAjaxRow
        $(document).on('click', '.deleteAjaxRow', function() {
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentMasteryEvalId = $(this).attr('studentMasteryEvalId');
            var title = $(this).attr('studentName');

            var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';
            var isUser = 1; //for Admin

            alertify.confirm('Mastery Eval: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentMasteryEvalId,
                        userId: userId,
                        isUser: isUser,
                        type: 'Mastery'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});
        });

    </script>
</body>

</html>