<?php
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSystemUser.php');
	include('../class/clsSchool.php');
    include('../class/clsStudent.php');
	include('../setRequest.php');
    
	// print_r($_POST) ;
	// print_r($_FILES) ;
    $studentId=0;
    if(isset($_GET['studentId'])) 
	{
		$studentId= DecodeQueryData($_GET['studentId']);	
		
	}
	if(isset($_GET['Type'])) 
	{
		$Type= ($_GET['Type']);
          
	}
	if(isset($_GET['studentImmunizationId'])) 
	{
		$studentImmunizationId= DecodeQueryData($_GET['studentImmunizationId']);
         
    }
    
	if($Type =='C')
	{
		$recordid =0;
	}
	else{
		$recordid =1;
	}
	$loggedUserId = $_SESSION["loggedUserId"];  
	
	
	if(isset($_POST['btnSubmit']))
	{
			
		
			if(isset($_FILES['studentDocument']))
			{

				$docTitle =($_FILES['studentDocument']['name']);
				
				$totalDocumentCount =count(array_filter($_FILES['studentDocument']['name']));
				if($totalDocumentCount > 0) 
				{
					
					$objstudent= new clsStudent();				
					
					if($docTitle) 
					{

						

						//Check User Directory
						$uploaddir = "../upload/schools/".$currentSchoolId;
						if(!file_exists($uploaddir))
						{
							mkdir($uploaddir); 
						}
						$uploaddir .= "/student";
						
						if(!file_exists($uploaddir))
						{
							mkdir($uploaddir); 
						}

						$uploaddir .= "/".$studentId."/";
						
						if(!file_exists($uploaddir))
						{
							mkdir($uploaddir); 
						}

						$uploaddir .= "documents/";
						
						if(!file_exists($uploaddir))
						{
							mkdir($uploaddir); 
						}
						
						for ($i = 0; $i < count($_FILES['studentDocument']['name']); $i++) 
						{
							if($docTitle[$i]!='')
							{
								$filetitle = preg_replace('/[&%$]+/', '-', $docTitle[$i]);
								$exitsFile =$objstudent->getStudentFile($studentId,$Type,$filetitle);
					
								if($exitsFile)
									{
										header('location:documentUpload.html?studentId='.EncodeQueryData($studentId).'&Type='.$Type.'&status=DuplicateFile');
										exit();
									}

								$ext = strtolower(pathinfo($filetitle, PATHINFO_EXTENSION));
								if($ext!="pdf")
								{
									header('location:documentUpload.html?studentId='.EncodeQueryData($studentId).'&Type='.$Type.'&status=InvalidFile');
									exit();
								}

								
						
								
								$UploadLargeFilePath = $uploaddir.$filetitle;
								copy($_FILES['studentDocument']['tmp_name'][$i], $UploadLargeFilePath);

							
								
								$objstudent->recordid = $recordid;
								$objstudent->documetype = $Type;
								$objstudent->StudentId = $studentId;
								$objstudent->uploadedBy = $loggedUserId;
								$objstudent->studentImmunizationId = $studentImmunizationId;
								
								$objstudent->fileTitle = $filetitle;
								$objstudent->fileName = $studentId.'_'.$filetitle;

								
								//-----------------------------------
								$retstudentDocumentId=$objstudent->savestudentDocumet();
								//-----------------------------------
							}
						}	
					}
				}
			}

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = $objLog::ADD;
			$type= "Document";
			$userType = $objLog::ADMIN; // User type is set to ADMIN
			$IsMobile = 0;

			$objStudent = new clsStudent();
			$objStudent->saveStudentAuditLog($retstudentDocumentId, $loggedUserId, $userType, $action, $IsMobile,0,$type);
			unset($objLog);
			//Audit Log Ends

			
			unset($objstudent);
			header('location:studentDocuments.html?studentId='.EncodeQueryData($studentId).'&Type='.$Type.'&studentImmunizationId='.EncodeQueryData($studentImmunizationId));
		
	}
	else
	{
		header('location:studentDocuments.html');
	}
	
?>