<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsSystemUser.php');
include('../includes/commonfun.php');
include('../setRequest.php');

//For School Name
$objSchool  = new clsSchool();
$displaySchoolName = $objSchool->GetSchoolName($currentSchoolId);

$loginTitle = ($currentSchoolId == 1) ? 'Login as user' : 'Login as school user';
unset($objSchool);

//For School System Users
$totalSchoolUser = '';
$objSystemUsers = new clsSystemUser();

$getSchoolUsers = isset($_GET['type']) ? 'GetDisplayAllSchoolSystemUsers' : 'GetSchoolSystemUsers';
$rowsSchoolUsers = $objSystemUsers->$getSchoolUsers($currentSchoolId);


if ($rowsSchoolUsers != '') {
    $totalSchoolUser = mysqli_num_rows($rowsSchoolUsers);
} else {
    header('location:settings.html?error=1');
    exit;
}
unset($objSystemUsers);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Users </title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">Super Admin</li>
                </ol>
            </div>
            <div class="pull-right">

                <a class="btn btn-link" href="addsystemuser.html">Add</a>
            </div>
        </div>
    </div>

    <div class="container">

        <?php
        function showAlert($type, $message)
        {
        ?>
            <div class="alert alert-<?php echo htmlspecialchars($type); ?> alert-dismissible fade in" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span></button>
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php
        }

        if (isset($_GET["status"])) {
            switch ($_GET["status"]) {
                case "added":
                    showAlert("success", "User added successfully.");
                    break;
                case "updated":
                    showAlert("success", "User updated successfully.");
                    break;
                case "StatusUpdated":
                    showAlert("success", "User status updated successfully.");
                    break;
                case "blockStatusUpdated":
                    showAlert("success", "User lock status updated successfully.");
                    break;
                case "Error":
                    showAlert("danger", "Error occurred.");
                    break;
            }
        }
        ?>


        <div id="divTopLoading">Loading...</div>

        <div class="row" style="margin: 0 !important;">
            <!-- <form name="rotationlist" id="rotationbtn" method="POST" action="rotations.html"> -->
            <a href="schoolusers.html?type=all" id="btnSearch" name="btnSearch" class="btn btn-success pull-right" style="margin-right: 16px;">Display All</a>
            <!-- </form> -->
        </div> <br>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Full Name</th>
                    <th>User Role</th>
                    <th>Email</th>
                    <th>Phone</th>

                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalSchoolUser > 0) {
                    while ($row = mysqli_fetch_array($rowsSchoolUsers)) {

                        $systemUserMasterId = $row['systemUserMasterId'];
                        $firstName = stripslashes($row['firstName']);
                        $lastName = stripslashes($row['lastName']);
                        $fullName = $firstName . ' ' . $lastName;
                        $email = stripslashes($row['email']);
                        $phone = stripslashes($row['phone']);
                        $isPrimaryUser = stripslashes($row['isPrimaryUser']);
                        $userName = stripslashes($row['username']);
                        $schoolRoleTitle = stripslashes($row['roleTitle']);

                        $IsPublished = $row['isActive'];
                        $isBlocked = $row['isBlocked'];
                        $displayStatus = "";
                        $updateStatus = "0";
                        $buttoncss = "btn-primary";
                        $displayBlockStatus = "";
                        if ($IsPublished == "1") {
                            $displayStatus = "Active";
                            $updateStatus = "0";
                            $buttoncss = "text-primary";
                            $isShowemail = 1;
                        } else {
                            $displayStatus = "Inactive";
                            $updateStatus = "1";
                            $buttoncss = "text-warning";
                            $isShowemail = 0;
                        }
                        if ($isBlocked == "0") {
                            $displayBlockStatus = "Unlocked";
                            $updateBlockStatus = "1";
                            $buttoncss = "text-primary";
                        } else {
                            $displayBlockStatus = "Locked";
                            $updateBlockStatus = "0";
                            $buttoncss = "text-warning";
                        }
                ?>
                        <tr class="dataTableRowSelected">
                            <td>
                                <?php echo ($fullName); ?>
                            </td>

                            <td>
                                <?php echo ($schoolRoleTitle); ?>
                            </td>
                            <td>
                                Email:<a href="mailto:<?php echo ($email); ?>">
                                    <?php echo ($email); ?>
                                </a>
                                <br>Username: <?php echo ($userName); ?>


                            </td>
                            <td>
                                <a href="tel:<?php echo ($phone); ?>">
                                    <?php echo ($phone); ?>
                                </a>
                            </td>

                            <td style="text-align: center">
                                <a class="<?php echo ($buttoncss); ?>" href="schoolusertranssubmit.html?id=<?php echo EncodeQueryData($systemUserMasterId); ?>&newStatus=<?php echo ($updateStatus); ?>&userId=<?php echo $_SESSION['loggedUserId']; ?>&isUser=<?php echo "1"; ?>&type=status">
                                    <?php echo ($displayStatus); ?>
                                </a>
                                |
                                <a class="<?php echo ($buttoncss); ?>" href="schoolusertranssubmit.html?id=<?php echo EncodeQueryData($systemUserMasterId); ?>&newblockStatus=<?php echo ($updateBlockStatus); ?>&userId=<?php echo $_SESSION['loggedUserId']; ?>&isUser=<?php echo "1"; ?>&type=block">
                                    <?php echo ($displayBlockStatus); ?>
                                </a>
                                <?php if ($isShowemail == 1) { ?>
                                    |
                                    <a href="javascript:void(0);" systemUserMasterId="<?php echo ($systemUserMasterId); ?>" currentSchoolId="<?php echo ($currentSchoolId); ?>" class="loginEmailAjaxRow" schoolUserFullName="<?php echo ($fullName); ?>">Send email</a>
                                <?php } ?>
                                | <a href="javascript:void(0)" class="loginAsSchoolUser" systemUserMasterId="<?php echo EncodeQueryData($systemUserMasterId); ?>" schoolUserFullName="<?php echo ($fullName); ?>"><?php echo $loginTitle; ?></a>
                                | <a href="addsystemuser.html?id=<?php echo (EncodeQueryData($systemUserMasterId)); ?>">Edit</a>

                                <?php
                                if ($isPrimaryUser == 1) {
                                ?>
                                    | <a class="deleteAjaxRow" onclick="javascript:ShowDeleteMessage();" href="javascript:void(0);">Delete</a>
                                <?php
                                } else {
                                ?>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow" systemUserMasterId="<?php echo EncodeQueryData($systemUserMasterId); ?>" currentSchoolId="<?php echo ($currentSchoolId); ?>" schoolUserFullName="<?php echo ($fullName); ?>">Delete</a>
                                <?php
                                }
                                ?>

                            </td>
                        </tr>
                <?php


                    }
                }
                ?>



            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "aoColumns": [
                { "sWidth": "20%" },
                { "sWidth": "25%", "sClass": "alignCenter" },
                { "sWidth": "10%", "bSortable": false },
                { "sWidth": "10%", "bSortable": false },
                { "sWidth": "40%", "sClass": "alignCenter", "bSortable": false }
            ]

        });

        function ShowDeleteMessage() {
            alertify.alert('Warning', 'This is the Primary User. You can\'t delete this.');
        }

        $(document).on('click', '.loginAsSchoolUser', function() {

            var systemUserMasterId = $(this).attr('systemUserMasterId');
            var schoolUserFullName = $(this).attr('schoolUserFullName');

            alertify.confirm('Login Confirmation', 'Continue with login as ' + schoolUserFullName + '?', function() {
                window.location.href = 'loginasschooluser.html?userId=' + systemUserMasterId + '&type=adminUser';
            }, function() {});

        });

        $(document).on('click', '.loginEmailAjaxRow', function() {

            var systemUserMasterId = $(this).attr('systemUserMasterId');
            var schoolUserFullName = $(this).attr('schoolUserFullName');
            var currentSchoolId = $(this).attr('currentSchoolId');


            alertify.confirm('User: ' + schoolUserFullName, 'Continue with send login detail\'s email?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_login_email_to_school_user.html",
                    data: {
                        id: systemUserMasterId,
                        schoolId: currentSchoolId,
                        type: 'systemuser'
                    },
                    success: function() {
                        alertify.success('Sent');
                    }
                });
            }, function() {});

        });


        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var systemUserMasterId = $(this).attr('systemUserMasterId');
            var schoolUserFullName = $(this).attr('schoolUserFullName');
            var currentSchoolId = $(this).attr('currentSchoolId');

            var isCurrentSchoolSuperAdmin = "<?php echo $isCurrentSchoolSuperAdmin; ?>";

            var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';
            var isUser = 1; //for Admin


            alertify.confirm('User: ' + schoolUserFullName, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: systemUserMasterId,
                        userId: userId,
                        isUser: isUser,
                        type: 'systemuser',
                        schoolId: currentSchoolId,
                        isCurrentSchoolSuperAdmin : isCurrentSchoolSuperAdmin,

                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });
    </script>


</body>

</html>