<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');
    include('../class/clsClinicianImmunization.php');   
	include('../setRequest.php'); 
	
   
    
	  $clinicianIdImmunizationId=0;
	 if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
	   if(isset($_GET['clinicianIdImmunizationId'])) 
	  {
		$clinicianIdImmunizationId = $_GET['clinicianIdImmunizationId'];
        $clinicianIdImmunizationId = DecodeQueryData($clinicianIdImmunizationId);
      }
	   
         
		$status = ($clinicianIdImmunizationId > 0) ? 'Updated' : 'Added';
		 //echo 'status->'.$status;exit;	
		$systemUserId= $_SESSION['loggedUserId'];
		$immunizationId=0;
		$immunizationDate=GetDateStringInServerFormat($_POST['StartDate']);
		$immunizationMId  = ($_POST['txtreimmunizationMId']);
		$clinicianId  = ($_POST['txtclinicianname']);
		$immunizationNote  = ($_POST['immunization_note']);
		$ImmunizationNotification  = GetDateStringInServerFormat($_POST['ImmunizationNotification']);
		$ExpiryDate=GetDateStringInServerFormat($_POST['ExpiryDate']);
		
		$objimmunization = new clsClinicianImmunization();
		         
		   $objimmunization->immunizationDate = $immunizationDate;
		   $objimmunization->immunizationNotificationDate = $ImmunizationNotification;
		   $objimmunization->expiryDate = $ExpiryDate;
		   $objimmunization->immunizationMId = $immunizationMId;		
		   $objimmunization->clinicianId = $clinicianId;	
		   $objimmunization->immunizationNote = $immunizationNote;	
		   $objimmunization->createdBy = $systemUserId;	
		   $clinicianIdImmunizationId=$objimmunization->SaveClinicianImmunization($clinicianIdImmunizationId); 	                     
					  
       unset($objimmunization);

		if($clinicianIdImmunizationId > 0)
		{
			
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action =  $objLog::EDIT;
			$userType = $objLog::ADMIN; // User type is set to ADMIN
			$IsMobile = 0;

			$objimmunization = new clsClinicianImmunization();
			$objimmunization->saveClinicianImmunizationAuditLog($clinicianIdImmunizationId, $_SESSION['loggedUserId'], $userType, $action, $IsMobile);

			unset($objLog);
			//Audit Log End
			
		  if($clinicianIdImmunizationId==0)
					header('location:singleclinicianimmunization.html?clinicianIdImmunizationId='.EncodeQueryData($retstudentImmunizationId).'&clinicianId='.EncodeQueryData($clinicianId).'&status='.$status);
				else
					header('location:singleclinicianimmunization.html?clinicianIdImmunizationId='.EncodeQueryData($retstudentImmunizationId).'&clinicianId='.EncodeQueryData($clinicianId).'&status='.$status);
			exit();	
							
		}
		else
		{
			header('location:studentimmunization.html?status=error');
		}
		
	}
	
	{
		header('location:studentimmunization.html');
		exit();
	}
		
?>
 