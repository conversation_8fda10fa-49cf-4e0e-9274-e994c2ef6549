<?php
// phpinfo();exit;
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsTemplateEngine.php');
include('../setRequest.php');

@session_start();

//$attempt = isset($_GET["attempt"]) ? $_GET["attempt"] : 0;
//	$attempt = DecodeQueryData($attempt);
//	$attentCount  = $attempt + 1;

$attempt = isset($_GET["attempt"]) ? $_GET["attempt"] : 0;
$attentCount = 0;
if ($attempt) {
	$attempt = DecodeQueryData($attempt);
	$attentCount  = $attempt + 1;
}

if (isset($_SESSION['loggedUserId'])) {
	header('location:dashboard.html');
	exit();
}
if (isset($_GET['employercoarcId'])) {
	if (isset($_GET['studentId']) && ($_GET['employercoarcId'])) {
		$studentId = $_GET['studentId'];
		$studentId = DecodeQueryData($studentId);
		$employercoarcId = $_GET['employercoarcId'];
		$employercoarcId = DecodeQueryData($employercoarcId);
	}
} else
		if (isset($_GET['personnelCoarcId']) && ($_GET['studentId'])) {
	$studentId = $_GET['studentId'];
	$studentId = DecodeQueryData($studentId);
	$personnelCoarcId = $_GET['personnelCoarcId'];
	$personnelCoarcId = DecodeQueryData($personnelCoarcId);
}

$BASEPATH = BASE_PATH;
?>


<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<link rel="shortcut icon" href="<?php echo ($dynamicOrgUrl); ?>/assets/images/favicon.ico" type="image/x-icon">
	<link rel="icon" href="<?php echo ($dynamicOrgUrl); ?>/assets/images/favicon.ico" type="image/x-icon">
	<title>Administrator Sign In</title>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/login.css">
	<link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
	<script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"></script>
	<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.7.2/css/all.min.css" integrity="sha512-Evv84Mr4kqVGRNSgIGL/F/aIDqQb7xQ2vcrdIwxfjThSH8CSR7PBEakCr51Ck+w+/U6swU2Im1vVX0SVk9ABhg==" crossorigin="anonymous" referrerpolicy="no-referrer" />

	<style>

	</style>
</head>

<body class="login-page-body-bg">


	<div class="row main-slider">

		<div class="right-panel p-0 mobile-hide" style="">
			<div id="demo" class="carousel slide" data-ride="carousel">

				<!-- Indicators -->
				<ul class="carousel-indicators">
					<li data-target="#demo" data-slide-to="0" class="active"></li>
					<li data-target="#demo" data-slide-to="1"></li>
					<li data-target="#demo" data-slide-to="2"></li>
				</ul>

				<!-- The slideshow -->
				<div class="carousel-inner no-padding">
					<div class="carousel-item active">
						<div class="col-xs-12 col-sm-12 col-md-12 p-0" style="height: 100vh;">
							<img class="slider-img" src="<?php echo $BASEPATH; ?>/assets/images/Login/slider-img6.jpg">

							<div class="custum-div">
								<div class="slider-msg">
									<h1 class="slider-heading animated fadeInLeft">Welcome to <span><?php echo ($currenschoolDisplayname); ?></span></h1>
									<p class="slider-desc animated fadeInRightBig">
										Expert Support at Your Fingertips: Our Clinical Trac™ Online Clinical Management Tool is backed by a dedicated team of specialists, ready to guide you through every aspect, ensuring seamless integration with your existing documentation and tracking procedures.
									</p>
								</div>
							</div>
						</div>

					</div>
					<div class="carousel-item">
						<div class="col-xs-12 col-sm-12 col-md-12 p-0" style="height: 100vh;">
							<img class="slider-img" src="<?php echo $BASEPATH; ?>/assets/images/Login/slider-img10.jpg">
							<div class="custum-div">
								<div class="slider-msg">
									<h1 class="slider-heading animated fadeInLeft">Welcome to <span><?php echo ($currenschoolDisplayname); ?></span></h1>
									<p class="slider-desc animated fadeInRightBig">Clinical Trac™ is your comprehensive platform for managing all aspects of your clinical program. Streamline attendance tracking, simplify performance evaluations, and enhance collaboration among faculty, staff, and students. With Clinical Trac, you gain valuable insights into program effectiveness and ensure seamless operations. Let's get started.
									</p>
								</div>
							</div>
						</div>

					</div>
					<div class="carousel-item">
						<div class="col-xs-12 col-sm-12 col-md-12 p-0" style="height: 100vh;">
							<img class="slider-img" src="<?php echo $BASEPATH; ?>/assets/images/Login/slider-img11.jpg">
							<div class="custum-div">
								<div class="slider-msg">
									<h1 class="slider-heading animated fadeInLeft">Welcome to <span><?php echo ($currenschoolDisplayname); ?></span></h1>
									<p class="slider-desc animated fadeInRightBig">Experience the power of Clinical Trac™. You have the tools to optimize your entire clinical program. From managing student and tracking attendance to conducting comprehensive evaluations and ensuring compliance, Clinical Trac simplifies your workload and provides valuable data-driven insights. We are here to support your success.
									</p>
								</div>
							</div>
						</div>

					</div>
				</div>

			</div>
		</div>

		<div class="left-panel p-0 mobile-center" style="position: relative;">

			<!-- <div class="alert alert-success alert-dismissible fade show" style="position: absolute; top: 20px; width: fit-content; right: 10px;border-radius: 8px; z-index: 99;" role="alert">
	   successfully Logged Out
		<button type="button" class="close" data-dismiss="alert" aria-label="Close">
		  <span aria-hidden="true">&times;</span>
		</button>
	  </div> -->

			<!-- <div class="alert alert-danger alert-dismissible fade show" style="position: absolute; top: 20px; width: fit-content; right: 10px;border-radius: 8px; z-index: 99;" role="alert">
				successfully Logged Out
				<button type="button" class="close" data-dismiss="alert" aria-label="Close">
					<span aria-hidden="true">&times;</span>
				</button>
			</div> -->

			<?php

			if (isset($_GET["attempt"]) && $_GET["status"] == "error") {

				if ($attempt == "1") { ?>

					<div class="alert alert-danger alert-dismissible fade show alert-position" role="alert">
						You have one more attempt, if unsuccessful click on "Forgot password" and a new Temporary password will be emailed to you.
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				<?php
				} elseif ($attempt >= "2") { ?>
					<div class="alert alert-danger alert-dismissible fade show alert-position" role="alert">
						Please click on “Forgot password” to have a new Temporary Password emailed to you
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				<?php }
			}

			if (isset($_GET["status"])) {
				if ($_GET["status"] == "inactive") {
				?>

					<div class="alert alert-danger alert-dismissible fade show alert-position" role="alert">
						Your account is inactive.
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>

				<?php
				} else if ($_GET["status"] == "sessionexpire") {
				?>

					<div class="alert alert-danger alert-dismissible fade show alert-position" role="alert">
						Session expired, Please login again.
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				<?php
				} else if ($_GET["status"] == "logout") {
				?>
					<div class="alert alert-danger alert-dismissible fade show alert-position" role="alert">
						You have logged out successfully.
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
				<?php
				} else if ($_GET["status"] == "blocked") {
				?>
					<div class="alert alert-danger alert-dismissible fade show alert-position" role="alert">
						You are Blocked .
						<button type="button" class="close" data-dismiss="alert" aria-label="Close">
							<span aria-hidden="true">&times;</span>
						</button>
					</div>
			<?php
				}
			}
			?>


			<div class="left-section">
				<div class="ct-logo"><img src="<?php echo $BASEPATH; ?>/assets/images/logo_small.png" alt=""></div>
				<div class="form-section">
					<form accept-charset="UTF-8" id="frmLogin" data-parsley-validate="" method="post" <?php if (isset($_GET['employercoarcId'])) {
																											if (isset($_GET['studentId']) && ($_GET['employercoarcId'])) { ?>action="userLoginsubmit.html?employercoarcId=<?php echo EncodeQueryData($employercoarcId); ?>&studentId=<?php echo EncodeQueryData($studentId); ?>" <?php }
																																																																															} elseif (isset($_GET['personnelCoarcId']) && ($_GET['studentId'])) { ?>action="userLoginsubmit.html?personnelCoarcId=<?php echo EncodeQueryData($personnelCoarcId); ?>&studentId=<?php echo EncodeQueryData($studentId); ?>" <?php } else { ?>action="userLoginsubmit.html" <?php } ?>>
						<input type="hidden" name="attempt" id="attempt" value="<?php echo EncodeQueryData($attentCount); ?>">

						<div class="school-logo-section">
							<img class="school-logo" style="object-fit: contain;" src="<?php echo ($currenSchoolLogoImagePath); ?>" alt="">
						</div>
						<div class="mobile-padding">
							<div class="form-group">
								<label for="exampleInputEmail1">Username</label>
								<input class="form-control rounded-7" placeholder="Username" required name="txtUsername" type="text" value="">
							</div>
							<div class="form-group m-0 position-relative">
								<label for="exampleInputPassword1">Password</label>
								<input type="password" class="form-control rounded-7" id="exampleInputPassword1" placeholder="Password" required name="txtPassword" value="">
								<span
									id="togglePassword"
									class="toggle-password">
									<i class="fa fa-eye" aria-hidden="true"></i>
								</span>
							</div>

							<div>
								<a class="forgot-password-label" href="forgotpassword.html">Forgot Password</a>
							</div>

							<button type="submit" class="btn btn-submit rounded-7" name="btnLogin" value="Sign In as Student">Sign In as Admin</button>
						</div>
					</form>

					<div class="login-footer">
					<div class="footer-agreement-text">
  Clinical Trac™ provides a services level warranty and is only redistributed under a Clinical Trac™ Online Clinical Management System License Agreement.
  
  <div class="custom-divider-icon">
  <span class="line"></span>
  <span class="icon">★</span>
  <span class="line"></span>
</div>
  
  © 2013 - 2025 Clinical Trac™, LLC.
  <br>Powered by C&G Technical Group.
</div>


						<div class="download-icon">
							<a target="_blank" href="https://play.google.com/store/apps/details?id=com.ctradiology.app">
								<img style="width: 120px;" src="<?php echo $BASEPATH; ?>/assets/images/Login/google-play.png" alt="">
							</a>
							<a target="_blank" href="https://apps.apple.com/cz/app/clinical-trac-rad/id6742132391">
								<img style="width: 120px;" src="<?php echo $BASEPATH; ?>/assets/images/Login/app-store.png" alt="">
							</a>
						</div>
					</div>
				</div>
			</div>


		</div>
	</div>


	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
	<script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>


	<script type="text/javascript">
		alertify.defaults.transition = "slide";
		alertify.defaults.theme.ok = "btn btn-primary";
		alertify.defaults.theme.cancel = "btn btn-danger";
		alertify.defaults.theme.input = "form-control";
		alertify.defaults.notifier.delay = 5;

		$(function() {

			$('#frmLogin').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {
					ShowProgressAnimation();
					return true; // Don't submit form for this demo
				});

		});
		$(document).ready(function() {
			setTimeout(function() {
				$(".alert").addClass("alert-slide-out");
				$('body').css('overflow', 'hidden');
				setTimeout(function() {
					$(".alert").alert('close');
				}, 500);
			}, 5000);
		});
	</script>

	<script>
		const togglePassword = document.getElementById("togglePassword");
		const passwordInput = document.getElementById("exampleInputPassword1");

		togglePassword.addEventListener("click", function() {
			const type =
				passwordInput.getAttribute("type") === "password" ? "text" : "password";
			passwordInput.setAttribute("type", type);

			// Toggle icon using innerHTML instead of textContent
			this.innerHTML =
				type === "password" ?
				`<i class="fa fa-eye" aria-hidden="true"></i>` :
				`<i class="fa fa-eye-slash" aria-hidden="true"></i>`;
		});
	</script>
</body>

</html>