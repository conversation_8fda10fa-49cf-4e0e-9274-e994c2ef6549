<?php
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsTemplateEngine.php');
include('../setRequest.php');

?>

<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link rel="shortcut icon" href="<?php echo ($dynamicOrgUrl); ?>/assets/images/favicon.ico" type="image/x-icon">
  <link rel="icon" href="<?php echo ($dynamicOrgUrl); ?>/assets/images/favicon.ico" type="image/x-icon">
  <title>Reset Password</title>
  <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/forgotpassword.css">
  <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/css/bootstrap.min.css">
  <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.3.1/jquery.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/popper.js/1.12.9/umd/popper.min.js"></script>

  <?php
  include('includes/headercss.php');

  $BASEPATH = BASE_PATH;
  ?>

</head>

<body class="login-page-body-bg">
  <!-- <div class="row margin_zero" id="login-page">
    <div class="row margin_zero" id="top-bar-header">
      <nav class="navbar navbar-default">
        <div class="container">
          <div class="navbar-header">
            <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#bs-example-navbar-collapse-1" aria-expanded="false">
              <span class="sr-only">Toggle navigation</span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
              <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="index.html"><img class="img-responsive" src="<?php echo ($currenSchoolLogoImagePath); ?>" alt=""></a>
          </div>

          <div class="collapse navbar-collapse" id="bs-example-navbar-collapse-1">
          </div>
        </div>
      </nav>
    </div>
  </div> -->

  <!-- <div class="row margin_zero login-wrapper">
    <div class="container">
      <div class="row">
        <div class="col-md-7 col-sm-7">
          <div class="row margin_zero left-box-title">
            <h2><?php echo ($currenschoolDisplayname); ?></h2>
            <p>Designed to simplify processes & manage areas of clinical programs, including IRR, attendance, procedures, check-offs, interactions and much more.</p>

          </div>
        </div>
        <div class="col-md-5 col-sm-5">
          <div class="row margin_zero right-box-title">
            <div class="panel panel-default">
              <div class="panel-heading">
                <h3 class="panel-title"> Send Password Reset </h3>
              </div>
              <div class="panel-body">

                <?php
                if (isset($_GET["status"])) {
                  if ($_GET["status"] == "InvalidEmail") {
                ?>
                    <div class="alert alert-danger alert-dismissible fade in" role="alert">
                      <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                      </button>This email is not registered.
                    </div>
                  <?php
                  } else if ($_GET["status"] == "EmailSent") {
                  ?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                      <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                      </button>
                      Temporary password has been sent to
                      <br>your registered email.
                    </div>
                <?php
                  }
                }
                ?>

                <form accept-charset="UTF-8" role="form" id="frmFG" data-parsley-validate="" method="post" action="forgotpasswordsubmit.html">
                  <fieldset>
                    <div class="form-group">
                      <input class="form-control" placeholder="Email" name="txtEmail" required type="email">
                    </div>

                    <div class="form-group">
                      <input class="btn btn-lg btn-blue btn-block" type="submit" value="Submit" name="btnSubmit">
                    </div>
                    <div class="row margin_zero forgot-password text-center myclass w-full">
                      <a href="index.html">Sign In</a>
                    </div>
                  </fieldset>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div> -->

  <div class="row main-slider">

    <div class="right-panel p-0 mobile-hide" style="">
      <div id="demo" class="carousel slide" data-ride="carousel">

        <ul class="carousel-indicators">
          <li data-target="#demo" data-slide-to="0" class="active"></li>
          <li data-target="#demo" data-slide-to="1"></li>
          <li data-target="#demo" data-slide-to="2"></li>
        </ul>

        <!-- The slideshow -->
        <div class="carousel-inner no-padding">
          <div class="carousel-item active">
            <div class="col-xs-12 col-sm-12 col-md-12 p-0" style="height: 100vh;">
              <img class="slider-img" src="<?php echo $BASEPATH; ?>/assets/images/Login/slider-img6.jpg">

              <div class="custum-div">
                <div class="slider-msg">
                  <h1 class="slider-heading animated fadeInLeft">Welcome to <span><?php echo ($currenschoolDisplayname); ?></span></h1>
                  <p class="slider-desc animated fadeInRightBig">
                    Expert Support at Your Fingertips: Our Clinical Trac™ Online Clinical Management Tool is backed by a dedicated team of specialists, ready to guide you through every aspect, ensuring seamless integration with your existing documentation and tracking procedures.
                  </p>
                </div>
              </div>
            </div>

          </div>
          <div class="carousel-item">
            <div class="col-xs-12 col-sm-12 col-md-12 p-0" style="height: 100vh;">
              <img class="slider-img" src="<?php echo $BASEPATH; ?>/assets/images/Login/slider-img10.jpg">
              <div class="custum-div">
                <div class="slider-msg">
                  <h1 class="slider-heading animated fadeInLeft">Welcome to <span><?php echo ($currenschoolDisplayname); ?></span></h1>
                  <p class="slider-desc animated fadeInRightBig">Clinical Trac™ is your comprehensive platform for managing all aspects of your clinical program. Streamline attendance tracking, simplify performance evaluations, and enhance collaboration among faculty, staff, and students. With Clinical Trac, you gain valuable insights into program effectiveness and ensure seamless operations. Let's get started.
                  </p>
                </div>
              </div>
            </div>

          </div>
          <div class="carousel-item">
            <div class="col-xs-12 col-sm-12 col-md-12 p-0" style="height: 100vh;">
              <img class="slider-img" src="<?php echo $BASEPATH; ?>/assets/images/Login/slider-img11.jpg">
              <div class="custum-div">
                <div class="slider-msg">
                  <h1 class="slider-heading animated fadeInLeft">Welcome to <span><?php echo ($currenschoolDisplayname); ?></span></h1>
                  <p class="slider-desc animated fadeInRightBig">Experience the power of Clinical Trac™. You have the tools to optimize your entire clinical program. From managing student and tracking attendance to conducting comprehensive evaluations and ensuring compliance, Clinical Trac simplifies your workload and provides valuable data-driven insights. We are here to support your success.
                  </p>
                </div>
              </div>
            </div>

          </div>
        </div>

      </div>
    </div>

    <div class="left-panel p-0 mobile-center" style="position: relative;">

      <?php

      if (isset($_GET["status"])) {
        if ($_GET["status"] == "inactive") {
      ?>

          <div class="alert alert-danger alert-dismissible fade show alert-position" role="alert">
            Your account is inactive.
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>

        <?php
        } else if ($_GET["status"] == "blocked") {
        ?>
          <div class="alert alert-danger alert-dismissible fade show alert-position" role="alert">
            You are Blocked .
            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
              <span aria-hidden="true">&times;</span>
            </button>
          </div>
      <?php
        }
      }
      ?>


      <div class="left-section">
        <div class="ct-logo"><img src="<?php echo $BASEPATH; ?>/assets/images/logo_small.png" alt=""></div>
        <div class="form-section">
          <?php
          if (isset($_GET["status"])) {
            if ($_GET["status"] == "InvalidEmail") {
          ?>
              <div class="alert alert-danger alert-dismissible fade show alert-position" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                </button>
                Invalid Email.
              </div>

            <?php
            } else if ($_GET["status"] == "EmailSent") {
            ?>
              <div class="alert alert-success alert-dismissible fade show alert-position" role="alert">
                <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                </button>
                Temporary Password has been sent to your registered email.
              </div>
          <?php
            }
          }
          ?>
          <form accept-charset="UTF-8" role="form" id="frmFG" data-parsley-validate="" method="post" action="forgotpasswordsubmit.html">


            <div class="school-logo-section">
              <img class="school-logo" style="object-fit: contain;" src="<?php echo ($currenSchoolLogoImagePath); ?>" alt="">
            </div>

            <fieldset>
              <div class="form-group">
                <label for="exampleInputEmail1" style="color: #000000;">Email</label>
                <input class="form-control rounded-7" placeholder="Email" name="txtEmail" required type="email">
              </div>

              <div class="form-group">
                <input class="btn btn-submit rounded-7" type="submit" value="Submit" name="btnSubmit">
              </div>
              <div class="row margin_zero forgot-password text-center myclass w-full" style="display: flex; justify-content: center;">
                <a href="index.html">Sign In</a>
              </div>
            </fieldset>
          </form>
        
        <div class="login-footer">
          <div class="footer-agreement-text">
            Clinical Trac™ provides a services level warranty and is only redistributed under a Clinical Trac™ Online Clinical Management System License Agreement.
            <div class="custom-divider-icon">
  <span class="line"></span>
  <span class="icon">★</span>
  <span class="line"></span>
</div>
              © 2013 - 2025 Clinical Trac™, LLC.
            <br>Powered by C&G Technical Group.
          </div>
        
            <div class="download-icon">
              <a target="_blank" href="https://play.google.com/store/apps/details?id=com.ctradiology.app">
                <img style="width: 100px;" src="<?php echo $BASEPATH; ?>/assets/images/Login/google-play.png" alt="">
              </a>
              <a target="_blank" href="https://apps.apple.com/cz/app/clinical-trac-rad/id6742132391">
                <img style="width: 100px;" src="<?php echo $BASEPATH; ?>/assets/images/Login/app-store.png" alt="">
              </a>
            </div>
          </div>
        </div>
      </div>


    </div>
  </div>

  <?php //include('includes/portalfooter.php'); 
  ?>
  <?php //include('includes/footer.php'); 
  ?>

  <?php
  if (detect_mobile()) {
  ?>
    <script type="text/javascript">
      $('.footer-bg').hide();
    </script>
  <?php
  }
  ?>

  <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
  <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/4.0.0/js/bootstrap.min.js"></script>

  <script type="text/javascript">
    alertify.defaults.transition = "slide";
    alertify.defaults.theme.ok = "btn btn-primary";
    alertify.defaults.theme.cancel = "btn btn-danger";
    alertify.defaults.theme.input = "form-control";

    $(function() {
      $('#frmFG').parsley().on('field:validated', function() {
          var ok = $('.parsley-error').length === 0;
        })
        .on('form:submit', function() {
          ShowProgressAnimation();
          return true; // Don't submit form for this demo
        });
    });

    $(document).ready(function() {
      setTimeout(function() {
        $(".alert").addClass("alert-slide-out");
        $('body').css('overflow', 'hidden');
        setTimeout(function() {
          $(".alert").alert('close');
        }, 500);
      }, 5000);
    });
  </script>

</body>

</html>