<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');      	
	include('../class/clsSemester.php');
	include('../setRequest.php'); 
	
		//print_r($_POST);
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		
		$semesterId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;
		
		$status = ($semesterId > 0) ? 'updated' : 'added';
		
		$title = $_POST['txtSemester'];
		$sortOrder = $_POST['txtOrder'];
		$startdate = GetDateStringInServerFormat($_POST['start']);
		$enddate = GetDateStringInServerFormat($_POST['end']);
		
		
		//Save data
		$objSemester = new clsSemester();
		$objSemester->title = $title;		
		$objSemester->sortOrder = $sortOrder;		
		$objSemester->fromDate = $startdate;		
		$objSemester->toDate = $enddate;		
		$objSemester->schoolId = $currentSchoolId;					
		$objSemester->createdBy = $_SESSION['loggedUserId'];
		$objSemester->updatedBy = $_SESSION['loggedUserId'];

		$retsemesterId = $objSemester->SaveSemester($semesterId);
		
		unset($objSemester);
		if($retsemesterId > 0)
		{
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = ($semesterId > 0) ? $objLog::EDIT : $objLog::ADD;
			$userType = $objLog::ADMIN; // User type is set to ADMIN
			$IsMobile = 0;

			$objSemester = new clsSemester();
			$objSemester->saveSemesterAuditLog($retsemesterId, $_SESSION['loggedUserId'], $userType, $action, $IsMobile);

			unset($objLog);
			//Audit Log End

			header('location:viewsemester.html?status='.$status);
		}
		else
		{
			header('location:addsemester.html?status=error');
		}
	}
	else
	{
		header('location:viewsemester.html');
		exit();
	}

?>