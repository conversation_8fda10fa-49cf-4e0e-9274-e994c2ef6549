<?php
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php'); 
	include('../class/clsState.php'); 	
	include('../class/clsStudent.php'); 
	include('../class/clsClinician.php'); 
	include('../class/clsCountryStateMaster.php');
    include('../class/clsClinicianCertificationLog.php'); 
	include('../setRequest.php');

	
  //  $systemUserRoleMasterId  = '';
	$studentId  = '0';
	$accreditationId  = 0;
    $displayName  = '';
	
	$dbStateId=0;
	$drugScreeningDate = null;
	$criminalBackgroundCheck = null;
	$healthCertificationDate = null;
	$licenseExpiration = null;
	$aclsCertificationDate = null;
	$aclsExpirationDate = null;
	$bclsCertificationDate = null;
	$bclsExpirationDate = null;
	$palsCertificationDate = null;
	$palsExpirationDate = null;
	$AARCMembershipNumber = null;
	$CSRCMembershipNumber  = null;
	$licenseNumber = null;
	$aclsNumber = null;
	$palsNumber = null;
	$licenseCredentials = null;
	$bclsNumber = null;
	$bclsStatus = null;
	$palsStatus = null;
	$aclsStatus  = null;
   
	    $schoolId= $currentSchoolId;
	
		$objSchool = new clsSchool();
		$row = $objSchool->GetSchoolDetails($schoolId);
        unset($objSchool);
	
	
	
	
		if(isset($_GET['clinicianId'])) //Edit Mode
		{
	
			$clinicianId = $_GET['clinicianId'];
			$clinicianId = DecodeQueryData($clinicianId);

			$title ="Edit Certification Log";
			$bedCrumTitle = 'Edit';

			
			$objCertificationLog = new clsClinicianCertificationLog();
			$row = $objCertificationLog->GetClinicianCertificationLog($clinicianId);  
			unset($objCertificationLog);
			
			if($row){

				$drugScreeningDate = '';
				if($row['drugScreeningDate'] != '0000-00-00' && isset($row['drugScreeningDate']))
				$drugScreeningDate = date('m/d/Y', strtotime($row['drugScreeningDate']));
			
			$criminalBackgroundCheck = '';
			if($row['criminalBackgroundCheck'] != '0000-00-00' && isset($row['criminalBackgroundCheck']))
				$criminalBackgroundCheck = date('m/d/Y', strtotime($row['criminalBackgroundCheck']));
			
			$healthCertificationDate = '';
			if($row['healthCertificationDate'] != '0000-00-00' && isset($row['healthCertificationDate']))
			$healthCertificationDate = date('m/d/Y', strtotime($row['healthCertificationDate']));
		
			$licenseExpiration = '';
			if($row['licenseExpiration'] != '0000-00-00' && isset($row['licenseExpiration']))
				$licenseExpiration = date('m/d/Y', strtotime($row['licenseExpiration']));

			
			$aclsCertificationDate = '';
			if($row['aclsCertificationDate'] != '0000-00-00' && isset($row['aclsCertificationDate']))
				$aclsCertificationDate = date('m/d/Y', strtotime($row['aclsCertificationDate']));
			
			$drugScreeningDate = '';
			if($row['drugScreeningDate'] != '0000-00-00' && isset($row['drugScreeningDate']))
				$drugScreeningDate = date('m/d/Y', strtotime($row['drugScreeningDate']));

			$aclsExpirationDate = '';
			if($row['aclsExpirationDate'] != '0000-00-00' && isset($row['aclsExpirationDate']))
				$aclsExpirationDate = date('m/d/Y', strtotime($row['aclsExpirationDate']));

			$bclsCertificationDate = '';
			if($row['bclsCertificationDate'] != '0000-00-00' && isset($row['bclsCertificationDate']))
				$bclsCertificationDate = date('m/d/Y', strtotime($row['bclsCertificationDate']));

			$bclsExpirationDate = '';
			if($row['bclsExpirationDate'] != '0000-00-00' && isset($row['bclsExpirationDate']))
				$bclsExpirationDate = date('m/d/Y', strtotime($row['bclsExpirationDate']));

			$palsCertificationDate = '';
			if($row['palsCertificationDate'] != '0000-00-00' && isset($row['palsCertificationDate']))
				$palsCertificationDate = date('m/d/Y', strtotime($row['palsCertificationDate']));

			$palsExpirationDate = '';
			if($row['palsExpirationDate'] != '0000-00-00' && isset($row['palsExpirationDate']))
				$palsExpirationDate = date('m/d/Y', strtotime($row['palsExpirationDate']));

			$NRPCerificationDate = '';
			if($row['NRPCerificationDate'] != '0000-00-00' && isset($row['NRPCerificationDate']))
			$NRPCerificationDate = date('m/d/Y', strtotime($row['NRPCerificationDate']));

			$NRPExpirationdate = '';
			if($row['NRPExpirationdate'] != '0000-00-00' && isset($row['NRPExpirationdate']))
				$NRPExpirationdate = date('m/d/Y', strtotime($row['NRPExpirationdate']));

			$AARCExpirationDate = '';
			if($row['AARCExpirationDate'] != '0000-00-00' && isset($row['AARCExpirationDate']))
				$AARCExpirationDate = date('m/d/Y', strtotime($row['AARCExpirationDate']));

			$CSRCExpirationDate = '';
			if($row['CSRCExpirationDate'] != '0000-00-00' && isset($row['CSRCExpirationDate']))
				$CSRCExpirationDate = date('m/d/Y', strtotime($row['CSRCExpirationDate']));

			$licenseNumber=stripslashes($row['licenseNumber']);
			$licenseCredentials=stripslashes($row['licenseCredentials']);
			$AARCMembershipNumber=stripslashes($row['AARCMembershipNumber']);
			$CSRCMembershipNumber=stripslashes($row['CSRCMembershipNumber']);
			
			
			$aclsNumber=stripslashes($row['aclsNumber']);
		
			$aclsStatus=stripslashes($row['aclsStatus']);
			
			$bclsNumber=stripslashes($row['bclsNumber']);
			
			$bclsStatus=stripslashes($row['bclsStatus']);
			
			$palsNumber=stripslashes($row['palsNumber']);
			
			$palsStatus=stripslashes($row['palsStatus']);

			$covid1Date = '';
			if($row['covid1Date'] != '0000-00-00' && isset($row['covid1Date']))
				$covid1Date = date('m/d/Y', strtotime($row['covid1Date']));
			
			$covid2Date = '';
			if($row['covid2Date'] != '0000-00-00' && isset($row['covid2Date']))
				$covid2Date = date('m/d/Y', strtotime($row['covid2Date']));
			
			$covidBoosterDate = '';	
			if($row['covidBoosterDate'] != '0000-00-00' && isset($row['covid2Date']))
			$covidBoosterDate = date('m/d/Y', strtotime($row['covidBoosterDate']));
		}
       
    }			
		//school
    $title ="Add Certification Log";
    $bedCrumTitle = 'Add';
	
		
	//Read Country From State
	$objCountryStateMaster = new clsCountryStateMaster();
	$dbCountryId = $objCountryStateMaster->GetParentIdFromChildId($dbStateId);
	unset($objLocation);
	
	$objCountryStateMaster = new clsCountryStateMaster();
    $countries = $objCountryStateMaster->GetAllCountry();
	unset($objCountryStateMaster);
	
	$objClinician = new clsClinician();
	$clinician =$objClinician->GetClinicianDetails($clinicianId);
	$firstName= isset($clinician['firstName']) ? $clinician['firstName'] : '';
	$lastName= isset($clinician['lastName']) ? $clinician['lastName'] : '';
	$fullName=$firstName.' '.$lastName;

	unset($objClinician);

	
	
	
	
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($title); ?></title>
    <?php include('includes/headercss.php');?>

     <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
	  <link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	  <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
	<style>
		.calender-input {
			width: 100%;
			position: relative;
		}
		.formSubHeading {
            border-bottom: 2px solid #d9d6d657;
            padding:3px 0;
            /* margin: 10px 15px 20px; */
            position:relative;
        }
        input[type="file"] {
            background-color: #fff !important;
        }

		@media screen and (max-width: 500px) {
			.formSubHeading{
				font-size: 16px;
				margin-bottom: 12px;
				padding-bottom: 0;
			}
			.breadcrumb-bg{
				margin-bottom: 5px;
			}
		}
	</style>
</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="schoolclinicians.html">Evaluators</a></li>
                   <li><?php echo $fullName; ?></li>
				   <li>Certification Log</li>
                    <li class="active"><?php  echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

    <div class="pageheading"></div>
         <form id="frmCertificationLog" data-parsley-validate class="form-horizontal" method="POST" action="ClinicianCertificationLogSubmit.html?clinicianId=<?php echo(EncodeQueryData($clinicianId)); ?>" enctype="multipart/form-data">
            <div class="row">
				<div class="col-md-12">
				<div class="formSubHeading">Certification Log</div>
				</div>
			
                <div class="col-md-6">
                 
                   <div class="form-group">					   
						<label class="col-md-12 control-label" for="drugScreeningDate">Drug Screening Date</label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='drugScreeningDate'>
	                          
									<input type='text' name="drugScreeningDate"  id="drugScreeningDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php  echo ($drugScreeningDate);   ?>"  data-parsley-errors-container="#error-drugScreeningDate" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-drugScreeningDate"></div>
							</div>		
					</div>
		         </div>
				 <div class="col-md-6">

                   <div class="form-group">					   
						<label class="col-md-12 control-label" for="criminalBackgroundCheck">Criminal Background Check</label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='criminalBackgroundCheck'>
	                          
									<input type='text' name="criminalBackgroundCheck"  id="criminalBackgroundCheck" class="form-control input-md  rotation_date dateInputFormat" value="<?php echo ($criminalBackgroundCheck);  ?>"  data-parsley-errors-container="#error-criminalBackgroundCheck" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-criminalBackgroundCheck"></div>
							</div>		
					</div>
		         </div>
	</div>
	<div class="row">
				<div class="col-md-6">
                   <div class="form-group">					   
						<label class="col-md-12 control-label" for="healthCertificationDate">Health Certification Date</label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='healthCertificationDate'>
	                          
									<input type='text' name="healthCertificationDate"  id="healthCertificationDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php  echo ($healthCertificationDate); ?>"  data-parsley-errors-container="#error-healthCertificationDate" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-healthCertificationDate"></div>
							</div>		
					</div>
		         </div>
				 <div class="col-md-6">
                   <div class="form-group">					   
						<label class="col-md-12 control-label" for="aclsCertificationDate">ACLS Certification Date</label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='aclsCertificationDate'>
	                          
									<input type='text' name="aclsCertificationDate"  id="aclsCertificationDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php  echo ($aclsCertificationDate);  ?>"  data-parsley-errors-container="#error-aclsCertificationDate" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon ">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-aclsCertificationDate"></div>
							</div>		
					</div>
		         </div>
	</div>
				 <div class="row">
					<div class="col-md-6">

                   <div class="form-group">					   
						<label class="col-md-12 control-label" for="AARCMembershipNumber">AARC Membership Number </label>
							<div class="col-md-12">	
							<input id="AARCMembershipNumber" data-parsley-pattern="^[0-9]+$"  name="AARCMembershipNumber" value="<?php if($AARCMembershipNumber !='' && $AARCMembershipNumber!='0' ) { echo ($AARCMembershipNumber); } ?>"  type="text" placeholder="" class="form-control input-md " >
							</div>		
					</div>
					</div>
					<div class="col-md-6">

					<div class="hidden">
                        <label class="col-md-12 control-label" for="cboCountry">Country</label>
                        <div class="col-md-12">
                            <select id="cboCountry" name="cboCountry" class="form-control step1 input-md  select2_single"  >
                            <option value="" selected>Select</option>
                            <?php
                                if($countries!="")
                                {
                                    while($row = mysqli_fetch_assoc($countries))
                                    {
                                         $location_id  = $row['location_id'];
                                         $name  = stripslashes($row['name']);

                                         ?>
                                          <option value="<?php echo($location_id); ?>" <?php if($dbCountryId==$location_id){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
                                         <?php

                                    }
                                }
                            ?>
                            </select>
                        </div>
                    </div>
                        </div>
                    </div>
					<div class="row">
				<div class="col-md-6">
					<div class="form-group">					   
						<label class="col-md-12 control-label" for="AARCExpirationDate">AARC Expiration Date</label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='AARCExpirationDate'>
	                          
									<input type='text' name="AARCExpirationDate"  id="AARCExpirationDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php  echo ($AARCExpirationDate);  ?>"  data-parsley-errors-container="#error-AARCExpirationDate" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-AARCExpirationDate"></div>
							</div>		
					</div>
				</div>
				<div class="col-md-6">

					<div class="form-group">
                        <label class="col-md-12 control-label" for="txtCSRCMembershipNumber">CSRC Membership Number</label>
                        <div class="col-md-12">
                            <input id="txtCSRCMembershipNumber" data-parsley-pattern="^[0-9]+$"  name="txtCSRCMembershipNumber" value="<?php if($CSRCMembershipNumber !='' && $CSRCMembershipNumber!='0' ) { echo ($CSRCMembershipNumber); } ?>"  type="text" placeholder="" class="form-control input-md " >
						</div>
                    </div>
						</div>
                    </div>
					<div class="row">
					<div class="col-md-6">
					<div class="form-group">					   
						<label class="col-md-12 control-label" for="CSRCExpirationDate">CSRC Expiration Date</label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='CSRCExpirationDate'>
	                          
									<input type='text' name="CSRCExpirationDate"  id="CSRCExpirationDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php  echo ($CSRCExpirationDate); ?>"  data-parsley-errors-container="#error-CSRCExpirationDate" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-CSRCExpirationDate"></div>
							</div>		
					</div>
					</div>
					<div class="col-md-6">
                   <div class="form-group">					   
						<label class="col-md-12 control-label">License Expiration</label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='licenseExpiration'>
	                          
									<input type='text' name="licenseExpiration"  id="licenseExpiration" class="form-control input-md  rotation_date" value="<?php echo ($licenseExpiration);  ?>"  data-parsley-errors-container="#error-licenseExpiration" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar "></span>
										</span>
								</div>
									<div id="error-licenseExpiration"></div>
							</div>		
					</div>
					</div>
					</div>
					<div class="row">
				<div class="col-md-6">
				 <div class="form-group">
                        <label class="col-md-12 control-label" for="txtLicenseNumber">License Number</label>
                        <div class="col-md-12">
                            <input id="txtLicenseNumber" data-parsley-pattern="^[0-9]+$"  name="txtLicenseNumber" value="<?php if($licenseNumber !='' && $licenseNumber!='0' ) { echo ($licenseNumber); } ?>"  type="text" placeholder="" class="form-control input-md " >

                        </div>
                    </div>
				</div>
                   
				<div class="col-md-6">

				 <div class="form-group">
                        <label class="col-md-12 control-label" for="txtpalsNumber">PALS Number</label>
                        <div class="col-md-12">
                            <input id="txtpalsNumber" data-parsley-pattern="^[0	-9]+$"  name="txtpalsNumber" value="<?php if($palsNumber !='' && $palsNumber !='0') { echo ($palsNumber); } ?>"  type="text" placeholder="" class="form-control input-md " >

                        </div>
                    </div>
                        </div>
                    </div>

					<div class="row">
					<div class="col-md-6">
			  <div class="form-group">					   
						<label class="col-md-12 control-label" for="bclsStatus">BCLS Status</label>
							<div class="col-md-12">	
								<div class='input-group date' id='bclsStatus'>
	                          	  
									<input type="radio"  class="mr-10" id="bclsStatus" name="bclsStatus" <?php if ($bclsStatus=="N/A") { ?>checked <?php } else { ?> checked <?php } ?> value="N/A" >N/A &nbsp;&nbsp;&nbsp;
                                    <input type="radio"  class="mr-10" id="bclsStatus" name="bclsStatus"  <?php if ($bclsStatus=="Active") { ?>checked <?php } ?>value="Active"  >Active &nbsp;&nbsp;&nbsp;
                                    <input type="radio"  class="mr-10" id="bclsStatus" name="bclsStatus" <?php if ($bclsStatus=="Expired") { ?>checked <?php } ?> value="Expired"  > Expired &nbsp;&nbsp;&nbsp;
								</div>
									
							</div>		
					</div>
					</div>
					<div class="col-md-6">

					<div class="form-group">					   
						<label class="col-md-12 control-label" for="palsStatus">PALS Status</label>
							<div class="col-md-12">	
								<div class='input-group date' id='palsStatus'>
									<input type="radio"  class="mr-10" id="palsStatus" name="palsStatus" value="N/A" <?php if ($palsStatus=="N/A") { ?>checked <?php } else { ?> checked <?php } ?>>N/A &nbsp;&nbsp;&nbsp;
                                    <input type="radio"  class="mr-10" id="palsStatus" name="palsStatus" value="Active" <?php if ($palsStatus=="Active") { ?>checked <?php } ?>>Active &nbsp;&nbsp;&nbsp;
                                    <input type="radio"  class="mr-10" id="palsStatus" name="palsStatus"  value="Expired" <?php if ($palsStatus=="Expired") { ?>checked <?php } ?>> Expired &nbsp;&nbsp;&nbsp;
								</div>	
							</div>		
					</div>
		         </div>
                </div>
                 <div class="row">
				 <div class="col-md-6">

                   <div class="form-group">					   
						<label class="col-md-12 control-label" for="aclsExpirationDate">ACLS Expiration Date</label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='aclsExpirationDate'>
	                          
									<input type='text' name="aclsExpirationDate"  id="aclsExpirationDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php echo ($aclsExpirationDate);  ?>"  data-parsley-errors-container="#error-aclsExpirationDate" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-aclsExpirationDate"></div>
							</div>		
					</div>
				 </div>
				 <div class="col-md-6">

					<div class="form-group">					   
						<label class="col-md-12 control-label" for="bclsCertificationDate">BCLS Certification Date </label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='bclsCertificationDate'>
	                          
									<input type='text' name="bclsCertificationDate"  id="bclsCertificationDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php  echo ($bclsCertificationDate);  ?>"  data-parsley-errors-container="#error-bclsCertificationDate"placeholder="MM-DD-YYYY" />
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-bclsCertificationDate"></div>
							</div>		
					</div>
				 </div>
				 </div>
				 <div class="row">
				 <div class="col-md-6">
					<div class="form-group">					   
						<label class="col-md-12 control-label" for="bclsExpirationDate">BCLS Expiration Date </label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='bclsExpirationDate'>
	                          
									<input type='text' name="bclsExpirationDate"  id="bclsExpirationDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php echo ($bclsExpirationDate);  ?>"  data-parsley-errors-container="#error-bclsExpirationDate" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-bclsExpirationDate"></div>
							</div>		
					</div>
				 </div>
				 <div class="col-md-6">

					<div class="form-group">					   
						<label class="col-md-12 control-label" for="covid1Date">COVID 19 I Date </label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='covid1Date'>
									<input type='text' name="covid1Date"  id="covid1Date" class="form-control input-md  rotation_date dateInputFormat" value="<?php echo ($covid1Date);  ?>"  data-parsley-errors-container="#error-covid1Date" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-covid1Date"></div>
							</div>		
					</div>
				 </div>
				 </div>
				 <div class="row">
				 <div class="col-md-6">
					<div class="form-group">					   
						<label class="col-md-12 control-label" for="covid2Date">COVID 19 II Date </label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='covid2Date'>
									<input type='text' name="covid2Date"  id="covid2Date" class="form-control input-md  rotation_date dateInputFormat" value="<?php echo ($covid2Date);  ?>"  data-parsley-errors-container="#error-covid2Date" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-covid2Date"></div>
							</div>		
					</div>
				 </div>
				 <div class="col-md-6">

					<div class="form-group">					   
						<label class="col-md-12 control-label" for="covidBoosterDate">COVID 19 Booster Date </label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='covidBoosterDate'>
									<input type='text' name="covidBoosterDate"  id="covidBoosterDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php echo ($covidBoosterDate);  ?>"  data-parsley-errors-container="#error-covidBoosterDate" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-covidBoosterDate"></div>
							</div>		
					</div>
				 </div>
				 </div>
				 <div class="row">
				 <div class="col-md-6">
					<div class="form-group">					   
						<label class="col-md-12 control-label" for="palsCertificationDate">PALS Certification Date </label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='palsCertificationDate'>
	                          
									<input type='text' name="palsCertificationDate"  id="palsCertificationDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php echo ($palsCertificationDate);  ?>"  data-parsley-errors-container="#error-palsCertificationDate" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-palsCertificationDate"></div>
							</div>		
					</div>
				 </div>
				 <div class="col-md-6">

					<div class="form-group">					   
						<label class="col-md-12 control-label" for="palsExpirationDate">PALS Expiration Date</label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='palsExpirationDate'>
	                               
									<input type='text' name="palsExpirationDate"  id="palsExpirationDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php echo ($palsExpirationDate);  ?>"  data-parsley-errors-container="#error-palsExpirationDate" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-palsExpirationDate"></div>
									<input type="hidden" id="studentId" name="studentId" value="<?php echo ($studentId); ?>" type="text" placeholder="" class="form-control input-md " >
							</div>		
					</div>
				 </div>
				 </div>
				 <div class="row">
				 <div class="col-md-6">
					<div class="form-group">					   
						<label class="col-md-12 control-label" for="NRPCerificationDate">NRP Certification Date</label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='NRPCerificationDate'>
	                          
									<input type='text' name="NRPCerificationDate"  id="NRPCerificationDate" class="form-control input-md  rotation_date dateInputFormat" value="<?php echo ($NRPCerificationDate);  ?>"  data-parsley-errors-container="#error-NRPCerificationDate" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-NRPCerificationDate"></div>
							</div>		
					</div>
				 </div>
				 <div class="col-md-6">

					
					<div class="form-group">					   
						<label class="col-md-12 control-label" for="NRPExpirationdate">NRP Expiration Date</label>
							<div class="col-md-12">	
								<div class='input-group date calender-input' id='NRPExpirationdate'>
	                          
									<input type='text' name="NRPExpirationdate"  id="NRPExpirationdate" class="form-control input-md  rotation_date dateInputFormat" value="<?php  echo ($NRPExpirationdate);  ?>"  data-parsley-errors-container="#error-NRPExpirationdate" placeholder="MM-DD-YYYY"/>
										<span class="input-group-addon calender-icon">										
											<span class="glyphicon glyphicon-calendar"></span>
										</span>
								</div>
									<div id="error-NRPExpirationdate"></div>
							</div>		
					</div>
				 </div>
				 </div>
				 <div class="row">
				 <div class="col-md-6">
					 <div class="form-group">
                        <label class="col-md-12 control-label" for="txtaclsNumber">ACLS Number</label>
                        <div class="col-md-12">
                            <input id="txtaclsNumber" data-parsley-pattern="^[0	-9]+$"  name="txtaclsNumber" value="<?php if($aclsNumber !='' && $aclsNumber !='0') { echo ($aclsNumber); } ?>"  type="text" placeholder="" class="form-control input-md " ="">

                        </div>
                    </div>
				 </div>
				 <div class="col-md-6">

					 <div class="form-group">
                        <label class="col-md-12 control-label" for="txtlicenseCredentials">License Credentials</label>
                        <div class="col-md-12">
                            <input id="txtlicenseCredentials" data-parsley-pattern="^[0	-9]+$"  name="txtlicenseCredentials" value="<?php if($licenseCredentials !='' && $licenseCredentials!='0') { echo ($licenseCredentials); } ?>"  type="text" placeholder="" class="form-control input-md " ="">

                        </div>
                    </div>
				 </div>
				 </div>
				 <div class="row">
				 <div class="col-md-6">
					<div class="form-group">
                        <label class="col-md-12 control-label" for="txtbclsNumber">BCLS Number</label>
                        <div class="col-md-12">
                            <input id="txtbclsNumber" data-parsley-type="number"  name="txtbclsNumber" value="<?php if($bclsNumber !='' && $bclsNumber !='0') { echo ($bclsNumber); } ?>"  type="text" placeholder="" class="form-control input-md " ="">

                        </div>
                    </div>
				 </div>
				 <div class="col-md-6">

					<div class="form-group">					   
						<label class="col-md-12 control-label" for="aclsStatus">ACLS Status</label>
							<div class="col-md-12">	
								<div class='input-group date' id='aclsStatus'>
									<input type="radio"  class="mr-10" id="aclsStatus"  name="aclsStatus" value="N/A" <?php if ($aclsStatus=="N/A") { ?>checked <?php } else { ?> checked <?php } ?>>N/A &nbsp;&nbsp;&nbsp;
                                    <input type="radio"  class="mr-10" id="aclsStatus" name="aclsStatus"  value="Active" <?php if ($aclsStatus=="Active") { ?>checked <?php } ?> >Active &nbsp;&nbsp;&nbsp;
                                    <input type="radio"  class="mr-10" id="aclsStatus" name="aclsStatus"  value="Expired" <?php if ($aclsStatus=="Expired") { ?>checked <?php } ?> > Expired &nbsp;&nbsp;&nbsp;
							</div>		
					</div>
					
		         </div>	
					
				</div>     
			</div>
            <div class="form-group m-0">
                        <!-- <label class="col-md-2 control-label"></label> -->
						<div class="col-md-12" style="display: flex; justify-content: center; margin: 0 0 20px 0;gap: 15px;"> 
							<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
							<a type="button" href="schoolclinicians.html" class="btn btn-default">Cancel</a>
						</div>

            </div>
        </form>
    </div>

    <?php include('includes/footer.php');?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>		
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>	 
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>

    <script type="text/javascript">

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";
		
        $(window).load(function(){
			$(".select2_single").select2();
			$(".select2_tags").select2();
			$('#drugScreeningDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						 
						
           			});
            $('#criminalBackgroundCheck').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
			$('#healthCertificationDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
			$('#licenseExpiration').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
					$('#aclsCertificationDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
					$('#aclsExpirationDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
					$('#bclsCertificationDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
					$('#bclsExpirationDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
					$('#palsCertificationDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
					$('#palsExpirationDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
					$('#AARCExpirationDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
					$('#CSRCExpirationDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
			
					$('#NRPCerificationDate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
					$('#NRPExpirationdate').datetimepicker({
				         
                 		format: 'MM/DD/YYYY'
						
           			});
					   $('#covid1Date').datetimepicker({format: 'MM/DD/YYYY'});
					$('#covid2Date').datetimepicker({format: 'MM/DD/YYYY'});
					$('#covidBoosterDate').datetimepicker({format: 'MM/DD/YYYY'});
					
});
	
</script>
</body>

</html>