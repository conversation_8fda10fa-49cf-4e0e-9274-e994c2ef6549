<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');  
    include('../class/clsSystemUser.php'); 
    include('../class/clsJournal.php'); 
	include('../setRequest.php'); 
	

	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']) )
	{
		
		
		$journalId = isset($_GET['editjournalid']) ? DecodeQueryData($_GET['editjournalid']) : 0;
			
		$currentstudentId=0;
		if(isset($_GET['studentId']))
		{
			$currentstudentId= DecodeQueryData($_GET['studentId']);	
		}
		if(isset($_GET['rotationId']))
		{
			$selrotationId= DecodeQueryData($_GET['rotationId']);		
		}
		$journalDate  = GetDateStringInServerFormat($_POST['journalDate']);
		$journalDate = str_replace('00:00:00','12:00 PM',$journalDate);
		$journalDate = date('Y-m-d H:i',strtotime($journalDate));
		$cborotation = $_POST['cborotation'];		
		$cbohospitalsites = $_POST['cbohospitalsites'];	
		$hospitalsiteunits = $_POST['cbohospitalsiteunits'];
		$school_response =$_POST['school_response'];
		$txtTimeSpent =isset($_POST['txtTimeSpent']) ? $_POST['txtTimeSpent'] : ' ' ;
		$cbostudent =$currentstudentId;
		$student_journal_entry =$_POST['student_journal_entry'];
		
		
		$journalId = isset($_GET['editjournalid']) ? DecodeQueryData($_GET['editjournalid']) : 0;
		$status = ($journalId > 0) ? 'updated' : 'added';
		

		//Save data
		$objJournal = new clsJournal();
		$objJournal->journalDate = $journalDate;		
		$objJournal->rotationId = $cborotation;		
		$objJournal->studentId = $cbostudent;		
		$objJournal->schoolId = $currentSchoolId;
		$objJournal->hospitalSiteId = $cbohospitalsites;
		$objJournal->timeSpent = $txtTimeSpent;
		$objJournal->hospitalSiteUnitId = $hospitalsiteunits;
		$objJournal->journalStudentEntry = $student_journal_entry;
		$objJournal->locationId = $_SESSION["loggedUserLocation"];
		$objJournal->journalSchoolResponse = $school_response;
		$objJournal->createdBy = $_SESSION['loggedUserId'];
		
		//$objJournal->updatedBy = $_SESSION['loggedUserId'];
		$retjournalId = $objJournal->SaveAdminJournal($journalId);
		
		unset($objJournal);
		if($retjournalId > 0)
		{	

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = $objLog::EDIT;
			$userType = $objLog::ADMIN; // User type is set to STUDENT
			$IsMobile = 0;

			$objJournal = new clsJournal();
			$objJournal->saveDailyJournalAuditLog($retjournalId, $_SESSION['loggedUserId'], $userType, $action, $IsMobile);

			unset($objLog);


			if(isset($_GET['rotationId']))
			{
			header('location:journallist.html?journalId='.EncodeQueryData($retjournalId).'&rotationId='.EncodeQueryData($selrotationId).'&studentId='.EncodeQueryData($currentstudentId).'&status='.$status);

			}
			elseif(isset($_GET['studentId']))
			{
				header('location:journallist.html?journalId='.EncodeQueryData($retjournalId).'&studentId='.EncodeQueryData($currentstudentId).'&status='.$status);
			}
			else 
			{
				header('location:journallist.html?journalId='.EncodeQueryData($retjournalId).'&studentId='.EncodeQueryData($currentstudentId).'&status='.$status);
			}
		}
		else
		{
			header('location:addjournal.html?status=error');
		}
	}
	else
	{
		header('location:journallist.html');
		exit();
	}

?>