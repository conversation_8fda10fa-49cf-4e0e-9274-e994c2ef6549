<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include('../class/clsSystemUser.php');
include('../class/clsStudent.php');
include('../class/clsClinician.php');
include("../class/clsSMTPSettings.php");
include("../class/class.phpmailer.php");
include_once('../class/clsSendEmails.php');
include('../class/clsEmailRecord.php');
$getDate = isset($_GET['date']) ? $_GET['date'] : '';
$rsEmailRecordList = '';
$totalEmailRecord = 0;
$objEmailRecord = new clsEmailRecord();
if($getDate != '')
    $rsEmailRecordList = $objEmailRecord->GetEmailRecords($getDate);
else 
    $rsEmailRecordList = $objEmailRecord->GetAllEmailRecords($getDate);
if ($rsEmailRecordList != '') {
    $totalEmailRecord = mysqli_num_rows($rsEmailRecordList);
}
unset($objEmailRecord);
$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];

?>
<!DOCTYPE html>
<html lang="en">

<head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Email Report</title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css"> 
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
        <link href="https://cdn.datatables.net/fixedcolumns/3.2.4/css/fixedColumns.bootstrap4.min.css" rel="stylesheet" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    </head>

<body>
  <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li><a href="email.html">Email</a></li>
                    <li><a href="emailReport.html">Email Report</a></li>
                    <li class="active">Email Details Report</li>
                </ol>
            </div>
            <div class="pull-right">
              <a href="viewEmailReport.html"><input type="submit" name="btnExport" id="btnExport" class="btn btn-link" value="View All"></a>
            </div>
        </div>
    </div>
    <div class="container">
  <div id="divTopLoading" >Loading...</div>

    <table id="clinicianlist_table" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                                <thead>
                                    <tr>
                                        <th>Date</th>
                                        <th>Email Subject</th>
                                        <th>Email Body</th>
                                        <th>Email</th>
                                        <th>Status</th>

                                    </tr>
                                </thead>
                                <tbody>
                                    <?php
                                    if ($totalEmailRecord > 0) {
                                        while ($row = mysqli_fetch_array($rsEmailRecordList)) {
                                            $emailRecordId = $row['id'];
                                            $subject = $row['emailSubject'];
                                            $message = $row['emailBody'];
                                            $emailId = $row['email'];
                                            $status = $row['isSend'];
                                            $date = $row['createdDate'];
                                            $checkoffDateTime = converFromServerTimeZone($date,$TimeZone); 
                                            $emailRecordDate = date('m/d/Y H:i:s',strtotime($date));

                                            //Display Status
                                            if ($status == 1)
                                                $status = 'Sent';
                                            else
                                                $status = 'Pending';

                                    ?>
                                            <tr>
                                                <td style="background:white"><?php echo ($emailRecordDate); ?></td>
                                                <td style="background:white"><?php echo ($subject); ?></td>
                                                <td style="background:white ; white-space:normal;"><?php echo ($message); ?></td>
                                                <td style="background:white"><?php echo ($emailId); ?></td>
                                                <td style="background:white"><?php echo ($status); ?></td>
                                            </tr>
                                    <?php
                                        }
                                    }
                                    unset($objCoarc);
                                    ?>
                                </tbody>
                            </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="https://cdn.datatables.net/buttons/1.2.2/js/buttons.html5.min.js"></script>
    <script src="https://cdn.datatables.net/buttons/1.2.2/js/dataTables.buttons.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/2.5.0/jszip.min.js"></script>
    <script src="https://cdn.rawgit.com/bpampuch/pdfmake/0.1.18/build/pdfmake.min.js"></script>
    <script src="https://cdn.rawgit.com/bpampuch/pdfmake/0.1.18/build/vfs_fonts.js"></script>

    <script>
        var current_clinicianlist_table = $("#clinicianlist_table").DataTable({

            // paging: true,
            "aoColumns": [{
                "sWidth": "10%"
            }, {
                "sWidth": "20%"
            }, {
                "sWidth": "40%"
            },{
                "sWidth": "20%"
            },{
                "sWidth": "10%"
            }],
            fields: [ {
                label: "Description:",
                name: "description",
                type: "ckeditor"
            } ],
            // Field:inst( 'contract.description' ),
            dom: 'Bfrtip',
            buttons: [
               {
                  extend: 'excelHtml5',
                  text:'Export to Excel',
                  message: "Email Report",
                  title: "Email Report",
                  className: 'btn-primary pef2evl'
               }

               
            ]
        });
        $(window).load(function(){
           $("#divTopLoading").addClass('hide');
        });
    </script>
</body>

</html>