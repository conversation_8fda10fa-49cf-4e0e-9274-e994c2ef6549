<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include('../class/clsClinician.php');
include('../class/clsSystemUserRolePermission.php');

$clinicianSearch = '';
$userPermissions = '';
$schoolsArray = $settingsArray = $headersArray = '';
$schools = $settings = $headers = '';
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSearch'])) {

    $clinicianSearch = $_POST['clinicianSearch'];
    $clinicianSearch = formatPhoneNumberForSearch($clinicianSearch);

    // Check if the user's role ID is available in the session
    $loggedUserRoleId = $_SESSION['loggedUserRoleId'];

    // Initialize a new instance of the clsSystemUserRolePermission class
    $objSystemUserRolePermission = new clsSystemUserRolePermission();

    // Get system user role permissions based on the user's role ID
    $userPermissions = $objSystemUserRolePermission->GetSystemUserRolePermissions($loggedUserRoleId);

    // Check if any user permissions were returned
    if ($userPermissions != '') {
        // Extract the user permissions for schools, headers, and settings
        $schools = $userPermissions['schools'];
        $headers = $userPermissions['headers'];
        $settings = $userPermissions['settings'];

        // Convert the user permissions for schools, headers, and settings into arrays
        $schoolsArray = explode(",", $schools);
        $headersArray = explode(",", $headers);
        $settingsArray = explode(",", $settings);
    }

    $loggedUserId = $_SESSION["loggedUserId"];
    $objDB = new clsDB();
    $isPrimaryUser = 0;
    $isPrimaryUser = $objDB->GetSingleColumnValueFromTable('systemusermaster', 'isPrimaryUser', 'systemUserMasterId', $loggedUserId);
    unset($objDB);
    //Get All Jornal List
    $objClinician = new clsClinician();
    if ($isPrimaryUser)
        $rowsClinicianData = $objClinician->GetClinicianDetailForSuperAdmin($clinicianSearch);
    else
        $rowsClinicianData = $objClinician->GetClinicianDetailForSuperAdminBySchoolIds($clinicianSearch, $schools);
    $totalClinicianCount = 0;
    if ($rowsClinicianData != '') {
        $totalClinicianCount = mysqli_num_rows($rowsClinicianData);
    }
    unset($objClinician);
}



?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Evaluator List</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active">Evaluator</li>
                </ol>
            </div>
        </div>
    </div>
    <div class="container">
        <div id="divTopLoading">Loading...</div>
        <div class="row">
            <form name="clinicianList" id="clinicianList" method="POST" action="clinicianListForSuperAdmin.html">
                <div class="form-group col-md-1 control-label">
                    <label class="pull-right" style="margin-top:10px">Search :</label>
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control" placeholder="Search by Name, UserName, Email, Phone Number" name="clinicianSearch" id="clinicianSearch" value="<?php echo $clinicianSearch; ?>">
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <button id="btnSearch" name="btnSearch" class="btn btn-success">Go</button>
                    </div>
                </div>
            </form>
        </div>
        <?php if ($clinicianSearch != '') { ?>
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>School Name</th>
                        <th>Login Info</th>
                        <th>Contact Info</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($totalClinicianCount > 0) {
                        while ($row = mysqli_fetch_array($rowsClinicianData)) {
                            $firstName = $row['firstName'];
                            $lastName = $row['lastName'];
                            $fullName = $firstName . ' ' . $lastName;
                            $username = $row['username'];
                            $phone = $row['phone'];
                            $cellPhone = $row['cellPhone'];
                            $title = $row['title'];
                            $email = $row['email'];
                            $schoolName = $row['schoolName'];
                            $slug = $row['slug'];

                    ?>
                            <tr>
                                <td><?php echo $fullName; ?></td>
                                <td><?php echo $schoolName; ?></td>
                                <td>Login URL: https://rad.clinicaltrac.net/school/<?php echo $slug; ?>/clinician/index.html <br>
                                    UserName: <?php echo $username; ?> <br>
                                    Role: <?php echo $title; ?> </td>
                                <td>Email: <?php echo $email; ?> <br>
                                    Phone: <?php echo $phone; ?> <br>
                                    <?php if ($cellPhone > 0) { ?>
                                        Cell Phone: <?php echo $cellPhone; ?>
                                    <?php } ?> </td>
                            </tr>
                        <?php
                        }
                    } else { ?>
                        <tr>
                            <td colspan="4" align="center">No record(s) available</td>
                        <tr>
                        <?php } ?>
                </tbody>
            </table>
        <?php } ?>

    </div>


    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>


    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(document).ready(function() {
            var current_datatable = $("#datatable-responsive").DataTable({
                pageLength: 100
            });
        });

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });
    </script>


</body>

</html>