<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');      	
	include('../class/clsusafEquiment.php');
	include('../setRequest.php'); 
	
	$currentSchoolId;
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		
		$equipmentId = isset($_GET['equipmentId']) ? DecodeQueryData($_GET['equipmentId']) : 0;
		
		$status = ($equipmentId > 0) ? 'updated' : 'added';
		
		
        $title = $_POST['equipmentTitle'];
		
		//Save data
		$objusafEquiment = new clsusafEquiment();
		$objusafEquiment->title = $title;	
		$objusafEquiment->schoolId = $currentSchoolId;
		$retEquipmentId = $objusafEquiment->SaveSchoolUsafEquipment($equipmentId);
		
		unset($objusafEquiment);
		if($retEquipmentId > 0)
		{	
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = ($equipmentId > 0) ? $objLog::EDIT : $objLog::ADD;
			$type = "";
			$isSuperAdmin = 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to Admin
			$objusafEquiment = new clsusafEquiment();
			$objusafEquiment->saveEquipmentAuditLog($retEquipmentId, $_SESSION["loggedUserId"], $userType, $action, $IsMobile = 0, $type, $isSuperAdmin);
			unset($objusafEquiment);

			unset($objLog);
			//Audit Log End

            header('location:usafschoolviewequipment.html?status='.$status);
		}
		else
		{
			header('location:usafschooladdequipment.html?status=error');
		}
	}
    exit();
	
?>