<?php
	include('includes/validateUserLogin.php');
    include('../includes/config.php');
    include('../class/clsDB.php');
    include('../includes/commonfun.php');
    include('../class/clsSchool.php');	 
    include('../setRequest.php');
    include('../class/clscheckoff.php');
    include('../class/clsIrr.php');
	require_once "PHPExcel/PHPExcel.php"; // Or whatver the path to your PHPExcel library is
	require_once "PHPExcel/PHPExcel/IOFactory.php";
	
            $clinicianId = 0;  
            $preceptorId = 0; 

			$schoolTopicId = $_GET['schoolTopicId'];
            $schoolTopicId = DecodeQueryData($schoolTopicId);

            $irrMasterId = $_GET['irrMasterId'];
            $irrMasterId = DecodeQueryData($irrMasterId);

            if(isset($_GET['clinicianId'])) 
            {
                $clinicianId = DecodeQueryData($_GET['clinicianId']);
                $clinicianId= DecodeQueryData($_GET['clinicianId']);	
            }

            if(isset($_GET['preceptorId'])) 
            {
                $preceptorId = DecodeQueryData($_GET['preceptorId']);
                $preceptorId= DecodeQueryData($_GET['preceptorId']);	
            }
            $objcheckoff = new clscheckoff();
            $rowscheckoff = $objcheckoff->GetDefaulttopicDetails($schoolTopicId);
            $totalRowscheckoff = ($rowscheckoff != '') ? mysqli_num_rows($rowscheckoff) : 0;
			// print_r($rowscheckoff);exit;
						
			date_default_timezone_set('Asia/Kolkata');
			$today= (date('m/d/Y, H:i A'));
			 if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnirrExport']))					
                        $reportType = $_POST['cboreporttype'];
                        
            
            $title='Irr Report';
            
            $titleOne='Equipment Preparation';									
            $titleTwo='PEF Foundation';
            $titleThree='Follow-up Assessment';
            $titleFour='Comments and Suggestions';
            $titleFive='Performance Evaluation';
            
            $reportname='IrrReport_';
		 switch($reportType)
			{	
                case "Irr_Report":
						
						$objPHPExcel = new \PHPExcel();
						
						// Set document properties
						$objPHPExcel->getProperties()->setCreator('Irr')
													 ->setLastModifiedBy('JCC')
													 ->setTitle('Reports')
													 ->setSubject('Irr Report')
						    						 ->setDescription('Irr Report');
						
                           
                         
                        if($totalRowscheckoff > 0)
                        {
                            // echo 'jj';exit;
                            $styleArray = array('font'  => array('size'  => 10));
                            $counter=0;
                            while($row = mysqli_fetch_array($rowscheckoff))
                            {
                                $printStartRowCounter =7;
                                $schoolSectionId = $row['schoolSectionId'];
                                $schoolSectionTitle = $row['schoolSectionTitle'];
                                $sortOrder = $row['sortOrder'];
                                if($sortOrder == 4)
                                           {
                                            continue;
                                           }
                                $schoolSectionTitleShort = substr($schoolSectionTitle,0,25) ;

                                $schoolSectionTitleShort = str_replace('/','',$schoolSectionTitleShort);
                                $schoolSectionTitleShort = str_replace(':','',$schoolSectionTitleShort);

                                $rowsquistion= $objcheckoff->GetschooldefaultquestionforIRRReport($schoolSectionId);
                                $totalRowsquistion = ($rowsquistion != '') ? mysqli_num_rows($rowsquistion) : 0;

                                //Active Sheet
                                $objPHPExcel->createSheet();
                                $objPHPExcel->setActiveSheetIndex($counter);
                                $objPHPExcel->getActiveSheet()->setTitle($schoolSectionTitleShort); 	

                                 //Print Heading	
                                 $headerstyleArray = array('font'  => array('bold'  => true,'size'  => 16));
                                        
                                 $objPHPExcel->getActiveSheet()->mergeCells("B2:Q2");
                                 $objPHPExcel->getActiveSheet()->setCellValue('B2', $title);
                                 $objPHPExcel->getActiveSheet()->getStyle('B2')->applyFromArray($headerstyleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('B2')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()
                                             ->getStyle('B2')
                                             ->getFill()
                                             ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
                                             ->getStartColor()
                                             ->setRGB('E0E0E0');
                                     
                                 $styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));
                                 $objPHPExcel->getActiveSheet()->getStyle('B2:Q2')->applyFromArray($styleBorderArray);
                                 
                                 
                                 $styleArray = array('font'  => array('bold'  => true,'size'  => 12));
                                 
                                 $objPHPExcel->getActiveSheet()->mergeCells("B4:Q4");
                                 $objPHPExcel->getActiveSheet()->setCellValue('B4',$schoolSectionTitle );
                                 $objPHPExcel->getActiveSheet()->getStyle('B4')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('B4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()
                                             ->getStyle('B4')
                                             ->getFill()
                                             ->setFillType(PHPExcel_Style_Fill::FILL_SOLID)
                                             ->getStartColor()
                                             ->setRGB('E0E0E0');
                                             
                                 $styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));
                                 $objPHPExcel->getActiveSheet()->getStyle('B4:Q4')->applyFromArray($styleBorderArray);
                                 
                                 //Make Table Heading
                                 $styleArray = array('font'  => array('bold'  => true,'size'  => 10));
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('B6', 'STEPS');
                                 $objPHPExcel->getActiveSheet()->getStyle('B6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('B6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('C6', 'RATING');
                                 $objPHPExcel->getActiveSheet()->getStyle('C6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('C6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('D6', 'SCORE');
                                 $objPHPExcel->getActiveSheet()->getStyle('D6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('D6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                             
                                 $objPHPExcel->getActiveSheet()->setCellValue('E6', 'YES');
                                 $objPHPExcel->getActiveSheet()->getStyle('E6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('E6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('F6', 'NO');
                                 $objPHPExcel->getActiveSheet()->getStyle('F6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('F6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('G6', '100');
                                 $objPHPExcel->getActiveSheet()->getStyle('G6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('G6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('H6', '88');
                                 $objPHPExcel->getActiveSheet()->getStyle('H6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('H6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 

                                 $objPHPExcel->getActiveSheet()->setCellValue('I6', '75');
                                 $objPHPExcel->getActiveSheet()->getStyle('I6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('I6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                             
                                 $objPHPExcel->getActiveSheet()->setCellValue('J6', '63');
                                 $objPHPExcel->getActiveSheet()->getStyle('J6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('J6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('K6', 'TOTAL');
                                 $objPHPExcel->getActiveSheet()->getStyle('K6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('K6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('L6', 'YES');
                                 $objPHPExcel->getActiveSheet()->getStyle('L6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('L6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('M6', 'NO');
                                 $objPHPExcel->getActiveSheet()->getStyle('M6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('M6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('N6', '100');
                                 $objPHPExcel->getActiveSheet()->getStyle('N6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('N6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('O6', '88');
                                 $objPHPExcel->getActiveSheet()->getStyle('O6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('O6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('P6', '75');
                                 $objPHPExcel->getActiveSheet()->getStyle('P6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('P6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 
                                 $objPHPExcel->getActiveSheet()->setCellValue('Q6', '63');
                                 $objPHPExcel->getActiveSheet()->getStyle('Q6')->applyFromArray($styleArray);
                                 $objPHPExcel->getActiveSheet()->getStyle('Q6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                 

                                 

                                 $objPHPExcel->getActiveSheet()->getStyle('B6:Q6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
                                 $styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));
                                $objPHPExcel->getActiveSheet()->getStyle('B6:Q6'.$printStartRowCounter)->applyFromArray($styleBorderArray);
						
                             
                                 // Auto size columns for each worksheet
                                 foreach(range('B','C') as $columnID)
                                 {
                                     $objPHPExcel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
                                 }
                                                     
                  
                                 if($totalRowsquistion > 0)
                                 {
                                    while($row = mysqli_fetch_array($rowsquistion))
                                    { 
                                         $schoolQuestionTitle = $row['schoolQuestionTitle'];													
                                         $schoolQuestionId = $row['schoolQuestionId'];													
                                         $schoolQuestionType = $row['schoolQuestionType'];    
                                         if($schoolQuestionType == 5){
                                            continue;
                                        }
                                         $GetCal=$objcheckoff->GetCalculationsforIRRReport($schoolSectionId,$schoolQuestionId,$clinicianId,$irrMasterId,$preceptorId);
                                     
                                        while($GetCalrows = mysqli_fetch_array($GetCal))
                                        {
                                                $YesCount = $GetCalrows['YesCount'];
                                                $NoCount = $GetCalrows['NoCount'];
                                                $TotalyesNoCount=$YesCount + $NoCount;
                                                $Avalue = $GetCalrows['A'];
                                                $Bvalue = $GetCalrows['B'];
                                                $Cvalue = $GetCalrows['C'];
                                                $Dvalue = $GetCalrows['D'];
                                                $TotalABCDCount=$Avalue+$Bvalue+$Cvalue+$Dvalue;       
                                             $objIrr = new clsIrr(); 	 
                                             $rowsirrdetails = $objIrr->GetSingleIRRDetailsForAdmin($irrMasterId,$currentSchoolId); 
                                             while($rows = mysqli_fetch_array($rowsirrdetails))
                                             {
                                                 $irrMasterId = $rows['irrMasterId'];
                                                 
                                                 $cliniciancountrow=$objIrr->GetClinicianCount($irrMasterId,$clinicianId,$preceptorId);
                                                 $cliniciancount = stripslashes($cliniciancountrow['clinicianId']);
                                                
                                                 $Avalueinperc=0;
                                                 if($TotalABCDCount > 0)
                                                 {
                                                     $Avalueinperc=$Avalue*100/$TotalABCDCount;
                                                     $Bvalueinperc=$Bvalue*100/$TotalABCDCount;
                                                     $Cvalueinperc=$Cvalue*100/$TotalABCDCount;
                                                     $Dvalueinperc=$Dvalue*100/$TotalABCDCount;
                                                 }
                                                 else
                                                 {
                                                     $Avalueinperc=$Avalue*100/$cliniciancount;
                                                     $Bvalueinperc=$Bvalue*100/$cliniciancount;
                                                     $Cvalueinperc=$Cvalue*100/$cliniciancount;
                                                     $Dvalueinperc=$Dvalue*100/$cliniciancount;
                                                 }
                                                 
                                                 
                                                 $YesCountinperc=0;
                                                 if($TotalyesNoCount > 0)
                                                 {
                                                     $YesCountinperc=$YesCount*100/$TotalyesNoCount;
                                                     $NoCountinperc=$NoCount*100/$TotalyesNoCount;
                                                 }
                                                 else 
                                                 {
                                                     $YesCountinperc=$YesCount*100/$cliniciancount;
                                                     $NoCountinperc=$NoCount*100/$cliniciancount;
                                                 }
                                                 $Totalsubmited=0;
																	if($TotalyesNoCount > 0)
																	 {
																		 $Totalsubmited=$TotalyesNoCount;
																	 }
																	 else 
																	 {
																		 $Totalsubmited=$TotalABCDCount;
																	 }
                                                 $Score=0;
                                                 $Rating='';
                                                 if($YesCountinperc > 0 || $NoCountinperc > 0)
                                                 {	
                                                         if($YesCountinperc > $NoCountinperc)
                                                         {
                                                             $Score=$YesCountinperc;
                                                         }
                                                         else 
                                                         {
                                                             $Score=$NoCountinperc;
                                                         }
                                                     
                                                 }
                                                 elseif($Avalueinperc > 0 || $Bvalueinperc > 0 || $Cvalueinperc > 0 || $Dvalueinperc > 0)
                                                 {
                                                    $totalAvalue = 0;
																$totalBvalue = 0;
																$totalCvalue = 0;
																$totalDvalue = 0;
																
																if($Avalue)
																	$totalAvalue = 100*$Avalue;
																if($Bvalue)
																	$totalBvalue = 88*$Bvalue;
																if($Cvalue)
																	$totalCvalue = 75*$Cvalue;
																if($Dvalue)
																	$totalDvalue = 63*$Dvalue;
																
																$Score = ($totalAvalue+$totalBvalue+$totalCvalue+$totalDvalue )/$TotalABCDCount;
                                                 }
                                                 //For Rating 
                                                 if($Score >= 70)
                                                 {
                                                     $Rating='Satisfactory';
                                                     $buttoncss = "text-secondary";
                                                 }
                                                 else
                                                 {
                                                     $Rating='Unsatisfactory ';
                                                     $buttoncss = "text-danger";
                                                 }

                 
                 
                                                 
                                                $objPHPExcel->getActiveSheet()->setCellValue('B'.$printStartRowCounter, $schoolQuestionTitle);
                                                $objPHPExcel->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                                                $objPHPExcel->getActiveSheet()->getStyle('B'.$printStartRowCounter)->applyFromArray($styleArray);
                                                
                                                $objPHPExcel->getActiveSheet()->setCellValue('C'.$printStartRowCounter, $Rating);
                                                $objPHPExcel->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
                                                $objPHPExcel->getActiveSheet()->getStyle('C'.$printStartRowCounter)->applyFromArray($styleArray);
                                                
                                                        
                                                $objPHPExcel->getActiveSheet()->setCellValue('D'.$printStartRowCounter, number_format((float)$Score, 2, '.', '').'%');
                                                $objPHPExcel->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('D'.$printStartRowCounter)->applyFromArray($styleArray);
                                                
                                                
                                                
                                                $objPHPExcel->getActiveSheet()->setCellValue('E'.$printStartRowCounter, number_format((float)$YesCountinperc, 2, '.', '').'%');
                                                $objPHPExcel->getActiveSheet()->getStyle('E'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('E'.$printStartRowCounter)->applyFromArray($styleArray);
                                                
                                                
                                                $objPHPExcel->getActiveSheet()->setCellValue('F'.$printStartRowCounter, number_format((float)$NoCountinperc, 2, '.', '').'%');
                                                $objPHPExcel->getActiveSheet()->getStyle('F'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('F'.$printStartRowCounter)->applyFromArray($styleArray);
                                                
                                                $objPHPExcel->getActiveSheet()->setCellValue('G'.$printStartRowCounter, number_format((float)$Avalueinperc, 2, '.', '').'%');
                                                $objPHPExcel->getActiveSheet()->getStyle('G'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('G'.$printStartRowCounter)->applyFromArray($styleArray);
                                                
                                                $objPHPExcel->getActiveSheet()->setCellValue('H'.$printStartRowCounter, number_format((float)$Bvalueinperc, 2, '.', '').'%');
                                                $objPHPExcel->getActiveSheet()->getStyle('H'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('H'.$printStartRowCounter)->applyFromArray($styleArray);
                                                
                                                $objPHPExcel->getActiveSheet()->setCellValue('I'.$printStartRowCounter, number_format((float)$Cvalueinperc, 2, '.', '').'%');
                                                $objPHPExcel->getActiveSheet()->getStyle('I'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('I'.$printStartRowCounter)->applyFromArray($styleArray);

                                                $objPHPExcel->getActiveSheet()->setCellValue('J'.$printStartRowCounter, number_format((float)$Dvalueinperc, 2, '.', '').'%');
                                                $objPHPExcel->getActiveSheet()->getStyle('J'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('J'.$printStartRowCounter)->applyFromArray($styleArray);

                                                $objPHPExcel->getActiveSheet()->setCellValue('K'.$printStartRowCounter, $Totalsubmited);
                                                $objPHPExcel->getActiveSheet()->getStyle('K'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('K'.$printStartRowCounter)->applyFromArray($styleArray);

                                                $objPHPExcel->getActiveSheet()->setCellValue('L'.$printStartRowCounter, $YesCount);
                                                $objPHPExcel->getActiveSheet()->getStyle('L'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('L'.$printStartRowCounter)->applyFromArray($styleArray);
                                                
                                                $objPHPExcel->getActiveSheet()->setCellValue('M'.$printStartRowCounter, $NoCount);
                                                $objPHPExcel->getActiveSheet()->getStyle('M'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('M'.$printStartRowCounter)->applyFromArray($styleArray);

                                                $objPHPExcel->getActiveSheet()->setCellValue('N'.$printStartRowCounter, $Avalue);
                                                $objPHPExcel->getActiveSheet()->getStyle('N'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('N'.$printStartRowCounter)->applyFromArray($styleArray);

                                                $objPHPExcel->getActiveSheet()->setCellValue('O'.$printStartRowCounter, $Bvalue);
                                                $objPHPExcel->getActiveSheet()->getStyle('O'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('O'.$printStartRowCounter)->applyFromArray($styleArray);

                                                $objPHPExcel->getActiveSheet()->setCellValue('P'.$printStartRowCounter, $Cvalue);
                                                $objPHPExcel->getActiveSheet()->getStyle('P'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('P'.$printStartRowCounter)->applyFromArray($styleArray);

                                                $objPHPExcel->getActiveSheet()->setCellValue('Q'.$printStartRowCounter, $Dvalue);
                                                $objPHPExcel->getActiveSheet()->getStyle('Q'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
                                                $objPHPExcel->getActiveSheet()->getStyle('Q'.$printStartRowCounter)->applyFromArray($styleArray);

                                                
                                                $printStartRowCounter++;
                                                
                                             }
                                        }
                                    }
                                }
                                                

                                $counter++;
                            }
                        
                        }
                        $styleBorderArray = array('borders' => array('allborders' => array('style' => 'thin')));
						$objPHPExcel->getActiveSheet()->getStyle('B6:Q6'.$printStartRowCounter)->applyFromArray($styleBorderArray);
						
						// Auto size columns for each worksheet
						foreach(range('B','Q') as $columnID)
						{
							$objPHPExcel->getActiveSheet()->getColumnDimension($columnID)->setAutoSize(true);
						}
                       
                break;
                	
				
			default:	echo "<b>Please Select Valid Type.</b>";
						break;
			}
						$objPHPExcel->setActiveSheetIndex(0);
                                               
						$currentDate = date('m_d_Y_h_i');
						
						header('Content-type: application/vnd.ms-excel; charset=UTF-8');
						header('Content-Disposition: attachment;filename="'.$reportname.$today.'.xls"');		
						header("Pragma: no-cache");
						header("Expires: 0");
						
						$objWriter = \PHPExcel_IOFactory::createWriter($objPHPExcel, 'Excel5');
						$objWriter->save('php://output');
?>