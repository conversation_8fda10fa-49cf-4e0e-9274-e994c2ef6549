<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');  
    include('../class/clsUsafMasterCheckoffTopic.php');
    include('../class/clsUsafProcedureCategory.php');
	include('../setRequest.php'); 

    $schoolId = 0;	
    $procedureCategoryId = 0;	
    $proceduteCountId = 0;	
    $checkoffTitleId = '';	
  
	$schoolId= $currentSchoolId;
	$title ="Add Comps Topic - ".$currenschoolDisplayname;   
    $page_title ="Add Comps Topic";
    $checkofftopicmasterId = 0;
    $title = '';
	$sordOrder ='';
    $bedCrumTitle = 'Add';
	$objCheckoffTopicMaster = new clsUsafMasterCheckoffTopic();
    if(isset($_GET['editid'])) //Edit Mode
	{
        $checkofftopicmasterId = DecodeQueryData($_GET['editid']);
	    $page_title ="Edit Comps Topic";
        $bedCrumTitle = 'Edit';

        
		$row = $objCheckoffTopicMaster->GetUsafCheckoffTopicByCheckoffTopicMasterId($checkofftopicmasterId);
        
        if($row=='')
        {
            header('location:masteraddcheckofftopic.html');
            exit;
        }

        $title  = stripslashes($row['title']);      
        $checkoffTitleId  = stripslashes($row['checkoffTitleId']);      
        $procedureCategoryId  = stripslashes($row['procedureCategoryId']);      
        $proceduteCountId  = stripslashes($row['proceduteCountId']);      
	       
	}
   
    unset($objCheckoffTopicMaster);
	
	$objProcedureCategory=new clsUsafProcedureCategory();
    $Category = $objProcedureCategory->GetAllUsafCategory();
    
	$ProcedureCategory = $objProcedureCategory->GetAllUsafProcedureCountMaster();
    unset($objProcedureCategory);
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
	<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">


    

</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li><a href="usafmastercheckofftopic.html">Comps Steps</a></li>
                    <li class="active"><?php echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

         <form id="frmcheckoff" data-parsley-validate class="form-horizontal" method="POST" <?php if(isset($_GET['editid'])){ ?>action="masterusafcheckofftopicsubmit.html?editid=<?php echo(EncodeQueryData($checkofftopicmasterId)); ?>" <?php } else { ?> action="masterusafcheckofftopicsubmit.html" <?php } ?>>

            <div class="row">
                <div class="col-md-6">

                    <!-- Text input-->
					<div class="form-group">
                        <label class="col-md-12 control-label" for="txtcheckoffid">Comps ID</label>
                        <div class="col-md-12">
                            <input  id="txtcheckoffid"  name="txtcheckoffid" value="<?php echo($checkoffTitleId); ?>"  type="text"  class="form-control input-md required-input" required>

                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="txtcheckofftopic">Comps Topic</label>
                        <div class="col-md-12">
                            <input  id="txtcheckofftopic"  name="txtcheckofftopic" value="<?php echo($title); ?>"  type="text"  class="form-control input-md required-input" required>

                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
					<div class="form-group">
                        <label class="col-md-12 control-label" for="cboCategory">Comps Category</label>
                        <div class="col-md-12 flex-col-reverse">
                            <select id="cboCategory" name="cboCategory"  
							 class="form-control input-md required-input select2_single" required>
                            <option value="" selected>Select</option>
								<?php
                                if($Category!="")
                                {
                                    while($row = mysqli_fetch_assoc($Category))
                                    {
                                         $selCategoryId  = $row['procedureCategoryId'];
                                         $name  = stripslashes($row['categoryName']);

                                         ?>
										<option value="<?php echo($selCategoryId); ?>" <?php if($procedureCategoryId==$selCategoryId){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
                                        
										<?php

                                    }
                                }
                            ?>
                            </select>				
                       <input type="hidden" name="hcboCategory" value="<?php echo($procedureCategoryId); ?>" />
						</div>
                    </div>
                </div>
                <div class="col-md-6">					
					<div class="form-group">
                        <label class="col-md-12 control-label" for="cboProcedureCount">Procedure Count</label>
                        <div class="col-md-12 flex-col-reverse">
                            <select id="cboProcedureCount" name="cboProcedureCount"  
							 class="form-control input-md required-input select2_single"  required>
                            <option value="" selected>Select</option>
								<?php
                                if($ProcedureCategory!="")
                                {
                                    while($row = mysqli_fetch_assoc($ProcedureCategory))
                                    {
                                         $selproceduteCountId  = $row['proceduteCountId'];
                                         $name  = stripslashes($row['proceduteCountName']);

                                         ?>
										<option value="<?php echo($selproceduteCountId); ?>" <?php if($proceduteCountId==$selproceduteCountId){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
                                        
										<?php

                                    }
                                }
                            ?>
                            </select>				
                       <input type="hidden" name="hcboCategory" value="<?php echo($proceduteCountId); ?>" />
						</div>
                    </div>
				</div>
            </div>
            
				<div class="form-group">
                        <!-- <label class="col-md-2 control-label"></label> -->
                        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                            <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                            <a type="button" href="usafmastercheckofftopic.html" class="btn btn-default">Cancel</a>
						</div>
                </div>
        </form>


    </div>

    <?php include('includes/footer.php');?>
   
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

    <script type="text/javascript">
     
          $(".select2_single").select2();
           $('#select2-cboCategory-container').addClass('required-select2');
           $('#select2-cboProcedureCount-container').addClass('required-select2');

		
		$(window).load(function(){

             $('#frmcheckoff').parsley().on('field:validated', function() {
			    var ok = $('.parsley-error').length === 0;
            })
            .on('form:submit', function() {
                ShowProgressAnimation();									  
                return true; // Don't submit form for this demo
            });
		
        });
		
	
       
    </script>

 
    



</body>

</html>