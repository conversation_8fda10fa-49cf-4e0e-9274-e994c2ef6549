<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');      	
	include('../class/clsUsafMasterCheckoffTopic.php');
	include('../setRequest.php'); 
	
	//print_r($_POST);
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		
		$checkofftopicmasterId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;
		
		$status = ($checkofftopicmasterId > 0) ? 'updated' : 'added';
		
		$id = $_POST['txtcheckoffid'];
		$title = $_POST['txtcheckofftopic'];
		$Category = $_POST['cboCategory'];
		$ProcedureCount = $_POST['cboProcedureCount'];		
		//Save data
		$objCheckoffTopic = new clsUsafMasterCheckoffTopic();
		$objCheckoffTopic->checkoffTitleId = $id;		
		$objCheckoffTopic->title = $title;				
		$objCheckoffTopic->procedureCategoryId = $Category;		
		$objCheckoffTopic->proceduteCountId = $ProcedureCount;		
		$objCheckoffTopic->isTopicAdded = '1';		
		$retcheckofftopicmasterId = $objCheckoffTopic->SaveCheckoffTopic($checkofftopicmasterId);
		
		unset($objCheckoffTopic);
		if($retcheckofftopicmasterId > 0)
		{
				header('location:usafmastercheckofftopic.html?status='.$status);
		}
		else
		{
			header('location:usafmasteraddcheckofftopic.html?status=error');
		}
	}
	else
	{
		header('location:mastercheckofftopic.html');
		exit();
	}

?>