<?php
require '../vendor/autoload.php'; // PhpSpreadsheet autoload

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsStudent.php');
include('../setRequest.php');

$objStudent = new clsStudent();
$rowsStudentData = $objStudent->GetAllSchoolStudentList($currentSchoolId);
$totalStudentCount = $rowsStudentData ? mysqli_num_rows($rowsStudentData) : 0;

$objDB = new clsDB();
$schoolName = $objDB->GetSingleColumnValueFromTable('schools', 'displayName', 'schoolId', $currentSchoolId);

$title = "Student ID's List";
date_default_timezone_set('Asia/Kolkata');
$today = date('m/d/Y, H:i A');

// Create Spreadsheet
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('Student List');

// Title Style
$headerStyleArray = [
    'font' => ['bold' => true, 'size' => 16],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E0E0E0']],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
];

// Title
$sheet->mergeCells("B2:E2");
$sheet->setCellValue('B2', $title);
$sheet->getStyle('B2:E2')->applyFromArray($headerStyleArray);

// School Name Style
$schoolStyleArray = [
    'font' => ['bold' => true, 'size' => 12],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E0E0E0']],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
];

// School Name
$sheet->mergeCells("B4:E4");
$sheet->setCellValue('B4', $schoolName);
$sheet->getStyle('B4:E4')->applyFromArray($schoolStyleArray);

// Table Heading
$tableHeaderStyleArray = [
    'font' => ['bold' => true, 'size' => 10],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E0E0E0']],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
];

$sheet->setCellValue('B6', 'Student ID')->getStyle('B6')->applyFromArray($tableHeaderStyleArray);
$sheet->setCellValue('C6', 'Name')->getStyle('C6')->applyFromArray($tableHeaderStyleArray);
$sheet->setCellValue('D6', 'Rank')->getStyle('D6')->applyFromArray($tableHeaderStyleArray);
$sheet->setCellValue('E6', 'Email')->getStyle('E6')->applyFromArray($tableHeaderStyleArray);

// Data Population
$dataStyleArray = [
    'font' => ['size' => 10],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
];

$printStartRowCounter = 7;
if ($totalStudentCount) {
    while ($row = mysqli_fetch_array($rowsStudentData)) {
        $recordIdNumber = $row['recordIdNumber'] ?? '';
        $studentId = $row['studentId'];
        $fullName = $row['firstName'] . ' ' . $row['lastName'];
        $rank = $row['rank'];
        $email = $row['email'];
        $createdDate = date("m/d/Y", strtotime($row['createdDate']));

        $sheet->setCellValue('B' . $printStartRowCounter, $recordIdNumber);
        $sheet->setCellValue('C' . $printStartRowCounter, $fullName);
        $sheet->setCellValue('D' . $printStartRowCounter, $rank);
        $sheet->setCellValue('E' . $printStartRowCounter, $email);

        $sheet->getStyle("B{$printStartRowCounter}:E{$printStartRowCounter}")->applyFromArray($dataStyleArray);

        $printStartRowCounter++;
    }
}

// Auto Size Columns
foreach (range('B', 'E') as $columnID) {
    $sheet->getColumnDimension($columnID)->setAutoSize(true);
}
$sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows

// Output File
$reportName = 'StudentIdsListReport_';
$fileName = $reportName . $today . '.xls';

header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="' . $fileName . '"');
header('Cache-Control: max-age=0');

$writer = new Xls($spreadsheet);
$writer->save('php://output');
?>
