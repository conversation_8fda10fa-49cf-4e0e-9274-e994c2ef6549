<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');
    include('../class/clsImmunization.php');   
	include('../setRequest.php'); 
	
   
    
	  $studentImmunizationId=0;
	 if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
	   if(isset($_GET['studentImmunizationId'])) 
	  {
		$studentImmunizationId = $_GET['studentImmunizationId'];
        $studentImmunizationId = DecodeQueryData($studentImmunizationId);
      }
	   
         
		$status = ($studentImmunizationId > 0) ? 'Updated' : 'Added';
		 //echo 'status->'.$status;exit;	
		$systemUserId= $_SESSION['loggedUserId'];
		$immunizationId=0;
		$immunizationDate=GetDateStringInServerFormat($_POST['StartDate']);
		$immunizationMId  = ($_POST['txtreimmunizationMId']);
		$studentId  = ($_POST['txtstudentname']);
		$immunizationNote  = ($_POST['immunization_note']);
		$ImmunizationNotification  = GetDateStringInServerFormat($_POST['ImmunizationNotification']);
		$ExpiryDate=GetDateStringInServerFormat($_POST['ExpiryDate']);
		
		$objimmunization = new clsImmunization();
		         
		   $objimmunization->immunizationDate = $immunizationDate;
		   $objimmunization->immunizationNotificationDate = $ImmunizationNotification;
		   $objimmunization->expiryDate = $ExpiryDate;
		   $objimmunization->immunizationMId = $immunizationMId;		
		   $objimmunization->studentId = $studentId;	
		   $objimmunization->immunizationNote = $immunizationNote;	
		   $objimmunization->createdBy = $systemUserId;	
		   $studentImmunizationId=$objimmunization->SaveImmunization($studentImmunizationId); 	                     
					  
       unset($objimmunization);

		if($studentImmunizationId > 0)
		{
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = $objLog::EDIT;
			$userType = $objLog::ADMIN; // User type is set to ADMIN
			$IsMobile = 0;

			$objimmunization = new clsImmunization();
			$objimmunization->saveStudentImmunizationAuditLog($studentImmunizationId, $_SESSION['loggedUserId'], $userType, $action, $IsMobile);

			unset($objLog);
			//Audit Log End
			
		  if($studentImmunizationId==0)
					header('location:singlestudentimmunization.html?studentImmunizationId='.EncodeQueryData($retstudentImmunizationId).'& studentId='.EncodeQueryData($studentId).'& status='.$status);
				else
					header('location:singlestudentimmunization.html?studentImmunizationId='.EncodeQueryData($retstudentImmunizationId).'& studentId='.EncodeQueryData($studentId).'& status='.$status);
			exit();	
							
		}
		else
		{
			header('location:studentimmunization.html?status=error');
		}
		
	}
	
	{
		header('location:studentimmunization.html');
		exit();
	}
		
?>
 