<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsClinician.php');
include('../class/clsPersonnelCoarcRequestMaster.php');
include('../class/clsCoarc.php');
include('../setRequest.php');
// 	print_r($_POST); exit;
$personnelCoarcId = 0;
$systemUserMasterId = 0;

$surveyId = 0;

if (isset($_GET['surveyId'])) {
    $surveyId = $_GET['surveyId'];
    $surveyId = DecodeQueryData($surveyId);
}

//For Clinician List
$objclinician = new clsClinician();
$rowsClinicianData = $objclinician->GetAllSchoolClinicianForPersonnelCoarc($currentSchoolId, $surveyId);
$totalClinicianCount = 0;
if ($rowsClinicianData != '') {
    $totalClinicianCount = mysqli_num_rows($rowsClinicianData);
}

$rowsSurveyList = $objclinician->GetAllSchoolClinicianForPersonnelCoarcList($currentSchoolId);

unset($objclinician);

?>

<!DOCTYPE html>
<html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Personnel JRCERT Survey</title>
        <?php include('includes/headercss.php');?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
        <?php include("includes/datatablecss.php") ?>
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css"> 
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    </head>

<body>
    <?php include('includes/header.php'); ?>

    <form name="coarcsurvey" id="coarcsurvey" data-parsley-validate method="POST" action="addpersonnelcoarcsurvey.html">
        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Setting</a></li>
                        <li class="active">Personnel JRCERT Survey</li>
                    </ol>
                </div>
                <div class="pull-right">
                </div>
                <div class="pull-right">
                    <!-- <button class="btn btn-default" name="btnsendrequest" id="btnsendrequest" disabled="disabled" >Send Request</button> -->
                </div>
            </div>
        </div>

        <div class="container">
            <?php
            if (isset($_GET["status"])) {
                if ($_GET["status"] == "added") {
            ?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button>Sent request successfully.
                    </div>
                <?php
                } else if ($_GET["status"] == "updated") {
                ?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Request updated successfully.
                    </div>
                <?php
                } else if ($_GET["status"] == "Error") {
                ?>
                    <div class="alert alert-danger alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Error occurred.
                    </div>
            <?php
                }
            }
            ?>
            <div id="divTopLoading">Loading...</div>

            <div class="row">
                <div class="col-md-8"></div>
                <div class="col-md-4 pull-right">
                    <div class="form-group">
                        <label class="col-md-5 control-label margin_top_seven padding_zero" for="cborank">JRCERT Survey Title</label>
                        <div class="col-md-7 padding_zero">
                            <select id="surveyTitle" name="surveyTitle" class="form-control select2_single">
                                <option value="" selected>Select All</option>

                                <?php
                                if ($rowsSurveyList != '') {
                                    while ($row = mysqli_fetch_assoc($rowsSurveyList)) {

                                        $selsurveyId  = $row['coarcSurveyMasterId'];
                                        $name  = stripslashes($row['surveyTitle']);

                                ?>
                                        <option value="<?php echo EncodeQueryData($selsurveyId); ?>" <?php if ($surveyId == $selsurveyId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-10  margin_bottom_ten"></div>
                <div class="col-md-2  margin_bottom_ten">
                    <div class="form-group">
                        <!--button id="btncoarcrequest" name="btncoarcrequest" class="btn btn-success">Send CoARC Survey</button-->
                    </div>
                </div>
            </div>
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th><input type="checkbox" id="selectall" name="selectall[0]" class="selectall check" value=""> &nbsp;Select All </th>
                        <th>First Name</th>
                        <th>Last Name</th>
                        <th>Rank</th>
                        <th>JRCERT Survey Title</th>
                        <th>Status</th>
                        <th style="text-align: center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($totalClinicianCount > 0) {
                        while ($row = mysqli_fetch_array($rowsClinicianData)) {

                            $clinicianId = $row['clinicianId'];
                            $fName = stripslashes($row['firstName']);
                            $lastName = stripslashes($row['lastName']);
                            $fullName =  $fName . ' ' . $lastName;
                            $Role = stripslashes($row['Role']);
                            $surveyTitle = stripslashes($row['surveyTitle']);
                            $isDelivery = $row['isDelivery'];
                            //$status = stripslashes($row['status']);                           
                            // $personnelCoarcId = stripslashes($row['personnelCoarcId']);     

                            //Get Coarc Survey MasterId 
                            $coarcSurveyMasterId = stripslashes($row['coarcSurveyMasterId']);
                            $objCoarc = new clsCoarc();
                            $surveyDetail = $objCoarc->GetPersonnelCoarcSurveyByStudent($clinicianId, $coarcSurveyMasterId);
                            $personnelCoarcId = isset($surveyDetail['personnelCoarcId'])? stripslashes($surveyDetail['personnelCoarcId']) : '';
                            $status = isset($surveyDetail['status']) ? stripslashes($surveyDetail['status']) : '';
                            $updaterow = 'updaterow';

                            if ($personnelCoarcId) {
                                if ($status == 1) {
                                    $status = 'Completed';
                                } elseif ($status == 0) {
                                    $status = 'Pending';
                                }
                            } else {
                                $status = '-';
                            }
                            //Get Clinician CoarcId
                            $objCoarc = new clsPersonnelCoarcRequestMaster();
                            $getClinicianCoarcId = $objCoarc->GetCoarcIdForClinician($clinicianId, $coarcSurveyMasterId);

                            unset($objCoarc);
                    ?>
                            <tr <?php if ($status == 'Pending') { ?> class="<?php echo $updaterow; ?>" <?php } ?>>
                                <td>
                                    <input clinicianId="<?php echo EncodeQueryData($clinicianId); ?>" getClinicianCoarcId="<?php echo EncodeQueryData($getClinicianCoarcId); ?>" coarcSurveyMasterId="<?php echo EncodeQueryData($coarcSurveyMasterId); ?>" SchoolId="<?php echo EncodeQueryData($currentSchoolId); ?>" type="checkbox" id="coarcsurvey" name="coarcsurvey[]"
                                        value="<?php echo ($clinicianId); ?>" <?php if (($status == 'Completed') || ($status == 'Pending')) { ?> checked id="coarcsurvey" class="coarcsurvey sendrequest chkstudent" <?php } else { ?> class="sendrequest chkstudent" <?php } ?>>
                                </td>
                                <div id="error-selectall"></div>
                                <td><?php echo ($fName); ?> </td>
                                <td><?php echo ($lastName); ?></td>
                                <td><?php echo ($Role); ?></td>
                                <td><?php echo ($surveyTitle); ?></td>
                                <td><?php echo ($status); ?></td>
                                <td align="center">
                                    <?php if ($status == 'Completed') {
                                    ?>
                                        <a href="addpersonnelsurvey.html?personnelCoarcMasterId=<?php echo (EncodeQueryData($coarcSurveyMasterId)); ?>&clinicianId=<?php echo (EncodeQueryData($clinicianId)); ?>&personnelCoarcId=<?php echo (EncodeQueryData($personnelCoarcId)); ?>">View</a>|
                                        <?php
                                    } elseif ($status == 'Pending') {
                                        if ($isDelivery == '1') { ?>
                                            <a href="javascript:void(0);" coarcSurveyMasterId="<?php echo EncodeQueryData($coarcSurveyMasterId); ?>" getClinicianCoarcId="<?php echo EncodeQueryData($getClinicianCoarcId); ?>" clinicianId="<?php echo EncodeQueryData($clinicianId); ?>" SchoolId="<?php echo EncodeQueryData($currentSchoolId); ?>" class="reSendrequest">Resend SMS</a> |
                                            <a href="javascript:void(0);" coarcSurveyMasterId="<?php echo ($coarcSurveyMasterId); ?>" getClinicianCoarcId="<?php echo ($getClinicianCoarcId); ?>" clinicianId="<?php echo ($clinicianId); ?>" SchoolId="<?php echo ($currentSchoolId); ?>" class="copyLink" evaluationType='personnel' onclick="copyLinkUrl(this)">Click to Copy URL</a> |

                                        <?php } elseif ($isDelivery == '0') { ?>
                                            <a href="javascript:void(0);" coarcSurveyMasterId="<?php echo EncodeQueryData($coarcSurveyMasterId); ?>" getClinicianCoarcId="<?php echo EncodeQueryData($getClinicianCoarcId); ?>" clinicianId="<?php echo EncodeQueryData($clinicianId); ?>" SchoolId="<?php echo EncodeQueryData($currentSchoolId); ?>" class="reSendrequest">Resend Email</a> |
                                    <?php }
                                    }  ?>
                                    <a href="javascript:void(0);" class="deleteAjaxRow" personnelCoarcId="<?php echo EncodeQueryData($personnelCoarcId); ?>" coarcSurveyMasterId="<?php echo EncodeQueryData($coarcSurveyMasterId); ?>" clinicianId="<?php echo (EncodeQueryData($clinicianId)); ?>" systemUserMasterId="<?php echo EncodeQueryData($systemUserMasterId); ?>" schoolStudentName="<?php echo ($fullName); ?>"
                                        href="personnelrequest.html?personnelCoarcId=<?php echo (EncodeQueryData($personnelCoarcId)); ?>&clinicianId=<?php echo (EncodeQueryData($clinicianId)); ?>&coarcSurveyMasterId=<?php echo EncodeQueryData($coarcSurveyMasterId); ?>">Delete</a>
                                </td>
                            </tr>
                    <?php
                        }
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </form>
    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        var current_datatable = $("#datatable-responsive").DataTable({
            responsive: false,
            scrollX: true,
            "aaSorting": [],
            "aoColumns": [{
                "sWidth": "2%",
                "bSortable": false
            }, {
                "sWidth": "5%"
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "5%",
                "bSortable": false
            }]
        });

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();

            //check all
            var rows = current_datatable.rows().nodes();
            if ($(".coarcsurvey", rows).is(':checked'))
                $('.coarcsurvey', rows).attr('disabled', true);
            else
                $('.coarcsurvey', rows).attr('disabled', false);

        });

        //selectall
        $('.selectall').click(function() {

            if ($(this).is(':checked')) {
                $('#btnsendrequest').attr('disabled', false);
                $('input:checkbox:not(:disabled)').prop('checked', true);
            } else {
                $('#btnsendrequest').attr('disabled', true);
                $('input:checkbox:not(:disabled)').prop('checked', false);
            }
        });

        $('.chkstudent').click(function() {
            var TotalCheckboxCount = $('input[name="coarcsurvey[]"]').length;
            var CheckedCheckboxCount = $('input[name="coarcsurvey[]"]:checked').length;
            if (TotalCheckboxCount == CheckedCheckboxCount) {
                $('.selectall').prop('checked', true);
            } else {
                $('.selectall').prop('checked', false);
            }


            if (CheckedCheckboxCount > 0) {
                $('#btnsendrequest').attr('disabled', false);
            } else {
                $('#btnsendrequest').attr('disabled', true);
            }

        });
        //Delete Student

        $(document).on('click', '.deleteAjaxRow', function() {
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var personnelCoarcId = $(this).attr('personnelCoarcId');
            var clinicianId = $(this).attr('clinicianId');
            var coarcSurveyMasterId = $(this).attr('coarcSurveyMasterId');
            var schoolStudentName = $(this).attr('schoolStudentName');

            alertify.confirm('Student Name: ' + schoolStudentName, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: personnelCoarcId,
                        clinicianId: clinicianId,
                        coarcSurveyMasterId: coarcSurveyMasterId,
                        type: 'personnel_coarc_request_student'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});
        });

        //FOR SEND REQUEST 1 by 1
        $('#datatable-responsive').on("change", ".sendrequest", function(event) {

            var disabled = $(this).attr('disabled');
            if (disabled)
                return false;

            var clinicianId = $(this).attr('clinicianId');
            var clinicianId = $(this).attr('clinicianId');

            var SchoolId = $(this).attr('SchoolId');
            var getClinicianCoarcId = $('.reSendrequest').attr('getClinicianCoarcId');
            var coarcSurveyMasterId = $(this).attr('coarcSurveyMasterId');

            $.ajax({
                type: "POST",
                url: "personnelcoarcrequestsubmit.html",
                data: {
                    id: clinicianId,
                    SchoolId: SchoolId,
                    coarcSurveyMasterId: coarcSurveyMasterId,
                    getClinicianCoarcId: getClinicianCoarcId,
                    type: 'Personnel'
                },
                success: function() {
                    alertify.success('Sent');
                    // history.go(0);
                }
            });

        });


        $(document).on('click', '.reSendrequest', function() {

            var clinicianId = $(this).attr('clinicianId');
            var SchoolId = $(this).attr('SchoolId');
            var getClinicianCoarcId = $(this).attr('getClinicianCoarcId');
            var coarcSurveyMasterId = $(this).attr('coarcSurveyMasterId');

            $.ajax({
                type: "POST",
                url: "personnelcoarcrequestsubmit.html",
                data: {
                    id: clinicianId,
                    SchoolId: SchoolId,
                    getClinicianCoarcId: getClinicianCoarcId,
                    coarcSurveyMasterId: coarcSurveyMasterId,
                    type: 'Sent_Request'
                },
                success: function() {
                    alertify.success('Sent');
                    // history.go(0);
                }
            });
        });

        //copy link
        function copyLink(eleObj) {
            var clinicianId = $(eleObj).attr('clinicianId');
            var SchoolId = $(eleObj).attr('SchoolId');
            var getClinicianCoarcId = $(eleObj).attr('getClinicianCoarcId');
            var coarcSurveyMasterId = $(eleObj).attr('coarcSurveyMasterId');
            // var evaluationType = 'checkoff';




        }

        $("#selectall").change(function() {
            $('.sendrequest').trigger('change');
        });

        $("#surveyTitle").change(function() {
            var surveyId = $(this).val();

            if (surveyId) {
                window.location.href = "personnelrequest.html?surveyId=" + surveyId;
            } else {
                window.location.href = "personnelrequest.html";
            }
        });
    </script>
</body>

</html>