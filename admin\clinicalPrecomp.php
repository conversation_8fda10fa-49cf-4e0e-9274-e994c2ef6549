<?php
// Redirect from mobile
$IsMobile = isset($_GET['IsMobile']) ? ($_GET['IsMobile']) : 0;

include('../includes/config.php');
include('includes/validateUserLogin.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsRotation.php');
include('../class/clsClinician.php');
include('../class/clsClinicalPreComp.php');
include('../class/clsCheckoffTopicMaster.php');


// echo '<pre>';
@session_start();
// print_r($_SESSION);
// exit;
$schoolId = 0;
$rotationId = 0;
$termId = 0;
$studentId = 0;
$clinicalPrecompId = 0;
$clinicianId = isset($_SESSION['loggedClinicianId']) ? $_SESSION['loggedClinicianId'] : 0;
$clinicalPrecompRotationId = 0;
$patientCareAdultAreaId = 0;
$patientCarePediatricAreaId = 0;
$patientCareNeonatalaAreaId = 0;
$hospitalSiteId = 0;
$courselocationId = 0;
$totalSection = 0;
$parentRotationId = 0;
$rotationLocationId = 0;
$display_to_date = date('m/d/Y');
$evaluationDate = '';
$evaluatorDate = $studentDate = '';
$currentDate = date('m/d/Y');
$view = '';
$examId = 0;
$technologiestsignature = '';
$dateOftechnologiestSignature = '';
$accession = $comment = $reason = '';
$radioType = '0';
$radioRepeatReason = $radioRepeat = $radioResult = '0';
$checkoffType = '';

$weeks = 0;
$objDB = new clsDB();
$getTimezoneId = $objDB->GetSingleColumnValueFromTable('schools', 'timeZoneId', 'schoolId', $currentSchoolId);
$TimeZone = $objDB->GetSingleColumnValueFromTable('timezonemaster', 'timezone', 'timeZoneId', $getTimezoneId);
unset($objDB);

//object
$objRotation = new clsRotation();
$objDB = new clsDB();
$objClinicalPreComp = new clsClinicalPreComp();
if (isset($_GET['clinicalPrecompRotationId']))
    $clinicalPrecompRotationId = DecodeQueryData($_GET['clinicalPrecompRotationId']);

//For Edit CI Evaluation
if (isset($_GET['clinicalPrecompId'])) {
    $clinicalPrecompId = DecodeQueryData($_GET['clinicalPrecompId']);

    if ($clinicalPrecompId) {
        $schoolId = $currentSchoolId;
        $page_title = "Edit Clinical Pre-Comp";
        $bedCrumTitle = 'Edit';

        //Get CI Evalution Details

        $rowClinicalPreComp = $objClinicalPreComp->GetClinicalPrecomp($currentSchoolId, $clinicalPrecompId);

        if ($rowClinicalPreComp == '') {
            header('location:clinicalPrecompList.html?clinicalPrecompRotationId="' . EncodeQueryData($clinicalPrecompRotationId) . '"');
            exit;
        }
        // echo '<pre>';
        // print_r($rowClinicalPreComp);
        // $clinicalPrecompId = $rowEvaluation['clinicalPrecompId'];
        $clinicianId = $rowClinicalPreComp['clinicianId'];
        $studentId = $rowClinicalPreComp['studentId'];
        $clinicianName = $rowClinicalPreComp['clinicianName'];
        $studentName = $rowClinicalPreComp['studentName'];
        $rotationId = $rowClinicalPreComp['termId'];
        $termName = $rowClinicalPreComp['rotationName'];
        $examId = $rowClinicalPreComp['examId'];
        $accession = $rowClinicalPreComp['accession'];
        $technologiestsignature = $rowClinicalPreComp['technologiestsignature'];
        $radioResult = $rowClinicalPreComp['result'];
        $reason = $rowClinicalPreComp['reason'];
        $comment = $rowClinicalPreComp['comment'];


        $evaluationDate = isset($rowClinicalPreComp['evaluationDate']) ? stripslashes($rowClinicalPreComp['evaluationDate']) : '';
        if ($evaluationDate != '' && $evaluationDate != '0000-00-00 00:00:00') {
            $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
            $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
        } else
            $evaluationDate = '';

        $dateOftechnologiestSignature = isset($rowClinicalPreComp['dateOftechnologiestSignature']) ? stripslashes($rowClinicalPreComp['dateOftechnologiestSignature']) : '';
        if ($dateOftechnologiestSignature != '' && $dateOftechnologiestSignature != '0000-00-00 00:00:00') {
            $dateOftechnologiestSignature = converFromServerTimeZone($dateOftechnologiestSignature, $TimeZone);
            $dateOftechnologiestSignature = $dateOftechnologiestSignature = date("m/d/Y", strtotime($dateOftechnologiestSignature));
        } else
            $dateOftechnologiestSignature = "";

        // $studentSignatureDate = isset($rowClinicalPreComp['studentSignatureDate']) ? stripslashes($rowClinicalPreComp['studentSignatureDate']) : '';
        // if ($studentSignatureDate != '' && $studentSignatureDate != '0000-00-00 00:00:00') {
        //     $studentSignatureDate = converFromServerTimeZone($studentSignatureDate, $TimeZone);
        //     $studentDate = $studentSignatureDate = date("m/d/Y", strtotime($studentSignatureDate));
        // } else
        //     $studentDate = "";
    }
} else {
    $schoolId = $currentSchoolId;
    $page_title = "Add Clinical Pre-Comp";
    $bedCrumTitle = 'Add';
}

//----------------------------//
//Get Clinician Names
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, $clinicalPrecompRotationId);
unset($objClinician);

///----------------------//

// $clinicianSection = $objClinicalEval->GetSections($currentSchoolId);
// if ($clinicianSection != '') {
//     $totalSection = mysqli_num_rows($clinicianSection);
// }

//Get Hospital Site
// $objHospitalSite = new clsHospitalSite();
// $hospitalSite = $objHospitalSite->GetAllHospitalSite($currentSchoolId);
// unset($objHospitalSite);

//Get Rotation Name

$RotationName = $objRotation->GetrotationDetails($clinicalPrecompRotationId, $currentSchoolId);

$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] : '';
$endDate = isset($RotationName['endDate']) ? $RotationName['endDate'] : '';

//For Schedule
$isSchedule = isset($RotationName['isSchedule']) ? $RotationName['isSchedule'] :0;
$parentsRotationId = isset($RotationName['parentRotationId']) ? $RotationName['parentRotationId'] : 0;
// if ($isSchedule)
//     $endDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentsRotationId);
// //-----------------
// $endDate = date('m/d/Y', strtotime($endDate));

$view = isset($_GET['view']) ? $_GET['view'] : '';
$bedCrumTitle = ($view) ? 'View' : $bedCrumTitle;

//For Student
$objStudent = new clsStudent();
$totalstudent = 0;
$rowsstudent = $objStudent->GetStudentsByRotation($currentSchoolId, $clinicalPrecompRotationId);
if ($rowsstudent != '') {
    $totalstudent = mysqli_num_rows($rowsstudent);
}
unset($objStudent);
unset($objDB);

//rotation
$objrotation = new clsRotation();
$totalRotation = 0;
if (isset($_GET['clinicalPrecompId'])) {
    $rotation = $objrotation->GetAllrotationByClinicianForRad($schoolId, $rotationId);
} else {
    $rotation = $objrotation->GetCurrentrotationByClinicianForRad($schoolId, $clinicianId);
}
if ($rotation != '') {
    $totalRotation = mysqli_num_rows($rotation);
}

//For CheckOff Topic/ Exam
$objCheckoffTopicMaster = new clsCheckoffTopicMaster();
$rowsCheckoffTopics = $objCheckoffTopicMaster->GetAllCheckoffTopic($currentSchoolId, $checkoffType);
$totalCount = 0;
if ($rowsCheckoffTopics != '') {
    $totalCount = mysqli_num_rows($rowsCheckoffTopics);
}

// Term
$objClinicalPreComp = new clsClinicalPreComp();
$term = $objClinicalPreComp->Getterm();
if ($term != '') {
    $totalTerm = mysqli_num_rows($term);
}



?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($page_title); ?></title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    <style type="text/css">
        .some-class {
            float: left;
            clear: none;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #2e3192; */
            /* color: #fff; */
            color: #555;
            background: #f6f6ff;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f6ff !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        /* Style for the collapsible content */
        .panel-collapse {
            display: none;
            /* Hidden by default */
            /* padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9; */
        }

        /* Style for the collapsible button */
        .collapsible {
            /* background-color: #3498db;
            color: white;
            padding: 10px;
            border: none; */
            width: 100%;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            /* Align content horizontally */
        }

        .panel-heading {
            width: 100%;
        }

        /* Style for the arrow icons */
        .arrow-icon {
            transition: transform 0.3s;
        }

        .collapsible.expanded .arrow-icon i {
            transform: rotate(180deg);
        }

        /* .border-right {
            border-right: 1px solid #d9d9d9;
        } */

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }
        }

        @media screen and (max-width: 500px) {}
    </style>

</head>

<body>
    <?php if ($IsMobile == 0) { ?>

        <?php include('includes/header.php');
        ?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="rotations.html?active=1">Rotation</a></li>
                        <li><a href="rotations.html?active=1"><?php echo ($rotationtitle); ?></a></li>
                        <li><a href="clinicalPrecompList.html?clinicalPrecompRotationId=<?php echo EncodeQueryData($clinicalPrecompRotationId); ?>">Clinical Pre-Comp</a></li>
                        <li class="active"><?php echo ($bedCrumTitle); ?></li>
                    </ol>
                </div>

            </div>
        </div>
    <?php  } else  ?>

    <div class="container">

        <form id="frmevaluation" data-parsley-validate class="form-horizontal" method="POST" action="clinicalPrecompsubmit.html?clinicalPrecompId=<?php echo (EncodeQueryData($clinicalPrecompId)); ?>
																									&clinicalPrecompRotationId=<?php echo (EncodeQueryData($clinicalPrecompRotationId)); ?>">

            <div class="row">
                <!-- Mobile redirect -->
                <input type="hidden" name="IsMobile" id="IsMobile" value="<?php echo $IsMobile; ?>">

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="Adult">Evaluator</label>
                        <div class="col-md-12">
                            <select id="cboclinician" name="cboclinician" class="form-control input-md required-input select2_single disableClass" required data-parsley-errors-container="#cboclinician-err">
                                <option value="" selected>Select</option>
                                <?php
                                if ($Clinician != "") {
                                    while ($row = mysqli_fetch_assoc($Clinician)) {
                                        $selClinicianId  = $row['clinicianId'];
                                        $firstname  = stripslashes($row['firstName']);
                                        $lastname  = stripslashes($row['lastName']);

                                        $name = $firstname . ' ' . $lastname;


                                ?>
                                        <option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

                                <?php

                                    }
                                }

                                ?>
                            </select>
                            <div id="cboclinician-err"></div>
                        </div>
                    </div>

                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="evaluationDate">Evaluation Date</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='evaluationDate' style="position: relative;">

                                <input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date evaluationDate disableClass" value="<?php echo ($evaluationDate); ?>" required data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                </div>


            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cboStudent">Student</label>
                        <div class="col-md-12">
                            <select id="cboStudent" name="cboStudent" class="form-control input-md required-input select2_single disableClass" required data-parsley-errors-container="#error-cboStudent">
                                <option value="" selected>Select</option>
                                <?php
                                if ($totalstudent > 0) {

                                    while ($row = mysqli_fetch_array($rowsstudent)) {
                                        $selstudentId = $row['studentId'];
                                        $firstName = $row['firstName'];
                                        $lastName = $row['lastName'];
                                        // $rank = $row['rank'];
                                        $fullName = $firstName . ' ' . $lastName;

                                ?>
                                        <option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($fullName); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-cboStudent"></div>
                        </div>
                    </div>

                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cboTerm">Term</label>
                        <div class="col-md-12">
                            <!-- onChange="getstudent(this.value);" -->
                            <select id="cboTerm" name="cboTerm" class="form-control input-md required-input select2_single disableClass" required>
                                <option value="" selected>Select</option>
                                <?php
                                if ($rotation != "") {
                                    while ($row = mysqli_fetch_assoc($rotation)) {
                                        $selRotationId  = $row['rotationId'];
                                        $name  = stripslashes($row['title']);
                                ?>
                                        <option value="<?php echo ($selRotationId); ?>" <?php if ($clinicalPrecompRotationId == $selRotationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-cboTerm"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="exam">Exam</label>
                        <div class="col-md-12">
                            <select id="cboExam" name="cboExam" class="form-control input-md required-input select2_single disableClass" required>
                                <option value="" selected>Select</option>
                                <?php
                                if ($rowsCheckoffTopics != "") {
                                    while ($row = mysqli_fetch_assoc($rowsCheckoffTopics)) {
                                        $selTopicId  = ($row['TopicId']);
                                        $examName  = stripslashes($row['schooltitle']);
                                ?>
                                        <option value="<?php echo ($selTopicId); ?>" <?php if ($examId == $selTopicId) { ?> selected="true" <?php } ?>><?php echo ($examName); ?></option>

                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-exam"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="accession">Accession</label>
                        <div class="col-md-12">
                            <div id='accession'>
                                <input type='text' name="accession" id="accession" class="form-control input-md rotation_date disableClass" value="<?php echo ($accession);
                                                                                                                                                    ?>" data-parsley-errors-container="#error-accession" />
                            </div>
                            <div id="error-accession"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="technologiestsignature">Technologist Signature</label>
                        <div class="col-md-12">
                            <div id='technologiestsignature'>
                                <input type='text' name="technologiestsignature" id="technologiestsignature" class="form-control input-md rotation_date disableClass" value="<?php echo ($technologiestsignature);
                                                                                                                                                                                ?>" data-parsley-errors-container="#error-technologiestsignature" />
                            </div>
                            <div id="error-technologiestsignature"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="dateOftechnologiestSignature">Date Of Technologist Signature</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='dateOftechnologiestSignature' style="position: relative;">

                                <input type='text' name="dateOftechnologiestSignature" id="dateOftechnologiestSignature" class="form-control input-md  rotation_date dateOftechnologiestSignature disableClass" value="<?php echo ($dateOftechnologiestSignature); ?>" data-parsley-errors-container="#error-dateOftechnologiestSignature" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-dateOftechnologiestSignature"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 1st SECTION div start -->
            <br>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <div class="col-md-12 col-sm-12 col-xs-12">

                            <label class="control-label mr-15" for="floorCough">Result</label>&nbsp;&nbsp;
                            <label class="radio-inline control-label"><input type="radio" class=" input-md" value="0" name="radioResult" <?php echo ($radioResult == '0') ? "checked" : ""; ?>>Pass</label>
                            <label class="radio-inline control-label"><input type="radio" class="input-md" value="1" name="radioResult" <?php echo ($radioResult == '1') ? "checked" : ""; ?>> Fail</label>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row hide" id="resonDiv">
                <div class="col-md-12 col-sm-12 col-xs-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="reason">Reason</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div id="reason" style="width: 100%;">
                                <textarea name="reason" id="textarea" class="form-control input-md clstextarea disableClass" rows="4" cols="100"><?php echo ($reason); ?></textarea>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 col-sm-12 col-xs-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="comment">Comments</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div id="comment" style="width: 100%;">
                                <textarea name="comment" id="textarea" class="form-control input-md clstextarea disableClass" rows="4" cols="100"><?php echo ($comment); ?></textarea>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" style="margin: 0;">
                <div class="col-md-12">
                    <div class="form-group">
                        <!-- <label class="col-md-2 control-label"></label> -->
                        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                            <?php //if ((strtotime($currentDate)) < (strtotime($endDate))) {
                            if ($view == '') {
                            ?>

                                <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                            <?php }
                            //} 
                            ?>
                            <?php if ($IsMobile) { ?>
                                <a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=clinicalPrecomp" class="btn btn-default">Cancel</a>
                            <?php } else { ?>
                                <a type="button" href="clinicalPrecompList.html?clinicalPrecompRotationId=<?php echo EncodeQueryData($clinicalPrecompRotationId); ?>" class="btn btn-default">Cancel</a>
                            <?php }
                            ?>
                        </div>
                    </div>
                </div>
            </div>
        </form>


    </div>

    <?php include('includes/footer.php'); ?>

    <?php //print_r($_SESSION); 
    ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>




    <script type="text/javascript">
        $(window).load(function() {
            $(".isAllRadioButton").trigger('click');

            $('#frmevaluation').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    $('#frmevaluation').find(':disabled').removeAttr('disabled');
                    return true; // Don't submit form for this demo
                });

            $('#evaluationDate').datetimepicker({
                format: 'MM/DD/YYYY',
                defaultDate: moment()
            });
            // $('#studentDate').datetimepicker({
            //     format: 'MM/DD/YYYY'
            // });
            // $('#evaluatorDate').datetimepicker({
            //     format: 'MM/DD/YYYY',
            //     defaultDate: moment()
            // });

            $('#dateOftechnologiestSignature').datetimepicker({
                format: 'MM/DD/YYYY'
            });

            //for searching dropdown
            $(".select2_single").select2();
            $('#select2-cbohospitalsites-container').addClass('required-select2');
            $('#select2-cboclinician-container').addClass('required-select2');
            $('#select2-cboStudent-container').addClass('required-select2');
            $('#select2-weeks-container').addClass('required-select2');
            $('#select2-cboTerm-container').addClass('required-select2');

            $("input[type=radio]").attr('disabled', true);
            $(".disableClass").attr('disabled', true);

        });

        $(document).ready(function() {
            var myRadio = $("input[name=radioResult]");

            $('input[type="radio"]').click(function() {

                const inputValue = $(this).attr("value");
                // console.log(inputValue);
                if (inputValue == 1)
                    $('#resonDiv').removeClass('hide');
                else
                    $('#resonDiv').addClass('hide');


            });

            // Trigger the click event on document ready
            myRadio.filter(":checked").trigger("click");

        });

      
    </script>
</body>

</html>