<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');
	include('../setRequest.php');	      
    include('../class/clsCheckoffQuestionMaster.php');  
	
	$schoolSectionId=0;
	$schoolQuestionId=0;
	
	$objQuestionMaster = new clsCheckoffQuestionMaster();
	// $rowsQuestionData=$objQuestionMaster->GetAllMasterCheckoffQuestions($currentSchoolId,$schoolSectionId);
	$totalCount =0;
	// if($rowsQuestionData !='')
	// {
	// 	$totalCount = mysqli_num_rows($rowsQuestionData);
	// }	
    $loggedAsBackUserId = isset($_SESSION["loggedAsBackUserId"]) ? isset($_SESSION["loggedAsBackUserId"]) : 0;
	unset($objQuestionMaster);
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Comps Steps</title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>
						<li><a href="settings.html">Settings	</a></li>                        
                         <li class="active">Steps</li>
                    </ol>
                </div>              
            </div>
        </div>

        <div class="container">

                <div id="divTopLoading" >Loading...</div>
				
					<form name="checkoffquestion" id="checkoffquestion" data-parsley-validate method="POST" action="questionsubmit.html?questionId=<?php echo $schoolQuestionId;  ?>">
						<div class="row">
						<div class="col-md-10  margin_bottom_ten"></div>
						<div class="col-md-2  margin_bottom_ten">
						
								<div class="form-group">
								</div>
						</div>
					
					</div>	
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr >
							
                           <th>Steps</th>                                                   
                           <th>Comps Steps Title</th>                                                   
                            <?php if(isset($_SESSION["loggedAsBackUserId"])) {?>   
                                <th style="text-align: center">Action</th>                                                   
                            <?php }?>   
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalCount > 0)
                        {
                            while($row = mysqli_fetch_array($rowsQuestionData))
                            {
								
                                $schoolQuestionId = ($row[0]);					
                                $schoolQuestionFullTitle = stripslashes($row['schoolQuestionTitle']);

                                //Check Question is Assign or not
                                $objDB = new clsDB();
                                $isPresentQuestionId = $objDB->GetSingleColumnValueFromTable('schooltopicdetails', 'schoolQuestionId','schoolQuestionId',$schoolQuestionId);
                                unset($objDB);
                                $sortOrder =($row['sortOrder']);
								
								$shortTitlelen=strlen($schoolQuestionFullTitle);
								if($shortTitlelen > 80)
								{
								   
								    $schoolQuestionTitle=substr($schoolQuestionFullTitle,0,80);
									$schoolQuestionTitle .= '...';
							      
								}
								else
								{
								    $schoolQuestionTitle=$schoolQuestionFullTitle;
								}							
								
                               ?>
                            <tr>
								
								<td style="text-align: center"><?php echo ($sortOrder); ?></td>
								
                                <td title="<?php echo ($schoolQuestionFullTitle); ?>">
                                    <?php echo($schoolQuestionTitle); 				
									?>
                                </td>
                                <?php if(isset($_SESSION["loggedAsBackUserId"])) {?>   
                                    <td style="text-align: center">
                                    <?php if($isPresentQuestionId > 0)
                                        { ?>
                                            <a href="editquestion.html?questionId=<?php echo $schoolQuestionId;  ?>">Edit</a>|
                                            <a id="warningAjax" class="text-muted" href="javascript:void(0);" QuestionId="<?php echo EncodeQueryData($schoolQuestionId); ?>" QuestionTitle="<?php echo($schoolQuestionTitle); ?>"  >Delete</a>
                                            
                                        <?php } else { ?>
                                        <a href="editquestion.html?questionId=<?php echo $schoolQuestionId;  ?>">Edit</a>|
                                        <a href="javascript:void(0);" class="deleteAjaxRow" QuestionId="<?php echo EncodeQueryData($schoolQuestionId); ?>" QuestionTitle="<?php echo($schoolQuestionTitle); ?>">Delete</a>	
                                        
                                        <?php } ?>
                                    </td>
                                <?php } ?> 
                            </tr>
                            <?php
							}
                        }
                    ?>
					</tbody>
                </table>
				</form>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <!-- <link rel="stylesheet" href="https://cdn.datatables.net/1.13.1/css/jquery.dataTables.min.css">
        <script src="https://cdn.datatables.net/1.13.1/js/jquery.dataTables.min.js"></script> -->
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>    
	
        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";        
               
			$(window).load(function(){
                $("#divTopLoading").addClass('hide');
				// $(".select2_single").select2();
				  });	
                var loggedAsBackUserId = '<?php echo $loggedAsBackUserId; ?>';

                var currentSchoolId='<?php echo ($currentSchoolId); ?>';  
                var current_datatable = $("#datatable-responsive").DataTable({
                    "autoWidth":false,
                    'iDisplayLength': 250,
					 "aoColumns": [{
                    "sWidth": "2%"
                }
                  <?php if($loggedAsBackUserId) { ?>
                    ,{
                    "sWidth": "83%"
				    },
                    {
                    "sWidth": "15%",
					"bSortable": false					
                    }
                    <?php }
                    else{ ?>
                    ,{
                    "sWidth": "98%"
				    }
                    
                    <?php }?>
                  ],
                
                  "ajax":{
                    url :"../ajax/ajax_get_checkoff_steps.html", // json datasource
                    type: "POST",  // method  , by default get
                    data: {
                        'currentSchoolId': currentSchoolId
                    },
                    error: function(){  // error handling
                        $(".employee-grid-error").html("");
                        $("#datatable-responsive").append('<tbody class="employee-grid-error"><tr><th colspan="3">No data found in the server</th></tr></tbody>');
                        $("#employee-grid_processing").css("display","none");
                    }
                }
				 }); 
		
		
			
			
				$(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var QuestionId = $(this).attr('QuestionId');
                var QuestionTitle = $(this).attr('QuestionTitle');
                var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']) ?>';
                
                alertify.confirm('Question Name: '+QuestionTitle, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: QuestionId,
                            type: 'PEF_Question',
                            userId: userId
                        },
                        success: function(data) {
                            console.log(data);
                            if(data == 'alreadyAssigned')
                            {
                                alertify.confirm('Warning!', 'This question already assigned, you cant delete it!', function(){
				                }, function() {});
                                return false;
                            }
                            else
                            {
                                current_datatable.row(current_datatable_row).remove().draw(false);
                                alertify.success('Deleted');
                            }
                            
                        }
                    });
                }, function() {});

            });
			
			
			$(document).on('click', '#warningAjax', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var QuestionId = $(this).attr('QuestionId');
                var QuestionTitle = $(this).attr('QuestionTitle');
                
                alertify.confirm('Warning!', 'This question already assigned, you cant delete it!', function(){
				 }, function() {});
            });
        </script>
    </body>
    </html>