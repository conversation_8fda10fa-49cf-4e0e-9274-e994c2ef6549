<?php 
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');  
	include('../class/clsschoolclinicalsiteunit.php');   
    include('../setRequest.php'); 
    
    $schoolId = 0;
    $transchooldisplayName = '';
    if(isset($_GET['schoolId'])) //Edit Mode
	{
		$schoolId = $_GET['schoolId'];
        $schoolId = DecodeQueryData($schoolId);
    }
    else
    {
        $schoolId = $currentSchoolId;       
        $transchooldisplayName=$currenschoolDisplayname;
    }

    $title ="Clinical Site Unit| ".$transchooldisplayName;

    //For Clinical Site Unit
	$objschoolclinicalsiteunit = new clsschoolclinicalsiteunit();
	$rowsClinicalSiteUnit = $objschoolclinicalsiteunit->GetAllClinicalSiteUnit($schoolId);
	if($rowsClinicalSiteUnit !='')
	{
		$totalClinicalSiteUnit =mysqli_num_rows($rowsClinicalSiteUnit);
	}
	unset($objschoolclinicalsiteunit);
?>
    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo($title); ?></title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>

		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Clinical Site Unit</li>
                    </ol>
                </div>
         
               <div class="pull-right">
                     <a class="btn btn-link" href="addclinicalsiteunit.html">Add</a>
               </div>
         

            </div>
        </div>

        <div class="container">

            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="Added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Clinical Site Unit added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Clinical Site Unit updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Deleted")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Clinical Site Unit deleted successfully.
                </div>
                <?php 
					}
                     else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalClinicalSiteUnit>0)
                        {
                            while($row = mysqli_fetch_array($rowsClinicalSiteUnit))
                            {
								
								$schoolClinicalSiteUnitId = $row['schoolClinicalSiteUnitId'];
                               	$title = stripslashes($row['title']);
                                ?>
                            <tr>
                                <td>
                                    <?php echo($title); ?>
                                </td>
                                 
                                <td style="text-align: center">    
                                    <a href="addclinicalsiteunit.html?id=<?php echo(EncodeQueryData($schoolClinicalSiteUnitId)); ?>">Edit</a> 
                                     | <a href="javascript:void(0);" class="deleteAjaxRow"
									schoolClinicalSiteUnitId="<?php echo EncodeQueryData($schoolClinicalSiteUnitId); ?>" clinicalsitetitle="<?php echo($title); ?>" >Delete</a>
												              
                                </td>
                            </tr>
                            <?php
                            }
                        }
                    ?>
                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
        <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            });

            var current_datatable = $("#datatable-responsive").DataTable({
                "aoColumns": [{
                    "sWidth": "50%"
                },{
                    "sWidth": "5%",
                    "sClass": "alignCenter",
					"bSortable": false
                }]
            });

            // ajax call for deleteAjaxRow
                 
             $(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var schoolClinicalSiteUnitId = $(this).attr('schoolClinicalSiteUnitId');
                var title = $(this).attr('clinicalsitetitle');
 
                var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';
                var isUser = 1; //for Admin
               
                alertify.confirm('Clinical Site Title: '+ title, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: schoolClinicalSiteUnitId,
                            userId: userId,
                            isUser: isUser,
                           type: 'clinicalsite'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });
        </script>
    </body>
    </html>