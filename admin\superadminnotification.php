<?php
// phpinfo();exit;
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');     
    include('../class/clsNotification.php');  
    include('../class/clsStudent.php'); 
    include('../class/clsClinician.php');
    include('../class/clsSystemUser.php');
	include('../setRequest.php'); 
		
		$schoolId= $currentSchoolId;		
		$isSuperAdmin = $_SESSION['isSuperAdmin'];		
		
        $page_title ="Notifications";
        $bedCrumTitle = 'Notifications';
        
        $TotalStudentNotifications='';
        $allStudentNotifications='';
		$year ='';
		$isArchive =0;
        $objNotification= new clsNotification();
       	
		if(isset($_GET['year']))
		{
			$year = $_GET['year'];
		}else{
			$year= date('Y');
		}
	
		if(isset($_GET['isArchive']))
		{
			$isArchive = $_GET['isArchive'];
		}else{
		    $isArchive = 0;
		}
		
		if($isSuperAdmin =='1')
		{
		    
		if($year !='' && !$isArchive){
				$allStudentNotifications=$objNotification->SentNotificationToSuperAdmin($year,$isArchive);
		}else{
				$allStudentNotifications=$objNotification->SentNotificationToSuperAdmin($year,$isArchive);
		}
	
		
		if($allStudentNotifications !='')
			{
				$TotalStudentNotifications = mysqli_num_rows($allStudentNotifications);
			}
		}
       
	
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    
    
	<style>
		.hide{
			display:none;
		}
		.archivelink{
			margin-left: -72px;
            margin-top: -10px;
		}
		#years{
		    margin: -7px;
            width: 80px;
		}
		.archive{
		    margin-left: 15px;
		}
	</style>
	
</head>

<body>
<?php include('includes/header.php');?>

<div class="row margin_zero breadcrumb-bg">
	<div class="container">
		<div class="pull-left">
			<ol class="breadcrumb">
				<li><a href="dashboard.html">Home</a></li>           
				<?php
                    if($isArchive ==0){
                ?>
                         
				<li class="active"><?php echo($bedCrumTitle);?></li>
				<?php }else{ ?>
				         
				<li class=""><a href="<?= BASE_PATH.'/school/'.$schoolSlug.'/admin/superadminnotification.html' ?>"><?php echo($bedCrumTitle);?></a></li>
				<li class="active">Archive</li>
				<?php } ?>
			</ol>
		</div>
		<?php
        // if($isArchive ==0){
        ?>
		<div class="pull-right">
			<ol class="breadcrumb">
				<li class="pull-right">

				<?php
			
				$currently_selected = date('Y'); 
				// Year to start available options at
				$earliest_year = 2017; 
				// Set your latest year you want in the range, in this case we use PHP to just set it to the current year.
				$latest_year = date('Y'); 

				echo '<select name="years" id="years" class="form-control input-md select2_single year">';
			
					foreach ( range( $latest_year, $earliest_year ) as $i ) {
						if($year == $i){
								$selectVal = 'selected';
							}else{
								$selectVal = '';
							}
						echo '<option value="'.$i.'"  '.$selectVal.'>'.$i.'</option>';
					}
				echo '</select>';
    		?>
			</li>
			</ol>
		</div>
		<?php
        // }
        ?>
	</div>
</div>
<?php
if($isArchive ==1){
?>  
<div class="col-md-11 text-right archivelink">
	<a href="<?= BASE_PATH.'/school/'.$schoolSlug.'/admin/superadminnotification.html' ?>">Back</a>         
</div>
<?php }else{
?>
<div class="col-md-11 text-right archivelink">
	<a href="<?= BASE_PATH.'/school/'.$schoolSlug.'/admin/superadminnotification.html?isArchive=1' ?>">Archive</a>         
</div>
<?php
}
?>
<div class="container">
	<div class="container margin_bottom_fifty">
			<div class="col-md-12 margin_bottom_twenty" id="divNotification">
				<?php
				  if($TotalStudentNotifications > 0)
				  {
					while($notification = mysqli_fetch_assoc($allStudentNotifications))
					{
						$notificationId = $notification['MnotificationId'];
						$modul = stripslashes($notification['modul']);
						$userType = stripslashes($notification['MuserType']);
						$Notification = stripslashes($notification['notification']);								
						$isArchive = stripslashes($notification['isArchive']);
						$created_at = stripslashes($notification['created_at']);
						$isActiveNotification = stripslashes($notification['isActiveNotification']);								
						$created_atDate = date('m/d/Y', strtotime($created_at));
						
						?>
							<div class="media" id="mainDiv_<?= $notificationId ?>" >
								<div class="media-left">
									<a href="javascript:void(0);">
										<img class="media-object" src="<?php echo ($dynamicOrgUrl); ?>/assets/images/notification-alert.png" alt="">
									</a>
								</div>
								<div class="media-body">
									<h4 class="media-heading"><?php echo($modul); ?></h4>
									<p style="font-style:italic" ><?php echo($created_atDate); ?></p>
									<a href="javascript:void(0);" class="linkShowMore" >Show more +</a>
									<?php
                                    if($isArchive ==0){
                                    ?>  
                                    <a href="javascript:void(0);" class="archive" notificationId = <?= $notificationId ?>  userType="<?= $userType ?>">Move to Archive</a>
									
                                    <?php } ?>
                                   
									<div class="notification-content hide">
											<br>
											<?php
											$Allschool='';
											$StudentCount=0;
											
											if($year !=''){
												$Allschool = $objNotification->GetSchoolAll($created_at,$year,$isArchive);
											}else{
												$Allschool = $objNotification->GetSchoolAll($created_at,$year,$isArchive);
											}
											 if($Allschool!='')
												{ 
													 while($row = mysqli_fetch_assoc($Allschool))
													 {
														$schoolId = $row['schoolId'];
														$schoolName = $row['displayName'];
														$slug = $row['slug'];
														
														unset($objSystemUsers);
															
														$objStudent = new clsStudent();
													    $StudentCount = $objStudent->StudentCount($schoolId,$created_at);
													
														//Get  Unread Count 
														$totalUnreadCount = 0;
														$countresult =$objNotification->SentNotificationReadCount(0,$schoolId);
														if($countresult !='')
															$totalUnreadCount = mysqli_num_rows($countresult);
														
														$newAdded = '';
														
														if($totalUnreadCount > 0)
														{
															$newAdded = '<span class="badge badge-secondary" >New</span>';
														}
														
													$dynamicLoginURL = BASE_PATH.'/school/'.$slug.'/admin/index.html';
													
													?>
												<b>School Name: </b>  <a href="javascript:void(0)" class="loginAsSchoolUser"  schoolId="<?php echo EncodeQueryData($schoolId); ?>"  schoolUserFullName="<?php echo($schoolName); ?>" typeNotification='T'><?php echo ($schoolName); ?></a>
												 |  <a href="javascript:void(0);"  class="studentsCount" onclick="getStudent(this);" schoold="<?php echo $schoolId; ?>" notificationId="<?php echo($notificationId); ?>" userType="<?php echo($userType); ?>" created_atDate="<?php echo($created_at); ?>" isActiveNotification="<?php echo($isActiveNotification); ?>"><?php echo ($StudentCount); ?>  </a>   <?php echo($newAdded); ?>
												<br><br>
												<div id="schoolIdDiv_<?php echo $schoolId;?>_<?php echo $created_at;?>" class="studentsCount-content hide">
												<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
												<thead>
													<tr>
														<th>First Name</th>
														<th>Last Name</th>
														<th>Contact Info</th>
														<th>Start Date</th>
													</tr>
												</thead>
												<tbody>
													<?php
													$totalstudentDetails='';
													$studentDetails = $objStudent->GetDetail($schoolId,$created_at);
													if($studentDetails !='')
													{
														$totalstudentDetails = mysqli_num_rows($studentDetails);
													}
																									
													if($totalstudentDetails>0)
													{
														while($row = mysqli_fetch_array($studentDetails))
														{
															$studentId='';
															$studentId = $row[0];					
															$firstName = stripslashes($row['firstName']);
															$lastName = stripslashes($row['lastName']);
															$fullName = $firstName.' '.$lastName;
															$email = stripslashes($row['email']);
															$phone = stripslashes($row['phone']);
															$cellPhone  = stripslashes($row['cellPhone']);
															
															?>
														<tr>
															<td> <?php echo($firstName);?></td>
															<td><?php echo($lastName);?></td>
														
															<td>
																Email: <a href="mailto:<?php echo($email); ?>">
																	<?php echo($email); ?>
																</a> <br>
																Phone: <a href="tel:<?php echo($phone); ?>">
																	<?php echo($phone); ?>
																</a>
																<?php if($cellPhone) { ?>
																<br>
																Cell Phone: <?php echo($cellPhone); } ?>
															</td>
															<td><?php echo($created_atDate);?></td>
														</tr>
														<?php
														}
													}
												?>
												</tbody>
											</table>
											</div>
										<?php  }}?>
									</div>
								</div>
							</div>
						<?php
					}
				  }
				  else
				  {
					?>
						<div class="text-center">You do not have any notifications at this time.</div>
					<?php
				  }
				 unset($objStudent);
				?>
			</div>
	</div>
</div>
<?php include('includes/footer.php');?>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

<script >
	ShowProgressAnimation();		
	$(window).load(function(){
	   HideProgressAnimation();		
	});
	
	alertify.defaults.transition = "slide";
	alertify.defaults.theme.ok = "btn btn-success";
	alertify.defaults.theme.cancel = "btn btn-danger";
	alertify.defaults.theme.input = "form-control";
	
	$('body').on('click', '.linkShowMore', function() {
		if($(this).nextAll('.notification-content').hasClass('hide'))
		{
			$(this).nextAll('.notification-content').removeClass('hide');
			$(this).text('Less more -');
		}
		else
		{
			$(this).nextAll('.notification-content').addClass('hide');
			$(this).text('Show more +');
		}
	});
	
	function getStudent(eleObj)
	{
		var  schoold = $(eleObj).attr('schoold');
		var  created_atDate = $(eleObj).attr('created_atDate');
		
		if($('#schoolIdDiv_'+schoold+'_'+created_atDate).hasClass('hide'))
		{
			$('#schoolIdDiv_'+schoold+'_'+created_atDate).removeClass('hide');
		}
		else
		{
			$('#schoolIdDiv_'+schoold+'_'+created_atDate).addClass('hide');
		}
	}
	
	  $(document).on('click', '.loginAsSchoolUser', function() {
		var schoolUserFullName = $(this).attr('schoolUserFullName');
		var schoolId = $(this).attr('schoolId');
		var typeNotification = $(this).attr('typeNotification');
		window.location.href='displayschoolsystemuser.html?typeNotification='+typeNotification+'&schoolId='+schoolId;
	});


	
	$('body').on('click', '.studentsCount', function(currentObj) {
		
		var currentDiv = $(this);
		var notificationId = $(currentDiv).attr('notificationId');
		var schoold = $(currentDiv).attr('schoold');
		var created_atDate = $(currentDiv).attr('created_atDate');
		var userType = $(currentDiv).attr('userType');
		var isActiveNotification = $(currentDiv).attr('isActiveNotification');
		
		$.ajax({
			type: "GET",
			url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_superadmin_notification_read_unread.html",
			data: {
				notificationId: notificationId,
				created_atDate: created_atDate,
				isActiveNotification: isActiveNotification,
				schoold: schoold,
				userType: userType
			},
			success: function() {
			   displayNotificationCount();
			}
		});
	});
	
		
	$('body').on('click', '.archive', function(currentObj) {
		
		
		var notificationId = $(this).attr('notificationId');
		var userType = $(this).attr('userType');
		
		$.ajax({
			type: "GET",
			url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_superadmin_notification_move_archive.html",
			data: {
				notificationId: notificationId,
				userType: userType
			},
			success: function() {
				$('#mainDiv_'+notificationId).addClass('hide');
			   alertify.success("Move to archive");
			}
		});
	});
	

   $(document).ready(function() {
      $('#years').on('change', function () {
		var year = $(this).val();
		var isArchive = '<?php echo $isArchive ?>';
 		if(year)
 		{
 			window.location.href = "superadminnotification.html?year="+year+"&isArchive="+isArchive;
 		}
 		else{
 			window.location.href = "superadminnotification.html";
 		}
	})
  });
</script>
</body>
</html>