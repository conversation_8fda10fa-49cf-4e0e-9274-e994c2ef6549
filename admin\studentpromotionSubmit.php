<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsStudent.php');
include('../class/clsStudentRankMaster.php');
include('../setRequest.php');


if ($_SERVER['REQUEST_METHOD'] == "POST") {

	$arrStudentId  = ($_POST['checkbox']);
	$rankId  = ($_POST['cboNewRanking']);
	$StudentId = implode(",", $arrStudentId);

	//For Rank Update
	$objStudent = new clsStudent();
	$objStudent->rankId = $rankId;
	$objStudent->UpdateStudentRank($StudentId);
	unset($objStudent);

	if ($rankId > 0) {
		//Audit Log Start
		// Instantiate the Logger class
		$objLog = new clsLogger();

		// Determine the action type (EDIT or ADD) based on the presence of a journal ID
		$action = $objLog::PROMOTION;
		$userType = $objLog::ADMIN; // User type is set to ADMIN

		$objStudentRankMaster = new clsStudentRankMaster();
		$objStudentRankMaster->saveRankAuditLog($rankId, $_SESSION["loggedUserId"], $userType, $action, $IsMobile = 0);
		unset($objStudentRankMaster);
		unset($objLog);
		//Audit Log End

		header('location:schoolstudents.html?status=Updated');
	} else {
		header('location:schoolstudents.html?status=error');
	}
} {
	header('location:schoolstudents.html');
	exit();
}
