<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include('../class/clsStudent.php');
include('../class/clsSystemUserRolePermission.php');

$studentSearch = '';
$userPermissions = '';
$schoolsArray = $settingsArray = $headersArray = '';
$schools = $settings = $headers = '';
$studentSearch = isset($_GET['studentSearch']) ? $_GET['studentSearch'] : '';

$objStudent = new clsStudent();
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSearch'])) {
    $studentSearch = $_POST['studentSearch'];
    $studentSearch = formatPhoneNumberForSearch($studentSearch);

    // Check if the user's role ID is available in the session
    $loggedUserRoleId = $_SESSION['loggedUserRoleId'];

    // Initialize a new instance of the clsSystemUserRolePermission class
    $objSystemUserRolePermission = new clsSystemUserRolePermission();

    // Get system user role permissions based on the user's role ID
    $userPermissions = $objSystemUserRolePermission->GetSystemUserRolePermissions($loggedUserRoleId);

    // Check if any user permissions were returned
    if ($userPermissions != '') {
        // Extract the user permissions for schools, headers, and settings
        $schools = $userPermissions['schools'];
        $headers = $userPermissions['headers'];
        $settings = $userPermissions['settings'];

        // Convert the user permissions for schools, headers, and settings into arrays
        $schoolsArray = explode(",", $schools);
        $headersArray = explode(",", $headers);
        $settingsArray = explode(",", $settings);
    }

    $loggedUserId = $_SESSION["loggedUserId"];
    $objDB = new clsDB();
    $isPrimaryUser = 0;
    $isPrimaryUser = $objDB->GetSingleColumnValueFromTable('systemusermaster', 'isPrimaryUser', 'systemUserMasterId', $loggedUserId);
    unset($objDB);
    //Get All Student List
    if ($isPrimaryUser)
        $rowsStudentData = $objStudent->GetStudentDetailForSuperAdmin($studentSearch);
    else
        $rowsStudentData = $objStudent->GetStudentDetailForSuperAdminBySchoolIds($studentSearch, $schools);

    $totalStudentCount = 0;
    if ($rowsStudentData != '') {
        $totalStudentCount = mysqli_num_rows($rowsStudentData);
    }
}

if (isset($_GET['studentSearch'])) {
    //Get All Student List
    $rowsStudentData = $objStudent->GetStudentDetailForSuperAdmin($studentSearch);
    $totalStudentCount = 0;
    if ($rowsStudentData != '') {
        $totalStudentCount = mysqli_num_rows($rowsStudentData);
    }
}

unset($objStudent);





?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Student List</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active">Student</li>
                </ol>
            </div>
        </div>
    </div>

    <div class="container">
        <?php if (isset($_GET["status"])) {
            if ($_GET["status"] == "copy") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Student Copy successfully.
                </div>
        <?php
            }
        }
        ?>
        <div id="divTopLoading">Loading...</div>
        <div class="row">
            <form name="studentList" id="studentList" method="POST" action="studentListForSuperAdmin.html">
                <div class="form-group col-md-1 control-label">
                    <label class="pull-right" style="margin-top:10px">Search :</label>
                </div>
                <div class="col-md-4">
                    <input type="text" class="form-control" placeholder="Search by Name, UserName, Email, Phone Number" name="studentSearch" id="studentSearch" value="<?php echo $studentSearch; ?>">
                </div>
                <div class="col-md-2">
                    <div class="form-group">
                        <button id="btnSearch" name="btnSearch" class="btn btn-success">Go</button>
                    </div>
                </div>
            </form>
        </div>
        <?php if ($studentSearch != '') { ?>
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>School Name</th>
                        <th>Login Info</th>
                        <th>Contact Info</th>
                        <th style="text-align:center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($totalStudentCount > 0) {
                        while ($row = mysqli_fetch_array($rowsStudentData)) {
                            $schoolId = $row['schoolId'];
                            $studentId = $row['studentId'];
                            $firstName = $row['firstName'];
                            $lastName = $row['lastName'];
                            $fullName = $firstName . ' ' . $lastName;
                            $username = $row['username'];
                            $phone = $row['phone'];
                            $cellPhone = $row['cellPhone'];
                            $title = $row['title'];
                            $email = $row['email'];
                            $schoolName = $row['schoolName'];
                            $slug = $row['slug'];

                    ?>
                            <tr>
                                <td><?php echo $fullName; ?></td>
                                <td><?php echo $schoolName; ?></td>
                                <td>Login URL: https://rad.clinicaltrac.net/school/<?php echo $slug; ?>/student/index.html <br>
                                    Username: <?php echo $username; ?> <br>
                                    Role: <?php echo $title; ?> </td>
                                <td>Email: <?php echo $email; ?> <br>
                                    Phone: <?php echo $phone; ?> <br>
                                    <?php if ($cellPhone > 0) { ?>
                                        Cell Phone: <?php echo $cellPhone; ?>
                                    <?php } ?> </td>
                                <td style="text-align:center"> <a href="copyStudent.html?schoolId=<?php echo EncodeQueryData($schoolId); ?>&studentId=<?php echo EncodeQueryData($studentId); ?>&studentSearch=<?php echo $studentSearch; ?>" class="addCopyPopup" data-organizationid="" oncontextmenu="return false">Copy</a>
                                </td>
                            </tr>
                        <?php
                        }
                    } else { ?>
                        <tr>
                            <td colspan="4" align="center">No record(s) available</td>
                        <tr>
                        <?php } ?>
                </tbody>
            </table>
        <?php } ?>

    </div>


    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";
        $(document).ready(function() {
        var current_datatable = $("#datatable-responsive").DataTable({  
            pageLength: 100
        });
    });


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });

        $('.addCopyPopup').magnificPopup({
            'type': 'ajax',

            'closeOnBgClick': false
        });

        $(".select2_single").select2();
        $('#select2-copySchool-container').addClass('required-select2');
        $('#form-control step2 input-md select2_single').addClass('required-select2');

        $.magnificPopup.instance._onFocusIn = function(e) {
            // Do nothing if target element is select2 input
            if ($(e.target).hasClass('select2-input')) {
                return true;
            }
            // Else call parent method
            $.magnificPopup.proto._onFocusIn.call(this, e);
        }
    </script>


</body>

</html>