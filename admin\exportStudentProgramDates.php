<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsStudent.php');	
include('../setRequest.php');

require '../vendor/autoload.php'; // PhpSpreadsheet autoload

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;

$objStudent = new clsStudent();
// Get All Student List
$rowsStudentData = $objStudent->GetAllSchoolStudentProgramDateList($currentSchoolId);
$totalStudentCount = 0;

if ($rowsStudentData != '') {
    $totalStudentCount = mysqli_num_rows($rowsStudentData);
}

$objDB = new clsDB();
$schoolName = $objDB->GetSingleColumnValueFromTable('schools', 'displayName', 'schoolId', $currentSchoolId);

unset($objStudent);

$title = "Student Program Dates List";
date_default_timezone_set('Asia/Kolkata');
$today = date('m/d/Y, H:i A');

$spreadsheet = new Spreadsheet();

// Set document properties
$spreadsheet->getProperties()
    ->setCreator('Schools')
    ->setLastModifiedBy('JCC')
    ->setTitle('Reports')
    ->setSubject('Student Program Dates List')
    ->setDescription('All School Reports');

// Active Sheet
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('Program Dates List');

// Print Heading
$headerStyleArray = [
    'font' => ['bold' => true, 'size' => 16],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E0E0E0']],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
];

$sheet->mergeCells("B2:K2");
$sheet->setCellValue('B2', $title);
$sheet->getStyle('B2:K2')->applyFromArray($headerStyleArray);

// School Name
$sheet->mergeCells("B4:K4");
$sheet->setCellValue('B4', $schoolName);
$sheet->getStyle('B4:K4')->applyFromArray($headerStyleArray);

// Table Heading
$tableHeaderStyleArray = [
    'font' => ['bold' => true, 'size' => 10],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E0E0E0']],
];

$columns = [
    'B6' => 'Enrollment Date',
    'C6' => 'On Time Grad Date',
    'D6' => 'Actual Grad Date',
    'E6' => 'TMC Completion Date',
    'F6' => 'CSE Completion Date',
    'G6' => 'Program Drop Date',
    'H6' => 'Name',
    'I6' => 'Rank',
    'J6' => 'Email',
    'K6' => 'Created Date',
];

foreach ($columns as $cell => $value) {
    $sheet->setCellValue($cell, $value);
    $sheet->getStyle($cell)->applyFromArray($tableHeaderStyleArray);
}

// Populate Data
$printStartRowCounter = 7;
$rowStyleArray = [
    'font' => ['size' => 10],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
];

if ($totalStudentCount) {
    while ($row = mysqli_fetch_array($rowsStudentData)) {
        $programEnrollmentDate = $row['programEnrollmentDate'] ? date("m/d/Y", strtotime($row['programEnrollmentDate'])) : '';
        $ontimeGraduationDate = $row['ontimeGreduationDate'] ? date("m/d/Y", strtotime($row['ontimeGreduationDate'])) : '';
        $actualGraduationDate = $row['actualGraduationDate'] ? date("m/d/Y", strtotime($row['actualGraduationDate'])) : '';
        $crtCompletionDate = $row['crtCompletionDate'] ? date("m/d/Y", strtotime($row['crtCompletionDate'])) : '';
        $wrrtCompletionDate = $row['wrrtCompletionDate'] ? date("m/d/Y", strtotime($row['wrrtCompletionDate'])) : '';
        $programDropDate = $row['programDropDate'] ? date("m/d/Y", strtotime($row['programDropDate'])) : '';
        $fullName = $row['firstName'] . ' ' . $row['lastName'];
        $rank = $row['rank'];
        $email = $row['email'];
        $createdDate = date("m/d/Y", strtotime($row['createdDate']));

        $sheet->setCellValue('B' . $printStartRowCounter, $programEnrollmentDate);
        $sheet->setCellValue('C' . $printStartRowCounter, $ontimeGraduationDate);
        $sheet->setCellValue('D' . $printStartRowCounter, $actualGraduationDate);
        $sheet->setCellValue('E' . $printStartRowCounter, $crtCompletionDate);
        $sheet->setCellValue('F' . $printStartRowCounter, $wrrtCompletionDate);
        $sheet->setCellValue('G' . $printStartRowCounter, $programDropDate);
        $sheet->setCellValue('H' . $printStartRowCounter, $fullName);
        $sheet->setCellValue('I' . $printStartRowCounter, $rank);
        $sheet->setCellValue('J' . $printStartRowCounter, $email);
        $sheet->setCellValue('K' . $printStartRowCounter, $createdDate);

        $sheet->getStyle('B' . $printStartRowCounter . ':K' . $printStartRowCounter)->applyFromArray($rowStyleArray);

        $printStartRowCounter++;
    }
}

// Apply borders and auto-size columns
$sheet->getStyle('B6:K' . ($printStartRowCounter - 1))->applyFromArray(['borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]]);
foreach (range('B', 'K') as $columnID) {
    $sheet->getColumnDimension($columnID)->setAutoSize(true);
}
$sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows


// Output File
$reportName = 'StudentProgramDatesListReport_';
$fileName = $reportName . $today . '.xls';

header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="' . $fileName . '"');
header('Cache-Control: max-age=0');

$writer = new Xls($spreadsheet);
$writer->save('php://output');
?>
