<?php
require '../vendor/autoload.php'; // Ensure PhpSpreadsheet is installed via Composer

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Border;

$objAttendance = new clsAttendance();

// Get Total Hours 
$originalId = unserialize($individual_student);
$rowsTotalAttendanceHour = $objAttendance->GetStudentTotslAttendanceHoursForReport($currentSchoolId, $rotationId, $originalId, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $cbosemester, $subcborotation);

$totalAttendanceHours = mysqli_num_rows($rowsTotalAttendanceHour) ?: 0;
$arrays = [];
if ($totalAttendanceHours) {
    while ($row = mysqli_fetch_array($rowsTotalAttendanceHour)) {
        $arrays[] = $row;
    }
}

$studentCounts = array_count_values(array_column($arrays, 'studentId'));

$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set document properties
$spreadsheet->getProperties()
    ->setCreator($schoolname)
    ->setLastModifiedBy('JCC')
    ->setTitle('Reports')
    ->setSubject('School Report')
    ->setDescription('All School Reports');

// Active Sheet
$sheet->setTitle('Attendance Summary Reports');

// Header style
$headerStyleArray = [
    'font' => ['bold' => true, 'size' => 16],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
    'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER],
    'fill' => ['fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E0E0E0']]
];

// Set title and heading
$sheet->mergeCells("B2:G2")->setCellValue('B2', $schoolname)->getStyle('B2:G2')->applyFromArray($headerStyleArray);
$sheet->mergeCells("B4:G4")->setCellValue('B4', 'Attendance Reports')->getStyle('B4:G4')->applyFromArray([
    'font' => ['bold' => true, 'size' => 12],
    'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER],
    'fill' => ['fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E0E0E0']],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],

]);

// Table headers style
$tableHeaderStyle = [
    'font' => ['bold' => true, 'size' => 10],
    'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT],
    'fill' => ['fillType' => 'solid', 'startColor' => ['rgb' => 'E0E0E0']]
];

$headers = ['First Name', 'Last Name', 'Ranking', 'Rotation Name', 'Total Original Hours', 'Total Approved Hours'];
$columns = ['B', 'C', 'D', 'E', 'F', 'G'];
foreach ($headers as $index => $header) {
    $sheet->setCellValue($columns[$index] . '6', $header)->getStyle($columns[$index] . '6')->applyFromArray($tableHeaderStyle);
}

// Fill data rows
$styleArray = ['font' => ['size' => 10], 'alignment' => ['horizontal' => \PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_LEFT]];
$printStartRowCounter = 7;

foreach ($arrays as $row) {
    $studentId = $row['studentId'];
    $arrayCount = $studentCounts[$studentId];

    // Extract fields
    $firstName = stripslashes($row['firstName']);
    $lastName = stripslashes($row['lastName']);
    $rank = stripslashes($row['rank']);
    $title = stripslashes($row['title']);
    $attendanceTotalHours = formatTime($row['AttendanceTotalHours']);
    $approvedTotalHours = formatTime($row['ApprovedTotalHours']);

    $sheet->setCellValue('B' . $printStartRowCounter, $firstName)->getStyle('B' . $printStartRowCounter)->applyFromArray($styleArray);
    $sheet->setCellValue('C' . $printStartRowCounter, $lastName)->getStyle('C' . $printStartRowCounter)->applyFromArray($styleArray);
    $sheet->setCellValue('D' . $printStartRowCounter, $rank)->getStyle('D' . $printStartRowCounter)->applyFromArray($styleArray);
    $sheet->setCellValue('E' . $printStartRowCounter, $title)->getStyle('E' . $printStartRowCounter)->applyFromArray($styleArray);
    $sheet->setCellValue('F' . $printStartRowCounter, $attendanceTotalHours)->getStyle('F' . $printStartRowCounter)->applyFromArray($styleArray)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);
    $sheet->setCellValue('G' . $printStartRowCounter, $approvedTotalHours)->getStyle('G' . $printStartRowCounter)->applyFromArray($styleArray)->getAlignment()->setHorizontal(\PhpOffice\PhpSpreadsheet\Style\Alignment::HORIZONTAL_CENTER);

    $printStartRowCounter++;
}

// Apply border to all data
$sheet->getStyle('B6:G' . ($printStartRowCounter - 1))->applyFromArray(['borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]]);

// Auto size columns
foreach (range('B', 'G') as $columnID) {
    $sheet->getColumnDimension($columnID)->setAutoSize(true);
}
$sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows

$reportname = 'AttendanceReport_';

// Helper function to format time (if required)
function formatTime($time) {
    if (!$time) return '-';
    $timeParts = explode(':', $time);
    return isset($timeParts[0], $timeParts[1]) ? $timeParts[0] . ':' . $timeParts[1] : '-';
}
?>
