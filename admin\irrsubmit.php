<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsIrr.php');
	include('../class/clsSMTPSettings.php');
	include('../setRequest.php');
	include('../class/clsExternalPreceptors.php');
	
	 $irrMasterId=0;	
	//  echo '<pre>'; 
	 
	//  print_r($_POST);
	//  print_r($_GET);
	 
	// // print_r($preceptorIds);

	//  $result = array_map('array_merge', $preceptorFirstName, $preceptorLastName,$preceptorMobile);
	// print_r($result);
	//  exit;
	 if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		$rotationId=0;	
		$checkifftopicid=0;	
		 
	   if(isset($_GET['irrMasterId'])) 
	  {
		$irrMasterId = $_GET['irrMasterId'];
        $irrMasterId = DecodeQueryData($irrMasterId);
      }
	   if(isset($_GET['courseId']))
		{
		$courseId = $_GET['courseId'];
        $courseId = DecodeQueryData($courseId);
      }
	
		$irrMasterId = isset($_GET['irrMasterId']) ? DecodeQueryData($_GET['irrMasterId']) : 0;
		$status = 'added';
		// exit;

		$objIrr=new clsIrr();
		$txtTitle  = ($_POST['txtTitle']);		
		$startDate=GetDateStringInServerFormat($_POST['startDate']);
		$endDate=GetDateStringInServerFormat($_POST['endDate']);				
		$irrstudents  = ($_POST['irrstudents']);		
				
			
		if(isset($_GET['irrMasterId']))
		{
			$checkifftopicid  = ($_POST['schoolTopicId']);
		}
		else
		{
				$checkifftopicid  = ($_POST['checkifftopicid']);	
		}
		 
		
		$irrCourseTitle='';
		 $objIrr->title = $txtTitle;		 		
		 $objIrr->studentId = $irrstudents;		 		
		 $objIrr->irrCourseTitle = $irrCourseTitle;	
		 $objIrr->irrStartDate = $startDate;		 		
		 $objIrr->irrEndDate = $endDate;			
		 $objIrr->schoolTopicId = $checkifftopicid;			
		 $objIrr->schoolId = $currentSchoolId;			
		 $objIrr->createdBy = $_SESSION["loggedUserId"];			
		 $retIrrId=$objIrr->SaveIRRMaster($irrMasterId);
		 
		 if($irrMasterId)
		 {
			if(isset($_POST['clinicians_hospital']))
			{
				$saved_record_ids = array(); 

				foreach($_POST['clinicians_hospital'] as $value)
				{		
				
					$id = explode("_", $value);
					
					$clinicianId=$id[0];
					$hospitalSiteId=$id[1];	
					$isIrrDetailId=$objIrr->CheckIRRDetailsExist($irrMasterId,$hospitalSiteId,$clinicianId);
					$irrMasterId = ($isIrrDetailId > 0) ? $irrMasterId: $retIrrId;	
					$objIrr->hospitalSiteId = $hospitalSiteId;		 		
					$objIrr->clinicianId = $clinicianId;
					$objIrr->preceptorId = 0;
					$objIrr->irrMasterId = $irrMasterId;
					$retIrrDetailId=$objIrr->SaveIRRDetail($isIrrDetailId);
					if($retIrrDetailId > 0)
					{
						array_push($saved_record_ids, $retIrrDetailId);
					}
					if($isIrrDetailId > 0)
					{
						array_push($saved_record_ids, $isIrrDetailId);
					}
				}
				// print_r($saved_record_ids);
				$irrDetailIds = implode(',', $saved_record_ids);
				$objIrr->DeleteIrrDetailsById($irrMasterId,$irrDetailIds);
			}

		 }
		else
		{
			$objIrr->DeleteIrrDetails($irrMasterId);
		   if(isset($_POST['clinicians_hospital']))
		   {
			   foreach($_POST['clinicians_hospital'] as $value)
			   {		
			   
				   $id = explode("_", $value);
				   
				   $clinicianId=$id[0];
				   $hospitalSiteId=$id[1];		 		
				   $objIrr->hospitalSiteId = $hospitalSiteId;		 		
				   $objIrr->clinicianId = $clinicianId;
				   $objIrr->preceptorId = 0;
				   $objIrr->irrMasterId = $retIrrId;
				   $retIrrDetailId=$objIrr->SaveIRRDetail(0);
			   
			   }
		   }
		   
		}

		
	 	unset($objIrr);	
		 $preceptorFirstName  = isset($_POST['preceptorFirstName']) ? $_POST['preceptorFirstName'] : '';
		 $preceptorLastName  = isset($_POST['preceptorLastName']) ? $_POST['preceptorLastName'] : '';
		 $preceptorMobile  = isset($_POST['preceptorMobile']) ? $_POST['preceptorMobile'] : '';
		 $irrDetailIds  = isset($_POST['irrDetailIds']) ? $_POST['irrDetailIds'] : '';
		 $cbohospitalsites  = isset($_POST['cbohospitalsites']) ? $_POST['cbohospitalsites'] : '';
		 
 
		 $contacts = array_map(function($first, $last, $mobile,$irrDetailId,$cbohospitalSiteId) {
			 return array(
				 'firstname' => $first,
				 'lastname' => $last,
				 'mobile' => $mobile,
				 'irrDetailId' => $irrDetailId,
				 'preceptorHospitalSiteId' => $cbohospitalSiteId,
				 
			 );
		 }, $preceptorFirstName, $preceptorLastName, $preceptorMobile, $irrDetailIds,$cbohospitalsites);
		 
	//  print_r($contacts);
	//  exit;
	 $objExternalPreceptors = new clsExternalPreceptors();
	 $preceptorIds = array();
	 
	 foreach ($contacts as $subarray) {
		//  print_r($subarray);
		 $mobile = $subarray['mobile'];
		 $preceptorHospitalSiteId = $subarray['preceptorHospitalSiteId'];
		 $objExternalPreceptors->firstName = $subarray['firstname'];
		 $objExternalPreceptors->lastName = $subarray['lastname'];
		 $objExternalPreceptors->mobile_num = $subarray['mobile'];
		 if($subarray['irrDetailId'] == 0 && $mobile != '')
		 {
			$preceptorId = $objExternalPreceptors->SaveExternalPreceptor();
			$objIrr=new clsIrr();
			$objIrr->hospitalSiteId = $preceptorHospitalSiteId;		 		
			$objIrr->clinicianId = 0;
			$objIrr->preceptorId = $preceptorId;
			$objIrr->irrMasterId = $retIrrId;
			$retIrrDetailId=$objIrr->SaveIRRDetail(0);


			$isEvalTypeLabel = 'IRR Assignment';
			// http://localhost/clinicaltrac/school/dcctx/clinician/addirr.html?irrMasterId=MTMz&clinicianId=MTEyNA==&schoolTopicId=NDE5MA==&irrDetailId=MTA3Mw==&Add=MQ==	
			$URL = $dynamicOrgUrl.'/school/'.$schoolSlug.'/clinician/addirr.html?irrMasterId='.EncodeQueryData($retIrrId).'&preceptorId='.EncodeQueryData($preceptorId).'&preceptorNum='.EncodeQueryData($mobile).'&irrDetailId='.EncodeQueryData($retIrrDetailId).'&schoolTopicId='.EncodeQueryData($checkifftopicid).'&Add=MQ==';
			
			// Create TinyUrl
			$randomUrl = getTinyUrl($URL);
			$redirectUrl = $dynamicOrgUrl.'/redirect/'.EncodeQueryData($randomUrl);
			$body='Hello ,You received a '.$isEvalTypeLabel.'. Click the link below to complete. '.$redirectUrl;

			sendSMS('+1'.$mobile,$body);
		 }
		 

	 }
	 	unset($objIrr);	
		//  exit;
		if($retIrrId > 0)
		{
			header('location:irr.html?irrMasterId='.EncodeQueryData($retIrrId).'&status='.$status);	
			exit();	
		}
		else
		{
			header('location:irr.html?irrMasterId='.EncodeQueryData($retIrrId).'&status='.$status);
		}
		 
	}
	else
	{
		header('location:irr.html?irrMasterId='.EncodeQueryData($retIrrId).'&status='.$status);
		exit();
	}	
?>