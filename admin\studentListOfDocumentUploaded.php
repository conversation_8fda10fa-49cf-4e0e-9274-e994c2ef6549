<?php
    include('includes/validateUserLogin.php');
    include('../includes/config.php');
    include('../class/clsDB.php');
    include('../includes/commonfun.php');
	include('../setRequest.php');
    include_once('../class/clsSchool.php'); 
    include('../class/clsStudent.php');
    include('../class/clsStudentRankMaster.php');
    
    $studentSearch = isset($_GET['studentSearch']) ? $_GET['studentSearch'] : '';
    $rankId = isset($_GET['rankId']) ? DecodeQueryData($_GET['rankId']) : 0;

    $objStudent = new clsStudent();
	//Get All Student List
	$rowsStudentData = $objStudent->GetDocumentUploadedStudentList($currentSchoolId,$rankId);
	$totalStudentCount = 0;
	if($rowsStudentData !='')
	{
		$totalStudentCount = mysqli_num_rows($rowsStudentData);
	}
    unset($objStudent);

    //Get All Rank List
	$objStudentRankMaster = new clsStudentRankMaster();
	$ranks = $objStudentRankMaster->GetAllStudentRankBySchool($currentSchoolId);
	unset($objStudentRankMaster);

 ?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Student List</title>
    <?php include('includes/headercss.php');?>
    <?php include("includes/datatablecss.php") ?>
    <style>
       
    </style>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css"> 
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php');?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">Document Upload</li>
                </ol>
            </div>
        </div>
    </div>
          
    <div class="container">
        
        <div class="row margin_bottom_ten">
            <div class="col-md-3 pull-right">
                <div class="form-group">
                    <label class="col-md-4 control-label margin_top_ten" for="cborank">Rank</label>
                    <div class="col-md-8 padding_left_zero">
                        <select id="cborank" name="cborank" class="form-control select2_single"  style="width: 110%" >
                            <option value="" selected>Select All</option>
                            
                                <?php
                                if($ranks!="")
                                {
                                    while($row = mysqli_fetch_assoc($ranks))                                    {
                                        
                                        $selrankId  = $row['rankId'];
                                        $name  = stripslashes($row['title']);

                                        ?>
                                        <option value="<?php echo EncodeQueryData($selrankId); ?>" <?php if($rankId==$selrankId){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
                                        <?php

                                    }
                                }
                            ?>
                        </select>
                    </div>
                </div>
            </div>
        </div>
        <div id="divTopLoading" >Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Name</th>      
                    <th>Rank</th>      
                    <th style="text-align:center">Action</th>
                </tr>
            </thead>
            <tbody>
            <?php
                if($totalStudentCount > 0)
                {
                    while($row = mysqli_fetch_array($rowsStudentData))
                    {
                        $title = $row['title'];
                        $studentId = $row['studentId'];
                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $fullName = $firstName.' '.$lastName; 
                            
            		?>
                    <tr>
                        <td><?php echo $fullName ; ?></td>
                        <td><?php echo $title ; ?></td>
                        <td style="text-align:center"> <a href="studentDocuments.html?studentId=<?php echo EncodeQueryData($studentId); ?>&Type=S" class="" >View</a>
                        </td>
                    </tr>
                    <?php
                    }
                }  ?> 
            </tbody>
        </table>
       

    </div>


    <?php include('includes/footer.php');?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
        <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script> 

    <script type="text/javascript">

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function(){
           $("#divTopLoading").addClass('hide');
        });

       
        $(".select2_single").select2();
        $('#select2-copySchool-container').addClass('required-select2');
        $('#form-control step2 input-md select2_single').addClass('required-select2');
        
		var current_datatable = $("#datatable-responsive").DataTable({
			"aoColumns": [
				{"sWidth": "60%"}, 
				{"sWidth": "20%"}, 
				{ "sWidth": "20%","sClass": "alignCenter","bSortable": false
			} ]
		});

        $("#cborank").change(function(){
            var rankId = $(this).val();
            
            if(rankId)
                window.location.href = "studentListOfDocumentUploaded.html?rankId="+rankId;
            else
                window.location.href = "studentListOfDocumentUploaded.html";
            
        });
    </script>


</body>

</html>