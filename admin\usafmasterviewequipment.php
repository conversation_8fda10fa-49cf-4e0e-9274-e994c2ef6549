<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');      
    include('../class/clsusafEquiment.php');      
    include('../setRequest.php'); 	
	
	$sectionId=0;
	$defaultTopicId=0;
	$objusafEquiment = new clsusafEquiment();
	$rowsEquipmentList=$objusafEquiment->GetAlldefaultusafequipmentlist();
	$totalCount = 0;
	if($rowsEquipmentList !='')
	{
		$totalCount = mysqli_num_rows($rowsEquipmentList);
    }	
    
  

	
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Military Equipment List</title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>
						<li><a href="settings.html">Setting	</a></li>
                        <li class="active">Military Equipment List</li>
                    </ol>
                </div>  
                <div class="pull-right">
                    <ol class="breadcrumb">
                        <a href="usafmasteraddequipment.html">Add</a> 
                    </ol>
                </div>           
            </div>
        </div>

        <div class="container">
        <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Equipment added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Equipment updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Deleted")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Equipment deleted successfully.
                </div>
                <?php 
					}
                     else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>
            <div id="divTopLoading" >Loading...</div>
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th style="text-align:center">Item</th>                                                   
                        <th>Title</th>                                                   
                        <th style="text-align: center">Action</th>                                                   
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if($totalCount > 0)
                    {
                        while($row = mysqli_fetch_array($rowsEquipmentList))
                        {							;	
                                            
                            $title = stripslashes($row['title']);
                            $equipmentId =($row['equipmentId']);
                            
                            $totalEquomentAssign=0;
                            $equomentAssign=$objusafEquiment->GetUsafEquipment($equipmentId);
                            if($equomentAssign !='')
                            {
                                $totalEquomentAssign=mysqli_num_rows($equomentAssign);
                            }
                            ?>
                        <tr>
                            
                            <td style="text-align:center"><?php echo ($equipmentId); ?></td>
                            <td><?php echo($title); ?> </td>
                            <td style="text-align: center">
                            <a href="usafmasteraddequipment.html?equipmentId=<?php echo $equipmentId;  ?>" >Edit </a>|
                            <?php if($totalEquomentAssign > 0)
                                { ?>
                                    <a id="warningAjax" class="text-muted" href="javascript:void(0);" equipmentId="<?php echo ($equipmentId); ?>" title="<?php echo($title); ?>"  >Delete</a>
                                <?php } 
                                else  { ?>
                                <a href="javascript:void(0);" class="deleteAjaxRow" equipmentId="<?php echo EncodeQueryData($equipmentId); ?>" title="<?php echo($title); ?>">Delete</a>		
                            <?php } ?>
                            </td>
                        </tr>
                        <?php
                        }
                    }
                    unset($objusafEquiment);
                ?>
                </tbody>
            </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
		
     
	
        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";        
               
			$(window).load(function(){
                $("#divTopLoading").addClass('hide');				
				  });	
                 var current_datatable = $("#datatable-responsive").DataTable({
					 "aoColumns": [{
                    "sWidth": "10%"
                },{
                    "sWidth": "50%"
				  },{
                    "sWidth": "15%",
					"bSortable": false					
                }]
				 }); 
		
		
			
			
				$(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var equipmentId = $(this).attr('equipmentId');
                var title = $(this).attr('title');
                
                alertify.confirm('Question Name: '+title, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: equipmentId,
                            type: 'EquipmentMaster'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });
			
			
			$(document).on('click', '#warningAjax', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var equipmentId = $(this).attr('equipmentId');
                var title = $(this).attr('title');
                
                alertify.confirm('Warning!', 'This Equipment already assigned, you cant delete it!', function(){
				 }, function() {});
            });
        </script>
    </body>
    </html>