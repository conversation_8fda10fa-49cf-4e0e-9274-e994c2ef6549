<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsCIevaluation.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsPerformance.php');
include('../class/clsExternalPreceptors.php');

$performanceRotationId = 0;
$schoolId = 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$isDefaultCiEval = '';
// $loggedUserId = $_SESSION["loggedUserId"];   
// $clinicianId = $_SESSION['loggedClinicianId'];
// $loggedClinicianType = $_SESSION['loggedClinicianType'];
$isActiveCheckoff = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$isDefaultCiEval = $_SESSION["isDefaultCiEval"];
if (isset($_GET['performanceRotationId'])) //Edit Mode
{
    $performanceRotationId = $_GET['performanceRotationId'];
    $performanceRotationId = DecodeQueryData($performanceRotationId);
} else {
    $transchooldisplayName = $currenschoolDisplayname;
}

$studentId = isset($_GET['studentId']) ? DecodeQueryData($_GET['studentId']) : 0;
$title = "Performance Evaluation| " . $transchooldisplayName;

//For CI Evalution
$objPerformance = new clsPerformance();
$getEvaluationdetails = $objPerformance->GetAllPerformanceEvaluation($schoolId, $performanceRotationId, $studentId);

$totalEvaluationCount = 0;
if ($getEvaluationdetails != '') {
    $totalEvaluationCount = mysqli_num_rows($getEvaluationdetails);
}

//For Rotation Name 
$objRotation = new clsRotation();
$RotationName = $objRotation->GetrotationDetails($performanceRotationId, $schoolId);
$rotationtitle = $RotationName['title'] ?? '';

$objDB = new clsDB();
$getTimezoneId = $objDB->GetSingleColumnValueFromTable('schools', 'timeZoneId', 'schoolId', $currentSchoolId);
$TimeZone = $objDB->GetSingleColumnValueFromTable('timezonemaster', 'timezone', 'timeZoneId', $getTimezoneId);
unset($objDB);

$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $studentId);
$studentfullname = $Rowstudent ? $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']:'';
unset($objStudent);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    <style>
        .form-control {
            height: 45px;
        }

        div.dataTables_wrapper div.dataTables_length select {
            height: 45px;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if (isset($_GET['studentId'])) { ?> <li><a href="clinical.html">Clinical</a></li>
                        <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>

                    <?php } else { ?>
                        <li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                    <?php } ?>
                    <li class="active">Performance Evaluation</li>

                </ol>
            </div>
            <?php //if ( $rotationStatus == 0) { 
            ?>
            <!-- <div class="pull-right">
                <a class="btn btn-link" href="performanceEval.html?performanceRotationId=<?php echo EncodeQueryData($performanceRotationId); ?>">Add</a>
            </div> -->
            <?php //} 
            ?>

        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Performance Evaluation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Performance Evaluation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Performance Evaluation deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>

                    <th>Evaluation Date</th>
                    <th>Term</th>
                    <th>Student</th>
                    <th>Technologist <br>Score</th>
                    <th>Evaluator <br>Score</th>
                    <th>Total <br>Score</th>
                    <th>Percentage</th>
                    <th>Technologist <br>Details</th>
                    <th>Technologist <br> Signature Date</th>
                    <th>Evaluator <br> Signature Date</th>
                    <th>Student <br> Signature Date</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // echo '<pre>';
                if ($totalEvaluationCount > 0) {
                    while ($row = mysqli_fetch_array($getEvaluationdetails)) {

                        $performanceEvaluationMasterId = $row['performanceEvaluationMasterId'];
                        $clinicianName = $row['clinicianName'];
                        $studentName = $row['studentName'];
                        $rotationId = $row['rotationId'];
                        $rotationName = $row['rotationName'];
                        // $totalPoints = ($row['totalPoints'] == 0) ? '' : $row['totalPoints'];
                        // $percentage = ($row['percentage'] == 0) ? '' : $row['percentage'];
                        $weeks = $row['weeks'];
                        // $technologistScore =  $row['technologistScore'];
                        // $evaluatorScore =  $row['evaluatorScore'];
                        $preceptorId = $row['preceptorId'];
                        $isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];

                        if ($preceptorId > 0) {
                            $objExternalPreceptors = new clsExternalPreceptors();
                            $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
                            $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                            $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                            $preceptorNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
                            $maskedPreceptorNum = maskNumber($preceptorNum);
                            $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                        }

                        $evaluationDate = isset($row['evaluationDate']) ? stripslashes($row['evaluationDate']) : '';
                        if ($evaluationDate != '' && $evaluationDate != '0000-00-00 00:00:00') {
                            $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
                            $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                        } else
                            $evaluationDate = '-';

                        $evaluatorSignatureDate = isset($row['evaluatorSignatureDate']) ? stripslashes($row['evaluatorSignatureDate']) : '';
                        if ($evaluatorSignatureDate != '' && $evaluatorSignatureDate != '0000-00-00 00:00:00') {
                            $evaluatorSignatureDate = converFromServerTimeZone($evaluatorSignatureDate, $TimeZone);
                            $evaluatorSignatureDate = date("m/d/Y", strtotime($evaluatorSignatureDate));
                        } else
                            $evaluatorSignatureDate = '-';

                        $studentSignatureDate = isset($row['studentSignatureDate']) ? stripslashes($row['studentSignatureDate']) : '';
                        if ($studentSignatureDate != '' && $studentSignatureDate != '0000-00-00 00:00:00') {
                            $studentSignatureDate = converFromServerTimeZone($studentSignatureDate, $TimeZone);
                            $studentSignatureDate = date("m/d/Y", strtotime($studentSignatureDate));
                        } else
                            $studentSignatureDate = '-';

                        $preceptorSignatureDate = isset($row['preceptorSignatureDate']) ? stripslashes($row['preceptorSignatureDate']) : '';
                        if ($preceptorSignatureDate != '' && $preceptorSignatureDate != '0000-00-00 00:00:00') {
                            $preceptorSignatureDate = converFromServerTimeZone($preceptorSignatureDate, $TimeZone);
                            $preceptorSignatureDate = date("m/d/Y", strtotime($preceptorSignatureDate));
                        } else
                            $preceptorSignatureDate = '-';

                        if ($preceptorSignatureDate != '-') {
                            $technologistScore =  $row['technologistScore'];
                        } else {
                            $technologistScore = '';
                        }

                        if ($evaluatorSignatureDate != '-') {
                            $evaluatorScore = $row['evaluatorScore'];
                        } else {
                            $evaluatorScore = '';
                        }

                        if ($preceptorSignatureDate != '-' && $evaluatorSignatureDate != '-') {
                            $totalPoints = $row['totalPoints'];
                            $percentage = $row['percentage'];
                        } else {
                            $totalPoints = '';
                            $percentage = '';
                        }

                ?>
                        <tr>

                            <td><?php echo ($evaluationDate); ?></td>
                            <td><?php echo ($rotationName); ?></td>
                            <td><?php echo ($studentName); ?></td>
                            <td style="text-align: center;"><?php echo ($technologistScore); ?></td>
                            <td style="text-align: center;"><?php echo ($evaluatorScore); ?></td>
                            <td style="text-align: center"><?php echo ($totalPoints); ?></td>
                            <td style="text-align: center"><?php echo ($percentage); ?></td>
                            <td><?php
                                // if ($preceptorId > 0) {

                                //  echo ($preceptorId); 
                                $preceptorInfo = 'Name: ' . $preceptorFullName . '</br>Phone: ' . $maskedPreceptorNum;
                                if ($isPreceptorCompletedStatus == 1 && $preceptorSignatureDate != '-')
                                    $preceptorInfo .= '</br>Status: Completed';
                                else

                                    $preceptorInfo .= '</br>Status: Pending';

                                echo $preceptorInfo;
                                // echo $preceptorId;
                                ?>
                            </td>
                            <td><?php echo ($preceptorSignatureDate); ?></td>
                            <td><?php echo ($evaluatorSignatureDate); ?></td>
                            <td><?php echo ($studentSignatureDate); ?></td>

                            <td style="text-align: center">
                                <a href="performanceEval.html?performanceEvaluationMasterId=<?php echo (EncodeQueryData($performanceEvaluationMasterId)); ?>
									&performanceRotationId=<?php echo (EncodeQueryData($performanceRotationId)); ?>&studentId=<?php echo EncodeQueryData($studentId); ?>&view=V">View</a>
                                <?php if (isset($_SESSION["loggedAsBackUserId"])) { ?>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow" performanceEvaluationMasterId="<?php echo EncodeQueryData($performanceEvaluationMasterId); ?>">Delete</a>
                                <?php } ?>

                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objRotation);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');

        });

        var current_datatable = $("#datatable-responsive").DataTable({
            responsive: false,
            scrollX: false,
            "sScrollX": true,
            "ordering": true,
            "order": [
                [0, "desc"]
            ],

            "aoColumns": [{
                    "sWidth": "20%",
                    "sClass": "alignCenter"

                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
                {
                    "sWidth": "20%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
                {
                    "sWidth": "20%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
                {
                    "sWidth": "20%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
                {
                    "sWidth": "20%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }
            ]
        });

        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var performanceEvaluationMasterId = $(this).attr('performanceEvaluationMasterId');


            alertify.confirm('Performance Evaluation ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: performanceEvaluationMasterId,
                        type: 'PerfEval'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });
    </script>


</body>

</html>