<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsStudent.php');
include('../class/clsJuniorMidtermPerformanceEval.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../setRequest.php');
include('../class/clsExternalPreceptors.php');
// echo '<pre>';
// print_r($_SESSION);
$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$isSuperAdmin = $_SESSION["isSuperAdmin"];
$isActiveCheckoff = '';
$isDefaultCiEval = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$isDefaultCiEval = $_SESSION["isDefaultCiEval"];
$loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$rotationId = 0;
$studentId = isset($_GET['studentId']) ? DecodeQueryData($_GET['studentId']) : 0;

$canvasStatus = isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';
$type = isset($_GET['type']) ? $_GET['type'] : '';
if ($type == 'canvas')
    $canvasStatus = 1;

//For Rotation Site
if (isset($_GET['jrmidtermrotationid'])) {
    $rotationId = $_GET['jrmidtermrotationid'];
    $rotationId = DecodeQueryData($rotationId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

if (isset($_GET['Type'])) {
    $Type = ($_GET['Type']);
}

if (isset($_GET['studentId'])) {
	$studentId = DecodeQueryData($_GET['studentId']);
}

// if (isset($_GET['courseId'])) {
//     $courseId = DecodeQueryData($_GET['courseId']);
// }

$title = "Jr Professional Evaluation |" . $transchooldisplayName;

//For Daily Weekly List
$objJrMidtermEval = new clsJuniorMidtermPerformanceEval();

$getJPEdetails = $objJrMidtermEval->GetAllJrMidTermEvaluationList($currentSchoolId, $rotationId,  $studentId);

$totalDailyCount = 0;
if ($getJPEdetails != '') {
    $totalDailyCount = mysqli_num_rows($getJPEdetails);
}
unset($objJrMidtermEval);

//For Rotation Name
$objRotation = new clsRotation();
if ($rotationId != '') {
    $rotationtitle = $objRotation->GetrotationTitle($rotationId, $schoolId);
}

$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $studentId);
$studentfullname = $Rowstudent ? ($Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']) : '';
unset($objStudent);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link href="https://cdn.datatables.net/fixedcolumns/3.2.4/css/fixedColumns.bootstrap4.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">


    <style>
        .DTFC_LeftBodyLiner {
            overflow-y: unset !important
        }

        .DTFC_RightBodyLiner {
            overflow-y: unset !important
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #2e3192; */
            /* color: #fff; */
            color: #555;
            background: #f6f6ff;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #2e3192; */
            /* color: #fff; */
            color: #555;
            background: #f6f6ff;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f6ff !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        .input-group {
            width: 100%;
        }

        div.dataTables_wrapper div.dataTables_length select {
            height: 45px;
        }

        /* .form-horizontal .form-group {
                margin-right: 0;
                margin-left: 0;
            } */

        .alertify .ajs-header {
            border-bottom: 1px solid #e5e5e5 !important;
        }

        .alertify .ajs-footer{
            border-top: 1px solid #e5e5e5 !important; 
        }

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            /* .container-zero{
                    padding: 0;
                } */

            div.dataTables_wrapper div.dataTables_length {
                text-align: left !important;
                margin-top: 10px;
            }
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if (isset($_GET['studentId'])) { ?> <li><a href="clinical.html">Clinical</a></li>
                        <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>

                    <?php }

                    if ($rotationId != '') { ?>
                        <li><a href="rotations.html">Rotations</a></li>
                        <?php if ($rotationtitle != '') { ?>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                        <?php } ?>
                    <?php } ?>
                    <li class="active">Jr Professional Evaluation</li>

                </ol>
            </div>
        </div>
    </div>

    <div class="custom-container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Jr Professional Evaluation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Jr Professional Evaluation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Jr Professional Evaluation deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Rotation</th>
                    <th style="text-align: center">Evaluation<br>Date</th>
                    <th style="text-align: center">Student<br>Sign<br>Date</th>
                    <th style="text-align: center">Technologist<br>Sign<br>Date</th>
                    <th style="text-align: center">Meets <br>Expectations</th>
                    <th style="text-align: center">Needs Minor <br>Improvement</th>
                    <th style="text-align: center">Needs Major<br> Improvement</th>
                    <th style="text-align: center">Failing</th>
                    <th style="text-align: center">Total<br>Average</th>
                    <th style="text-align: center">Total<br>Percentage</th>
                    <th style="text-align: center">Comment</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalDailyCount > 0) {
                    while ($row = mysqli_fetch_array($getJPEdetails)) {

                        //$title = $row['title'];
                        $rotationame = $row['title'];
                        // $Ranktitle = $row['Ranktitle'];
                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $studentName = $firstName . ' ' . $lastName;
                        $studentJrMidtermEvalId = $row['studentJrMidtermEvalId'];
                        $evaluationDate = stripslashes($row['evaluationDate']);
                        $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                        $dateOfStudentSignature = stripslashes($row['dateOfStudentSignature']);
                        // $courselocationId = $row['locationId'];
                        $rotationId = $row['rotationId'];
                        // $parentRotationId = stripslashes($row['parentRotationId']);
                        // $rotationLocationId = stripslashes($row['rotationLocationId']);
                        $studentcomments = $row['studentcomments'];

                        $summarycomments = $row['summarycomments'];
                        $comment = ($summarycomments != '') ? 'Yes' : 'No';


                        //Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        // $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);
                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedStudentSchoolTimeZone"];

                        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                        $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                        $dateOfPreceptorSignature = stripslashes($row['dateOfPreceptorSignature']);
                        $dateOfPreceptorSignature = converFromServerTimeZone($dateOfPreceptorSignature, $TimeZone);
                        $dateOfPreceptorSignature = date("m/d/Y", strtotime($dateOfPreceptorSignature));


                        $preceptorsignature = $row['preceptorsignature'];
                        $isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];

                        if ($preceptorsignature > 0) {
                            $objExternalPreceptors = new clsExternalPreceptors();
                            $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorsignature);
                            $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                            $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                            $preceptorNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
                            $maskedPreceptorNum = maskNumber($preceptorNum);
                            $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                            // $isPreceptorCompletedStatus = isset($externalPreceptorDetail['isPreceptorCompletedStatus']) ? $externalPreceptorDetail['isPreceptorCompletedStatus'] : '';
                        }




                        //For Avg
                        $firstSectionAvg = $row['firstSectionAvg'];
                        $secondSectionAvg = $row['secondSectionAvg'];
                        $thirdSectionAvg = $row['thirdSectionAvg'];
                        $fourthSectionAvg = $row['fourthSectionAvg'];

                        $totalAvg = $row['totalAvg'];
                        $totalPercentage = $row['totalPercentage'];

                ?>
                        <tr>
                            <td><?php echo ($rotationame); ?></td>
                            <td style="text-align: center"><?php echo ($evaluationDate); ?></td>
                            <td style="text-align: center"><?php
                                                            if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {

                                                                echo ($dateOfStudentSignature);
                                                            } else {
                                                                echo "-";
                                                            }
                                                            ?></td>
                            <td>
                                <?php
                                if ($preceptorsignature > 0) {

                                    $preceptorsInfo = 'Name: ' . $preceptorFullName . '</br>Phone: ' . $maskedPreceptorNum;

                                    if ($isPreceptorCompletedStatus)
                                        $preceptorsInfo .= '</br>Status: Completed';
                                    else

                                        $preceptorsInfo .= '</br>Status: Pending';

                                    if ($dateOfPreceptorSignature != '' && $dateOfPreceptorSignature != '12/31/1969' && $dateOfPreceptorSignature != '01/01/1970' && $dateOfPreceptorSignature != '0000-00-00')
                                        $preceptorsInfo .= '</br>Date: ' . $dateOfPreceptorSignature;

                                    echo $preceptorsInfo;

                                    if ($isPreceptorCompletedStatus == 0) { ?>
                                        <br> <!--<a href="javascript:void(0);" class="reSendrequest" data-toggle="modal" data-target="#resendModal" preceptorId="<?php echo $preceptorsignature; ?>" preceptorNum="<?php echo $preceptorNum; ?>" evaluationId="<?php echo $studentJrMidtermEvalId; ?>" evaluationType='JPE' rotationId="<?php echo $rotationId; ?>" onclick="ShowEvaluationDetails(this)">Resend SMS</a> -->
                                        <a href="javascript:void(0);" class="copyLink" preceptorId=<?php echo EncodeQueryData($preceptorsignature); ?> preceptornum="<?php echo $preceptorNum; ?>" evaluationId="<?php echo $studentJrMidtermEvalId; ?>" rotationId="<?php echo ($rotationId); ?>" evaluationType='JPE' onclick="copyLinkUrl(this)">Click to Copy URL</a>
                                        <!-- <br> <a href="javascript:void(0);" class="reSendEmailrequest" data-toggle="modal" preceptorId="<?php echo EncodeQueryData($preceptorsignature); ?>" preceptorNum="<?php echo $preceptorNum; ?>" evaluationId="<?php echo $studentJrMidtermEvalId; ?>" rotationId="<?php echo $rotationId; ?>" data-target="#resendEmailModal" email="<?php echo $loggedStudentEmailId; ?>" evaluationType='JPE' onclick="ShowEvaluationDetailsForEmail(this)">Send URL to Email</a> -->
                                <?php  }
                                } else {
                                    echo "-";
                                }
                                ?>
                            </td>
                            <td style="text-align: center"><?php echo round($firstSectionAvg); ?></td>
                            <td style="text-align: center"><?php echo round($secondSectionAvg); ?></td>
                            <td style="text-align: center"><?php echo round($thirdSectionAvg); ?></td>
                            <td style="text-align: center"><?php echo round($fourthSectionAvg); ?></td>

                            <td style="text-align: center"><?php echo round($totalAvg); ?></td>
                            <td style="text-align: center"><?php echo  round($totalPercentage); ?></td>
                            <td style="text-align: center"><?php echo ($comment); ?></td>
                            <td style="text-align: center">
                                <?php if (isset($_GET['studentId'])) { ?>
                                    <a href="addMidtermperformance.html?studentJrMidtermEvalId=<?php echo (EncodeQueryData($studentJrMidtermEvalId)); ?>&studentId=<?php echo (EncodeQueryData($studentId)); ?>&view=V">View</a>
                                <?php } else { ?>
                                    <a href="addMidtermperformance.html?studentJrMidtermEvalId=<?php echo (EncodeQueryData($studentJrMidtermEvalId)); ?>&view=V">View</a>
                                <?php } ?>
                                <?php if (isset($_SESSION["loggedAsBackUserId"])) { ?>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow" studentJrMidtermEvalId="<?php echo EncodeQueryData($studentJrMidtermEvalId); ?>">Delete</a>
                                <?php } ?>

                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objRotation);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="https://cdn.datatables.net/fixedcolumns/3.2.4/js/dataTables.fixedColumns.min.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });
        $(".select2_single").select2();

        var current_datatable = $("#datatable-responsive").DataTable({

            responsive: false,
            scrollX: false,
            "sScrollX": true,
            "ordering": true,
            "order": [
                [1, "desc"]
            ],
            "aoColumns": [{
                    "sWidth": "25%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "25%"
                }, {
                    "sWidth": "25%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "25%"
                }, {
                    "sWidth": "25%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "25%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter"
                },
                {
                    "sWidth": "15%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }
            ]
        });


        $("#cboCourse").change(function() {
            var courseId = $(this).val();
            if (courseId) {
                window.location.href = "masteryList.html?courseId=" + courseId;
            } else {
                window.location.href = "masteryList.html";
            }
        });

        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentJrMidtermEvalId = $(this).attr('studentJrMidtermEvalId');
            var userId='<?php  echo EncodeQueryData($loggedUserId)?>';
            var isUser=1;

            alertify.confirm('JPE ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentJrMidtermEvalId,
                        userId:userId,
                        isUser:isUser,
                        type: 'JPE'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });
    </script>


</body>

</html>