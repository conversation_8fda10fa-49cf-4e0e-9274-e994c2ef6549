<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsFloorTherapyAndICUEvaluation.php');
include('../class/clsRotation.php');
include('../class/clsCourses.php');
include('../class/clsExternalPreceptors.php');

$rotationId = 0;
$schoolId = 0;
$currentstudentId = 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$rotationId = 0;
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0;
$canvasStatus = isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';

//For Check Checkoff 
$isActiveCheckoff = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];

$activebtn = '';
$clsBtnActiveFloor = '';
$clsBtnActiveAdult = '';
$clsBtnActive = '';
if (isset($_GET['active']))
    $activebtn = $_GET['active'];

if ($activebtn == 'floor')
    $clsBtnActiveFloor = "active";
elseif ($activebtn == 'icu')
    $clsBtnActiveAdult = "active";
else
    $clsBtnActive = "active";

$courseId = 0;
//For Rotation 
$encodedRotationId = '';
$encodedStudentId = '';
$isdefaultFloorAndIcuEval = 0;
if (isset($_GET['rotationId'])) {
    $encodedRotationId = $_GET['rotationId'];
    $rotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($rotationId);
} elseif (isset($_GET['studentId'])) {
    $encodedStudentId = $_GET['studentId'];
    $currentstudentId = $_GET['studentId'];
    $currentstudentId = DecodeQueryData($currentstudentId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

$directionType = isset($_GET['type']) ? $_GET['type'] : '';
if ($directionType == 'canvas')
    $canvasStatus = 1;

$title = "Floor Therapy And ICU Evaluation |" . $transchooldisplayName;

$objDB = new clsDB();
$isdefaultFloorAndIcuEval = $objDB->GetSingleColumnValueFromTable('schools', 'isdefaultFloorAndIcuEval', 'schoolId', $currentSchoolId);
unset($objDB);

//For Case Study List
$objEval = new clsFloorTherapyAndICUEvaluation();
if ($activebtn == 'floor')
    $getEvaldetails = $objEval->GetAllEval($schoolId, $rotationId, $currentstudentId, 'floor', '', '', 0, $isdefaultFloorAndIcuEval);
elseif ($activebtn == 'icu')
    $getEvaldetails = $objEval->GetAllEval($schoolId, $rotationId, $currentstudentId, 'icu');
else
    $getEvaldetails = $objEval->GetAllEval($schoolId, $rotationId, $currentstudentId, '', '', '', 0, $isdefaultFloorAndIcuEval);

$totalPEF = 0;
if ($getEvaldetails != '') {
    $totalPEF = mysqli_num_rows($getEvaldetails);
}
unset($objEval);

//For Student Name
$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname = $Rowstudent ? ($Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']) : '';
unset($objStudent);

//For Rotation Name
$objRotation = new clsRotation();
$RotationName = $objRotation->GetrotationDetails($rotationId, $schoolId);
$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] : '';
unset($objRotation);




?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
</head>

<body>
    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <?php if ($directionType == 'canvas') { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Floor Therapy And ICU Evaluation</li>
                    <?php } else { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <?php if ($rotationId != '') { ?>
                            <li><a href="rotations.html">Rotations</a></li>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                        <?php } else if ($currentstudentId > 0 && $isActiveCheckoff != 2) { ?>
                            <li><a href="clinical.html">clinical</a></li>
                            <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                        <?php } ?>
                        <li class="active">Floor Therapy And ICU Evaluation</li>
                    <?php } ?>
                </ol>
            </div>
        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Floor Therapy And ICU Evaluation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Floor Therapy And ICU Evaluation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Floor Therapy And ICU deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <?php if ($directionType != 'canvas') { ?>
            <div class="row margin_bottom_ten margin_right_zero">
                <div class="btn-group pull-right" role="group" aria-label="First group">
                    <?php if ($rotationId > 0) { ?>
                        <a role="button" class="btn btn-primary <?php echo $clsBtnActive; ?>" href="floorTherapyAndICUEvaluationlist.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">All</a>
                        <a role="button" class="btn btn-primary <?php echo $clsBtnActiveFloor; ?>" href="floorTherapyAndICUEvaluationlist.html?active=floor&rotationId=<?php echo EncodeQueryData($rotationId); ?>"> Floor Therapy </a>
                        <a role="button" class="btn btn-primary  <?php echo $clsBtnActiveAdult; ?>" href="floorTherapyAndICUEvaluationlist.html?active=icu&rotationId=<?php echo EncodeQueryData($rotationId); ?>">ICU / ABG Rotation</a>
                    <?php } else { ?>
                        <a role="button" class="btn btn-primary <?php echo $clsBtnActive; ?>" href="floorTherapyAndICUEvaluationlist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">All</a>
                        <a role="button" class="btn btn-primary <?php echo $clsBtnActiveFloor; ?>" href="floorTherapyAndICUEvaluationlist.html?active=floor&studentId=<?php echo EncodeQueryData($currentstudentId); ?>"> Floor Therapy </a>
                        <a role="button" class="btn btn-primary  <?php echo $clsBtnActiveAdult; ?>" href="floorTherapyAndICUEvaluationlist.html?active=icu&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">ICU / ABG Rotation</a>
                    <?php } ?>
                </div>
            </div>
        <?php } ?>


        <div id="divTopLoading">Loading...</div>

        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Rotation</th>
                    <th class="text-center">Evaluation</th>
                    <th class="text-center">Score</th>
                    <th class="text-center">Student Signture <br>Date</th>
                    <th class="text-center">Preceptor Info</th>

                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalPEF > 0) {
                    while ($row = mysqli_fetch_array($getEvaldetails)) {

                        $isType = $row['isType'];
                        if ($isType == 1)
                            $isPEF = 'ICU / ABG Rotation';
                        else
                            $isPEF = 'Floor Therapy';

                        $DBStudentId = $row['studentId'];
                        $rotationId = $row['rotationId'];

                        $rotationGrade = isset($row['rotationGrade']) ? $row['rotationGrade'] : '';
                        $studentMasterId = isset($row['studentMasterId']) ? $row['studentMasterId'] : 0;
                        $studentcomment = isset($row['studentComment']) ? $row['studentComment'] : '';
                        $isDateColor = ($studentcomment != '') ? 'text-success' : '';
                        $preceptorId = $row['preceptorId'];
                        $isPreceptorCompletedStatus = $row['isPreceptorCompletedStatus'];
                        $studentfirstName = $row['studentfirstName'];
                        $studentlastName = $row['studentlastName'];
                        $studentfullName = $studentfirstName . ' ' . $studentlastName;

                        $evaluationDate = $row['evaluationDate'];
                        $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
                        $evaluationDate = date("m/d/Y", strtotime($evaluationDate));

                        $dateOfStudentSignature = $row['dateOfStudentSignature'];
                        if ($dateOfStudentSignature != '0000-00-00 00:00:00') {
                            $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                            $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));
                        } else {
                            $dateOfStudentSignature = "-";
                        }
                        $aprovedDate = $row['aprovedDate'];

                        $rotationName = $row['rotationName'];
                        $preceptorFullName = '';
                        $preceptorMobileNum = 0;
                        $isCompletedStatus = 0;
                        $isCompletedStatus = '';
                        $aprovedloggedUserRoleId = isset($_SESSION['loggedUserRoleId'])  ? $_SESSION['loggedUserRoleId'] : 0;
                        $isAprovalId = ($aprovedloggedUserRoleId == 536 || $aprovedloggedUserRoleId == 537 || $aprovedloggedUserRoleId == 245 || $aprovedloggedUserRoleId == 246 || $aprovedloggedUserRoleId == 249  || $aprovedloggedUserRoleId == 540) ? $aprovedloggedUserRoleId : 0;
                        // echo "isPreceptorCompletedStatus".$isPreceptorCompletedStatus."</br>";
                        // echo "isAprovalId".$isAprovalId."</br>";
                        // echo "evaluationDate".$evaluationDate."</br>";
                        // echo "isdefaultFloorAndIcuEval".$isdefaultFloorAndIcuEval."</br></br>";
                        //  == 1 &&  $isdefaultFloorAndIcuEval == 0 && $isAprovalId
                        if ($preceptorId > 0) {
                            $objExternalPreceptors = new clsExternalPreceptors();
                            $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
                            $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
                            $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
                            $preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
                            $preceptorMobileNum = isset($externalPreceptorDetail['mobile_num']) ? $externalPreceptorDetail['mobile_num'] : '';
                            // if($isPreceptorCompletedStatus == 1 &&  $isdefaultFloorAndIcuEval == 0 && $isAprovalId == 0)
                            //     $isCompletedStatus = 'Completed';
                            // else if($isPreceptorCompletedStatus == 1 &&  $isdefaultFloorAndIcuEval == 1 && $isAprovalId == 0)
                            //     $isCompletedStatus = 'Completed';
                            // else if($isPreceptorCompletedStatus == 1 &&  $isdefaultFloorAndIcuEval == 1 && $aprovedDate != '' && $isAprovalId)
                            //     $isCompletedStatus = 'Completed';
                            // else if($isPreceptorCompletedStatus == 1 &&  $isdefaultFloorAndIcuEval == 1 && $aprovedDate == '' && $isAprovalId)
                            //     $isCompletedStatus = 'DCE Review';
                            // else    
                            //     $isCompletedStatus = 'Pending';

                            if ($isPreceptorCompletedStatus == 1 &&  $isdefaultFloorAndIcuEval == 0)
                                $isCompletedStatus = 'Completed';
                            else if ($isPreceptorCompletedStatus == 1 &&  $isdefaultFloorAndIcuEval == 1 && $aprovedDate != '')
                                $isCompletedStatus = 'Completed';
                            else if ($isPreceptorCompletedStatus == 1 &&  $isdefaultFloorAndIcuEval == 1 && $aprovedDate == '')
                                $isCompletedStatus = 'DCE Review';
                            else
                                $isCompletedStatus = 'Pending';
                            // $isCompletedStatus = $isPreceptorCompletedStatus ? 'Completed' : 'Pending';
                        }
                        // $isCompletedStatus = ($isPreceptorCompletedStatus == 1 && $aprovedDate != '' && $isdefaultFloorAndIcuEval) ? 'Completed' : (($isPreceptorCompletedStatus == 1 && $aprovedDate == '')  ? 'DCE Review' : 'Pending');

                        // echo '$isCompletedStatus'.$isCompletedStatus;
                        $isView = ($isPreceptorCompletedStatus == 1 && $aprovedDate == '' && $isAprovalId && $isdefaultFloorAndIcuEval)  ? 'Edit' : 'View';

                ?>
                        <tr>
                            <td><?php echo ($evaluationDate); ?></td>
                            <td><?php echo ($studentfirstName); ?></td>
                            <td><?php echo ($studentlastName); ?></td>
                            <td><?php echo ($rotationName); ?></td>
                            <td class="text-center"><?php echo ($isPEF); ?></td>
                            <td class="text-center"><?php echo ($rotationGrade); ?></td>
                            <td class="text-center <?php echo $isDateColor; ?>"><?php echo ($dateOfStudentSignature); ?></td>
                            <td class="<?php if ($preceptorMobileNum == 0) {
                                            echo 'text-center';
                                        }  ?>">
                                <?php if ($preceptorFullName || $preceptorMobileNum || $isCompletedStatus) { ?>
                                    Name: <?php echo $preceptorFullName; ?> <br>
                                    Phone: <?php echo $preceptorMobileNum; ?> <br>
                                    Status: <?php echo $isCompletedStatus; ?> <br>
                                    <?php if ($isCompletedStatus == 'Pending') { ?>
                                        <a href="javascript:void(0);" class="copyLink" preceptorId="<?php echo EncodeQueryData($preceptorId); ?>" preceptorMobileNum="<?php echo ($preceptorMobileNum); ?>" evaluationId="<?php echo ($studentMasterId); ?>" rotationId="<?php echo ($rotationId); ?>" evaluationType = 'floorTherapy' onclick="copyLinkUrl(this)">Click to Copy URL</a>
                                    <?php }
                                } else { ?>
                                    <span>-</span>
                                <?php } ?>

                            </td>

                            <td class="text-center">
                                <a href="addFloorTherapyAndICUEvaluation.html?studentMasterId=<?php echo EncodeQueryData($studentMasterId); ?>&rotationId=<?php echo (EncodeQueryData($rotationId)); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>&isEvalType=<?php echo EncodeQueryData($isType) ?>"><?php echo $isView; ?></a>
                            </td>
                        </tr>
                <?php

                    }
                }
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });

        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentMasterId = $(this).attr('studentMasterId');


            alertify.confirm('Floor Therapy: ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentMasterId,
                        type: 'pef1Evalution'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });


        $(".select2_single").select2();

        var current_datatable = $("#datatable-responsive").DataTable({
            "scrollX": true,
            responsive: false,
            "ordering": true,
            "order": [
                [0, "desc"]
            ],
            "aoColumns": [{
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "30%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%"
                }
            ]
        });

        
    </script>


</body>

</html>