<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
	include('../class/clsCheckoffQuestionMaster.php'); 
	include('../class/clsSchool.php');      
	include('../setRequest.php'); 
    $schoolId = 0;
    $questionTypeId = 0;
    $questionType = 0;
	
    $schoolId= $currentSchoolId;
    $title ="Add Question - ".$currenschoolDisplayname;
 
    $page_title ="Add Question";
    $QueId = 0;
    $title = '';
	$bedCrumTitle = 'Add';
	
	if(isset($_GET['sectionId']))
	{
		$schoolSectionId=DecodeQueryData($_GET['sectionId']);
	}
    if(isset($_GET['QueId'])) //Edit Mode
	{
        $QueId = DecodeQueryData($_GET['QueId']);
	    $page_title ="Edit Question";
        $bedCrumTitle = 'Edit';        
	}

	//Get School Details
	$objSchool = new clsSchool();
	$schoolDetails=$objSchool->GetSchoolDetails($schoolId);
	$slug = $schoolDetails['slug'];
	unset($objSchool);

	//For Question type
	$objQuestion = new clsCheckoffQuestionMaster();
    $GetQuestionType = $objQuestion->GetQuestionType();
    unset($objQuestion);
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

	</head>


<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
                    <li><a href="dashboard.html">Setting</a></li>
                    <li><a href="checkofftopics.html">Comps Topics</a></li>
                    <li><a href="checkoffsection.html?schoolSectionId=<?php echo EncodeQueryData($schoolSectionId); ?>">Comps Section </a></li>
                    <li><a href="questions.html?sectionId=<?php echo EncodeQueryData($schoolSectionId); ?>">Comps Steps</a></li>                   
                    <li class="active"><?php echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    
	<div class="container">

<form id="frmaddquestion" name="frmaddquestion" data-parsley-validate class="form-horizontal" method="POST" action="questionsubmit.html?sectionId=<?php echo EncodeQueryData($schoolSectionId); ?>">

	<div class="row">

		<div class="col-md-6">

			<div class="form-group">
				<label class="col-md-12 control-label" for="cboQuestionType">Question Type</label>
				<div class="col-md-12 flex-col-reverse">
					<select id="cboQuestionType" name="cboQuestionType" class="form-control input-md required-input select2_single quetype" required>
						<option value="" selected>Select</option>
						<?php
						if ($GetQuestionType != "") {
							while ($row = mysqli_fetch_assoc($GetQuestionType)) {
								$selquestionTypeId  = $row['questionTypeId'];
								$selquestionType  = $row['questionType'];
								$name  = stripslashes($row['title']);

						?>
								<option value="<?php echo ($selquestionType); ?>" <?php if ($questionType == $selquestionType) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
						<?php

							}
						}
						?>
					</select>
					<input type="hidden" value="" name="questionType" id="questionType">
				</div>
			</div>
			<div class="form-group">
				<label class="col-md-12 control-label clslongans" for="Note" style="display: none;">Read</label>
				<label class="col-md-12 control-label clssingle" for="Note" style="display: none;">Read</label>
				<label class="col-md-12 control-label clsyesno" for="Note" style="display: none;">Read</label>
				<label class="col-md-12 control-label clscheckbox" for="Note" style="display: none;">Read</label>
				<label class="col-md-12 control-label clsdatepicker" for="Note" style="display: none;">Read</label>
				<label class="col-md-12 control-label clsall" for="Note" style="display: none;">Read</label>
				<label class="col-md-12 control-label clsdropdown" for="Note" style="display: none;">Read</label>
				<div class="col-md-12">
					<div class="clslongans" style="display: none;">
						<input class="form-control" readonly type="text" value="Answer is of text type">
					</div>
					<div class="clssingle" style="display: none;">
						<input class="form-control" readonly type="text" value="Multiple choices with only one correct answer">
					</div>
					<div class="clsyesno" style="display: none;">
						<input class="form-control" readonly type="text" value="Answser of type Yes/No">
					</div>
					<div class="clscheckbox" style="display: none;">
						<input class="form-control" readonly type="text" value="Answser of type checkbox">
					</div>
					<div class="clsdatepicker" style="display: none;">
						<input class="form-control" readonly type="text" value="Answser of type datepicker">
					</div>
					<div class="clsall" style="display: none;">
						<input class="form-control" readonly type="text" value="All type of bunch">
					</div>
					<div class="clsdropdown" style="display: none;">
						<input class="form-control" readonly type="text" value="Answser of type dropdown">
					</div>
				</div>
			</div>
		</div>

		<div class="col-md-6">
			<div class="">
				<div class="form-group">
					<label class="col-md-12 control-label" for="question">Question Title</label>
					<div class="col-md-12">
						<textarea id="question" name="question" class="form-control input-md required-input" required></textarea>

					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-6 clsmark">
			<div class="">
				<div class="form-group">
					<label class="col-md-12 control-label" for="Marks">Marks</label>
					<div class="col-md-12">
						<input type="text" id="Marks" name="Marks" class="form-control input-md" required>

					</div>
				</div>
			</div>
		</div>
		<div class="col-md-6">
			<div class="">
				<div class="form-group">
					<label class="col-md-12 control-label" for="SortOrder">Sort Order</label>
					<div class="col-md-12">
						<input type="text" id="SortOrder" name="SortOrder" class="form-control input-md required-input" required>

					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-6 clsall" style="display: none;">
			<div class="">
				<div class="form-group clsall">
					<label class="col-md-12 control-label" for="questionLabel">Question Label</label>
					<div class="col-md-12">
						<textarea id="questionLabel" name="questionLabel" class="form-control input-md required-input" required></textarea>

					</div>
				</div>
			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12">

			<?php if ($slug == 'fshaftx') { ?>
				<div class="form-group clsyesno" style="display: none;">

					<div class="form-group yesnoboxdiv">
						<label class="col-md-12 control-label" for="yes">Defualt Answer</label>
						<div class="col-md-12">
							<input type="radio" class="clsyesnoans" id="yesno" name="yesno[]" value="0">
							<input type="hidden" id="yesnoanswers" name="yesnoanswers[]" value="0">

							<input type="hidden" name="txtyesno[]" value="Sat">
							<label class="control-label" for="yes">Sat</label>
						</div>
					</div>
					<div class="form-group yesnoboxdiv">
						<label class="col-md-12 control-label" for="no"></label>
						<div class="col-md-12">
							<input type="radio" class="clsyesnoans" id="yesno" name="yesno[]" value="0">
							<input type="hidden" id="yesnoanswers" name="yesnoanswers[]" value="0">
							<input type="hidden" name="txtyesno[]" value="UnSat">

							<label class="control-label" for="no">UnSat</label>
						</div>
					</div>

					<div class="form-group yesnoboxdiv">
						<label class="col-md-12 control-label" for="None"></label>
						<div class="col-md-12">
							<input type="radio" class="clsyesnoans" id="yesno" name="yesno[]" value="0">
							<input type="hidden" id="yesnoanswers" name="yesnoanswers[]" value="0">
							<input type="hidden" name="txtyesno[]" value="NI">

							<label class="control-label" for="NI">None</label>
						</div>
					</div>
				</div>
			<?php } else { ?>
				<div class=" clsyesno" style="display: none;">

					<div class="form-group yesnoboxdiv">
						<label class="col-md-12 control-label" for="yes">Defualt Answer</label>
						<div class="col-md-12">
							<input type="radio" class="clsyesnoans" id="yesno" name="yesno[]" value="0">
							<input type="hidden" id="yesnoanswers" name="yesnoanswers[]" value="0">

							<input type="hidden" name="txtyesno[]" value="Yes">
							<label class="control-label" for="yes">Yes</label>
						</div>
					</div>
					<div class="form-group yesnoboxdiv">
						<label class="col-md-12 control-label" for="no"></label>
						<div class="col-md-12">
							<input type="radio" class="clsyesnoans" id="yesno" name="yesno[]" value="0">
							<input type="hidden" id="yesnoanswers" name="yesnoanswers[]" value="0">
							<input type="hidden" name="txtyesno[]" value="No">

							<label class="control-label" for="no">No</label>
						</div>
					</div>

					<div class="form-group yesnoboxdiv">
						<label class="col-md-12 control-label" for="None"></label>
						<div class="col-md-12">
							<input type="radio" class="clsyesnoans" id="yesno" name="yesno[]" value="0">
							<input type="hidden" id="yesnoanswers" name="yesnoanswers[]" value="0">
							<input type="hidden" name="txtyesno[]" value="None">

							<label class="control-label" for="None">None</label>
						</div>
					</div>
				</div>
			<?php } ?>
			<div class="col-md-12 px-0">

				<div class="form-group clslongans" style="display: none;">
					<label class="col-md-12 control-label" for="Answer">Defualt Answer </label>
					<div class="col-md-12">
						<textarea id="longans" name="longans" class="form-control input-md" rows="4"></textarea>
					</div>
				</div>

				<div class="form-group clssingle" style="display: none;">
					<div class="">
						<div class="col-md-12 control-label">Defualt Answer</div>
						<div class="col-md-12">
							<div class="row">
								<div class="col-md-12 textboxDiv">
									<div class="singlechoiceboxdiv" style="display: flex;">
										<div class="col-md-6" style="
												display: flex;
											align-items: center;
																		gap: 10px;
									padding: 0;">
											<input type="radio" id="singlechoice" name="singlechoice[]" class="choicebox singlechoice" value="0">
											<input type="hidden" id="hid_anser" class="hid_anser" name="answers[]" value="0">
											<input type="text" id="txtsinglechoice" class="form-control input-md required-input choicebox" name="txtsinglechoice[]" placeholder="Add an answer" required><br>
										</div>
										<div class="col-md-6" style="display: flex;align-items: center;gap: 10px;padding: 0;">

											<input type="text" id="txtsinglechoicemarks" class="form-control input-md required-input choicebox" name="txtsinglechoicemarks[]" placeholder="Add marks" required>
											<span id="Remove" style="color:red;" class="glyphicon glyphicon-trash Remove"></span>
										</div>

									</div>
								</div>
								<div class="col-md-12 mt-15" style="display: flex;justify-content: end;">
									<button type="button" id="Add" class="btn btn-success">Add</button>
								</div>


							</div>
						</div>
					</div>

				</div>
			</div>
			<div class="col-md-6 px-0">
				<div class="form-group clsdropdown row" style="display: none;">
					<div class="col-md-12">
						<label class="col-md-12 control-label alert alert-danger" for="None" style="padding: 15px !important;">You need to select label for dropdown</label>
						<label class="col-md-12 control-label px-0" for="None">Dropdown</label>
					</div>
					<div class="col-md-12">
						<select id="dropdownans" name="dropdownans" class="form-control mb-15 clsdropdownans">
							<option value="" selected>Select</option>
							<option value="0">Preceptor</option>
						</select>
						<div style="display: flex; align-items: center; gap: 15px;">
							<div>
								<input type="radio" class="clsdd" id="1" name="cbodd" value="Clinical Instructor:"> Clinical Instructor
							</div>
							<div>
								<input type="hidden" id="dropdownanswers" name="dropdownanswers[]" value="">
								<input type="radio" class="clsdd" id="2" name="cbodd" value="Preceptor:"> Preceptor
							</div>
							<div>
								<input type="radio" class="clsdd" id="3" name="cbodd" value="Lab Instructor:"> Lab Instructor
							</div>
						</div>
					</div>
				</div>
			</div>

			<div class="form-group clscheckbox" style="display: none;">
				<div>
					<label class="col-md-12 control-label" for="yes">Default Answer</label>
					<div class="col-md-12" style="display: flex;align-items: center;gap: 15px;">
						<div style="display: flex;align-items: center;gap: 5px;">
							<input type="checkbox" class="clscheckboxans" id="checkboxes" name="checkboxes[]" value="0">
							<input type="hidden" id="checkboxanswers" name="checkboxanswers[]" value="1">

							<input type="hidden" name="txtcheckbox[]" value="Clinical">
							<label class="control-label" style="margin-bottom: 0 !important;" for="Clinical">Clinical</label><br>
						</div>
						<div style="display: flex;align-items: center;gap: 5px;">
							<input type="checkbox" class="clscheckboxans" id="checkboxes" name="checkboxes[]" value="0">
							<input type="hidden" id="checkboxanswers" name="checkboxanswers[]" value="2">

							<input type="hidden" name="txtcheckbox[]" value="Lab">
							<label class="control-label" style="margin-bottom: 0 !important;" for="Lab">Lab</label>
						</div>
					</div>
				</div>
			</div>
			

		</div>
		<div class="row mx-0">
				<div class="col-md-6">
					<div class="form-group clsdatepicker" style="display: none;">
						<label class="col-md-12 control-label" for="None">Date</label>
						<div class="col-md-12">
							<input type="Date" class="clsdatepickerans form-control" id="questiondate" name="Date" value="Date:">
							<input type="hidden" id="dateanswers" name="dateanswers[]" value="">
						</div>

						<div class="col-md-12 mt-10">
							<label class="w-full control-label alert alert-danger" for="None" style="padding: 15px !important;">You need to select label for date</label>
							<label class="col-md-12 control-label" for="no"></label>
						</div>
						<div class="col-md-12" style="display: flex; align-items: center; gap: 20px;">

							<input type="radio" class="clsCILabDate" id="CI_Date" name="CILab_Date" value="CI Date:" style="margin-top: 0;"> CI Date<br>


							<input type="radio" class="clsCILabDate" id="Lab_Date" name="CILab_Date" value="Lab Date:" style="margin-top: 0;"> Lab Date

						</div>
					</div>
				</div>
			</div>
	</div>

	<br>
	<div class="row">
		<div class="col-md-12">
			<div class="form-group clsall mx-0" style="display: none;">
				<label class="col-md-12 control-label alert alert-danger" for="None" style="padding: 15px !important; margin-bottom: 20px !important;">You need to select ALL options for this question</label>

				<div class="col-md-12 px-0"><input type="checkbox" class="allcheckbox" name="allcheckbox[1st Complition Date]" value="1st"> 1st
					1st Complition Date: <input type="text" class="dateInputFormat form-control" style="width: 200px;display: inline;" id="1stCompletion" value="0">
					Preceptor:
					<select id="alldropdownans" class="select2_single form-control">
						<option value="" selected>Select</option>
						<option value="0">Preceptor</option>
					</select>
				</div>

			</div>
		</div>
	</div>
	<div class="row">
		<div class="col-md-12">
			<div class="form-group clsall" style="display: none;">
				<label class="col-md-2 control-label" for="None"></label>
				<div class="col-md-12">
					<input type="checkbox" class="allcheckbox" name="allcheckbox[2nd Complition Date]" value="2nd"> 2nd

					2nd Complition Date: <input type="text" id="2ndCompletion" class="dateInputFormat form-control" style="width: 200px;display: inline;" value="0">
					Preceptor:
					<select id="alldropdownans" class="select2_single form-control">
						<option value="" selected>Select</option>
						<option value="0">Preceptor</option>
					</select>
				</div>

			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<div class="form-group clsall" style="display: none;">
				<label class="col-md-2 control-label" for="None"></label>
				<div class="col-md-12"><input type="checkbox" class="allcheckbox" name="allcheckbox[3rd Complition Date]" value="3rd"> 3rd
					3rd Complition Date: <input type="text" id="3rdCompletion" class="dateInputFormat form-control" style="width: 200px;display: inline;" value="0">
					Preceptor:
					<select id="alldropdownans" class="select2_single form-control">
						<option value="" selected>Select</option>
						<option value="0">Preceptor</option>
					</select>
				</div>

			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<div class="form-group clsall" style="display: none;">
				<label class="col-md-2 control-label" for="None"></label>
				<div class="col-md-12"><input type="checkbox" class="allcheckbox" name="allcheckbox[4th Complition Date]" value="4th"> 4th
					4th Complition Date: <input type="text" id="4thCompletion" class="dateInputFormat form-control" style="width: 200px;display: inline;" value="0">
					Preceptor:
					<select id="alldropdownans" class="select2_single form-control">
						<option value="" selected>Select</option>
						<option value="0">Preceptor</option>
					</select>
				</div>

			</div>
		</div>
	</div>

	<div class="row">
		<div class="col-md-12">
			<div class="form-group clsall" style="display: none;">
				<label class="col-md-2 control-label" for="None"></label>
				<div class="col-md-12">
					<input type="checkbox" class="allcheckbox" name="allcheckbox[5th Complition Date]" value="5th"> 5th

					5th Complition Date: <input type="text" id="5thCompletion" class="dateInputFormat form-control" style="width: 200px;display: inline;" value="0">
					Preceptor:
					<select id="alldropdownans" class="select2_single form-control">
						<option value="" selected>Select</option>
						<option value="0">Preceptor</option>
					</select>
				</div>

			</div>
		</div>
	</div>

	<div class="form-group">
		<!-- <label class="col-md-2 control-label"></label> -->
		<div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
			<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
			<a type="button" href="questions.html?sectionId=<?php echo  EncodeQueryData($schoolSectionId); ?>" class="btn btn-default">Cancel</a>
		</div>
	</div>
</form>
</div>


    <?php include('includes/footer.php');?>
   
	
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>	
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

    <script type="text/javascript">
 
 
 		$(".select2_single").select2();
        $('#select2-cboQuestionType-container').addClass('required-select2');
        $(window).load(function(){

             $('#frmaddquestion').parsley().on('field:validated', function() {
			    var ok = $('.parsley-error').length === 0;
            })
            .on('form:submit', function() {
                ShowProgressAnimation();									  
                return true; // Don't submit form for this demo
            });
			
		});
		
		$('#1stCompletion').datetimepicker({
		format: 'MM/DD/YYYY'
		});
		$('#2ndCompletion').datetimepicker({
		format: 'MM/DD/YYYY'
		});
		$('#3rdCompletion').datetimepicker({
		format: 'MM/DD/YYYY'
		});
		$('#4thCompletion').datetimepicker({
		format: 'MM/DD/YYYY'
		});
		$('#5thCompletion').datetimepicker({
		format: 'MM/DD/YYYY'
		});

		//For Question Type
		$(function() {
		$('#cboQuestionType').change(function() {							
							var x = $('#cboQuestionType option:selected').val();							
							$('#questionType').val(x);							
						 });
						 
					});

		//For Yes/No Question			
		$( document ).ready(function() {
			$("select").change(function(){
				if($(this).val() == 1)
					$('.clsyesno').show();
				else
					$('.clsyesno').hide();
				document.getElementById("yesno").required = false;
			});
		});

		//For Long Ans Question
		$( document ).ready(function() {
			$("select").change(function(){
				if($(this).val() == 5)
					$('.clslongans').show();
				else
					$('.clslongans').hide();
				document.getElementById("longans").required = false;
			});
		});

		//For Single Choice Question
		$( document ).ready(function() {
			$("select").change(function(){
				if($(this).val() == 2)
					$('.clssingle').show();
				else
					$('.clssingle').hide();
				document.getElementById("singlechoice").required = false;
				document.getElementById("txtsinglechoice").required = false;
				document.getElementById("txtsinglechoicemarks").required = false;
			});
		});

		//For Checkbox question
		$( document ).ready(function() {
			$("select").change(function(){				
				if($(this).val() == 3)
					$('.clscheckbox').show();
				else
					$('.clscheckbox').hide();
				document.getElementById("checkboxes").required = false;
			});
		});

		//For date
		$( document ).ready(function() {
			$("select").change(function(){				
				if($(this).val() == 6)
				{
					$('.clsdatepicker').show();				    
					$('#btnSubmit').attr('disabled', true);
				}
				else
				{
					$('.clsdatepicker').hide();
				document.getElementById("questiondate").required = false;
				$('#btnSubmit').attr('disabled', false);

				}
			});
		});

		//For All
		$( document ).ready(function() {
			$("select").change(function(){				
				if($(this).val() == 7)
				{
					$('.clsall').show();				    
					//$('#btnSubmit').attr('disabled', true);
				}
				else
				{
					$('.clsall').hide();
				}
			});
		});

		//For Dropdown
		$( document ).ready(function() {
			$("select").change(function(){				
				if($(this).val() == 4)
				{
					$('.clsdropdown').show();				    
					//$('#btnSubmit').attr('disabled', true);
				}
				else
				{
					$('.clsdropdown').hide();
				//document.getElementById("checkboxes").required = false;
				}
			});
		});
		
		$('.clsCILabDate').click(function() {

			if ($(this).is(':checked')) {
				$('#btnSubmit').attr('disabled', false);					
			} else {
				$('#btnSubmit').attr('disabled', true);					
			}
		});
		$( document ).ready(function() {
			$("select").change(function(){
				if($(this).val() != 2)
					$('.clsmark').show();
				else
					$('.clsmark').hide();
				document.getElementById("Marks").required = false;
			});
		});
		
		$(document).ready(function() { 
		
			
            $("#Add").on("click", function() {  
			
				$(".textboxDiv").append("<div class='singlechoiceboxdiv mb-15'><br><div class='col-md-6' style='display: flex;align-items: center;gap: 10px;padding: 0;'><input type='radio'  class='choicebox singlechoice' id='singlechoice' value='0' name='singlechoice[]' '><input type='text' class='form-control input-md required-input choicebox' id='txtsinglechoice' name='txtsinglechoice[]' placeholder='Add an answer' required /><input type='hidden'  id='hid_anser' class='hid_anser' name='answers[]'  value='0'/><br></div><div class='col-md-6' style='display: flex;align-items: center;gap: 10px;padding: 0;'><input type='text' id='txtsinglechoicemarks'  class='form-control input-md required-input choicebox' name='txtsinglechoicemarks[]' placeholder='Add marks' required><span id='Remove' style='color:red;' class='glyphicon glyphicon-trash Remove'></span></div></div><br>");                 
            });  
           // $(".Remove").on("click", function() {  
            $(document).on("click", ".Remove", function() {  			
				$(this).closest('.singlechoiceboxdiv').remove();
            });  
        });  
	
	
	$(document).on("click", ".singlechoice", function(event){
			console.log($(event.target).hasClass('singlechoice'));
			if($(event.target).hasClass('singlechoice')){
				$('input[id=hid_anser]').val(0);
			$(this).closest(".singlechoiceboxdiv").find('input[id=hid_anser]').val('1');
				
			}
		});
		
	
	$(document).on("click", ".clsyesnoans", function(event){
			console.log($(event.target).hasClass('clsyesnoans'));
			if($(event.target).hasClass('clsyesnoans')){
				$('input[id=yesnoanswers]').val(0);
			$(this).closest(".yesnoboxdiv").find('input[id=yesnoanswers]').val('1');
				
			}
		});

		$(document).on("click", ".clscheckboxans", function(event){
			console.log($(event.target).hasClass('clscheckboxans'));
			if($(event.target).hasClass('clscheckboxans')){
				$('input[id=checkboxanswers]').val(0);
			$(this).closest(".checkboxdiv").find('input[id=checkboxanswers]').val('1');
				
			}
		});	
		
	
	$(document).on('click', '.choicebox', function(e) {	
		
			if(this.checked) {
				var txtinputboxvalue = $(this).closest('.singlechoiceboxdiv').find('input[name="txtsinglechoice[]"]').val();
					
				if(txtinputboxvalue == '')
				 {					  
					alertify.alert('Warning', 'Please write answer to selected option!');
					 $('#btnsubmit').addClass('disabled');
				 }
				 else
				 {
					 $('#btnsubmit').removeClass('disabled');
				 }
			}
		});
		
		 $(document).ready(function(){
        $("#btnSubmit").click(function(){ 

		var questionType = $("select").val();
		if(questionType == 2 || questionType == 1)
		{
            if($("input[type=radio]:checked").length > 0)
			{
			
               return true;
            }
			else
			{
				 alert("please select correct answer");
				 return  false;
			}
		}
        });
        
    });
		
    </script>

</body>

</html>