<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
    include('../includes/commonfun.php');
    include('../class/clsLocations.php');
    include('../setRequest.php');

    $totalLocations = 0;
    //For All Location List
    $objLocations = new clsLocations();
	$rowsSchoolLocation = $objLocations->GetAlllocation($currentSchoolId);
	if($rowsSchoolLocation !='')
	{
		$totalLocations = mysqli_num_rows($rowsSchoolLocation);
	}
    unset($objLocations);

    $loggedUserId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;
?>

    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Locations </title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>

        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
						<li><a href="settings.html">Settings</a></li>
                        <li class="active">Location</li>
                    </ol>
                </div>
                <div class="pull-right">
                    <a class="btn btn-link" href="addlocation.html">Add</a>
                </div>
            </div>
        </div>

        <div class="container">

            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="Added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Location added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Location updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="StatusUpdated")
					{
				?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Location status updated successfully.
                    </div>
                <?php
					}
                    else if($_GET["status"] =="Error")
					{
						?>
                    <div class="alert alert-danger alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Error occurred.
                    </div>
                <?php 
					}
				}
              ?>

                <div id="divTopLoading" >Loading...</div>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Location Name</th>
                            <th>Time Zone</th>
                            <th>Email</th>
                            <th>Phone</th>
                           
                            <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalLocations>0)
                        {
                            while($row = mysqli_fetch_array($rowsSchoolLocation))
                            {

                                $locationId = $row['locationId'];					
                                $title = stripslashes($row['title']);
                                $email = stripslashes($row['email']);
								$phone = stripslashes($row['phone']);
								$timezone = $row['timezone'] ? $row['timezone'] : '';
                                
                                $buttoncss = "btn-primary";
                                ?>
                            <tr>
							<td><?php echo($title); ?></td>
                            <td><?php echo($timezone); ?></td>
                               
                            <td>
                                <a href="mailto:<?php echo($email); ?>">
                                    <?php echo($email); ?>
                                </a>
                                
                            </td>
                            <td>
                                <a href="tel:<?php echo($phone); ?>">
                                    <?php echo($phone); ?>
                                </a>
                            </td>
                            
                            <td style="text-align: center">
                                <a class="<?php echo($buttoncss); ?>" href="schoolclinicianstransubmit.html?id=<?php echo($locationId); ?>&newStatus=<?php //echo($updateStatus); ?>">
                                    
                                </a>
                                <?php //if($isShowemail==1){?>
                                    
                                <a href="addlocation.html?id=<?php echo(EncodeQueryData($locationId)); ?>">Edit</a>
                                | <a href="javascript:void(0);" class="deleteAjaxRow"
                                locationId="<?php echo EncodeQueryData($locationId); ?>" locationtitle="<?php echo($title); ?>">Delete</a>
                                <?php } ?>

                                </td>
                            </tr>
                            <?php


                           // }
                        }
                    ?>



                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            });


            var current_datatable = $("#datatable-responsive").DataTable({
                "aoColumns": [{
                    "sWidth": "25%"
                }, {
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                }, {
                    "sWidth": "10%"
                },{
                    "sWidth": "10%"
                },{
                    "sWidth": "5%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }, ]
            });
 


             $(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var locationId = $(this).attr('locationId');
                var locationtitle = $(this).attr('locationtitle');
                var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
                var isUser = 1; //for Admin


                alertify.confirm('Location: '+locationtitle, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: locationId,
                            userId: userId,
                            isUser: isUser,
                            type: 'location'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });

        </script>


    </body>

    </html>