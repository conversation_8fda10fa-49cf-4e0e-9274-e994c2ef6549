<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsCheckoffSectionMaster.php');
include('../class/clsCheckoffQuestionMaster.php');
include('../class/clsCheckoffTopicMaster.php');
include('../class/clsOrientationChecklist.php');

if ($_SERVER['REQUEST_METHOD'] == "POST") {

    $objDB = new clsDB();

    $type = isset($_POST['type']) ? $_POST['type'] : '';


    ini_set('upload_max_filesize', '50M');
    ini_set('post_max_size', '50M');
    ini_set('max_input_time', 300000);
    ini_set('max_execution_time', 300000);

    $notifyMessage = 'Imported';
    $row = 1;
    $isExistOrientationChecklistSectionId = 0;
    $isExistOrientationChecklistQuestionId = 0;
    $orientationQuestionId = 0;

    if (isset($_FILES['file'])) {
        $filename = $_FILES["file"]["tmp_name"];
        $ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));

        if ($ext != "csv") {
            $error = "Upload only .csv file.";
            header('location:checkofftopics.html?status=Importerror');
            exit();
        }

        if ($_FILES["file"]["size"] > 0) {

            $file = fopen($filename, "r");
            $counter = 0;

            $retStudentId = 0;
            $questionSortOrder = 0;
            while (($getData = fgetcsv($file, 10000, ",")) !== FALSE) {
                // echo $type;exit; 
                if ($row == 1) {
                    $row++;
                    continue;
                }

                $sectionTitle = trim($getData[1]);
                $optionText = trim($getData[2]);
                $discription = trim($getData[3]);
                $optionType = trim($getData[4]);
                $optionValues = trim($getData[5]);
                $sortOrder = trim($getData[6]);
                $sectionSortOrder = trim($getData[7]);

                $isExistOrientationChecklistSectionId = $objDB->GetSingleColumnValueFromTable('defaultorientationchecklistsectionmaster', 'orientationSectionId	', 'title', $sectionTitle);
                // print_r( $isExistOrientationChecklistSectionId);exit;
                if ($sectionTitle != '') {
                    if (!$isExistOrientationChecklistSectionId) {

                        $objOrientationChecklist = new clsOrientationChecklist();
                        $objOrientationChecklist->title = $sectionTitle;
                        $objOrientationChecklist->sortOrder = $sectionSortOrder;
                        $isExistOrientationChecklistSectionId = $objOrientationChecklist->SaveAdminOrientationChecklistSection(0);
                    }
                }


                $isExistOrientationChecklistQuestionId = $objDB->GetSingleColumnValueFromTable('defaultorientationchecklistquestionmaster', 'orientationQuestionId', 'optionText', addslashes($optionText));

                if (!$isExistOrientationChecklistQuestionId) {

                    $questionSortOrder++;
                    $objOrientationChecklist = new clsOrientationChecklist();
                    $objOrientationChecklist->optionText = $optionText;
                    $objOrientationChecklist->orientationQuestionType = $optionType;
                    $objOrientationChecklist->orientationSectionId = $isExistOrientationChecklistSectionId;
                    $objOrientationChecklist->sortOrder =  $questionSortOrder;
                    $isExistOrientationChecklistQuestionId = $objOrientationChecklist->SaveAdminOrientationChecklistQuestions(0);

                    $orientationQuestionType = [4, 9]; // Answer types: dropdowsettingsn and textarea
                    foreach ($orientationQuestionType as $answerType) {
                        
                        $objOrientationChecklist->optionText = '';
                        $objOrientationChecklist->orientationQuestionType = $answerType;
                        $objOrientationChecklist->orientationSectionId = $isExistOrientationChecklistSectionId;
                        $objOrientationChecklist->sortOrder = $questionSortOrder;                        
                        $objOrientationChecklist->parentQuestionId = $isExistOrientationChecklistQuestionId;                        
                        $orientationQuestionId = $objOrientationChecklist->SaveAdminOrientationChecklistQuestions(0);
                    }
                    // $objOrientationChecklist->optionText = '';
                    // $objOrientationChecklist->orientationQuestionType = 4; 
                    // $objOrientationChecklist->orientationSectionId = $isExistOrientationChecklistSectionId; 
                    // $objOrientationChecklist->sortOrder = $sortOrder; 
                    // $orientationQuestionId=$objOrientationChecklist->SaveAdminOrientationChecklistQuestions(0);   


                    // $objOrientationChecklist->optionText = 'Comments';
                    // $objOrientationChecklist->orientationQuestionType = 9; 
                    // $objOrientationChecklist->orientationSectionId = $isExistOrientationChecklistSectionId; 
                    // $objOrientationChecklist->sortOrder = $sortOrder; 
                    // $orientationQuestionId=$objOrientationChecklist->SaveAdminOrientationChecklistQuestions(0);   

                }

                if ($isExistOrientationChecklistQuestionId) {
                    $objOrientationChecklist = new clsOrientationChecklist();
                    $isExistOrientationChecklistQuestionId = ($optionValues == '') ? $orientationQuestionId : $isExistOrientationChecklistQuestionId;

                    $objOrientationChecklist->orientationQuestionId = $isExistOrientationChecklistQuestionId;
                    $objOrientationChecklist->optionText = $optionValues;
                    $objOrientationChecklist->description = $discription;
                    $objOrientationChecklist->SaveAdminOrientationChecklistQuestionsDtls(0);
                }
                $result = 1;
            }

            // exit;
            fclose($file);

            $messageText = $result ? 'Imported' : 'Error';
            header('location:settings.html?status=' . $messageText);

            exit();
        }
    }
}
