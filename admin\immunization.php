<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsImmunizationMaster.php');
include('../setRequest.php');
include('../class/clsImmunization.php');

$schoolId = 0;
$immunizationMId = 0;
$transchooldisplayName = '';

if (isset($_GET['schoolId'])) //Edit Mode
{
    $schoolId = $_GET['schoolId'];
    $schoolId = DecodeQueryData($schoolId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

if (isset($_GET['immunizationMId'])) //Edit Mode
{
    $immunizationMId = $_GET['immunizationMId'];
    $immunizationMId = DecodeQueryData($immunizationMId);
}
$title = "Immunization   ";

//For Immunization List 
$objimmunization = new clsImmunizationMaster();
$totalimmunization = 0;
$rowsimmunization = $objimmunization->GetAllImmunizationMaster($currentSchoolId);
if ($rowsimmunization != '') {
    $totalimmunization = mysqli_num_rows($rowsimmunization);
}
unset($objimmunization);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content
        must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li>
                        <a href="dashboard.html">Home</a>
                    </li>
                    <li>
                        <a href="settings.html">Settings</a>
                    </li>
                    <li class="active">Immunization
                    </li>
                </ol>
            </div>
            <div class="pull-right">
                <a class="btn btn-link" href="addimmunizationmaster.html">Add</a>
            </div>

        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    Immunization Added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    Immunization updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    Immunization deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">×</span>
                    </button>
                    Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th style="text-align: left">Immunization Code</th>
                    <th style="text-align: center">Notify Day's</th>
                    <th style="text-align: center">Total No. Of Student</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalimmunization > 0) {

                    while ($row = mysqli_fetch_array($rowsimmunization)) {

                        $immunizationMId = $row['immunizationMId'];
                        $shortName = $row['shortName'];
                        $expiryDays = $row['expiryDays'];
                        $studentimulizationsMId = $row['studentimulizationsMId'];
                        // $TotalCount = $row['TotalCount'];
                        $objimmunization = new clsImmunization();
                        $TotalCount = $objimmunization->GetImmulizationStudentCount($immunizationMId);
                        unset($objimmunization);


                ?>
                        <tr class="dataTableRowSelected">

                            <td style="text-align: left">
                                <?php echo ($shortName); ?><br>
                            </td>
                            <td style="text-align: center">

                                <?php echo ($expiryDays); ?><br>

                            </td>

                            <td style="text-align: center">
                                <a href="studentimmunization.html?studentImmunizationId=<?php echo (EncodeQueryData($immunizationMId)); ?>"><?php echo ($TotalCount); ?></a>

                            </td>
                            <td style="text-align: center">
                                <a href="addimmunizationmaster.html?immunizationMId=<?php echo EncodeQueryData($immunizationMId); ?>">Edit</a>

                                <?php if ($studentimulizationsMId) { ?>
                                    |
                                    <a href="javascript:void(0);" onclick="javascript:ShowwarningMessage();" class="text-muted" immunizationmid="<?php echo EncodeQueryData($immunizationMId); ?>">Delete</a>
                                <?php } else { ?>
                                    |<a href="javascript:void(0);" class="deleteAjaxRow" immunizationmid="<?php echo EncodeQueryData($immunizationMId); ?>">Delete</a>
                                <?php } ?>
                            </td>

                        </tr>
                <?php
                    }
                }
                ?>

            </tbody>
        </table>

    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });

        var current_datatable = $("#datatable-responsive").DataTable({

            "aoColumns": [{
                "sWidth": "30%",
                "sClass": "alignCenter",
                "bSortable": false

            }, {
                "sWidth": "5%",
                "sClass": "alignCenter",
                "bSortable": false
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter"

            }, {
                "sWidth": "5%",
                "sClass": "alignCenter",
                "bSortable": false
            }]
        });

        function ShowwarningMessage() {
            alertify.alert(
                'Warning',
                'This Immunization is Assign To Student. You can\'t delete this.'
            );
        }

        // ajax call for deleteAjaxRow
        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var immunizationMId = $(this).attr('immunizationMId');
            var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';
            var isUser = 1; //for Admin
            

            alertify.confirm('Immunization ', 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: immunizationMId,
                        userId: userId,
                        isUser: isUser,
                        type: 'immunizationMaster'
                    },
                    success: function() {
                        current_datatable
                            .row(current_datatable_row)
                            .remove()
                            .draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });
    </script>

</body>

</html>