<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');    
	include('../class/clsStudentMinCharacterEntry.php');
	include('../setRequest.php'); 
	
		
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		
		
		$journalCount = isset($_POST['txtJournalCount']) ? $_POST['txtJournalCount'] : 0;

		$id = isset($_GET['id']) ? $_GET['id'] : 0;
		//Save data
		$objJournal = new clsStudentMinCharacterEntry();
		$objJournal->journalCharacters = $journalCount;	
		$retCountId = $objJournal->SaveStudentJournalEntryCount($id,$currentSchoolId);
		unset($objJournal);

		if($retCountId > 0)
		{
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = ($id > 0) ? $objLog::EDIT : $objLog::ADD;
			$userType = $objLog::ADMIN; // User type is set to STUDENT

			$objJournal = new clsStudentMinCharacterEntry();
			$objJournal->saveMinCharacterEntryAuditLog($retCountId, $_SESSION["loggedUserId"], $userType, $action, $IsMobile = 0,$type='Journal');
			unset($objJournal);
			unset($objLog);
			//Audit Log End

				header('location:settings.html?status=added');
		}
		else
		{
			header('location:studentJournalEntryCount.html?status=error');
		}
	}
	else
	{
		header('location:settings.html');
		exit();
	}

?>