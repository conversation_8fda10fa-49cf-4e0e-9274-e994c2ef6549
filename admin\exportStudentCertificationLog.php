<?php
	include('includes/validateUserLogin.php');
    include('../includes/config.php');
    include('../class/clsDB.php');
    include('../includes/commonfun.php');
    include('../class/clsAccreditation.php');	
    include('../class/clsStudent.php'); 
    include('../class/clsCountryStateMaster.php'); 
    include('../class/clsStudentCertificationLog.php'); 
    include('../setRequest.php');
	
	require '../vendor/autoload.php';  // Include PhpSpreadsheet's autoloader

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\IOFactory;
	
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnStudentExport']))		
	$accreditationId=0; 
	$currentSchoolId = $_GET['currentSchoolId'];
	$currentSchoolId = DecodeQueryData($currentSchoolId);
	$studentId = $_GET['studentId'];
	$studentId = DecodeQueryData($studentId);
	$ChildAbuseState='';
	$BackgroundState='';
	
	//Object
	$objCountryStateMaster = new clsCountryStateMaster();
	$objStudentCertificationLog = new clsStudentCertificationLog();
	
	// Get Post Data
	$drugScreeningDate  = ($_POST['drugScreeningDate']);
	$criminalBackgroundCheck  = ($_POST['criminalBackgroundCheck']);
	$healthCertificationDate  = ($_POST['healthCertificationDate']);
	$aclsCertificationDate  = ($_POST['aclsCertificationDate']);
	$licenseExpiration  = ($_POST['licenseExpiration']);
	$licenseNumber  = ($_POST['txtLicenseNumber']);
	$palsNumber  = ($_POST['txtpalsNumber']);
	$bclsStatus  = ($_POST['bclsStatus']);
	$palsStatus  = ($_POST['palsStatus']);
	$aclsExpirationDate  = ($_POST['aclsExpirationDate']);
	$bclsCertificationDate  = ($_POST['bclsCertificationDate']);
	$bclsExpirationDate  = ($_POST['bclsExpirationDate']);
	$palsCertificationDate  = ($_POST['palsCertificationDate']);
	$palsExpirationDate  = ($_POST['palsExpirationDate']);
	$aclsNumber  = ($_POST['txtaclsNumber']);
	$licenseCredentials  = ($_POST['txtlicenseCredentials']);
	$bclsNumber  = ($_POST['txtbclsNumber']);
	$aclsStatus  = ($_POST['aclsStatus']);
	$insuranceCarrier  = ($_POST['txtinsuranceCarrier']);
	$insuranceNumber  = ($_POST['txtinsuranceNumber']);
	$FBIfingerprinting  = ($_POST['FBIfingerprinting']);
	$StateBackGroundCheckdate  = ($_POST['StateBackGroundCheckdate']);
	$DepartmentOfAgingDate  = ($_POST['DepartmentOfAgingDate']);
	$NRPCerificationDate  = ($_POST['NRPCerificationDate']);
	$NRPExpirationdate  = ($_POST['NRPExpirationdate']);
	$ChildAbusedate  = ($_POST['ChildAbusedate']);
	
	$ChildAbuseState  =($_POST['ChildAbuseState']);
	$ChildAbuseStateName = $objCountryStateMaster->GetSingleState($ChildAbuseState);
	
	$BackgroundState  = isset($_POST['BackgroundState']) ? ($_POST['BackgroundState']) : "";
	$covid1Date  = isset($_POST['covid1Date']) ? ($_POST['covid1Date']) : "";
	$covid2Date  = isset($_POST['covid2Date']) ? ($_POST['covid2Date']) : "";
	$covidBoosterDate  = isset($_POST['covidBoosterDate']) ? ($_POST['covidBoosterDate']) : "";
	
	// Get BackgroundStates
	
	$StudentCertificationLog = $objStudentCertificationLog->GetStudentCertificationLog($studentId, $BackgroundState);

	$BackgroundStateIds  = $StudentCertificationLog ? $StudentCertificationLog['BackgroundStateIds'] : '';
	$BackgroundStateIds = explode(',',$BackgroundStateIds);
	$BackgroundStateNames = array();
	$BackgroundStateNameArray = '';
	if(count($BackgroundStateIds))
	{
		foreach($BackgroundStateIds as $BackgroundStateId)
		{
			$BackgroundStateName = $objCountryStateMaster->GetSingleState($BackgroundStateId);
			$BackgroundStateNames[] = $BackgroundStateName;
		}
	}
	if(count($BackgroundStateIds))
	{
		$BackgroundStateNameArray = implode(',',$BackgroundStateNames);
	}
	 unset($objStudentCertificationLog);
	 unset($objCountryStateMaster);
	 
	// Object
	$objStudent = new clsStudent();
	$rowStudent = $objStudent->GetStudentDetails($studentId);
	unset($objStudent);
	$firstName=$rowStudent['firstName'];
	$lastName=$rowStudent['lastName'];
	$fullName=$firstName.' '.$lastName;
	// echo '<pre>'; 
	// print_r($_POST);
	// echo '</pre>'; 
	// exit;
	$title="Student Certification Log Information";
	date_default_timezone_set('Asia/Kolkata');
	$today= (date('m/d/Y, H:i A'));
				
	$spreadsheet = new Spreadsheet();
	
	// Set document properties
	$spreadsheet->getProperties()->setCreator('Schools')
								 ->setLastModifiedBy('JCC')
								 ->setTitle('Reports')
								 ->setSubject('Student Accreditation List')
								 ->setDescription('All School Reports');
								 
	//Active Sheet
	$spreadsheet->setActiveSheetIndex(0);
	$spreadsheet->getActiveSheet();
	
	//Print Heading	
	$headerstyleArray = array('font'  => array('bold'  => true,'size'  => 16));
	
	$spreadsheet->getActiveSheet()->mergeCells("B2:P2");
	$spreadsheet->getActiveSheet()->setCellValue('B2', $title);
	$spreadsheet->getActiveSheet()->getStyle('B2')->applyFromArray($headerstyleArray);
	$spreadsheet->getActiveSheet()->getStyle('B2')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('B2')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
		
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B2:P6')->applyFromArray($styleBorderArray);
	
	$styleArray = array('font'  => array('bold'  => true,'size'  => 12));
	$spreadsheet->getActiveSheet()->mergeCells("B3:P3");
	$spreadsheet->getActiveSheet()->mergeCells("B4:P4");
	$spreadsheet->getActiveSheet()->setCellValue('B4',$fullName);
	$spreadsheet->getActiveSheet()->getStyle('B4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('B4')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
				
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B4:P6')->applyFromArray($styleBorderArray);
	$spreadsheet->getActiveSheet()->mergeCells("B5:P5");
	//Make Table Heading
	$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
	
	$spreadsheet->getActiveSheet()->setCellValue('B6', 'Drug Screening Date');
	$spreadsheet->getActiveSheet()->getStyle('B6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B6')->setWidth('30');
	
	$spreadsheet->getActiveSheet()->setCellValue('C6', 'Criminal Background Check Date');
	$spreadsheet->getActiveSheet()->getStyle('C6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('C6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('D6', 'Health Certification Date');
	$spreadsheet->getActiveSheet()->getStyle('D6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('D6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('E6', 'ACLS Certification Date');
	$spreadsheet->getActiveSheet()->getStyle('E6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('E6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('F6', 'FBI Fingerprinting');
	$spreadsheet->getActiveSheet()->getStyle('F6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('F6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('G6', 'State Background Check');
	$spreadsheet->getActiveSheet()->getStyle('G6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('G6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('H6', 'State Background Check Date');
	$spreadsheet->getActiveSheet()->getStyle('H6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('H6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('I6', 'Child Abuse State');
	$spreadsheet->getActiveSheet()->getStyle('I6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('I6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('J6', 'Child Abuse Date');
	$spreadsheet->getActiveSheet()->getStyle('J6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('J6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('K6', 'License Expiration');
	$spreadsheet->getActiveSheet()->getStyle('K6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('K6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('L6', 'License Number');
	$spreadsheet->getActiveSheet()->getStyle('L6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('L6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('M6', 'PALS Number');
	$spreadsheet->getActiveSheet()->getStyle('M6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('M6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('N6', 'BCLS Status');
	$spreadsheet->getActiveSheet()->getStyle('N6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('N6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('O6', 'Insurance Carrier');
	$spreadsheet->getActiveSheet()->getStyle('O6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('O6')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

	$spreadsheet->getActiveSheet()->mergeCells("O6:P6");
	$spreadsheet->getActiveSheet()->getStyle('B6:P6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
	
	$printStartRowCounter = 7;
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B7:P7')->applyFromArray($styleBorderArray);
	$spreadsheet->getActiveSheet()->mergeCells("O7:P7");
	$spreadsheet->getActiveSheet()->setCellValue('B'.$printStartRowCounter, $drugScreeningDate);
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('C'.$printStartRowCounter, $criminalBackgroundCheck);
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('D'.$printStartRowCounter, $healthCertificationDate);
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('E'.$printStartRowCounter, $aclsCertificationDate);
	$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter)->applyFromArray($styleArray);
			
	$spreadsheet->getActiveSheet()->setCellValue('F'.$printStartRowCounter, $FBIfingerprinting);
	$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('G'.$printStartRowCounter, $BackgroundStateNameArray);
	$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('H'.$printStartRowCounter, $StateBackGroundCheckdate);
	$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('I'.$printStartRowCounter, $ChildAbuseStateName);
	$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter)->applyFromArray($styleArray);

	$spreadsheet->getActiveSheet()->setCellValue('J'.$printStartRowCounter, $ChildAbusedate);
	$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('K'.$printStartRowCounter, $licenseExpiration);
	$spreadsheet->getActiveSheet()->getStyle('K'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('K'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('L'.$printStartRowCounter, $licenseNumber);
	$spreadsheet->getActiveSheet()->getStyle('L'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('L'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('M'.$printStartRowCounter, $palsNumber);
	$spreadsheet->getActiveSheet()->getStyle('M'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('M'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('N'.$printStartRowCounter, $bclsStatus);
	$spreadsheet->getActiveSheet()->getStyle('N'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('N'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('O'.$printStartRowCounter, $insuranceCarrier);
	$spreadsheet->getActiveSheet()->getStyle('O'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	$spreadsheet->getActiveSheet()->getStyle('O'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->mergeCells("B8:P8");

	//Make Table Heading
	$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B9:P9')->applyFromArray($styleBorderArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('B9', 'ACLS Expiration Date');
	$spreadsheet->getActiveSheet()->getStyle('B9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B9')->setWidth('30');
	
	$spreadsheet->getActiveSheet()->setCellValue('C9', 'BCLS Certification Date');
	$spreadsheet->getActiveSheet()->getStyle('C9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('C9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('D9', 'BCLS Expiration Date');
	$spreadsheet->getActiveSheet()->getStyle('D9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('D9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('E9', 'PALS Certification Date');
	$spreadsheet->getActiveSheet()->getStyle('E9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('E9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('F9', 'PALS Expiration Date');
	$spreadsheet->getActiveSheet()->getStyle('F9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('F9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('G9', 'Department of Aging State');
	$spreadsheet->getActiveSheet()->getStyle('G9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('G9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('H9', 'Department of Aging Date');
	$spreadsheet->getActiveSheet()->getStyle('H9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('H9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('I9', 'NRP Certification Date');
	$spreadsheet->getActiveSheet()->getStyle('I9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('I9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('J9', 'NRP Expiration Date');
	$spreadsheet->getActiveSheet()->getStyle('J9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('J9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('K9', 'ACLS Number');
	$spreadsheet->getActiveSheet()->getStyle('K9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('K9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('L9', 'License Credentials');
	$spreadsheet->getActiveSheet()->getStyle('L9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('L9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('M9', 'BCLS Number');
	$spreadsheet->getActiveSheet()->getStyle('M9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('M9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('N9', 'Insurance Number');
	$spreadsheet->getActiveSheet()->getStyle('N9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('N9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('O9', 'ACLS Status');
	$spreadsheet->getActiveSheet()->getStyle('O9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('O9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('P9', 'PALS Status');
	$spreadsheet->getActiveSheet()->getStyle('P9')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('P9')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->getStyle('B9:P9')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
	
	$printStartRowCounter = 10;
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B10:P10')->applyFromArray($styleBorderArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('B'.$printStartRowCounter, $aclsExpirationDate);
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('C'.$printStartRowCounter, $bclsCertificationDate);
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('D'.$printStartRowCounter, $bclsExpirationDate);
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('E'.$printStartRowCounter, $palsCertificationDate);
	$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter)->applyFromArray($styleArray);
			
	$spreadsheet->getActiveSheet()->setCellValue('F'.$printStartRowCounter, $palsExpirationDate);
	$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('G'.$printStartRowCounter, $ChildAbuseStateName);
	$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('H'.$printStartRowCounter, $DepartmentOfAgingDate);
	$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('I'.$printStartRowCounter, $NRPCerificationDate);
	$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter)->applyFromArray($styleArray);

	$spreadsheet->getActiveSheet()->setCellValue('J'.$printStartRowCounter, $NRPExpirationdate);
	$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('K'.$printStartRowCounter, $aclsNumber);
	$spreadsheet->getActiveSheet()->getStyle('K'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('K'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('L'.$printStartRowCounter, $licenseCredentials);
	$spreadsheet->getActiveSheet()->getStyle('L'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('L'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('M'.$printStartRowCounter, $bclsNumber);
	$spreadsheet->getActiveSheet()->getStyle('M'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('M'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('N'.$printStartRowCounter, $insuranceNumber);
	$spreadsheet->getActiveSheet()->getStyle('N'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('N'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('O'.$printStartRowCounter, $aclsStatus);
	$spreadsheet->getActiveSheet()->getStyle('O'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('O'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('P'.$printStartRowCounter, $palsStatus);
	$spreadsheet->getActiveSheet()->getStyle('P'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('P'.$printStartRowCounter)->applyFromArray($styleArray);


	//Make Table Heading
	$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B12:D12')->applyFromArray($styleBorderArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('B12', 'COVID 19 I Date');
	$spreadsheet->getActiveSheet()->getStyle('B12')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B12')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('C12', 'COVID 19 II Date');
	$spreadsheet->getActiveSheet()->getStyle('C12')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('C12')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('D12', 'COVID 19 Booster Date');
	$spreadsheet->getActiveSheet()->getStyle('D12')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('D12')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->getStyle('B12:D12')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
	
	$printStartRowCounter = 13;
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B13:D13')->applyFromArray($styleBorderArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('B'.$printStartRowCounter, $covid1Date);
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('C'.$printStartRowCounter, $covid2Date);
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->applyFromArray($styleArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('D'.$printStartRowCounter, $covidBoosterDate);
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->applyFromArray($styleArray);
	

	$reportname='StudentCertificationLogReport_';	
	
	$spreadsheet->setActiveSheetIndex(0);
	
	$currentDate = date('m_d_Y_h_i');
	
	foreach ($spreadsheet->getAllSheets() as $sheet) 
	{
		for ($col = 0; $col <= \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($sheet->getHighestDataColumn()); $col++) {
			$sheet->getColumnDimensionByColumn($col)->setAutoSize(true);
		}
	}	

	$sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows

	
	header('Content-type: application/vnd.ms-excel; charset=UTF-8');
	header('Content-Disposition: attachment;filename="'.$reportname.$today.'.xls"');		
	header("Pragma: no-cache");
	header("Expires: 0");
	
	$writer = IOFactory::createWriter($spreadsheet, 'Xls');
    $writer->save('php://output');
?>