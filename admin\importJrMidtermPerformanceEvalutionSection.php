<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsCheckoffSectionMaster.php');
include('../class/clsCheckoffQuestionMaster.php');
include('../class/clsCheckoffTopicMaster.php');
include('../class/clsJuniorMidtermPerformanceEval.php');

if ($_SERVER['REQUEST_METHOD'] == "POST") {

    $objDB = new clsDB();

    $type = isset($_POST['type']) ? $_POST['type'] : '';


    ini_set('upload_max_filesize', '50M');
    ini_set('post_max_size', '50M');
    ini_set('max_input_time', 300000);
    ini_set('max_execution_time', 300000);

    $notifyMessage = 'Imported';
    $row = 1;
    $isExistmidTermPerformanceSectionId = 0;
    $isExistdfMidtermPerformanceQuestionId = 0;
    if (isset($_FILES['file'])) {
        $filename = $_FILES["file"]["tmp_name"];
        $ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));

        if ($ext != "csv") {
            $error = "Upload only .csv file.";
            header('location:checkofftopics.html?status=Importerror');
            exit();
        }

        if ($_FILES["file"]["size"] > 0) {

            $file = fopen($filename, "r");
            $counter = 0;

            $retStudentId = 0;
            $questionSortOrder = 0;

            while (($getData = fgetcsv($file, 10000, ",")) !== FALSE) {
                // echo $type;exit; 
                if ($row == 1) {
                    $row++;
                    continue;
                }

                $sectionTitle = trim($getData[1]);
                $optionText = trim($getData[2]);
                $discription = trim($getData[3]);
                $optionType = trim($getData[4]);
                $optionValues = trim($getData[5]);
                $sortOrder = trim($getData[6]);
                $sectionSortOrder = trim($getData[7]);

               $isExistmidTermPerformanceSectionId= $objDB->GetSingleColumnValueFromTable('defaultmidtermperformanceevalsectionmaster', 'dfmidTermPerformanceSectionId', 'title', $sectionTitle);

                if($sectionTitle != '')
                {
                    if (!$isExistmidTermPerformanceSectionId) {

                         $objJuniorMidtermPerformanceEval = new clsJuniorMidtermPerformanceEval();
                         $objJuniorMidtermPerformanceEval->title = $sectionTitle;
                         $objJuniorMidtermPerformanceEval->sortOrder = $sectionSortOrder; 
                         $isExistmidTermPerformanceSectionId=$objJuniorMidtermPerformanceEval->SaveAdminJrMidtermPerEvalSection(0);                    
                    }
                }


               $isExistdfMidtermPerformanceQuestionId = $objDB->GetSingleColumnValueFromTable('defaultmidtermperformanceevalquestionmaster', 'dfMidtermPerformanceQuestionId', 'optionText', addslashes($optionText));

                if (!$isExistdfMidtermPerformanceQuestionId) {
                    $questionSortOrder++;
                    $objJuniorMidtermPerformanceEval = new clsJuniorMidtermPerformanceEval();
                    $objJuniorMidtermPerformanceEval->optionText = $optionText;
                    $objJuniorMidtermPerformanceEval->midTermQuestionType = $optionType; 
                    $objJuniorMidtermPerformanceEval->dfmidTermPerformanceSectionId = $isExistmidTermPerformanceSectionId; 
                    $objJuniorMidtermPerformanceEval->sortOrder = $questionSortOrder; 
                    $isExistdfMidtermPerformanceQuestionId=$objJuniorMidtermPerformanceEval->SaveAdminJrMidtermPerEvalQuestions(0);   

                    $questionSortOrder++;
                    $objJuniorMidtermPerformanceEval->optionText = 'Comments';
                    $objJuniorMidtermPerformanceEval->midTermQuestionType = 9; 
                    $objJuniorMidtermPerformanceEval->dfmidTermPerformanceSectionId = $isExistmidTermPerformanceSectionId; 
                    $objJuniorMidtermPerformanceEval->sortOrder = $questionSortOrder; 
                    $dfMidtermPerformanceQuestionId=$objJuniorMidtermPerformanceEval->SaveAdminJrMidtermPerEvalQuestions(0);   

                }

                if($isExistdfMidtermPerformanceQuestionId)
                {   
                    $isExistdfMidtermPerformanceQuestionId = ($optionValues =='') ? $dfMidtermPerformanceQuestionId : $isExistdfMidtermPerformanceQuestionId;
                    
                    $objJuniorMidtermPerformanceEval->dfMidtermPerformanceQuestionId = $isExistdfMidtermPerformanceQuestionId;
                    $objJuniorMidtermPerformanceEval->optionText = $optionValues; 
                    $objJuniorMidtermPerformanceEval->description = $discription; 
                    $objJuniorMidtermPerformanceEval->sortOrder = $sortOrder; 
                    $objJuniorMidtermPerformanceEval->SaveAdminJrMidtermPerEvalQuestionsDtls(0);
                }
                $result = 1;
            }
            fclose($file);

            $messageText = $result ? 'Imported' : 'Error';
            header('location:settings.html?status=' . $messageText);

            exit();
        }
    }
}
