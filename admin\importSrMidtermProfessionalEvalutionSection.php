<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsCheckoffSectionMaster.php');
include('../class/clsCheckoffQuestionMaster.php');
include('../class/clsCheckoffTopicMaster.php');
include('../class/clsSrMidtermProfessionalEval.php');
include('../class/clsPerformance.php');

if ($_SERVER['REQUEST_METHOD'] == "POST") {

    $objDB = new clsDB();

    $type = isset($_POST['type']) ? $_POST['type'] : '';


    ini_set('upload_max_filesize', '50M');
    ini_set('post_max_size', '50M');
    ini_set('max_input_time', 300000);
    ini_set('max_execution_time', 300000);

    $notifyMessage = 'Imported';
    $row = 1;
    $isExistmidTermPerformanceSectionId = 0;
    $isExistdfMidtermPerformanceQuestionId = 0;
    if (isset($_FILES['file'])) {
        $filename = $_FILES["file"]["tmp_name"];
        $ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));

        if ($ext != "csv") {
            $error = "Upload only .csv file.";
            header('location:settings.html?status=Importerror');
            exit();
        }

        if ($_FILES["file"]["size"] > 0) {

            $file = fopen($filename, "r");
            $counter = 0;

            $retStudentId = 0;
            $questionSortOrder = 0;


            while (($getData = fgetcsv($file, 10000, ",")) !== FALSE) {
                // echo $type;exit; 
                if ($row == 1) {
                    $row++;
                    continue;
                } 

            //   echo  
              $sectionTitle = trim($getData[1]);
            //   echo "<br>";
            //   echo 
               $optionText = trim($getData[2]);
            //   echo "<br>";
            //   echo 
              $discription = trim($getData[3]);
            //   echo "<br>";
            //   echo 
              $optionType = trim($getData[4]);
            //   echo "<br>";
            //   echo  
              $optionValues = trim($getData[5]);
            //   echo "<br>";
            //   echo 
              $sortOrder = trim($getData[6]);
            //   echo "<br>";
            //   echo
               $sectionSortOrder = trim($getData[7]);
            //   echo "<br>";
            //   echo "<br>";

                $isExistdfSrmidTermPerformanceSectionId= $objDB->GetSingleColumnValueFromTable('defaultsrmidtermprofessionalevalsectionmaster', 'dfSrmidTermProfessionalSectionId', 'title', addslashes($sectionTitle));

                if($sectionTitle != '')
                {
                    if (!$isExistdfSrmidTermPerformanceSectionId) {

                         $objSrMidtermProfessionalEval = new clsSrMidtermProfessionalEval();
                         $objSrMidtermProfessionalEval->title = $sectionTitle;
                         $objSrMidtermProfessionalEval->sortOrder = $sectionSortOrder; 
                         $isExistdfSrmidTermPerformanceSectionId=$objSrMidtermProfessionalEval->SaveAdminSrMidtermProfessionalEvalSection(0);                    
                    }
                }

                // if($optionText != 'Personal Appearance')
                // {    
                    $isExistdfSrMidtermProfessionalQuestionId = $objDB->GetSingleColumnValueFromTable('defaultsrmidtermprofessionalevalquestionmaster', 'dfSrMidtermProfessionalQuestionId', 'optionText', ($optionText));
                // }

                if (!$isExistdfSrMidtermProfessionalQuestionId) {
                    $questionSortOrder++;

                    
                    $objSrMidtermProfessionalEval = new clsSrMidtermProfessionalEval();
                    $objSrMidtermProfessionalEval->optionText = $optionText;
                    $objSrMidtermProfessionalEval->midTermQuestionType = $optionType; 
                    $objSrMidtermProfessionalEval->dfSrmidTermProfessionalSectionId = $isExistdfSrmidTermPerformanceSectionId; 
                    $objSrMidtermProfessionalEval->sortOrder = $questionSortOrder; 
                    $isExistdfSrMidtermProfessionalQuestionId=$objSrMidtermProfessionalEval->SaveAdminSrMidtermProfessionalEvalQuestions(0);   

                    $questionSortOrder++;

                    $objSrMidtermProfessionalEval->optionText = 'Comments';
                    $objSrMidtermProfessionalEval->midTermQuestionType = 9; 
                    $objSrMidtermProfessionalEval->dfSrmidTermProfessionalSectionId = $isExistdfSrmidTermPerformanceSectionId; 
                    $objSrMidtermProfessionalEval->sortOrder = $questionSortOrder; 
                    $dfSrMidtermProfessionalQuestionId=$objSrMidtermProfessionalEval->SaveAdminSrMidtermProfessionalEvalQuestions(0);   

                }

                if($isExistdfSrMidtermProfessionalQuestionId)
                {
                    $isExistdfSrMidtermProfessionalQuestionId = ($optionValues =='') ? $dfSrMidtermProfessionalQuestionId : $isExistdfSrMidtermProfessionalQuestionId;

                    $objSrMidtermProfessionalEval->dfSrMidtermProfessionalQuestionId = $isExistdfSrMidtermProfessionalQuestionId;
                    $objSrMidtermProfessionalEval->optionText = $optionValues; 
                    $objSrMidtermProfessionalEval->description = $discription; 
                    $objSrMidtermProfessionalEval->SaveAdminSrMidtermProfessionalEvalQuestionsDtls(0);
                }
                $result=1;
            }
            // exit;

            fclose($file);

            $messageText = $result ? 'Imported' : 'Error';
            header('location:settings.html?status=' . $messageText);

            exit();
        }
    }
}
