<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');
    include('../class/clsSchool.php');    
    include('../class/clsMasterCheckoffQuestion.php');    
    include('../class/clsMasterCheckoffSection.php');    
    include('../class/clsMasterCheckoffTopic.php');    
    include('../setRequest.php'); 	
	
	$defaultTopicId=0;
	$encodedDefaultTopicId=0;
	$encodedDefaultSectionId=0;
	$currentSchoolId;
	if(isset($_GET['topicid']))
	{
		$encodedDefaultTopicId=$_GET['topicid'];
		$defaultTopicId = DecodeQueryData($_GET['topicid']);
	}
	if(isset($_GET['sectionId']))
	{
		$encodedDefaultSectionId=$_GET['sectionId'];
		$sectionId=DecodeQueryData($_GET['sectionId']);
	}
	$objQuestionMaster = new clsMasterCheckoffQuestion();
	$rowsQuestionData=$objQuestionMaster->GetAllCheckoffQuestions($sectionId,$defaultTopicId);
	$totalCount = 0;
	if($rowsQuestionData !='')
	{
		$totalCount = mysqli_num_rows($rowsQuestionData);
	}	
	unset($objQuestionMaster);
	
	$objCheckoffSectionMaster=new clsMasterCheckoffSection();
    $GetSingleSection=$objCheckoffSectionMaster->GetCheckoffSctionByCheckoffSectionId($sectionId);
	$SingleSection=$GetSingleSection['title'];
	unset($objCheckoffSectionMaster);
	
	$objCheckoffTopicMaster=new clsMasterCheckoffTopic();
    $GetSingleTopicId=$objCheckoffTopicMaster->GetSingleCheckoffTopicId($defaultTopicId,$sectionId);
	$title=$GetSingleTopicId['title'] ?? '';
	unset($objCheckoffTopicMaster);
		
		
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Comps Steps</title>
        <?php include('includes/headercss.php');?>
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
        <?php include("includes/datatablecss.php") ?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
           <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">

   </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>
						<li><a href="settings.html">Setting	</a></li>
                        <li><a href="mastercheckofftopic.html">Comps Topic</a></li>	
                        <li><a title="<?php echo ($title); ?>" href="mastercheckoffsection.html?topicid=<?php echo EncodeQueryData($defaultTopicId); ?>&sectionId=<?php echo EncodeQueryData($sectionId); ?>"><?php echo  substr($title,0,21).(' - ' . $SingleSection); ?>-Comps Section</a></li>
                        <li class="active">Steps</li>
                    </ol>
                </div>
              <div class="pull-right">
				<ol class="breadcrumb">
                    <a href="masteraddquestion.html?sectionId=<?php echo EncodeQueryData($sectionId); ?>&topicid=<?php echo EncodeQueryData($defaultTopicId); ?>">Add</a> 
                  </ol>  
               </div>
            </div>
        </div>

        <div class="container">

					<?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Question added successfully.
                </div>
                <?php 
					}
					
				}?>
                <div id="divTopLoading" >Loading...</div>
				
					<form name="checkoffquestion" id="checkoffquestion" data-parsley-validate method="POST" action="questionsubmit.html">
						<div class="row">
						<div class="col-md-10  margin_bottom_ten"></div>
						<div class="col-md-2  margin_bottom_ten">
						
								<div class="form-group">
									<!--button id="btncoarcrequest" name="btncoarcrequest" class="btn btn-success">Send CoARC Survey</button--->
								</div>
						</div>
					
					</div>	
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr >
							
                           <th>Select<!--input type="checkbox" id="selectall" class="check selectall checkall" value=""---></th>
                           <th>Step Number</th>                                                   
                           <th>Comps Steps Title</th> 
                           <th>Type</th> 						   
                           <th style="text-align: center">Action</th>                                                   
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalCount > 0)
                        {
                            while($row = mysqli_fetch_array($rowsQuestionData))
                            {
								
                                $questionId = ($row[0]);					
                                $schoolQuestionFullTitle = stripslashes($row['questionTitle']);
                                $CheckdQueId =($row['CheckdQueId']);
                                $QueId =($row['QueId']);
                                $sortOrder =($row['sortOrder']);
                                $questiontype =($row['questiontype']);
								if($CheckdQueId > 0)
								{
									$actiontype="false";
								}
								else
								{
									$actiontype="true";
								}
								
								$shortTitlelen=strlen($schoolQuestionFullTitle);
								
								if($shortTitlelen > 80)
								{
								   
								    $questionTitle=substr($schoolQuestionFullTitle,0,80);
									$questionTitle .= '...';
							      
								}else{
								    $questionTitle=$schoolQuestionFullTitle;
								}
								
                               ?>
                            <tr>
								
								<td style="text-align: center"> 
								<input questionId="<?php echo EncodeQueryData($questionId);?>" SchoolId="<?php echo EncodeQueryData($currentSchoolId);?>"   type="checkbox"  id="checkoffquestion" name="checkoffquestion[]" value=""  actiontype="<?php echo ($actiontype); ?>" defaultTopicId="<?php echo EncodeQueryData($defaultTopicId);?>" sectionId="<?php echo EncodeQueryData($sectionId);?>" <?php if($CheckdQueId>0){?> checked  class="checkoffquestion sendrequest chkque"<?php }else { ?> class="sendrequest chkque" <?php } ?>>

								</td>
								<td style="text-align: center"><?php echo ($sortOrder); ?></td>
                                <td title="<?php echo ($schoolQuestionFullTitle); ?>">
                                    <?php echo($questionTitle); 				
									?>
                                </td>
								<td><?php echo ($questiontype); ?></td>
								<td style="text-align: center"><a class="addCommentpopup " title="Preview Question Details" href="masterquestiondetails.html?questionId=<?php echo $questionId;  ?>">Preview</a>				
								</td>
								
								

                            </tr>
                            <?php
							}
                        }
                    ?>
					</tbody>
                </table>
				</form>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>

        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

            $(window).load(function(){
				
				
					var TotalCheckboxCount = $('input[name="checkoffquestion[]"]').length;                       
					var CheckedCheckboxCount = $('input[name="checkoffquestion[]"]:checked').length;
					if(TotalCheckboxCount==CheckedCheckboxCount)
					{
						$('.selectall').prop('checked', true);
						
					}else{
						$('.selectall').prop('checked', false);
						
					}		
				
					
			
			
			
                $("#divTopLoading").addClass('hide');
					var current_datatable = $("#datatable-responsive").DataTable({
					 "aoColumns": [{
                    "sWidth": "1%",					
					"bSortable": false
                },{
                    "sWidth": "1%"
                },{
                    "sWidth": "20%"
				  },{
                    "sWidth": "1%"
                },{
                    "sWidth": "5%",
					"bSortable": false					
                }],
				
				// "aLengthMenu": [[100, 200, 300, 400,500, -1], [100, 200, 300, 400,500, "All"]],
                    "iDisplayLength": 250,
				 }); 
            });
			
			
			$('.chkque').click(function() {         
					var TotalCheckboxCount = $('input[name="checkoffquestion[]"]').length;                       
					var CheckedCheckboxCount = $('input[name="checkoffquestion[]"]:checked').length;
					if(TotalCheckboxCount==CheckedCheckboxCount)
					{
						$('.selectall').prop('checked', true);
						
					}else{
						$('.selectall').prop('checked', false);
						
					}		
				
					
			});
			
			$('.addCommentpopup').magnificPopup({'type': 'ajax',});
	
	$('#selectall').click(function() {				
				if ($(this).is(':checked')) {					
					$('input:checkbox').prop('checked', true);
					var ischeckall=1;
				} else {					
					//$('input:checkbox').prop('checked', false);
					$('input').filter(':checkbox').removeAttr('checked');
					var ischeckall=0;
				}
				
				
				
					var defaultTopicId = '<?php echo ($encodedDefaultTopicId); ?>';
					var sectionId = '<?php echo ($encodedDefaultSectionId); ?>';					
					
				
				$.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/assign_master_questions.html",
                        data: {	
							  defaultTopicId: defaultTopicId,
							  sectionId: sectionId,							  
							  ischeckall: ischeckall,
							  type: 'assign_all_questions'								  
                        },
						 success: function() {                           
                            if(ischeckall == 1){ //Assigned 
                            	alertify.success('Assigned');
							 }
								
							else if(ischeckall == 0) {//Removed
                            	alertify.error('Removed');
							 }
                        }
                    });
				});
				
			
			
         
								
				
			//FOR SEND REQUEST 1 by 1
			$("#datatable-responsive").on("click", ".sendrequest", function() {
			//$('#datatable-responsive .sendrequest').on('click', function(){
			//$(".sendrequest").change(function(){	
			
					var action;
					var thischeck= $(this);
			       var questionId = $(this).attr('questionId');			       
			       var actiontype = $(this).attr('actiontype');
					var defaultTopicId = $(this).attr('defaultTopicId');                 
			       var sectionId = $(this).attr('sectionId'); 
			        
					$.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/assign_master_questions.html",
                        data: {							
						      id: questionId,
							  action : actiontype,							  
							  defaultTopicId: defaultTopicId,
							  sectionId: sectionId,
							  type: 'assign_questions'								  
                        },
						 success: function() {                           
                            if(actiontype == 'true'){ //Assigned 
                            	alertify.success('Assigned');
							thischeck.attr('actiontype','false'); }
								
							else if(actiontype == 'false') {//Removed
                            	alertify.error('Removed');
							thischeck.attr('actiontype','true'); }
                        }
                    });
			        
			});
				

        </script>
    </body>
    </html>