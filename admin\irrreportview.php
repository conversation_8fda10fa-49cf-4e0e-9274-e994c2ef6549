<?php
   include('includes/validateUserLogin.php'); 
   include('../includes/config.php'); 
   include('../includes/commonfun.php'); 
   include('../class/clsDB.php');
   include('../setRequest.php');  
   include('../class/clsQuestionOption.php');  
   include('../class/clsClinician.php');
   include('../class/clsIrr.php');
   include('../class/clscheckoff.php'); 
   	
   
   $loggedUserLocationId = '';	
   $currentSchoolId;
   $tranSchoolDisplayname = $currenschoolDisplayname;
   
   $schoolTopicId = 0;   	
   $clinicianId = 0;  
   $preceptorId = 0;
   $title = '';   
   $id='';
   $totalrowscheckoff = 0;   
   $TimeZone='';
   $TimeZone= $TimeZone=$_SESSION["loggedUserSchoolTimeZone"];   
   if(isset($_GET['irrMasterId']))
   {
   	
      $page_title ="View IRR Report";
      $bedCrumTitle = 'View Report';
      
      $irrMasterId = DecodeQueryData($_GET['irrMasterId']);   
      
      $objIrr = new clsIrr();      
   	if(isset($_GET['schoolTopicId'])) 
      {
      $schoolTopicId = ($_GET['schoolTopicId']);
      $schoolTopicId = DecodeQueryData($schoolTopicId);
      }
   	 //For Checkoff Section
      $objcheckoff = new clscheckoff();
      $rowscheckoff = $objcheckoff->GetDefaulttopicDetails($schoolTopicId);
      
      if($rowscheckoff !='')
      {       
   	   $totalrowscheckoff =mysqli_num_rows($rowscheckoff);        
      } 
      
      if(isset($_GET['clinicianId'])) 
      {
         $clinicianId = DecodeQueryData($_GET['clinicianId']);
         $clinicianId= DecodeQueryData($_GET['clinicianId']);	
      }

      if(isset($_GET['preceptorId'])) 
      {
         $preceptorId = DecodeQueryData($_GET['preceptorId']);
         $preceptorId= DecodeQueryData($_GET['preceptorId']);	
      }
      $objClinician=new clsClinician();
      $clinician=$objClinician->GetIrrClinician($currentSchoolId,$irrMasterId);
      unset($objClinician);

      $rsIrrPreceptors = $objIrr->GetIrrPreceptors($irrMasterId);
      $totalIrrPreceptors = ($rsIrrPreceptors != '') ? mysqli_num_rows($rsIrrPreceptors):0;
   
   }  
   $reportType='Irr_Report';
    
   
   ?>
<!DOCTYPE html>
<html lang="en">
   <head>
      <meta charset="utf-8">
      <meta http-equiv="X-UA-Compatible" content="IE=edge">
      <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
      <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
      <title><?php echo($page_title); ?></title>
      <?php include('includes/headercss.php');?>
      <?php include("includes/datatablecss.php") ?>
      <link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
      <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
      <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
      <style>
         .text-color{
         color: red;
         }
      </style>
   </head>
   <body>
      <?php include('includes/header.php');?>
      <div class="row margin_zero breadcrumb-bg">
         <div class="container">
            <div class="pull-left">
               <ol class="breadcrumb">
                  <li><a href="dashboard.html">Home</a></li>
                  <li><a href="settings.html">Settings</a></li>
                  <li><a href="irrreport.html">IRR Report</a></li>
                  <li class="active"><?php echo($bedCrumTitle);?></li>
               </ol>
            </div>
            <div class="pull-right">
               <form id="frmexportreport" data-parsley-validate class="form-horizontal" method="POST" action="exportirrReport.html?Type=<?php echo ($reportType); ?>&currentSchoolId=<?php echo(EncodeQueryData($currentSchoolId)); ?>&irrMasterId=<?php echo(EncodeQueryData($irrMasterId)); ?>&schoolTopicId=<?php echo(EncodeQueryData($schoolTopicId)); ?>&clinicianId=<?php echo(EncodeQueryData($clinicianId)); ?>&preceptorId=<?php echo(EncodeQueryData($preceptorId)); ?>" enctype="multipart/form-data">
                  <input type="hidden" name="cboreporttype" value="<?php echo ($reportType); ?>">
                  <input type="submit" name="btnirrExport" id="btnirrExport" class="btn btn-link" value="Export to Excel">
               </form>
            </div>
         </div>
      </div>
      <div class="container">
         <?php 
            if(isset($_GET["status"]))
            {
                    if($_GET["status"] =="error")
                    {
            						?>
         <div class="alert alert-danger alert-dismissible fade in" role="alert">
            <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
            </button> Error occurred.
         </div>
         <?php   }
            } ?>
            <div class="row">
            <div class="col-md-4 pull-right padding_zero  margin_zero" >
            <div class="form-group">
               <div class="col-md-3">
                  <label class="control-label" for="cboPreceptor" style="margin-top:8px">Preceptor:</label>
               </div>
               <div class="col-md-9">
                  <select  id="cboPreceptor" name="cboPreceptor" class="form-control input-md  select2_single">
                     <option value="" selected>Select</option>
                     <?php
                        if($totalIrrPreceptors)
                        {
                           while($row = mysqli_fetch_assoc($rsIrrPreceptors))
                           {
                              $selpreceptorId  = $row['id'];
                              $preceptorfirstName  = stripslashes($row['firstName']);
                              $preceptorlastName  = stripslashes($row['lastName']);
                              $preceptorname  = $preceptorfirstName . ' ' . $preceptorlastName;
                     
                              ?>
                              <option value="<?php echo EncodeQueryData($selpreceptorId); ?>" <?php if($preceptorId==$selpreceptorId){ ?>  selected="true" <?php } ?>><?php echo($preceptorname); ?></option>
                              <?php
                           }
                        }
                        ?>
                  </select>
               </div>
            </div>
         </div>
         <div class="col-md-4 pull-right padding_zero  margin_zero" >
            <div class="form-group">
               <div class="col-md-3">
                  <label class="control-label" for="cboclinician" style="margin-top:8px">Clinician:</label>
               </div>
               <div class="col-md-9">
                  <select  id="cboclinician" name="cboclinician" class="form-control input-md  select2_single">
                     <option value="" selected>Select</option>
                     <?php
                        if($clinician!="")
                        {
                           while($row = mysqli_fetch_assoc($clinician))
                           {
                              $selclinicianId  = $row['clinicianId'];
                              $firstName  = stripslashes($row['firstName']);
                              $lastName  = stripslashes($row['lastName']);
                              $name  = $firstName . ' ' . $lastName;
                     
                              ?>
                              <option value="<?php echo EncodeQueryData($selclinicianId); ?>" <?php if($clinicianId==$selclinicianId){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
                              <?php
                           }
                        }
                        ?>
                  </select>
               </div>
            </div>
         </div>
            </div>
         
         <br><br>
         <form id="frmIRR" data-parsley-validate class="form-horizontal" method="POST">
            <div class="row">
               <div class="col-md-12">
                  <div class="panel-group" id="posts">
                     <?php  
                        if($totalrowscheckoff>0)
                        {
                           			
                           while($row = mysqli_fetch_array($rowscheckoff))
                           {
                              $schoolSectionId = $row['schoolSectionId'];
                              $schoolSectionTitle = $row['schoolSectionTitle'];
                              $sortOrder = $row['sortOrder'];
                              
                              if($sortOrder == 4)
                                 continue;
                              
                              ?>  
                              <div class="panel panel-default">
                                 <div class="panel-heading">
                                    <h4 class="panel-title">  
                                       <a href="#collapse1" data-toggle="collapse" data-parent="#accordion"><?php echo $schoolSectionTitle; ?></a>  
                                    </h4>
                                 </div>
                                 <div  class="panel-body">
                                    <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover mytablecustom" cellspacing="0" width="100%">
                                       <thead>
                                          <tr>
                                             <th>STEPS</th>
                                             <th>RATING</th>
                                             <th>SCORE</th>
                                             <th>YES</th>
                                             <th>NO</th>
                                             <th>100</th>
                                             <th>88</th>
                                             <th>75</th>
                                             <th>63</th>
                                             <th>TOT</th>
                                             <th>YES</th>
                                             <th>NO</th>
                                             <th>100</th>
                                             <th>88</th>
                                             <th>75</th>
                                             <th>63</th>
                                          </tr>
                                       </thead>
                                       <tbody>
                                          <?php 
                                             // for question
                                                $rowsquistion= $objcheckoff->GetschooldefaultquestionforIRRReport($schoolSectionId);
                                                if($rowsquistion !='')
                                                {       
                                                   $totalquistion =mysqli_num_rows($rowsquistion);        
                                                }  
                                                if($totalquistion>0)
                                                {
                                                   while($row = mysqli_fetch_array($rowsquistion))
                                                   { 
                                                      $schoolQuestionTitle = $row['schoolQuestionTitle'];													
                                                      $schoolQuestionId = $row['schoolQuestionId'];	
                                                      $schoolQuestionType = $row['schoolQuestionType'];
                                                                                          
                                                      if($schoolQuestionType == 5)
                                                         continue;
                                                                              
                                                   
                                                      $GetCal=$objcheckoff->GetCalculationsforIRRReport($schoolSectionId,$schoolQuestionId,$clinicianId,$irrMasterId,$preceptorId);
                                                
                                                      while($GetCalrows = mysqli_fetch_array($GetCal))
                                                      {
                                                         $YesCount = $GetCalrows['YesCount'];
                                                         $NoCount = $GetCalrows['NoCount'];
                                                         $TotalyesNoCount=$YesCount + $NoCount;															 
                                                         $Avalue = $GetCalrows['A'];
                                                         $Bvalue = $GetCalrows['B'];
                                                         $Cvalue = $GetCalrows['C'];
                                                         $Dvalue = $GetCalrows['D'];
                                                         $TotalABCDCount=$Avalue+$Bvalue+$Cvalue+$Dvalue;
                                                            
                                                         $rowsirrdetails = $objIrr->GetSingleIRRDetailsForAdmin($irrMasterId,$currentSchoolId); 
                                                         while($rows = mysqli_fetch_array($rowsirrdetails))
                                                         {
                                                            $irrMasterId = $rows['irrMasterId'];
                                                            
                                                            $cliniciancountrow=$objIrr->GetClinicianCount($irrMasterId,$clinicianId,$preceptorId);
                                                            $cliniciancount = stripslashes($cliniciancountrow['clinicianId']);
                                                            $Avalueinperc=0;
                                                            if($TotalABCDCount > 0)
                                                            {
                                                               $Avalueinperc=$Avalue*100/$TotalABCDCount;
                                                               $Bvalueinperc=$Bvalue*100/$TotalABCDCount;
                                                               $Cvalueinperc=$Cvalue*100/$TotalABCDCount;
                                                               $Dvalueinperc=$Dvalue*100/$TotalABCDCount;
                                                            }
                                                            else
                                                            {
                                                               $Avalueinperc=$Avalue*100/$cliniciancount;
                                                               $Bvalueinperc=$Bvalue*100/$cliniciancount;
                                                               $Cvalueinperc=$Cvalue*100/$cliniciancount;
                                                               $Dvalueinperc=$Dvalue*100/$cliniciancount;
                                                            }
                                                            
                                    
                                                            $YesCountinperc=0;
                                                            if($TotalyesNoCount > 0)
                                                            {
                                                               $YesCountinperc=$YesCount*100/$TotalyesNoCount;
                                                               $NoCountinperc=$NoCount*100/$TotalyesNoCount;
                                                            }
                                                            else 
                                                            {
                                                               $YesCountinperc=$YesCount*100/$cliniciancount;
                                                               $NoCountinperc=$NoCount*100/$cliniciancount;
                                                            }
                                                            $Totalsubmited=0;

                                                            if($TotalyesNoCount > 0)
                                                               $Totalsubmited=$TotalyesNoCount;
                                                            else 
                                                               $Totalsubmited=$TotalABCDCount;

                                                            $Score=0;
                                                            $Rating='';
                                                            if($YesCountinperc > 0 || $NoCountinperc > 0)
                                                            {	
                                                               if($YesCountinperc > $NoCountinperc)
                                                                  $Score=$YesCountinperc;
                                                               else 
                                                                  $Score=$NoCountinperc;
                                                               
                                                            
                                                            }	
                                                            elseif($Avalueinperc > 0 || $Bvalueinperc > 0 || $Cvalueinperc > 0 || $Dvalueinperc > 0)
                                                            {
																$totalAvalue = 0;
																$totalBvalue = 0;
																$totalCvalue = 0;
																$totalDvalue = 0;
																
																if($Avalue)
																	$totalAvalue = 100*$Avalue;
																if($Bvalue)
																	$totalBvalue = 88*$Bvalue;
																if($Cvalue)
																	$totalCvalue = 75*$Cvalue;
																if($Dvalue)
																	$totalDvalue = 63*$Dvalue;
																
																$Score = ($totalAvalue+$totalBvalue+$totalCvalue+$totalDvalue )/$TotalABCDCount;
																
                                                            }
                                                            
                                                            //For Rating 
                                                            if($Score >= 70)
                                                            {
                                                               $Rating='Satisfactory';
                                                               $buttoncss = "text-secondary";
                                                            }
                                                            else
                                                            {
                                                               $Rating='Unsatisfactory ';
                                                               $buttoncss = "text-color";
                                                            }
                                                                     
                                    
                                                            ?>													  
                                                            <tr>
                                                               <td style="white-space: normal;word-wrap: break-word;"><?php echo ($schoolQuestionTitle); ?></td>
                                                               <td class="<?php echo($buttoncss); ?>"><b><?php echo ($Rating); ?></b></td>
                                                               <td class="<?php echo($buttoncss); ?>"><?php echo number_format((float)$Score, 2, '.', '').'%'; ?></td>
                                                               <td><?php echo number_format((float)$YesCountinperc, 2, '.', '').'%'; ?></td>
                                                               <td><?php echo number_format((float)$NoCountinperc, 2, '.', '').'%'; ?></td>
                                                               <td><?php echo number_format((float)$Avalueinperc, 2, '.', '').'%'; ?></td>
                                                               <td><?php echo number_format((float)$Bvalueinperc, 2, '.', '').'%'; ?></td>
                                                               <td><?php echo number_format((float)$Cvalueinperc, 2, '.', '').'%'; ?></td>
                                                               <td><?php echo number_format((float)$Dvalueinperc, 2, '.', '').'%'; ?></td>
                                                               <td><?php echo ($Totalsubmited); ?></td>
                                                               <td><?php echo ($YesCount); ?></td>
                                                               <td><?php echo ($NoCount); ?></td>
                                                               <td><?php echo ($Avalue); ?></td>
                                                               <td><?php echo ($Bvalue); ?></td>
                                                               <td><?php echo ($Cvalue); ?></td>
                                                               <td><?php echo ($Dvalue); ?></td>
                                                            </tr>
                                                            <?php 
                                                         }
                                                      }
                                                   } 
                                                }				
                                             ?>
                                       </tbody>
                                    </table>
                                 </div>
                              </div>
                              <br> 
                              <?php  
                           } 
                        }				
                        ?>  
                  </div>
               </div>
            </div>
      </div>
      </div>
      </form>
      </div>
      <?php include('includes/footer.php');?>
      <?php include("includes/datatablejs.php") ?>
      <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
      <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
      <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
      <script type="text/javascript">
         $(window).load(function(){			
         $(".select2_single").select2();
           var current_datatable =  $(".mytablecustom").DataTable({	
         "paging":   false,
         "ordering": false,
         "info":     false,
         "searching": false,
         "aoColumns": [{
                    "sWidth": "10%",
         
                },{
                    "sWidth": "1%",
         
                },{
                    "sWidth": "1%",
         
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter"
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter"
                    
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter"
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter",
         "bSortable":false
                }, {
                    "sWidth": "1%",
                    "sClass": "alignCenter",
         "bSortable":false
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter",
         "bSortable":false
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter",
         "bSortable":false
                },
         {
                    "sWidth": "1%",
                    "sClass": "alignCenter",
         "bSortable":false
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter",
         "bSortable":false
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter",
         "bSortable":false
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter",
         "bSortable":false
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter",
         "bSortable":false
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter",
         "bSortable":false
                }]
         }); 
         
         
         
         
              $('#frmIRR').parsley().on('field:validated', function() {
         var ok = $('.parsley-error').length === 0;
             })
             .on('form:submit', function() {   
         
                 ShowProgressAnimation();									  
                 return true; // Don't submit form for this demo
             });
         
         
         });
         
         $("#cboclinician").change(function(){
         var clinicianId = $(this).val();
         var irrMasterId = "<?php echo EncodeQueryData($irrMasterId); ?>";
         var schoolTopicId = "<?php echo EncodeQueryData($schoolTopicId); ?>";
         
         if(clinicianId)
         {
         window.location.href = "irrreportview.html?irrMasterId="+irrMasterId+"&schoolTopicId="+schoolTopicId+"&clinicianId="+clinicianId;
         }
         else{
         window.location.href = "irrreportview.html?irrMasterId="+irrMasterId+"&schoolTopicId="+schoolTopicId;
         }
         });

         $("#cboPreceptor").change(function(){
         var preceptorId = $(this).val();
         var irrMasterId = "<?php echo EncodeQueryData($irrMasterId); ?>";
         var schoolTopicId = "<?php echo EncodeQueryData($schoolTopicId); ?>";
         
         if(preceptorId)
         {
         window.location.href = "irrreportview.html?irrMasterId="+irrMasterId+"&schoolTopicId="+schoolTopicId+"&preceptorId="+preceptorId;
         }
         else{
         window.location.href = "irrreportview.html?irrMasterId="+irrMasterId+"&schoolTopicId="+schoolTopicId;
         }
         });
         
                            
      </script>
   </body>
</html>