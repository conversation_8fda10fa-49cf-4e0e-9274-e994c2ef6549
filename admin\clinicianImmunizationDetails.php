<?php
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');
	include('../setRequest.php');
    include('../class/clsStudent.php'); 	
	include('../class/clsClinicianImmunization.php');
		 
	
	    $immunizationDate  = "";
        $reimmunizationMId  ="";
        $immunizationNote ="";
	    $clinicianIdImmunizationId="";
	    $shortName="";
	    $ExpiryDate=null;
	    $immunDate=null;   
   
    
    $bedCrumTitle = 'Add';
	 if(isset($_GET['clinicianId'])) //Edit Mode
	{
		$clinicianId = $_GET['clinicianId'];
        $clinicianId = DecodeQueryData($clinicianId);
	}
    if(isset($_GET['clinicianIdImmunizationId'])) //Edit Mode
	{
		
        $clinicianIdImmunizationId = $_GET['clinicianIdImmunizationId'];
        $clinicianIdImmunizationId = DecodeQueryData($clinicianIdImmunizationId);
        $title ="Edit Immunization";
        $bedCrumTitle = 'Edit';

        $objimmunization = new clsClinicianImmunization();
        $row = $objimmunization->GetClincianImmunization($clinicianIdImmunizationId,$clinicianId);        
        unset($objimmunization);

        if($row=='')
        {
            header('location:addClinicianImmunization.html');
            exit;
        }

        
		$immunizationDate  = date("m/d/Y", strtotime($row['immunizationDate']));
		
        $reimmunizationMId  = stripslashes($row['immunizationMId']);
        $MasterImmunizationMId  = stripslashes($row['MasterImmunizationMId']);
        $immunizationNote  = stripslashes($row['immunizationNote']);
        $immunizationName  = stripslashes($row['shortName']);
        $firstName  = stripslashes($row['firstName']);
        $lastName  = stripslashes($row['lastName']);
        $clinicianId  = stripslashes($row['clinicianId']);
		$fullName  =$firstName.' '.$lastName;
		$expiryDays  = $row['expiryDays'];	
		$immunizationDate  = date("m/d/Y", strtotime($row['immunizationDate']));		
		$immunDate=date("m/d/Y", strtotime($immunizationDate));	
		$ExpiryDate  = date("m/d/Y", strtotime($row['expiryDate']));		
		$ExpiryDate=date("m/d/Y", strtotime($ExpiryDate));	 
			
		
		      
	}
	
	
	
	//Read immulization From State
	
	
	$objimmunization = new clsClinicianImmunization();
    $immunization = $objimmunization->GetAllImmunization($currentSchoolId);
    unset($objimmunization);  

?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Immunization Details </title>       
    </head>
		<?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
    <body>    

        <div class="container">
			<div class="" tabindex="-1" role="dialog">
				<div class="modal-dialog" role="document">
					<div class="modal-content">
							<div class="modal-header">
								<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
									<h4 class="modal-title">Immunization Details</h4>
							</div>
							<div class="modal-body">
								<div class="row">
									<div class="col-md-12">									
											<div class="form-group">					
												<label class="col-md-4 control-label" for="SchoolDate">Immunization</label>
													<div class="col-md-5 col-sm-4 col-xs-12">	
														<div class='input-group date' id='EndDate'>
													<label for="SchoolDate" style="margin-top:8px"><?php echo ($immunizationName);?></label>
															
														
														</div>
															<div id="error-txtDate"></div>
													</div>		
											</div>
											<div class="form-group">					
												<label class="col-md-4 control-label" for="clinicianname">Evaluator</label>
													<div class="col-md-8 col-sm-8 col-xs-12">	
														<div class='input-group clinicianname' id='clinicianname'>
														<label  for="clinicianname" style="margin-top:8px"> <?php echo($fullName);?> </label>															
													   	
														</div>
															<div id="error-txtDate"></div>
													</div>		
											</div>
					
										<div class="form-group">					
												<label class="col-md-4 control-label" for="SchoolDate">Immunization Date</label>
													<div class="col-md-5 col-sm-4 col-xs-12">	
														<div class='input-group date' id='EndDate'>
													<label for="SchoolDate" style="margin-top:8px">
													<?php
													if($immunizationDate !='' && $immunizationDate !='0000-00-00' &&  $immunizationDate !='01/01/1970')
													{
													echo $immunizationDate;
													}
													else{
														echo "-";
													} 
													?></label>				
														   	
														</div>
															<div id="error-txtDate"></div>
													</div>		
											</div>
											<?php if($expiryDays !=0) {?>
											<div class="form-group">					
												<label class="col-md-4 control-label" for="SchoolDate">Expire Date</label>
													<div class="col-md-5 col-sm-4 col-xs-12">	
														<div class='input-group date' id='EndDate'>
													<label for="SchoolDate" style="margin-top:8px"><?php
													if($ExpiryDate !='' && $ExpiryDate !='0000-00-00' &&  $ExpiryDate !='01/01/1970')
													{
													echo $ExpiryDate;
													}
													else{
														echo "-";
													} 
													?></label>				
														   	
														</div>
															<div id="error-txtDate"></div>
													</div>		
											</div>
												<?php } ?>
											<div class="form-group">					
												<label class="col-md-4 control-label" for="SchoolDate">Note</label>
													<div class="col-md-5 col-sm-4 col-xs-12">	
														<div class='input-group date' id='EndDate'>
													<label for="SchoolDate" style="margin-top:8px"><?php echo ($immunizationNote);?></label>				
														   	
														</div>
															<div id="error-txtDate"></div>
													</div>		
											</div>
											
								
									</div>
								</div>
						</div>
					</div>
				</div>
			</div>
		</div> 
	
         <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php"); ?>
		
    </body>
    </html>