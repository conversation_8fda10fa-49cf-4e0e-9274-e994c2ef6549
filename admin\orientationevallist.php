<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsStudent.php');
include('../class/clsOrientationChecklist.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../setRequest.php');

$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$isActiveCheckoff = '';
$isDefaultCiEval = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$isDefaultCiEval = $_SESSION["isDefaultCiEval"];
$loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$rotationId = 0;
$studentId = isset($_GET['studentId']) ? DecodeQueryData($_GET['studentId']) : 0;


$canvasStatus = isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';
$type = isset($_GET['type']) ? $_GET['type'] : '';
if ($type == 'canvas')
    $canvasStatus = 1;

//For Rotation Site
if (isset($_GET['orientationrotationid'])) {
    $rotationId = $_GET['orientationrotationid'];
    $rotationId = DecodeQueryData($rotationId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

if (isset($_GET['Type'])) {
    $Type = ($_GET['Type']);
}


// if (isset($_GET['courseId'])) {
//     $courseId = DecodeQueryData($_GET['courseId']);
// }

$title = "Orientation Checklist |" . $transchooldisplayName;

//For Orientation Checklist List
$objOrientationEval = new clsOrientationChecklist();

$getOrientationDetails = $objOrientationEval->GetAllOrientationEvalForStudent($rotationId, $currentSchoolId, 0);

$totalDailyCount = 0;
if ($getOrientationDetails != '') {
    $totalDailyCount = mysqli_num_rows($getOrientationDetails);
}
unset($objOrientationEval);

//For Rotation Name
$objRotation = new clsRotation();
if ($rotationId != '') {
    $rotationtitle = $objRotation->GetrotationTitle($rotationId, $schoolId);
}

$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $studentId);
$studentfullname = $Rowstudent ? ($Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']) : '';
unset($objStudent);

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link href="https://cdn.datatables.net/fixedcolumns/3.2.4/css/fixedColumns.bootstrap4.min.css" rel="stylesheet" />


    <style>
        .DTFC_LeftBodyLiner {
            overflow-y: unset !important
        }

        .DTFC_RightBodyLiner {
            overflow-y: unset !important
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #2e3192; */
            /* color: #fff; */
            color: #555;
            background: #f6f6ff;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #2e3192; */
            /* color: #fff; */
            color: #555;
            background: #f6f6ff;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f6ff !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        .input-group {
            width: 100%;
        }

        div.dataTables_wrapper div.dataTables_length select {
            height: 45px;
        }

        /* .form-horizontal .form-group {
                margin-right: 0;
                margin-left: 0;
            } */

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

            /* .container-zero{
                    padding: 0;
                } */

            div.dataTables_wrapper div.dataTables_length {
                text-align: left !important;
                margin-top: 10px;
            }
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if (isset($_GET['studentId'])) { ?> <li><a href="clinical.html">Clinical</a></li>
                        <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>

                    <?php }
                    if ($rotationId != '') { ?>
                        <li><a href="rotations.html">Rotations</a></li>
                        <?php if ($rotationtitle != '') { ?>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                    <?php }
                    } ?>

                    <li class="active">Orientation Checklist</li>

                </ol>
            </div>
        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Orientation Checklist added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Orientation Checklist updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Orientation Checklist deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>First<br>Name</th>
                    <th>Last<br>Name</th>
                    <th>Rotation</th>
                    <th style="text-align: center">Evaluation<br>Date</th>
                    <th style="text-align: center">Student<br>Sign Date</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalDailyCount > 0) {
                    while ($row = mysqli_fetch_array($getOrientationDetails)) {

                        $rotationame = $row['title'];
                        $Ranktitle = $row['Ranktitle'];
                        $firstName = $row['firstName'];
                        $lastName = $row['lastName'];
                        $studentName = $firstName . ' ' . $lastName;
                        $studentOrientationEvalId = $row['orientationEvalID'];
                        $evaluationDate = stripslashes($row['evaluationDate']);
                        $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                        $dateOfStudentSignature = stripslashes($row['studentDate']);
                        $courselocationId = $row['locationId'];
                        $rotationId = $row['rotationId'];
                        $parentRotationId = stripslashes($row['parentRotationId']);
                        $rotationLocationId = stripslashes($row['rotationLocationId']);
                        $locationId = 0;

                        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                            if (!$rotationLocationId)
                                $locationId = $objRotation->GetLocationByRotation($rotationId);
                            else
                                $locationId  = $rotationLocationId;
                        } else {
                            $locationId  = $courselocationId;
                        }

                        //Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);
                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

                        $dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
                        $dateOfStudentSignature = date("m/d/Y", strtotime($dateOfStudentSignature));

                ?>
                        <tr>
                            <td><?php echo ($firstName); ?></td>
                            <td><?php echo ($lastName); ?></td>
                            <td><?php echo ($rotationame); ?></td>
                            <td style="text-align: center"><?php echo ($evaluationDate); ?></td>
                            <td style="text-align: center"><?php
                                                            if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {

                                                                echo ($dateOfStudentSignature);
                                                            } else {
                                                                echo "-";
                                                            }
                                                            ?></td>

                            <td style="text-align: center">
                                <?php if (isset($_GET['studentId'])) { ?>
                                    <a href="addorientationeval.html?studentOrientationEvalId=<?php echo (EncodeQueryData($studentOrientationEvalId)); ?>&studentId=<?php echo (EncodeQueryData($studentId)); ?>&view=V">View</a>
                                <?php } else { ?>
                                    <a href="addorientationeval.html?studentOrientationEvalId=<?php echo (EncodeQueryData($studentOrientationEvalId)); ?>&orientationrotationid=<?php echo (EncodeQueryData($rotationId)); ?>&view=V">View</a>
                                <?php } ?>

                                <?php if (isset($_SESSION["loggedAsBackUserId"])) { ?>
                                    | <a href="javascript:void(0);" class="deleteAjaxRow" studentOrientationEvalId="<?php echo EncodeQueryData($studentOrientationEvalId); ?>" studentName="<?php echo ($studentName); ?>">Delete</a>
                                <?php } ?>
                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objRotation);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="https://cdn.datatables.net/fixedcolumns/3.2.4/js/dataTables.fixedColumns.min.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });
        $(".select2_single").select2();

        var current_datatable = $("#datatable-responsive").DataTable({

            responsive: false,
            scrollX: true,
            // "ordering": true,
            // "order": [
            //     [0, "desc"]
            // ],
            "aoColumns": [{
                "sWidth": "25%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "20%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "25%"
            }, {
                "sWidth": "25%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "20%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "25%"
            }]

        });

        $(document).on('click', '.deleteAjaxRow', function() {
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentOrientationEvalId = $(this).attr('studentOrientationEvalId');
            var title = $(this).attr('studentName');

            var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';
            var isUser = 1; //for Admin

            alertify.confirm('Orientation Checklist Eval: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentOrientationEvalId,
                        userId: userId,
                        isUser: isUser,
                        type: 'orientationEval'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});
        });


        $("#cboCourse").change(function() {
            var courseId = $(this).val();
            if (courseId) {
                window.location.href = "masteryList.html?courseId=" + courseId;
            } else {
                window.location.href = "masteryList.html";
            }
        });
    </script>


</body>

</html>