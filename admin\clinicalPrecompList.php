<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsRotation.php');
include('../class/clsClinicalPreComp.php');


$clinicalPrecompRotationId = 0;
$schoolId = 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$isDefaultCiEval = '';
// $loggedUserId = $_SESSION["loggedUserId"];   
$clinicianId = isset($_SESSION['loggedClinicianId']) ? $_SESSION['loggedClinicianId'] : 0;
$loggedClinicianType = isset($_SESSION['loggedClinicianType']) ? $_SESSION['loggedClinicianType'] :'';
$isActiveCheckoff = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$isDefaultCiEval = $_SESSION["isDefaultCiEval"];
if (isset($_GET['clinicalPrecompRotationId'])) //Edit Mode
{
    $clinicalPrecompRotationId = $_GET['clinicalPrecompRotationId'];
    $clinicalPrecompRotationId = DecodeQueryData($clinicalPrecompRotationId);
} else {
    $transchooldisplayName = $currenschoolDisplayname;
}

$studentId = isset($_GET['studentId']) ? DecodeQueryData($_GET['studentId']) : 0;
$title = "Clinical Pre-Comp| " . $transchooldisplayName;

//For Clinical Pre-Comp
$objClinicalPreComp = new clsClinicalPreComp();
$getClinicalPreCompdetails = $objClinicalPreComp->GetAllClinicalPreComp($schoolId, $clinicalPrecompRotationId);

$totalClinicalPreCompCount = 0;
if ($getClinicalPreCompdetails != '') {
    $totalClinicalPreCompCount = mysqli_num_rows($getClinicalPreCompdetails);
}

//For Rotation Name 
$objRotation = new clsRotation();
$RotationName = $objRotation->GetrotationDetails($clinicalPrecompRotationId, $schoolId);
$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] : '';

// for Student
$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $studentId);
$studentfullname = $Rowstudent ? ($Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']) : '';
unset($objStudent);

$objDB = new clsDB();
$getTimezoneId = $objDB->GetSingleColumnValueFromTable('schools', 'timeZoneId', 'schoolId', $currentSchoolId);
$TimeZone = $objDB->GetSingleColumnValueFromTable('timezonemaster', 'timezone', 'timeZoneId', $getTimezoneId);
unset($objDB);


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    <style>
        .form-control {
            height: 45px;
        }

        div.dataTables_wrapper div.dataTables_length select {
            height: 45px;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if (isset($_GET['studentId'])) { ?> <li><a href="clinical.html">Clinical</a></li>
                        <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>

                    <?php } else { ?>
                        <li><a href="rotations.html?active=1"><?php echo ($rotationtitle); ?></a></li>
                    <?php } ?>
                    <li class="active">Clinical Pre-Comp</li>

                </ol>
            </div>
            <?php //if ( $rotationStatus == 0) { 
            ?>
            <!-- <div class="pull-right">
                <a class="btn btn-link" href="clinicalPrecomp.html?clinicalPrecompRotationId=<?php echo EncodeQueryData($clinicalPrecompRotationId); ?>">Add</a>
            </div> -->
            <?php //} 
            ?>

        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Clinical Pre-Comp added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Clinical Pre-Comp updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Clinical Pre-Comp deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>

                    <th>Evalution Date</th>
                    <th>Student</th>
                    <th>Evaluator</th>
                    <th>Term</th>
                    <th>Result</th>
                    <th style="text-align: center">Technologist<br>Sign<br>Date</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // echo '<pre>';
                if ($totalClinicalPreCompCount > 0) {
                    while ($row = mysqli_fetch_array($getClinicalPreCompdetails)) {
                        // print_r($row);
                        $clinicalPrecompId = $row['clinicalPrecompId'];
                        $clinicianName = $row['clinicianName'];
                        $studentName = $row['studentName'];
                        $clinicalPrecompRotationId = $row['termId'];
                        $termName = $row['rotationName'];
                        $examId = $row['examId'];
                        $accession = $row['accession'];
                        $reason = $row['reason'];
                        $comment = $row['comment'];
                        // $totalScore = $row['totalScore'];
                        $result = $row['result'];

                        $evaluationDate = isset($row['evaluationDate']) ? stripslashes($row['evaluationDate']) : '';
                        if ($evaluationDate != '' && $evaluationDate != '0000-00-00 00:00:00') {
                            $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
                            $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                        } else
                            $evaluationDate = '-';

                        $dateOftechnologiestSignature = isset($row['dateOftechnologiestSignature']) ? stripslashes($row['dateOftechnologiestSignature']) : '';
                        if ($dateOftechnologiestSignature != '' && $dateOftechnologiestSignature != '0000-00-00 00:00:00') {
                            $dateOftechnologiestSignature = converFromServerTimeZone($dateOftechnologiestSignature, $TimeZone);
                            $dateOftechnologiestSignature = date("m/d/Y", strtotime($dateOftechnologiestSignature));
                        } else
                            $dateOftechnologiestSignature = '-';

                        // $studentSignatureDate = isset($row['studentSignatureDate']) ? stripslashes($row['studentSignatureDate']) : '';
                        // if ($studentSignatureDate != '' && $studentSignatureDate != '0000-00-00 00:00:00') {
                        //     $studentSignatureDate = converFromServerTimeZone($studentSignatureDate, $TimeZone);
                        //     $studentSignatureDate = date("m/d/Y", strtotime($studentSignatureDate));
                        // } else
                        //     $studentSignatureDate = '-';

                        $viewParam = '';
                        $linkText = 'Edit';
                        // if ($studentSignatureDate != '-') {
                        //     $viewParam = '&view=V';
                        //     $linkText = 'View';
                        // }

                        $resultText = ($result) ? 'Fail' : 'Pass';


                ?>
                        <tr>

                            <td><?php echo ($evaluationDate); ?></td>
                            <td><?php echo ($studentName); ?></td>
                            <td><?php echo ($clinicianName); ?></td>
                            <td style="text-align: center"><?php echo ($termName); ?></td>
                            <td style="text-align: center"><?php echo ($resultText); ?></td>
                            <td style="text-align: center"><?php echo ($dateOftechnologiestSignature); ?></td>
                            <td style="text-align: center">
                                <a href="clinicalPrecomp.html?clinicalPrecompId=<?php echo (EncodeQueryData($clinicalPrecompId)); ?>&clinicalPrecompRotationId=<?php echo (EncodeQueryData($clinicalPrecompRotationId)); ?>&view=V">View</a>                                
                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objRotation);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');

        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "ordering": true,
            "order": [
                [0, "desc"]
            ],


            "aoColumns": [{
                "sWidth": "20%",
                "sClass": "alignCenter"

            }, {
                "sWidth": "20%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "20%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "20%",
                "sClass": "alignCenter",
                "bSortable": false
            }, {
                "sWidth": "20%",
                "sClass": "alignCenter",
                "bSortable": false
            },{
                "sWidth": "20%",
                "sClass": "alignCenter",
                "bSortable": false
            },{
                "sWidth": "20%",
                "sClass": "alignCenter",
                "bSortable": false
            }],

        });
    </script>


</body>

</html>