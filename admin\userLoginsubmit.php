<?php
	include("../includes/config.php");
	include("../class/clsDB.php");
	include('../class/clsSystemUser.php');
	include('../includes/commonfun.php');
	include('../class/clsSchool.php');
	include('../class/clsTemplateEngine.php');
	include('../class/PasswordHash.php');
	include('../setRequest.php');
	include('../class/clsSystemUserRoleMaster.php');
	include('../class/clsSystemUserHistoryId.php');
    include('../class/clsUserautolockoutlogs.php');
	
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnLogin']))
	{
		//Check Organization is active
		if(!$isOrganizationActive)
		{
			header('location:index.html');
			exit();
		}

		$userName = trim($_POST['txtUsername']);
		$password = trim($_POST['txtPassword']);
		$attempt = trim($_POST['attempt']);
		
		//Check username and pasword blank
		if($userName=="" || $password=="")
		{
			header('location:index.html?status=mandatory');
			exit();
		}

		//Check Login User 
		$objSystemUsers = new clsSystemUser();
		$rowUser = $objSystemUsers->CheckLogin($currentSchoolId,$userName);
		
		
        // $objuserautolockoutlogs='';
		//$objuserautolockoutlogs  =new clsuserautolockoutlogs();
		$userid='';
		// echo '<pre>';
		// print_r($rowUser);exit;
		

			
		if($rowUser !="")
		{
			//Now we are going to validate passwords
			$stored_pwd_hash = stripslashes($rowUser['passwordHash']);
				$userid = $rowUser['systemUserMasterId'];
				$systemUserMasterId = $rowUser['systemUserMasterId'];
			//Check Password with db password
			//-----------------------------------
			if(PasswordHash::check_password($stored_pwd_hash, $password)==false) 
			{
				
				$objSystemUsers->SaveFailedLogin($userid,$currentSchoolId);
				   
                 //save user LockOut			
				header('location:index.html?status=error&attempt='.$attempt);
			  
				unset($objSystemUsers);
				exit();
			} 
			$objSystemUsers->ClearFailedLogin($userid);
			unset($objSystemUsers);
			//-----------------------------------
			//Check System User is active
			//-----------------------------------
			if(stripslashes($rowUser['isActive'])==0)
			{
				header('location:index.html?status=inactive');
				exit();
			}
				if(stripslashes($rowUser['isBlocked'])==1)
			{
				header('location:index.html?status=blocked');
				exit();
			}
			//-----------------------------------
			//clsUserautolockoutlogs Check user lock Out
			  
        
			
			//------------------------------------------------------------------------------------------
			// check isSystemUserRole Primary
			//------------------------------------------------------------------------------------------
			$IsSystemUserRolePrimary = 0;
			$objSystemUserRoleMaster = new clsSystemUserRoleMaster();
			$IsSystemUserRolePrimary = $objSystemUserRoleMaster->IsSystemUserRolePrimary($rowUser['systemUserRoleMasterId']);
			unset($objSystemUserRoleMaster);

			//Get System User profile Image
			$profileImageName = stripslashes($rowUser['smallProfilePic']);
			$defaultProfileImagePath = GetUserImagePath($systemUserMasterId,$currentSchoolId,$profileImageName);

			$profileLargeImageName = stripslashes($rowUser['profilePic']);
			$defaultProfileLargeImagePath = GetUserImagePath($systemUserMasterId,$currentSchoolId,$profileLargeImageName);
			

			//Start Session
			//-----------------------------------
			@session_start();
			$_SESSION["loggedUserId"] = $systemUserMasterId;
			$_SESSION["loggedUserName"] = stripslashes($rowUser['username']);
			$_SESSION["loggedUserFirstName"] =  stripslashes($rowUser['firstName']);
			$_SESSION["loggedUserLastName"] = stripslashes($rowUser['lastName']);
			$_SESSION["loggedUserProfileImagePath"] = $defaultProfileImagePath;
			$_SESSION["loggedUserProfileLargeImagePath"] = $defaultProfileLargeImagePath;
			$_SESSION["loggedUserRoleId"] = $rowUser['systemUserRoleMasterId'];
			$_SESSION["loggedUserIsRolePrimary"] = $rowUser['systemUserMasterId'];
			$_SESSION["loggedUserLocation"] = ($rowUser['locationId']);
			$_SESSION["loggedUserEmail"] = ($rowUser['email']);
			$_SESSION["loggedUserSchoolTimeZone"] = ($rowUser['timezone']);
			$_SESSION["loggedUserRoleType"] = ($rowUser['type']);
			$_SESSION["isActiveCheckoff"] = ($rowUser['isActiveCheckoffForStudent']);
			$_SESSION["isDefaultCiEval"] = ($rowUser['isDefaultCiEval']);
			$_SESSION["isSuperAdmin"] = ($rowUser['isSuperAdmin']);
			$_SESSION["loggedUserSendRecordToCanvas"] = ($rowUser['isSendRecordToCanvas']);
			
			$_SESSION["isPopup"] = 0;

			//------------------------------------------------------------------------------------------
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();
			$logAction = $objLog::LOGIN;
			$isSuperAdmin = isset($rowUser['isSuperAdmin']) ? ($rowUser['isSuperAdmin']) : 0;
			$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;
			$IsMobile = 0;
			$objSystemUser = new clsSystemUser();
			$objSystemUser->saveSchoolAdminAuditLog( $systemUserMasterId, $_SESSION["loggedUserId"], $userType, $logAction);
			unset($objSystemUser);
			//------------------------------------------------------------------------------------------
			//Audit Log End
			//------------------------------------------------------------------------------------------
			// Creat Log
			//------------------------------------------------------------------------------------------
			  $objSystemUserHistory = new clsSystemUserHistoryid();
			  $typeUserId=$_SESSION["loggedUserId"];
			  $ip = $_SERVER['REMOTE_ADDR'];
			  $objSystemUserHistory->typeUserId =$typeUserId;
			  $objSystemUserHistory->type='A';
			  $objSystemUserHistory->ip =$ip;
			  $objSystemUserHistory->schoolId =$currentSchoolId;
			  $objSystemUserHistory->browserHistory ='browser History';
			  $systemUserloginhistoryId = $objSystemUserHistory->savesystemloginhistory($typeUserId);

			  $_SESSION["systemUserloginhistoryId"] = $systemUserloginhistoryId;
              unset($objSystemUserHistory);
         
			if(isset($_GET['studentId']) && ($_GET['employercoarcId']))
			{
				$studentId = $_GET['studentId'];
				$studentId = DecodeQueryData($studentId);
				$employercoarcId = $_GET['employercoarcId'];
				$employercoarcId = DecodeQueryData($employercoarcId);
				
				header('location:addemployercoarcsurvey.html?employercoarcId='.EncodeQueryData($employercoarcId).'&studentId='.EncodeQueryData($studentId));
				exit();
			}
			elseif(isset($_GET['studentId']) && ($_GET['personnelCoarcId']))
			{
				$studentId = $_GET['studentId'];
				$studentId = DecodeQueryData($studentId);
				$personnelCoarcId = $_GET['personnelCoarcId'];
				$personnelCoarcId = DecodeQueryData($personnelCoarcId);
				
				header('location:addpersonnelsurvey.html?personnelCoarcId='.EncodeQueryData($personnelCoarcId).'&studentId='.EncodeQueryData($studentId));
				exit();
			}
			elseif(isset($_GET['studentId'])) 
			{
				$studentId = $_GET['studentId'];
				
				$studentId = DecodeQueryData($studentId);	
			
				header('location:addcoarcsurvey.html?studentId='.EncodeQueryData($studentId));
				exit();
			}
			elseif(stripslashes($rowUser['isPasswordUpdated'])==0)
			{
				header('location:changepassword.html?status=changepassword');
				exit();
			}
				
				
			header("location:dashboard.html");
			exit();
		}
		else //Error
		{
			header('location:index.html?status=error&attempt='.$attempt);
		
			exit();
		}
		
		
	}
	else
	{
		
		header('location:index.html');
		exit();
	}
	
?>