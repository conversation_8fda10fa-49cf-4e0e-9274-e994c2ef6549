<?php
	include('includes/validateUserLogin.php');
    include('../includes/config.php');
    include('../class/clsDB.php');
    include('../includes/commonfun.php');
    include('../class/clsStudentadditationlContactInformation.php');	
    include('../class/clsCountryStateMaster.php');	
    include('../class/clsStudent.php'); 
    include('../setRequest.php');
	require '../vendor/autoload.php';  // Include PhpSpreadsheet's autoloader

	use PhpOffice\PhpSpreadsheet\Spreadsheet;
	use PhpOffice\PhpSpreadsheet\IOFactory;
		
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnStudentExport']))		
	
	$totalStudentAdditationlContactInformationCount=0; 
	$currentSchoolId;
	
	// Object
	$objStudentadditationlContactInformation = new clsStudentadditationlContactInformation();

	$allStudentAdditationlContactInformation = $objStudentadditationlContactInformation->GetAllStudentAdditationlContactInformation($currentSchoolId);
	
	if($allStudentAdditationlContactInformation !='')
	{
		$totalStudentAdditationlContactInformationCount = mysqli_num_rows($allStudentAdditationlContactInformation);
	}
	$title='Student Additationl Contact Information List';			
	$EmployerContactInfo= "Employer Contact Information";
	$EmergencyContactInfo= "Emergency  Contact Information";
	$OfficeContactInfo= "Office Contact Information";
	$PermanentContactInfo= "Permanent Mailing Address";
	
	date_default_timezone_set('Asia/Kolkata');
	$today= (date('m/d/Y, H:i A'));
		
	$spreadsheet = new Spreadsheet();
	// Set document properties
	$spreadsheet->getProperties()->setCreator('Schools')
								 ->setLastModifiedBy('JCC')
								 ->setTitle('Reports')
								 ->setSubject('Student Certification Log Information List')
								 ->setDescription('All School Reports');
								 
	//Active Sheet
	$spreadsheet->setActiveSheetIndex(0);
	// $spreadsheet->getActiveSheet()->setTitle();				
	
	$spreadsheet->getActiveSheet()->mergeCells("B1:AP1");
	
	//Print Heading	
$headerstyleArray = array('font'  => array('bold'  => true,'size'  => 16));
	
	$spreadsheet->getActiveSheet()->mergeCells("B2:AP2");
	$spreadsheet->getActiveSheet()->setCellValue('B2', $title);
	$spreadsheet->getActiveSheet()->getStyle('B2')->applyFromArray($headerstyleArray);
	$spreadsheet->getActiveSheet()->getStyle('B2')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('B2')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
		
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B2:AP2')->applyFromArray($styleBorderArray);
	
	$styleArray = array('font'  => array('bold'  => true,'size'  => 12));
	$spreadsheet->getActiveSheet()->mergeCells("B3:L3");
	$spreadsheet->getActiveSheet()->mergeCells("B4:L4");
	$spreadsheet->getActiveSheet()->setCellValue('B4',$EmployerContactInfo);
	$spreadsheet->getActiveSheet()->getStyle('B4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('B4')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
				
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B4:L6')->applyFromArray($styleBorderArray);
	
	//Make Table Heading
	$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
	$spreadsheet->getActiveSheet()->mergeCells("B5:L5");
	$spreadsheet->getActiveSheet()->setCellValue('B6', 'Student Name');
	$spreadsheet->getActiveSheet()->getStyle('B6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			
	$spreadsheet->getActiveSheet()->setCellValue('C6', 'Business Name');
	$spreadsheet->getActiveSheet()->getStyle('C6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('C6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('D6', 'Contact Name');
	$spreadsheet->getActiveSheet()->getStyle('D6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('D6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('E6', 'Street Address');
	$spreadsheet->getActiveSheet()->getStyle('E6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('E6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('F6', 'Suite/Dept/Floor');
	$spreadsheet->getActiveSheet()->getStyle('F6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('F6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('G6', 'City');
	$spreadsheet->getActiveSheet()->getStyle('G6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('G6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('H6', 'State');
	$spreadsheet->getActiveSheet()->getStyle('H6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('H6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('I6', 'Postal Code');
	$spreadsheet->getActiveSheet()->getStyle('I6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('I6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('J6', 'Email');
	$spreadsheet->getActiveSheet()->getStyle('J6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('J6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('K6', 'Work Phone');
	$spreadsheet->getActiveSheet()->getStyle('K6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('K6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('L6', 'Cell Phone');
	$spreadsheet->getActiveSheet()->getStyle('L6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('L6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->getStyle('B6:L6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
	
	$spreadsheet->getActiveSheet()->mergeCells("B7:L7");
	
	//
	$spreadsheet->getActiveSheet()->mergeCells("N3:V3");
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('N4:V4')->applyFromArray($styleBorderArray);
	$spreadsheet->getActiveSheet()->mergeCells("N4:V4");

	$styleArray = array('font'  => array('bold'  => true,'size'  => 12));
	
	$spreadsheet->getActiveSheet()->setCellValue('N4',$EmergencyContactInfo);
	$spreadsheet->getActiveSheet()->getStyle('N4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('N4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('N4')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
				 
	//Make Table Heading
	$spreadsheet->getActiveSheet()->mergeCells("N5:V5");
	$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
	
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('N6:V6')->applyFromArray($styleBorderArray);
	
	$spreadsheet->getActiveSheet()
			 ->getStyle('N6:V6')
			 ->getFill()
			 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
			 ->getStartColor()
			 ->setRGB('E0E0E0');
				 
	$spreadsheet->getActiveSheet()->setCellValue('N6', 'Street Address');
	$spreadsheet->getActiveSheet()->getStyle('N6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('N6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('O6', 'Suite/Dept/Floor');
	$spreadsheet->getActiveSheet()->getStyle('O6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('O6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('P6', 'City');
	$spreadsheet->getActiveSheet()->getStyle('P6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('P6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('Q6', 'State');
	$spreadsheet->getActiveSheet()->getStyle('Q6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('Q6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('R6', 'Postal Code');
	$spreadsheet->getActiveSheet()->getStyle('R6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('R6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('S6', 'Email');
	$spreadsheet->getActiveSheet()->getStyle('S6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('S6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('T6', 'Work Phone');
	$spreadsheet->getActiveSheet()->getStyle('T6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('T6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('U6', 'Cell Phone');
	$spreadsheet->getActiveSheet()->getStyle('U6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('U6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('V6', 'Contact Name');
	$spreadsheet->getActiveSheet()->getStyle('V6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('V6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->mergeCells("N7:V7");
	
	// Office Contact Info
	$spreadsheet->getActiveSheet()->mergeCells("X3:AF3");
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('X4:AF4')->applyFromArray($styleBorderArray);
	
	$styleArray = array('font'  => array('bold'  => true,'size'  => 12));
	
	$spreadsheet->getActiveSheet()->mergeCells("X4:AF4");
	$spreadsheet->getActiveSheet()->setCellValue('X4',$OfficeContactInfo);
	$spreadsheet->getActiveSheet()->getStyle('X4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('X4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('X4')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
				 
	//Make Table Heading
	$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
	$spreadsheet->getActiveSheet()->mergeCells("X5:AF5");
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('X6:AF6')->applyFromArray($styleBorderArray);
				 
	$spreadsheet->getActiveSheet()->setCellValue('X6', 'Business Name');
	$spreadsheet->getActiveSheet()->getStyle('X6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('X6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('Y6', 'Street Address');
	$spreadsheet->getActiveSheet()->getStyle('Y6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('Y6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('Z6', 'Suite/Dept/Floor');
	$spreadsheet->getActiveSheet()->getStyle('Z6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('Z6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AA6', 'City');
	$spreadsheet->getActiveSheet()->getStyle('AA6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AA6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AB6', 'State');
	$spreadsheet->getActiveSheet()->getStyle('AB6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AB6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AC6', 'Postal Code');
	$spreadsheet->getActiveSheet()->getStyle('AC6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AC6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AD6', 'Office-Phone');
	$spreadsheet->getActiveSheet()->getStyle('AD6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AD6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AE6', 'Fax');
	$spreadsheet->getActiveSheet()->getStyle('AE6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AE6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AF6', 'Website');
	$spreadsheet->getActiveSheet()->getStyle('AF6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AF6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->getStyle('X6:AF6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
	$spreadsheet->getActiveSheet()->mergeCells("X7:AF7");
	// Permanent Mailing Address
	$spreadsheet->getActiveSheet()->mergeCells("AH3:AP3");
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('AH4:AP4')->applyFromArray($styleBorderArray);
	$spreadsheet->getActiveSheet()->mergeCells("AH4:AP4");
	
	$styleArray = array('font'  => array('bold'  => true,'size'  => 12));
	
	$spreadsheet->getActiveSheet()->setCellValue('AH4',$PermanentContactInfo);
	$spreadsheet->getActiveSheet()->getStyle('AH4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AH4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('AH4')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
				 
	//Make Table Heading
	$spreadsheet->getActiveSheet()->mergeCells("AH5:AP5");
	$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
	$spreadsheet->getActiveSheet()->mergeCells("AH7:AP7");
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('AH6:AP6')->applyFromArray($styleBorderArray);
	
	$spreadsheet->getActiveSheet()->setCellValue('AH6', 'Street Address');
	$spreadsheet->getActiveSheet()->getStyle('AH6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AH6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AI6', 'Suite/Dept/Floor');
	$spreadsheet->getActiveSheet()->getStyle('AI6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AI6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AJ6', 'City');
	$spreadsheet->getActiveSheet()->getStyle('AJ6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AJ6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AK6', 'State');
	$spreadsheet->getActiveSheet()->getStyle('AK6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AK6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AL6', 'Postal Code');
	$spreadsheet->getActiveSheet()->getStyle('AL6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AL6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AM6', 'Email');
	$spreadsheet->getActiveSheet()->getStyle('AM6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AM6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AN6', 'Work Phone');
	$spreadsheet->getActiveSheet()->getStyle('AN6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AN6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AO6', 'Cell Phone');
	$spreadsheet->getActiveSheet()->getStyle('AO6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AO6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AP6', 'Contact Relationship');
	$spreadsheet->getActiveSheet()->getStyle('AP6')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AP6')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->getStyle('AH6:AP6')->getFill()->setFillType('solid')->getStartColor()->setRGB('E0E0E0');
	
	$printStartRowCounter = 8;
	$objCountryStateMaster = new clsCountryStateMaster();
	
	if($totalStudentAdditationlContactInformationCount!="")
    {
		while($row = mysqli_fetch_assoc($allStudentAdditationlContactInformation))
		{
			$studentId=stripslashes($row['studentId']);
			$fullName=$row['FullName'];
			$businessName=stripslashes($row['businessName']);
			$ContactFirstName=stripslashes($row['ContactFirstName']);
			$ContactLastName=stripslashes($row['ContactLastName']);
			$ContactFullName = $ContactFirstName.' '.$ContactLastName;
			$streetAddress=stripslashes($row['streetAddress']);
			$suiteDeptFloor=stripslashes($row['suiteDeptFloor']);
			$city=stripslashes($row['city']);
			$stateId=stripslashes($row['stateId']);
			$employerStateName = $objCountryStateMaster->GetSingleState($stateId);
			$postalCode=stripslashes($row['postalCode']);
			$workPhone=stripslashes($row['workPhone']);
			$cellPhone=stripslashes($row['cellPhone']);
			$emailAddress=stripslashes($row['emailAddress']);
			$emergencyStreetAddress=stripslashes($row['emergencyStreetAddress']);
			$emergencySuiteAptNoFloor=stripslashes($row['emergencySuiteAptNoFloor']);
			$emergencyCity=stripslashes($row['emergencyCity']);
			$emergencyState=stripslashes($row['emergencyState']);
			$emergencyStateName = $objCountryStateMaster->GetSingleState($emergencyState);
			$emergencyPostalCode=stripslashes($row['emergencyPostalCode']);
			$emergencyWorkPhone=stripslashes($row['emergencyWorkPhone']);
			$emergencyCellPhone=stripslashes($row['emergencyCellPhone']);
			$emergencyEmailAddress=stripslashes($row['emergencyEmailAddress']);
			$emergencyContactsName=stripslashes($row['emergencyContactsName']);
			$officeBusinessName=stripslashes($row['officeBusinessName']);
			$officeStreetAddress=stripslashes($row['officeStreetAddress']);
			$officeSuiteDeptFloor=stripslashes($row['officeSuiteDeptFloor']);
			$officeCity=stripslashes($row['officeCity']);
			$officeState=stripslashes($row['officeState']);
			$officeStateName = $objCountryStateMaster->GetSingleState($officeState);
			$officePostalCode=stripslashes($row['officePostalCode']);
			$officePhone=stripslashes($row['officePhone']);
			$officeFax=stripslashes($row['officeFax']);
			$officeWebsite=stripslashes($row['officeWebsite']);
			$permanentStreetAddress=stripslashes($row['permanentStreetAddress']);
			$permanentSuiteAptNoFloor=stripslashes($row['permanentSuiteAptNoFloor']);
			$permanentCity=stripslashes($row['permanentCity']);
			$permanentState=stripslashes($row['permanentState']);
			$permanentStateName = $objCountryStateMaster->GetSingleState($permanentState);
			$permanentPostalCode=stripslashes($row['permanentPostalCode']);
			$permanentWorkPhone=stripslashes($row['permanentWorkPhone']);
			$permanentCellPhone=stripslashes($row['permanentCellPhone']);
			$permanentEmailAddress=stripslashes($row['permanentEmailAddress']);
			$ContactsRelationshipId=stripslashes($row['ContactsRelationshipId']);         
			$contactsrelationshipName = $objStudentadditationlContactInformation->GetSingleContactsRelationshipName($ContactsRelationshipId);
			
			$spreadsheet->getActiveSheet()->setCellValue('B'.$printStartRowCounter, $fullName);
			$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('C'.$printStartRowCounter, $businessName);
			$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('D'.$printStartRowCounter, $ContactFullName);
			$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('E'.$printStartRowCounter, $streetAddress);
			$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('F'.$printStartRowCounter, $suiteDeptFloor);
			$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter)->applyFromArray($styleArray);
					
			$spreadsheet->getActiveSheet()->setCellValue('G'.$printStartRowCounter, $city);
			$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('H'.$printStartRowCounter, $employerStateName);
			$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('I'.$printStartRowCounter, $postalCode);
			$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('J'.$printStartRowCounter, $emailAddress);
			$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('K'.$printStartRowCounter, $workPhone);
			$spreadsheet->getActiveSheet()->getStyle('K'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('K'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('L'.$printStartRowCounter, $cellPhone);
			$spreadsheet->getActiveSheet()->getStyle('L'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('L'.$printStartRowCounter)->applyFromArray($styleArray);
			
			//Emergency  Contact Information
			$spreadsheet->getActiveSheet()->setCellValue('N'.$printStartRowCounter, $emergencyStreetAddress);
			$spreadsheet->getActiveSheet()->getStyle('N'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('N'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('O'.$printStartRowCounter, $emergencySuiteAptNoFloor);
			$spreadsheet->getActiveSheet()->getStyle('O'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('O'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('P'.$printStartRowCounter, $emergencyCity);
			$spreadsheet->getActiveSheet()->getStyle('P'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('P'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('Q'.$printStartRowCounter, $officeStateName);
			$spreadsheet->getActiveSheet()->getStyle('Q'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('Q'.$printStartRowCounter)->applyFromArray($styleArray);
					
			$spreadsheet->getActiveSheet()->setCellValue('R'.$printStartRowCounter, $emergencyPostalCode);
			$spreadsheet->getActiveSheet()->getStyle('R'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('R'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('S'.$printStartRowCounter, $emergencyEmailAddress);
			$spreadsheet->getActiveSheet()->getStyle('S'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('S'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('T'.$printStartRowCounter, $emergencyWorkPhone);
			$spreadsheet->getActiveSheet()->getStyle('T'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('T'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('U'.$printStartRowCounter, $emergencyCellPhone);
			$spreadsheet->getActiveSheet()->getStyle('U'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('U'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('V'.$printStartRowCounter, $emergencyContactsName);
			$spreadsheet->getActiveSheet()->getStyle('V'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('V'.$printStartRowCounter)->applyFromArray($styleArray);
			
			//Office Contact Information
			$spreadsheet->getActiveSheet()->setCellValue('X'.$printStartRowCounter, $officeBusinessName);
			$spreadsheet->getActiveSheet()->getStyle('X'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('X'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('Y'.$printStartRowCounter, $officeStreetAddress);
			$spreadsheet->getActiveSheet()->getStyle('Y'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('Y'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('Z'.$printStartRowCounter, $officeSuiteDeptFloor);
			$spreadsheet->getActiveSheet()->getStyle('Z'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('Z'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AA'.$printStartRowCounter, $officeCity);
			$spreadsheet->getActiveSheet()->getStyle('AA'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AA'.$printStartRowCounter)->applyFromArray($styleArray);
					
			$spreadsheet->getActiveSheet()->setCellValue('AB'.$printStartRowCounter, $emergencyStateName);
			$spreadsheet->getActiveSheet()->getStyle('AB'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AB'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AC'.$printStartRowCounter, $officePostalCode);
			$spreadsheet->getActiveSheet()->getStyle('AC'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AC'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AD'.$printStartRowCounter, $officePhone);
			$spreadsheet->getActiveSheet()->getStyle('AD'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AD'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AE'.$printStartRowCounter, $officeFax);
			$spreadsheet->getActiveSheet()->getStyle('AE'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AE'.$printStartRowCounter)->applyFromArray($styleArray);

			$spreadsheet->getActiveSheet()->setCellValue('AF'.$printStartRowCounter, $officeWebsite);
			$spreadsheet->getActiveSheet()->getStyle('AF'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AF'.$printStartRowCounter)->applyFromArray($styleArray);
			
			//Permanent Mailing Address
			$spreadsheet->getActiveSheet()->setCellValue('AH'.$printStartRowCounter, $permanentStreetAddress);
			$spreadsheet->getActiveSheet()->getStyle('AH'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AH'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AI'.$printStartRowCounter, $permanentSuiteAptNoFloor);
			$spreadsheet->getActiveSheet()->getStyle('AI'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AI'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AJ'.$printStartRowCounter, $permanentCity);
			$spreadsheet->getActiveSheet()->getStyle('AJ'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AJ'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AK'.$printStartRowCounter, $permanentStateName);
			$spreadsheet->getActiveSheet()->getStyle('AK'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AK'.$printStartRowCounter)->applyFromArray($styleArray);
					
			$spreadsheet->getActiveSheet()->setCellValue('AL'.$printStartRowCounter, $permanentPostalCode);
			$spreadsheet->getActiveSheet()->getStyle('AL'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AL'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AM'.$printStartRowCounter, $permanentEmailAddress);
			$spreadsheet->getActiveSheet()->getStyle('AM'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AM'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AN'.$printStartRowCounter, $permanentWorkPhone);
			$spreadsheet->getActiveSheet()->getStyle('AN'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AN'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AO'.$printStartRowCounter, $permanentCellPhone);
			$spreadsheet->getActiveSheet()->getStyle('AO'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AO'.$printStartRowCounter)->applyFromArray($styleArray);

			$spreadsheet->getActiveSheet()->setCellValue('AP'.$printStartRowCounter, $contactsrelationshipName);
			$spreadsheet->getActiveSheet()->getStyle('AP'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AP'.$printStartRowCounter)->applyFromArray($styleArray);
	
			$printStartRowCounter++;
				}	
			}	
	
	// Auto size columns for each worksheet
	foreach ($spreadsheet->getAllSheets() as $sheet) 
	{
		for ($col = 0; $col <= \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($sheet->getHighestDataColumn()); $col++) {
			$sheet->getColumnDimensionByColumn($col)->setAutoSize(true);
		}
	}	
	$reportname='StudentAdditationlContactInformationAllListReport_';	
	
	$spreadsheet->setActiveSheetIndex(0);
	
	$currentDate = date('m_d_Y_h_i');
	
	// $cacheMethod = PHPExcel_CachedObjectStorageFactory:: cache_to_phpTemp;
// $cacheSettings = array( ' memoryCacheSize ' => '8MB');
// PHPExcel_Settings::setCacheStorageMethod($cacheMethod, $cacheSettings);

	header('Content-type: application/vnd.ms-excel; charset=UTF-8');
	header('Content-Disposition: attachment;filename="'.$reportname.$today.'.xls"');		
	header("Pragma: no-cache");
	header("Expires: 0");
	
	$objWriter = IOFactory::createWriter($spreadsheet, 'Xls');
	$objWriter->save('php://output');
?>