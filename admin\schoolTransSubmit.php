<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsSMTPSettings.php');
include('../class/clsSendEmails.php');
@session_start();
if (isset($_GET['id'])) //Edit Mode
{
	$schoolId = $_GET['id'];
	$schoolId = base64_decode($schoolId);
	$loggedUserId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;

	//Create object 
	$objSchool = new clsSchool();
	$newStatus = $_GET['newStatus'];
	$status = ($newStatus) ? 'Activate' : 'Deactivate';
	$objSchool->SetSchoolStatus($schoolId, $newStatus);
	unset($objSchool);
	$objDB = new clsDB();
	$schoolName = $objDB->GetSingleColumnValueFromTable('schools', 'displayName', 'schoolId', $schoolId);
	unset($objDB);
	$currentSchoolId=1;
	$objSendEmails = new clsSendEmails($currentSchoolId);
	$objSendEmails->SendSchoolStatusUpdateEmail($loggedUserId, $status, $schoolName);

	header('location:schools.html?status=StatusUpdated');
} else {
	exit();
}

?>