<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsStudentRankMaster.php');
include('../class/clsCanvas.php');
$currentDomainName = GetCurrentDomainNameFromURL();

$objCanvas = new clsCanvas();

//Create Course
// $acceToken = '7~ubtFTB7ZRv3JiVrQdxteK78ZvHqjcHfK8AxUF2iWtpFVl9QTAPGhSmGWZkRRsupT';
// $objCanvas->updateCourseByStudent($acceToken,'4743002','29792219',$description);
// // exit;
// echo '<pre>';
// print_r($_GET);exit;
//Student Registration Link
$registrationLink = $dynamicOrgUrl . '/school/' . $schoolSlug . '/admin/studentRegister.html';

$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = '';
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

$loggedUserRoleType = '';
$loggedUserRoleType = $_SESSION["loggedUserRoleType"];

$isActiveCheckoff = $_SESSION["isActiveCheckoff"];

//Get School Name
$rankId = 0;
//CREATE OBJECT
$objStudents = new clsStudent();
$totalSchoolStudents = 0;
$accreditationId = 0;
$currentstudentId  = 0;
$currentSchoolId;
$Type = 'C';
$isDeleted = '';
$studentImmunizationId = 0;
if (isset($_GET['rankId'])) {
    $rankId = $_GET['rankId'];
    $rankId = DecodeQueryData($rankId);
}
if (isset($_GET['isDeleted'])) {
    $isDeleted = $_GET['isDeleted'];
}
if (isset($_GET['studentId'])) {
    $currentstudentId = $_GET['studentId'];
    $currentstudentId = DecodeQueryData($currentstudentId);
}
//SchoolStudents
if (isset($_GET['view'])) {
    $rowsSchoolStudents = $objStudents->GetAllStudents($currentSchoolId);
    if ($rowsSchoolStudents != '') {
        $totalSchoolStudents = mysqli_num_rows($rowsSchoolStudents);
    }
} else {

    $rowsSchoolStudents = $objStudents->GetAllSchoolStudentList($currentSchoolId, $rankId, $currentstudentId);
    if ($rowsSchoolStudents != '') {
        $totalSchoolStudents = mysqli_num_rows($rowsSchoolStudents);
    }
}
if (isset($_GET['studentId'])) {
    $studentId = $_GET['studentId'];
    $studentId = ($studentId);
}
if (isset($_GET['reaccreditationId'])) {
    $reaccreditationId = $_GET['reaccreditationId'];
    $reaccreditationId = ($reaccreditationId);
}


//StudentRank
$objStudentRankMaster = new clsStudentRankMaster();
$ranks = $objStudentRankMaster->GetAllStudentRankBySchool($currentSchoolId);
unset($objStudentRankMaster);

$isRow = isset($_GET['isRow']) ? $_GET['isRow'] : 0;
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Students Profile </title>
    <?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">

    <style>
        .mt-1 {
            margin-top: 10px;
            padding-left: 55px;
        }
        .dt-bootstrap{
            overflow-x: auto;
        }

        thead tr th{
            text-align: center;
        }

        .select2-container{
            width: 100% !important;
        }
    </style>

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active">Students</li>
                </ol>
            </div>
            <div class="pull-right">
                <a class="btn btn-link impostStudentList" href="importstudent.html">Import </a>|
                <a class="btn btn-link" href="addstudent.html">Add</a>
            </div>
        </div>
    </div>

    <div class="custom-container">

        <?php

        if (isset($_GET["status"])) {

            if ($_GET["status"] == "added") {

        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Student added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Student updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "StatusUpdated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Student status updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error" && $isRow && $_GET["type"] == "0" && $_GET["value"] != '') {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred while importing data from the CSV file at Row No. <?php echo $isRow; ?>. The Record Id number <?php echo $_GET["value"]; ?> is already exist.
                </div>
            <?php
            } else if ($_GET["status"] == "Error" && $isRow && $_GET["type"] == "1" && $_GET["value"] != '') {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred while importing data from the CSV file at Row No. <?php echo $isRow; ?>. The Phone number <?php echo $_GET["value"]; ?> is already exist.
                </div>
            <?php
            } else if ($_GET["status"] == "Error" && $isRow && $_GET["type"] == "2" && $_GET["value"] != '') {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred while importing data from the CSV file at Row No. <?php echo $isRow; ?>. Rank <?php echo "'" . $_GET["value"] . "'"; ?> is not exist.                </div>
            <?php
            } else if ($_GET["status"] == "Error" && $isRow && $_GET["type"] == "3" && $_GET["value"] != '') {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred while importing data from the CSV file at Row No. <?php echo $isRow; ?>. Location <?php echo "'" . $_GET["value"] . "'"; ?> is not exist.                </div>
            <?php
            } else if ($_GET["status"] == "Error" && $isRow) {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred while importing data from the CSV file at Row No. <?php echo $isRow; ?> . Please review the file format, header row, data content.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
            <?php
            } else if ($_GET["status"] == "datanotfound") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Certification log not available.
                </div>
            <?php
            } else if ($_GET["status"] == "Imported") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Student Imported
                </div>
            <?php
            } else if ($_GET["status"] == "Importerror") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Please Import .csv file
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <div class="row margin_bottom_fourty">
            <div class="col-md-11">
                <div class="form-group">
                    <label class="col-md-2 control-label margin_top_five padding_zero" for="">Registration Link:</label>
                    <div class="col-md-6">
                        <input id="copyText" type="text" class="form-control" value="<?php echo $registrationLink; ?>" readonly>
                    </div>
                    <button class="btn btn-success" id="copyBtn" onclick="CopyToClipboard('copyText')">Click to copy</button>

                    <button class="btn btn-success " id="privateBtn" style="margin-left: 2px;" data-toggle="modal" data-target="#generateLinkModal">Private Link</button>

                </div>

            </div>
            <div class="col-md-1 text-right">
                <a href="schoolstudents.html?view=all" class="btn btn-success pull-right">Display All</a>
            </div>
        </div>
        <!-- Modal -->
        <div class="modal fade" id="generateLinkModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
            <div class="modal-dialog" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title">Generate Private Link</h4>
                    </div>
                    <div class="modal-body">
                        <div class="modal-body">

                            <form id="resendForm" data-parsley-validate class="form-horizontal" method="POST">

                                <div class="form-group isPreceptorDiv">
                                    <table id="preceptorTable" width="100%">
                                        <tr>
                                            <td>
                                                <label for="email_emailId"> Email: </label>
                                                <input type="email" id="email_emailId" name="email_emailId" value="" placeholder="Enter email" class=" form-control margin_right_five" required>
                                            </td>
                                        </tr>
                                    </table>

                                    <small style="color: #201f1fdb;">
                                    </small>
                                </div>
                                <div>
                                    <button id="btnSendEmail" name="btnSendEmail" type="button" class="btn btn-success btnSendEmail" style="float: right;">Get Link</button>
                                    <br><br><span id="genrateLink" style="display: none; float: left;"> </span><br>
                                </div>
                            </form>

                        </div>

                        <!-- <div class="modal-footer"> -->

                        <!-- </div> -->
                        <!-- /.modal-content -->
                    </div>
                    <!-- /.modal-dialog -->
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-md-3 pull-right">
                <div class="form-group">
                    <label class="col-md-4 control-label mt-1" for="cborank">Rank</label>
                    <div class="col-md-8 pr-0">
                        <select id="cborank" name="cborank" class="form-control select2_single">
                            <option value="" selected>Select All</option>

                            <?php
                            if ($ranks != "") {
                                while ($row = mysqli_fetch_assoc($ranks)) {

                                    $selrankId  = $row['rankId'];
                                    $name  = stripslashes($row['title']);

                            ?>
                                    <option value="<?php echo EncodeQueryData($selrankId); ?>" <?php if ($rankId == $selrankId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                            <?php

                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
            </div>
        </div> <br>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Rank</th>
                    <th>Contact Info</th>
                    <?php if ($isActiveCheckoff == 2) { ?> <th>Location</th> <?php } ?>
                    <th>Account Created</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalSchoolStudents > 0) {
                    while ($row = mysqli_fetch_array($rowsSchoolStudents)) {
                        $studentId = '';
                        $studentId = $row[0];
                        $firstName = stripslashes($row['firstName']);
                        $lastName = stripslashes($row['lastName']);
                        $fullName = $firstName . ' ' . $lastName;
                        $email = stripslashes($row['email']);
                        $phone = stripslashes($row['phone']);
                        $cellPhone = stripslashes($row['cellPhone']);
                        $IsPublished = $row['isActive'];
                        $isBlocked = $row['isBlocked'];
                        $rank = stripslashes($row['rank']);
                        $createdDate = ($row['createdDate']);
                        $createdDate = date("m/d/Y", strtotime($createdDate));
                        $location = ($row['location']);
                        $updateBlockStatus = "0";
                        $buttoncss = "btn-primary";

                        if ($IsPublished == "1") {
                            $displayStatus = "Active";
                            $updateStatus = "0";
                            $buttoncss = "text-primary";
                            $isShowemail = 1;
                        } else {
                            $displayStatus = "Inactive";
                            $updateStatus = "1";

                            $buttoncss = "text-warning";
                            $isShowemail = 0;
                        }


                        if ($isBlocked == "0") {
                            $displayBlockStatus = "Unlocked";
                            $updateBlockStatus = "1";
                            $buttoncss = "text-primary";
                        } else {
                            $displayBlockStatus = "Padlock";
                            $updateBlockStatus = "0";
                            $buttoncss = "text-warning";
                        }
                        $TotaldocumentCount = $objStudents->getDocumentsCount($studentId, '', 0, 1);
                        $countDocument = $TotaldocumentCount['countDocument'];
                        $objDB = new clsDB();
                        $accrediation = $objDB->GetSingleRowValuesFromTable('accreditation', 'studentId', $studentId);
                        $graduationDate = $accrediation['actualGraduationDate'] ?? '';
                        $dropoutDate = $accrediation['programDropDate'] ?? '';

                        if ($graduationDate != '' && $graduationDate != '0000-00-00' && $graduationDate != '1969-12-31') {
                            $graduationDate = date("m/d/Y", strtotime($graduationDate));
                        } else {
                            $graduationDate = '';
                        }

                        if ($dropoutDate != '' && $dropoutDate != '0000-00-00' && $dropoutDate != '1969-12-31') {
                            $dropoutDate = date("m/d/Y", strtotime($dropoutDate));
                        } else {
                            $dropoutDate = '';
                        }


                ?>
                        <tr>
                            <td> <?php echo ($firstName); ?></td>
                            <td><?php echo ($lastName); ?></td>
                            <td>
                                <?php echo ($rank); ?>
                                <?php if ($graduationDate) { ?>
                                    <br /> Graduation Date: <?php echo '<span style="color: green;">' . $graduationDate . '</span>'; ?>
                                <?php } ?>
                                <?php if ($dropoutDate) { ?>
                                    <br /> Drop Date: <?php echo '<span style="color: red;">' . $dropoutDate . '</span>'; ?>
                                <?php } ?>

                            </td>
                            <td>
                                Email: <a href="mailto:<?php echo ($email); ?>">
                                    <?php echo ($email); ?>
                                </a> <br>
                                Mobile No: <a href="tel:<?php echo ($phone); ?>">
                                    <?php echo ($phone); ?>
                                </a>
                                <?php if ($cellPhone) { ?>
                                    <br>
                                    Alternate No: <a href="tel:<?php echo ($cellPhone); ?>">
                                        <?php echo ($cellPhone); ?>
                                    </a>
                                <?php } ?>
                            </td>
                            <?php if ($isActiveCheckoff == 2) { ?> <td><?php echo $location; ?></td> <?php } ?>
                            <td><?php echo ($createdDate); ?>
                                <br>
                                Status: <?php echo ($displayBlockStatus); ?>
                            </td>
                            <td style="text-align: center">

                                <a href="accreditation.html?studentId=<?php echo EncodeQueryData($studentId); ?>&rankId=<?php echo EncodeQueryData($rankId); ?>">Accreditation</a>
                                |<a href="AdditationlContactInformation.html?studentId=<?php echo EncodeQueryData($studentId); ?>&rankId=<?php echo EncodeQueryData($rankId); ?>">Additional Contact Information</a>
                                |<a href="CertificationLog.html?studentId=<?php echo EncodeQueryData($studentId); ?>&rankId=<?php echo EncodeQueryData($rankId); ?>">Certification Log</a>
                                |<a href="singlestudentimmunization.html?studentId=<?php echo EncodeQueryData($studentId); ?>&rankId=<?php echo EncodeQueryData($rankId); ?>" class="Immunization">Immunization</a>
                                |<a href="rotationhistory.html?studentId=<?php echo EncodeQueryData($studentId); ?>&rankId=<?php echo EncodeQueryData($rankId); ?>">Rotation History</a>
                                |<a href="studentDocuments.html?studentId=<?php echo EncodeQueryData($studentId); ?>&Type=C">Documents</a> <?php if ($countDocument > 0) { ?><span class="badge"><?php echo ($countDocument); ?></span> <?php } ?>

                                |<a id="active_<?php echo ($studentId); ?>" class="<?php echo ($buttoncss); ?> isActive" href="javascript:void(0);" studentId="<?php echo ($studentId); ?>" activestatus="<?php echo ($updateStatus); ?>">
                                    <?php echo ($displayStatus); ?>
                                </a>
                                |<a id="block_<?php echo ($studentId); ?>" href="javascript:void(0);" studentId="<?php echo ($studentId); ?>" blockstatus="<?php echo ($updateBlockStatus); ?>" class="<?php echo ($buttoncss); ?> isBlock"><?php echo ($displayBlockStatus); ?></a>

                                <?php if ($isShowemail == 1) { ?>
                                    |<a href="javascript:void(0);" studentId="<?php echo ($studentId); ?>" currentSchoolId="<?php echo ($currentSchoolId); ?>" class="loginEmailAjaxRow" schoolStudentFullName="<?php echo ($fullName); ?>">Send email</a>
                                <?php } ?>
                                <?php if ($loggedUserRoleType != 'H') {  ?>
                                    |<a href="javascript:void(0);" class="loginAsSchoolUser" studentId="<?php echo (EncodeQueryData($studentId)); ?>" schoolStudentFullName="<?php echo ($fullName); ?>" schoolId="<?php echo (EncodeQueryData($currentSchoolId)); ?>">Login As Student</a>
                                <?php } ?>
                                |<a href="addstudent.html?id=<?php echo (EncodeQueryData($studentId)); ?>">Edit</a>
                                <?php if ($loggedUserRoleType != 'H') {  ?>
                                    |<a href="javascript:void(0);" class="deleteAjaxRow" studentId="<?php echo EncodeQueryData($studentId); ?>" schoolStudentFullName="<?php echo ($fullName); ?>">Delete</a>
                                <?php } ?>

                            </td>
                        </tr>
                <?php


                    }
                }
                unset($objStudents);
                ?>



            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_tags").select2({
                'placeholder': 'Select'
            }); //for multiple selection
            $(".select2_single").select2();
            var isDeleted = '<?php echo $isDeleted ?>';
            if (isDeleted == 'Success')
                alertify.success('Deleted');

            var newUrl = 'schoolstudents.html';
            history.pushState({}, null, newUrl);
        });

        // $('.impostStudentList').magnificPopup({'type': 'ajax',});	

        $('#privateBtn').on('click', function() {
            $('#email_emailId').val('');
            $('#genrateLink').text('');

        });
        $('.impostStudentList').magnificPopup({
            type: 'ajax',
            'closeOnBgClick': false
        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "aoColumns": [{
                    "sWidth": "1%"
                }, {
                    "sWidth": "1%"
                },
                <?php if ($isActiveCheckoff == 2) { ?> {
                        "sWidth": "1%"
                    },
                <?php } ?> {
                    "sWidth": "1%"
                }, {
                    "sWidth": "1%",
                    "sClass": "alignCenter",
                }, {
                    "sWidth": "1%"
                },
                {
                    "sWidth": "95%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }
            ]
        });

        function ShowDeleteMessage() {
            alertify.alert('Warning', 'This is the Primary User. You can\'t delete this.');
        }


        $(document).on('click', '.loginAsSchoolUser', function() {

            var schoolId = $(this).attr('schoolId');
            var studentId = $(this).attr('studentId');
            var FullName = $(this).attr('schoolStudentFullName');

            alertify.confirm('Login Confirmation', 'Continue with login as ' + FullName + '?', function() {
                window.location.href = 'loginasschoolstudent.html?schoolId=' + schoolId + '&userId=' + studentId;
            }, function() {});

        });

        $(document).on('click', '.btnStudentImport', function() {

            var schoolId = $(this).attr('schoolId');
            var studentId = $(this).attr('studentId');
            var FullName = $(this).attr('schoolStudentFullName');

            alertify.confirm('Login Confirmation', 'Continue with login as ' + FullName + '?', function() {
                window.location.href = 'loginasschoolstudent.html?schoolId=' + schoolId + '&userId=' + studentId;
            }, function() {});

        });

        $(document).on('click', '.loginEmailAjaxRow', function() {

            var studentId = $(this).attr('studentId');
            var schoolStudentFullName = $(this).attr('schoolStudentFullName');
            var currentSchoolId = '<?php echo $currentSchoolId; ?>';


            alertify.confirm('Student Name: ' + schoolStudentFullName, 'Continue with send login detail\'s email?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_login_email_to_school_user.html",
                    data: {
                        id: studentId,
                        schoolId: currentSchoolId,
                        type: 'student'
                    },
                    success: function() {
                        alertify.success('Sent');
                    }
                });
            }, function() {});

        });

        $("#cborank").change(function() {
            var rankId = $(this).val();

            if (rankId) {
                window.location.href = "schoolstudents.html?rankId=" + rankId;
            } else {
                window.location.href = "schoolstudents.html";
            }
        });

        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentId = $(this).attr('studentId');
            var schoolStudentFullName = $(this).attr('schoolStudentFullName');
            var SchoolId = '<?php echo ($currentSchoolId); ?>';
            var rankId = '<?php echo ($rankId); ?>';
            var redirectUrl = (rankId > 0) ? 'schoolstudents.html?isDeleted=Success&rankId=' + btoa(rankId) : 'schoolstudents.html?isDeleted=Success';

            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1; //for Admin
            alertify.confirm('Student Name: ' + schoolStudentFullName, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentId,
                        SchoolId: SchoolId,
                        userId: userId,
                        isUser: isUser,
                        type: 'student'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        window.location.href = redirectUrl;
                        // alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $(document).on('click', '.isBlock', function() {

            var studentId = $(this).attr('studentId');
            var updateblockstatus = $('#block_' + studentId).attr('blockstatus');
            var thisBlock = $(this);

            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1; //for Admin

            $.ajax({
                type: "POST",
                url: "schoolstudentstranssubmit.html",
                data: {
                    id: studentId,
                    newblockstatus: updateblockstatus,
                    userId: userId,
                    isUser: isUser,
                    type: 'block'
                },
                success: function(responseData) {
                    $('#block_' + studentId).attr('blockstatus', responseData);
                    if (responseData == 1) {
                        thisBlock.text('Unlocked');
                    } else {
                        thisBlock.text('Padlock');
                    }
                }
            });

        });

        $(document).on('click', '.isActive', function() {

            var studentId = $(this).attr('studentId');
            var updateblockstatus = $('#active_' + studentId).attr('activestatus');
            var thisBlock = $(this);

            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1; //for Admin

            $.ajax({
                type: "POST",
                url: "schoolstudentstranssubmit.html",
                data: {
                    id: studentId,
                    newStatus: updateblockstatus,
                    userId: userId,
                    isUser: isUser,
                    type: 'status'
                },
                success: function(responseData) {
                    $('#active_' + studentId).attr('activestatus', responseData);
                    if (responseData == 1) {
                        thisBlock.text('Inactive');
                    } else {
                        thisBlock.text('Active');
                    }
                }
            });

        });

        $(document).on('click', '#btnSendEmail', function() {

            var email = $('#email_emailId').val();
            var SchoolId = '<?php echo ($currentSchoolId); ?>';
            var emailRegex = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
            if (email == '') {
                alertify.error('Please enter Email');
                return false;
            }

            if (emailRegex.test(email)) {
                $.ajax({
                    type: "POST",

                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_check_duplicate_email.html",
                    data: {
                        type: 'student',
                        currentEmail: email,
                        schoolId: SchoolId
                    },
                    success: function(responseData) {
                        if (responseData == 1) {
                            alertify.error("Email Already Exist.");
                            $("#email_emailId").val('');
                            return false;
                        } else {
                            $('.btnSendEmail').prop('disabled', true);
                            $.ajax({
                                type: "POST",
                                url: "../ajax/ajax_copy_link.html",
                                data: {
                                    email: email,
                                    evaluationType: 'privateLink'
                                },
                                success: function(data) {

                                    $('.btnSendEmail').prop('disabled', false);
                                    console.log(data);
                                    var linkURL = data;
                                    if (linkURL != '') {
                                        $('#genrateLink').text('URL: ' + linkURL);
                                        $('#genrateLink').show();
                                        try {
                                            navigator.clipboard.writeText(linkURL)
                                                .then(function() {
                                                    success = true;
                                                    // alertify.success('URL copied to clipboard');

                                                })
                                            var textArea = document.createElement('textarea');
                                            textArea.value = linkURL;
                                            document.body.appendChild(textArea);
                                            textArea.select();
                                            success = document.execCommand('copy');
                                            document.body.removeChild(textArea);
                                        } catch (err) {
                                            console.error('Error copying to clipboard manually: ', err);
                                        }

                                        if (success) {
                                            alertify.success('URL copied to clipboard');
                                        }
                                        // else {

                                        //     $('#genrateLink').text('URL: ' + linkURL);
                                        //     $('#genrateLink').show();
                                        // }
                                        // Display the copied URL in the span element

                                        // })
                                    }
                                    // window.location.reload();


                                }
                            });
                        }


                    }
                });
            } else {
                alertify.error('Invalid Email');
                return false;
            }

            // $('.btnSendEmail').html(loadingText());

        });
    </script>
</body>

</html>