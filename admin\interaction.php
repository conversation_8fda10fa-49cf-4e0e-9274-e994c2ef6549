<?php 
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsRotation.php');  
    include('../class/clsLocations.php');  
    include('../class/clsInteraction.php');  
    include('../class/clsStudent.php'); 
	include('../setRequest.php'); 
	
	$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
    $loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0; 
    $canvasStatus= isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';
    $schoolId = 0;
	$studentId=0;
    $currentstudentId=0;
	$rotationtitle ='';
    $transchooldisplayName = '';
	$DefaultrotationId=0;
	$interactionSchoolDate=null;
    $interactionClinicianDate=null;
    $TotalpointsAwarded=0;
    $TotaltimeSpent=0;
    $TotatlHours=0;
    $Totalminutes =0;
    $TotalHoursInMinutes=0;

    if(isset($_GET['schoolId'])) //Edit Mode
	{
		$schoolId = $_GET['schoolId'];
        $schoolId = DecodeQueryData($schoolId);
    }
    else
    {
        $schoolId = $currentSchoolId;
        $transchooldisplayName=$currenschoolDisplayname;
    }

    $title ="Dr.Interaction  ";
    $totalInteraction = 0;
	$bedCrumTitle = 'Clinical';
    
    //For Rotation Site
    $encodedRotationId = '';
	if(isset($_GET['rotationId'])) 
	{
        $encodedRotationId = $_GET['rotationId'];
		$DefaultrotationId = $_GET['rotationId'];
        $DefaultrotationId = DecodeQueryData($DefaultrotationId);
        $rotationId = DecodeQueryData($DefaultrotationId);
    }
    $encodedStudentId = '';
    //For Student Side
	if(isset($_GET['studentId'])) 
	{
		$studentId = $_GET['studentId'];
		$encodedStudentId = $_GET['studentId'];
        //$studentId = DecodeQueryData($studentId);
        $currentstudentId = DecodeQueryData($studentId);
    }

    $type = isset($_GET['type']) ? $_GET['type'] : '';
	if($type == 'canvas')
		$canvasStatus =1;
			
	//For Rotation Title
	$objrotation = new clsRotation();
	$rotation = $objrotation->GetRotationBySchool($schoolId);
    $rowsrotation=$objrotation->GetrotationTitleForInteraction($DefaultrotationId,$schoolId);
	$rotationtitle = $rowsrotation['title'];	

    //For All Interaction List
    $objInteraction = new clsInteraction();
	$rowsInteraction = $objInteraction->GetAllInteraction($schoolId,$DefaultrotationId,$currentstudentId,0,$canvasStatus);

	if($rowsInteraction !='')
	{
		$totalInteraction =mysqli_num_rows($rowsInteraction);
    }
    unset($objInteraction);
    
    //For Student Name
	$objStudent = new clsStudent();
	$rowsStudents = $objStudent->GetSingleStudent($currentSchoolId,$currentstudentId);
	$studentfullname=($rowsStudents['firstName'] . ' ' . $rowsStudents['lastName']);
	unset($objStudent);	
	
  
	
?>
    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo($title); ?></title>
        <?php include('includes/headercss.php');?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
        <?php include("includes/datatablecss.php") ?>

		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css"> 
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <?php if($type == 'canvas') { ?>
							<li><a href="dashboard.html">Home</a></li>
							<li><a href="settings.html">Settings</a></li>
							<li class="active">Dr.Interaction</li>
						<?php } else { ?>
                            <li><a href="dashboard.html">Home</a></li>
                            <?php if ($currentstudentId) 
                                { ?>
                            <li class="active"><a href="clinical.html">Clinical</a></li>
                            <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                            <li class="active">Dr.Interaction</li> 
                            
                            <?php } else { ?>
                            <li class="active"><a href="rotations.html"> Rotations </a></li>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li> 
                            <li class="active">Dr.Interaction</li> 
                                
                            <?php } ?>
                        <?php } ?>
                       
                        
                    </ol>
                </div>
            </div>
        </div>

        <div class="custom-container">
		<?php 
             if (isset($_GET["status"]))
                    {
                        if($_GET["status"] =="Added")
                        {
                            ?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                                </button> Interaction added successfully.
                    </div>
                    <?php 
                        }
                        else if($_GET["status"] =="Updated")
                        {
                            ?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                                </button> Interaction updated successfully.
                    </div>
                    
                   <?php 
                        }
                        else if($_GET["status"] =="Deleted")
                        {
                            ?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                                </button> Interaction deleted successfully.
                    </div>
                    <?php 
                        }
                         else if($_GET["status"] =="Error")
                        {
                            ?>
                    <div class="alert alert-danger alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                                </button> Error occurred.
                    </div>
                    <?php 
                        }
    
                    }
                  ?>
				  
                <div id="divTopLoading" >Loading...</div>

                <!--  -->
                <div class="row margin_bottom_ten">
                    <div class="col-md-5"></div>
                    <div class="col-md-4">
                    <?php if($isActiveCanvas && $type !='canvas') { ?>
                        <div class="form-group">
                            <label class="col-md-6 control-label text-right" for="" style="margin-top:8px">Canvas Status:</label>
                            <div class="col-md-6 padding_right_zero padding_left_zero">
                                <select id="canvasStatus" name="canvasStatus" class="form-control input-md required-input select2_single" studentId="<?php echo EncodeQueryData($currentstudentId); ?>" >
                                    <option value="" selected>All</option>
                                    <option value="1" <?php if($canvasStatus==1) { ?> selected="true" <?php } ?>>Sent</option>
                                    <option value="0" <?php if($canvasStatus==0 && $canvasStatus!='' ) { ?> selected="true" <?php } ?>>Not Sent</option>
                                </select>
                            </div>
                        </div>
                    <?php } ?>
                    </div>
                    <div class="col-md-3">
                        <div class="form-group">
                                    <label class="col-md-4 control-label" for="cbostudent" style="margin-top:8px">Rotation</label>
                                    <div class="col-md-8 padding_right_zero padding_left_zero">
                                        <select id="cboRotation" name="cboRotation" class="form-control input-md required-input select2_single"  >
                                        <option value="" selected>Select</option>
                                        
                                            <?php
                                            
                                                if($rotation!="")
                                                {
                                                    while($row = mysqli_fetch_assoc($rotation))
                                                    {
                                                        $selrotationIdDropdown  = $row['rotationId'];
                                                        $selrotationId='';
                                                        $name  = stripslashes($row['title']);
                                                        
                                                        ?>
                                                        <option value="<?php echo(EncodeQueryData($selrotationIdDropdown)); ?>" <?php if($DefaultrotationId==$selrotationIdDropdown){ ?>  selected="true" <?php }?>><?php echo($name); ?></option>
                                                        <?php
                                            
                                                    }
                                                }
                                                
                                        ?>
                                        </select>
                                        
                                        <input type="hidden" name="hidenstudentid" id="hidenstudentid" value="<?php echo EncodeQueryData($currentstudentId); ?>">
                                    </div>
                                </div>
                    </div>
                    
                </div>
                <!--  -->
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th style="text-align:center">Interaction<br>Date </th>
                           
							<?php if ($currentstudentId){ ?>
                            <th>Rotation</th> <?php 
							} else { ?>
							<th>Student<br>First Name</th>
							<th>Student<br>Last Name</th>
							<th>Rank</th>
							<?php } ?>
							
                            <th style="text-align:center">Student<br>Signature</th>
							 <th style="text-align:center">Clinician<br>Signature</th>
                            <th style="text-align:center">School<br>Signature</th>
                            <th style="text-align:center">Time Spent <br>(hh:mm)</th>
                            <th style="text-align:center">Points<br>Awarded</th>
                            <th>School<br>Response</th>  
                            <?php if($type !='canvas') { ?>                          
                            <th style="text-align:center">Action</th>
                            <?php } ?>
                            <?php if($isActiveCanvas && $type !='canvas') { ?>
                            	<th class="text-center">Canvas Status</th>
                            <?php } ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalInteraction>0)
                        {
                            while($row = mysqli_fetch_array($rowsInteraction))
                            {
								$interactionId = $row['interactionId'];
								$studentId = $row['studentId'];
								$interactionDate = $row['interactionDate'];
								$rotationId = stripslashes($row['rotationId']);
								$courselocationId = $row['locationId'];
								$parentRotationId = stripslashes($row['parentRotationId']);
								$rotationLocationId = stripslashes($row['rotationLocationId']);
								
								$locationId = 0;
									if($rotationLocationId != $courselocationId && $parentRotationId > 0)
								{
									if($parentRotationId > 0)
									{
										if(!$rotationLocationId)
											$locationId = $objrotation->GetLocationByRotation($rotationId);
										else
											$locationId  = $rotationLocationId;
									}
								}
								else
								{	
                                    $locationId  = $courselocationId;
								}
									
								//Get Time Zone By Rotation 
								$objLocation = new clsLocations();
								$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
								unset($objLocation);
								if($TimeZone =='')
									$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
								$interactionDate = converFromServerTimeZone($interactionDate,$TimeZone);
								$interactionDate =date("m/d/Y",strtotime($interactionDate));
								$firstName = $row['firstName'];	
                                $lastName = $row['lastName'];	
                                $rank = $row['rank'];	
								$fullName= $firstName.' '.$lastName;
                                $pointsAwarded = $row['pointsAwarded'];													
                                $clinicianSummary = $row['clinicianSummary'];
                                $interactionClinicianDate = $row['interactionClinicianDate'];
                                $interactionClinicianDate = converFromServerTimeZone($interactionClinicianDate,$TimeZone);
                                $interactionClinicianDate=date("m/d/Y",strtotime($interactionClinicianDate));
								 $interactionSchoolDate = $row['interactionSchoolDate'];
                                $interactionSchoolDate = converFromServerTimeZone($interactionSchoolDate,$TimeZone);
                                $interactionSchoolDate =date("m/d/Y",strtotime($interactionSchoolDate));
								
                                $interactionSummary = $row['interactionSummary'];
							    $schoolSummary = $row['schoolSummary'];	
							    $rotationname = $row['rotationname'];	
                                $studentfirstName = $row['studentfirstName'];													
                                $approved = $row['approved'];
                                $timeSpent = $row['timeSpent'];
                                $HospitalsitesName = $row['HospitalsitesName'];
                                $studentlastName = $row['studentlastName'];
                                $studentfullName= $studentfirstName.'   '.$studentlastName;
								$totalInteraction = 0;
								$totalUserCount = 0;
                                //$totalUserCount = $objSystemUserRoleMaster->GetSystemUserCountByRole($courseId);

                                $TotalpointsAwarded += $pointsAwarded ? $pointsAwarded :0;

                                $Totalminutes += $timeSpent ? $timeSpent :0;
          
                                if($interactionClinicianDate != '' && $interactionClinicianDate != '0000-00-00' && $interactionClinicianDate != '01/01/1970' && $interactionClinicianDate != '12/31/1969')
                                    $interactionClinicianDate = $interactionClinicianDate;
								else
                                    $interactionClinicianDate =  '-';
								
                                if($interactionSchoolDate != '' && $interactionSchoolDate != '0000-00-00' && $interactionSchoolDate != '01/01/1970' && $interactionSchoolDate != '12/31/1969')
                                    $interactionSchoolDate = $interactionSchoolDate; 
                                else
                                    $interactionSchoolDate = '-';
                                
                                if($interactionDate != '' && $interactionDate != '0000-00-00' && $interactionDate != '01/01/1970' && $interactionDate != '12/31/1969')
                                    $interactionDate = date("m/d/Y",strtotime($interactionDate));
                                else 
                                    $interactionDate =  '-';
                                    
                                 // For Canvas
								$isSendToCanvas  = isset($row['isSendToCanvas']) ? $row['isSendToCanvas'] : 0;

                                $isSendToCanvasClass ='isSendRecordToCanvas';
                                $isSentToCanvasClass ='hide';
                                if($isSendToCanvas)
                                {
                                    $isSendToCanvasClass ='hide';
                                    $isSentToCanvasClass ='';
                                }

								$isUserCanSendCompletedRecordToCanvas = 0;
								if($interactionClinicianDate != '-' || $interactionSchoolDate !='-')
									$isUserCanSendCompletedRecordToCanvas = 1;

                                // -- End Canvas --//

                                ?>
                            <tr>
                                <td>
                                    <?php echo ($interactionDate);?>
                                </td>

								<?php if ($currentstudentId) { ?>
                                <td><?php echo ($rotationname); ?>
                                    <br><small>Hospital:<?php echo $HospitalsitesName; ?></small>
                                </td> <?php } else {?>
								<td><?php echo ($studentfirstName);?></td>
								<td><?php echo ($studentlastName);?></td>
								<td><?php echo ($rank);?></td>
								<?php } ?>
								
								<td style="text-align: center">
                                   <?php echo $interactionDate; ?>
                                </td>
								
								<td style="text-align: center">
                                   <?php echo $interactionClinicianDate; ?>
                                </td>
								<td style="text-align: center">
                                    <?php echo $interactionSchoolDate; ?>
                                </td>
								<td style="text-align: center">
                                   <?php echo ($timeSpent); ?>
                                </td>
                                <td style="text-align: center">
                                   <small> <?php echo ($pointsAwarded); ?></small>
                                </td>
                                 <td style="text-align: center; ">
                                   <small> <?php 
                                   if ($schoolSummary == '') 
                                        echo 'No';
                                    else
                                        echo 'Yes';
                                     ?></small>
                                </td>   
                                <?php if($type !='canvas') { ?>                             
                                    <td style="text-align: center">
                                    <?php
									$rotationStatus = checkRotationStatus($rotationId);
									$viewParam = ($rotationStatus) ? '&view=V' : ''; // Add '&view=V' if $rotationStatus is true
									$linkText = ($rotationStatus) ? 'View' : 'Edit'; // Change link text based on $rotationStatus
									?>
                                    <a href="addinteraction.html?interactionId=<?php echo(EncodeQueryData($interactionId)); ?>
                                    <?php if ($currentstudentId){?>&studentId=<?php echo (EncodeQueryData($currentstudentId));}else { ?>&rotationId=<?php echo EncodeQueryData($DefaultrotationId); }?> <?php echo $viewParam; ?>"><?php echo $linkText; ?></a>  
                                    
                                    
                                        <!-- <div class="hide">	 -->
                                        <?php if($approved==0) { ?>
                                    | <a  href="javascript:void(0);" class="deleteAjaxRow" interactionId="<?php echo EncodeQueryData($interactionId); ?>" >Delete</a> 
                                        <?php } else { ?>
                                    | <a href="javascript:void(0);" onclick="javascript:ShowwarningMessage();"  class="text-muted" interactionId="<?php echo EncodeQueryData($interactionId); ?>" >Delete</a>
                                        <?php } ?>
                                    <!-- </div>	 -->
                            
                                    </td>
                                <?php } ?>
                                <?php if($isActiveCanvas && $type !='canvas') 
										{ 
											if($loggedUserSendRecordToCanvas && $isUserCanSendCompletedRecordToCanvas) 
											{ ?>
											<td class="text-center">
												<a href="javascript:void(0);" id="isSendToCanvas_<?php echo $interactionId; ?>" class="<?php echo $isSendToCanvasClass;?>" 
                                                    interactionDate="<?php echo $interactionDate; ?>"
													rotation="<?php echo $rotationname; ?>" 
													interactionClinicianDate="<?php echo $interactionClinicianDate; ?>"
													interactionSchoolDate="<?php echo $interactionSchoolDate; ?>"
													timeSpent="<?php echo $timeSpent; ?>"
													pointAwarded="<?php echo $pointsAwarded; ?>"
													interactionId="<?php echo $interactionId; ?>"
													studentId="<?php echo $studentId; ?>"
													studentFullName="<?php echo $studentfullName; ?>"
													>
													Send to Canvas
												</a>
												<label for=""  class="isSentToCanvas_<?php echo $interactionId; ?> <?php echo $isSentToCanvasClass; ?>">Sent</label>
												
											</td>
									
									<?php } 
											else
											{ ?>
											<td class="text-center"><label for=""  class=""> - 
												<?php //if($isSendToCanvas>0) { echo 'Sent'; } else { echo 'Send to canvas'; } ?>
											</label></td>
												
										<?php } 
										
									}
								?>
                            </tr>
                            <?php
                            }
                        }
						unset($objrotation);
                    ?>
                    </tbody>
                    <tfoot>
                            <tr>
                            <?php $CalTotalMinutes =$TotalHoursInMinutes + $Totalminutes; 
                                      $Total = $CalTotalMinutes/60;
                                
                                ?>
                                
                                 <?php if ($currentstudentId){ ?>
                                <td colspan="5" align="right" ><b>Total:</b></td>
                                <?php } 
                                else { ?>
                                <td <?php if($type =='canvas') { ?> colspan="6" <?php } else { ?> colspan="7" <?php } ?> align="right" ><b>Total:</b></td>
                                <?php } ?>
                                <td align="center">
                                <?php echo sprintf("%02d:%02d", floor($Totalminutes/60), $Totalminutes%60); ?>
                                
                                </td>
                                <td align="center"><?php echo $TotalpointsAwarded; ?></td>
                                <td>&nbsp;</td>
                                <td>&nbsp;</td>
                            </tr>
                    </tfoot>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

        <script type="text/javascript">

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


            $(window).load(function(){
                $(".select2_single").select2(); 
                $("#divTopLoading").addClass('hide');
            });

            var current_datatable = $("#datatable-responsive").DataTable({

                "scrollX": true,
                responsive :false,
                "ordering": true,
                "order": [[0, "desc" ]]   

            });

            // ajax call for deleteAjaxRow
			
			// ajax call for delete
		  function ShowwarningMessage()
            {
                alertify.alert('Warning','Check School Response. You can\'t delete this.');
            }    
  			
             $(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var interactionId = $(this).attr('interactionId');
                
                
                alertify.confirm('Interaction: ', 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: interactionId,
                            type: 'interaction'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });
			

            $(document).on('click', '.isSendRecordToCanvas', function() {
                var that = this;
                var interactionDate = $(this).attr('interactionDate');
                var rotation = $(this).attr('rotation');
                var interactionClinicianDate = $(this).attr('interactionClinicianDate');
                var interactionSchoolDate = $(this).attr('interactionSchoolDate');
                var timeSpent = $(this).attr('timeSpent');
                var pointAwarded = $(this).attr('pointAwarded');
                var interactionId = $(this).attr('interactionId');
                var studentId = $(this).attr('studentId');
                var studentFullName = $(this).attr('studentFullName');
                var schoolId = "<?php echo $currentSchoolId; ?>";

                alertify.confirm('Dr. Interaction', 'Continue with send record to Canvas?', function() {
                    $(that).text('Loading..');
                    $(that).prop('disabled',true);
                    $.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_record_to_canvas.html",
                        data: {
                            
                            interactionDate: interactionDate,
                            rotation: rotation,
                            interactionClinicianDate: interactionClinicianDate,
                            interactionSchoolDate: interactionSchoolDate,
                            timeSpent: timeSpent,
                            pointAwarded: pointAwarded,
                            interactionId: interactionId,
                            studentFullName: studentFullName,
                            studentId: studentId,
                            schoolId: schoolId,
                            type: 'Interaction'
                        },
                        success: function(response) {
                            if(response == 'Success')
                            {
                                $(that).addClass('hide');
                                $('.isSentToCanvas_'+interactionId).removeClass('hide')
                                alertify.success('Record Successfully Sent to Canvas.');
                            }
                            else
                            {
                                alertify.success(response);
                            }
                            
                        }
                    });
                }, function() {});

            });

            $("#canvasStatus").change(function(){
                var canvasStatus = $(this).val();
                var encodedRotationId = $('#cboRotation').val();;
                var encodedStudentId = '<?php echo $encodedStudentId; ?>';

                redirectUrl = 'interaction.html';
                if(encodedStudentId !='')
                    redirectUrl = redirectUrl+"?studentId="+encodedStudentId;

                if(encodedRotationId !='')
                    redirectUrl = redirectUrl+"&rotationId="+encodedRotationId;

                if(canvasStatus)
                    redirectUrl = redirectUrl+"&canvasStatus="+canvasStatus;

                 window.location.href = redirectUrl;
                // if(encodedRotationId !='' )
                // {
                //     if(canvasStatus)
                //         window.location.href = "interaction.html?canvasStatus="+canvasStatus+"&rotationId="+encodedRotationId+"&studentId="+encodedStudentId;
                //     else
                //         window.location.href = "interaction.html?rotationId="+encodedRotationId+"&studentId="+encodedStudentId;
                // }
                // else if(encodedRotationId !='')
                // {
                //     if(canvasStatus)
                //         window.location.href = "interaction.html?canvasStatus="+canvasStatus+"&rotationId="+encodedRotationId+"&studentId="+encodedStudentId;
                //     else
                //         window.location.href = "interaction.html?rotationId="+encodedRotationId+"&studentId="+encodedStudentId;
                // }
                // else if(encodedStudentId !='')
                // {
                //     if(canvasStatus)
                //         window.location.href = "interaction.html?canvasStatus="+canvasStatus+"&studentId="+encodedStudentId;
                //     else
                //         window.location.href = "interaction.html?studentId="+encodedStudentId;
                // }
                        
            });
            $("#cboRotation").change(function(){
                var encodedRotationId = $(this).val();
                var canvasStatus = '';
                var encodedStudentId = '<?php echo $encodedStudentId; ?>';

                redirectUrl = 'interaction.html';
                
                if(encodedRotationId !='')
                    redirectUrl = redirectUrl+"?rotationId="+encodedRotationId;

                if(encodedStudentId !='')
                {  
                    if(encodedRotationId !='')
                        redirectUrl = redirectUrl+"&studentId="+encodedStudentId;
                    else
                        redirectUrl = redirectUrl+"?studentId="+encodedStudentId;

                }
                if(canvasStatus)
                    redirectUrl = redirectUrl+"&canvasStatus="+canvasStatus;

                 window.location.href = redirectUrl;
                
                // return false;
                // if(encodedRotationId !='')
                // {
                //     if(canvasStatus)
                //         window.location.href = "interaction.html?canvasStatus="+canvasStatus+"&rotationId="+encodedRotationId;
                //     else
                //         window.location.href = "interaction.html?rotationId="+encodedRotationId;
                // }
                // else if(encodedStudentId !='')
                // {
                    // if(canvasStatus)
                    //     window.location.href = "interaction.html?canvasStatus="+canvasStatus+"&studentId="+encodedStudentId+"&rotationId="+encodedRotationId;
                    // else
                    //     window.location.href = "interaction.html?studentId="+encodedStudentId+"&rotationId="+encodedRotationId;
                // }
                        
            });

        </script>
</body>
</html>