<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsStudentRankMaster.php');
// print_r($_POST);
$loggedUserId = $_SESSION["loggedUserId"];

$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
//Get School Name
$rankId = 0;

//CREATE OBJECT
$objStudents = new clsStudent();
$totalSchoolStudents = 0;
$accreditationId = 0;
$currentstudentId  = 0;
$currentSchoolId;
$studentImmunizationId = 0;
$studentId = 0;

if (isset($_GET['studentId'])) {
    $studentId = $_GET['studentId'];
    $studentId = DecodeQueryData($studentId);
}
$title = 'Pouch Documents';


if (isset($_GET['studentDocumentId'])) {
    $studentDocumentId = DecodeQueryData($_GET['studentDocumentId']);
}
if (isset($_GET['studentImmunizationId'])) {
    $studentImmunizationId = DecodeQueryData($_GET['studentImmunizationId']);
}

$studentIds = isset($_POST['hideStudentIds']) ? $_POST['hideStudentIds'] : '';
//For Student Details
$studentDetail = $objStudents->GetStudentDetails($studentId);
$firstName = $studentDetail ? $studentDetail['firstName'] : '';
$lastName = $studentDetail ? $studentDetail['lastName'] : '';
$fullName = $firstName . ' ' . $lastName;

unset($objStudents);

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo $title; ?></title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/uploadFile.css">


    <style>
        .mt-1 {
            margin-top: 10px;
            padding-left: 55px;
        }
    </style>
        <style>
        .file-details {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 5px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #f9f9f9;
        }

        .file-name {
            font-size: 14px;
        }

        .file-list li {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px;
            background: #f9f9f9;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        .remove-file {
            cursor: pointer;
            color: red;
        }

        .file-list li span {
            margin-right: 10px;
        }

        .file-list li button {
            border: none;
            background: #ff4d4d;
            color: #fff;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
        }

        .file-list li button:hover {
            background: #e60000;
        }
    </style>
 <style>
        .file-list {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin-bottom: 5px;
            border: 1px solid #ccc;
            border-radius: 5px;
            background-color: #f9f9f9;
        }

        .file-name {
            font-size: 14px;
        }

        .delete-icon {
            cursor: pointer;
            color: red;
        }
      
    </style>

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="schoolstudents.html">Pouch </a></li>
                    <li class="active">Upload</li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

        <?php

        if (isset($_GET["status"])) {

            if ($_GET["status"] == "added") {

        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Student added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Student updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "DuplicateFile") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> File is Already Exists.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
            <?php
            } else if ($_GET["status"] == "InvalidFile") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Invalid File Type.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <div class="pageheading"></div>
        <form id="frmCertificationLog" data-parsley-validate class="form-horizontal" method="POST" action="pouchUploadSubmit.html" enctype="multipart/form-data">
            <div class="">
                <div class="formSubHeading"><?php echo $title; ?></div>
               
                        <div class="form-group upload-file-form mx-0" >
                        <div class="" style="width: 100%; display: flex; justify-content: center;">
                        <div class="upload-files-container">
                                <input type="hidden" id="studentIds" name="studentIds" value="<?php echo $studentIds; ?>">
                                <input type="file" id="studentDocument" name="studentDocument[]" class="imageUploadLimit" multiple="multiple" required accept="application/pdf">
                                <label for="studentDocument" id="file-drag">
    <i class="fa-solid fa-file-import fa-fade" style="--fa-animation-duration: 2s; --fa-fade-opacity: 0.6; font-size: 45px;"></i>
    <h3 class="dynamic-message"> Drag &amp; drop any PDF here </h3>
    OR
    <span id="file-upload-btn" class="button">Browse file</span>
    from device
</label>

                            <output class="file-info" id="messages"></output>
                            </div>
                        </div>
                    </div>
            </div>            
            <div class="margin_top_ten">
        <div class="form-group mx-0">
            <label class="col-md-1 control-label"></label>
            <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0; gap: 15px;">
            <button id="btnSubmit" name="btnSubmit" class="btn btn-success" >Save</button>

                <a type="button" href="pouch.html" class="btn btn-default">Cancel</a>

            </div>

        </div>
            </div>
            
    </form>
    </div>
    

    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_tags").select2({
                'placeholder': 'Select'
            }); //for multiple selection
            $(".select2_single").select2();
        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "aoColumns": [{
                "sWidth": "40%"
            }, {
                "sWidth": "30%"
            }, {
                "sWidth": "30%",
                "sClass": "alignCenter",
                "bSortable": false
            }]
        });
    </script>
    
    <script>
        (function() {
            let removedFiles = [];
            let isSubmitting = false; // Prevent multiple form submissions

            function Init() {
                var fileSelect = document.getElementById("studentDocument"),
                    fileDrag = document.getElementById("file-drag"),
                    form = document.getElementById("frmbriefcase");

                fileSelect.addEventListener("change", fileSelectHandler, false);

                var xhr = new XMLHttpRequest();
                if (xhr.upload) {
                    fileDrag.addEventListener("dragover", fileDragHover, false);
                    fileDrag.addEventListener("dragleave", fileDragHover, false);
                    fileDrag.addEventListener("drop", fileSelectHandler, false);
                }

                // Prevent multiple form submissions
                form.addEventListener("submit", function(e) {
                    if (isSubmitting) {
                        e.preventDefault();
                        return;
                    }
                    isSubmitting = true;
                });
            }

            function fileDragHover(e) {
                var fileDrag = document.getElementById("file-drag");

                e.stopPropagation();
                e.preventDefault();

                fileDrag.className = e.type === "dragover" ? "hover" : "modal-body studentDocument";
            }

            function fileSelectHandler(e) {
                e.preventDefault();
                fileDragHover(e);

                var files = e.target.files || e.dataTransfer.files;
                var fileInput = document.getElementById("studentDocument");

                // **CLEAR existing files first to prevent duplication**
                var dataTransfer = new DataTransfer();

                // Add only new files (avoid duplicates)
                for (var i = 0; i < files.length; i++) {
                    dataTransfer.items.add(files[i]);
                    parseFile(files[i]);
                }

                // Assign the updated files list to the input field
                fileInput.files = dataTransfer.files;
            }

            function output(msg) {
                var m = document.getElementById("messages");
                var div = document.createElement("div");
                div.className = "file-item";
                div.innerHTML = msg;

                m.appendChild(div);
            }

            function parseFile(file) {
                var fileName = file.name;
                var fileSize = (file.size / (1024 * 1024)).toFixed(2) + " MB";

                output(
                    `<div class="file-details">
                <span class="file-name">${fileName} - ${fileSize}</span>
                <span class="remove-file" onclick="removeFile(this, '${fileName}')"><i class="fa fa-trash"></i></span>
            </div>`
                );
            }

            window.removeFile = function(button, fileName) {
                var fileItem = button.parentElement;
                fileItem.parentElement.removeChild(fileItem);

                removedFiles.push(fileName);

                var fileInput = document.getElementById("studentDocument");
                var dataTransfer = new DataTransfer();

                for (var i = 0; i < fileInput.files.length; i++) {
                    if (fileInput.files[i].name !== fileName) {
                        dataTransfer.items.add(fileInput.files[i]);
                    }
                }

                fileInput.files = dataTransfer.files;
            };

            if (window.File && window.FileList && window.FileReader) {
                Init();
            } else {
                document.getElementById("file-drag").style.display = "none";
            }
        })();
    </script>
</body>

</html>