<?php
ini_set('memory_limit', '256M');
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsStudent.php');
include('../class/clsClinician.php');
include('../class/clsAttendance.php');
include('../class/clsClinicianAttendance.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../setRequest.php');

$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = '';
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$currentSchoolId;
$currentstudentId = '';
$rotationId = '';
$attendanceId = '';
$hospitalsiteId = '';
$usertype = '';
$currentclinicianId = 0;
//object
$objrotation = new clsRotation();

if (isset($_GET['latti'])) {
	$latti = ($_GET['latti']);
}
if (isset($_GET['long'])) {
	$long = ($_GET['long']);
}
if (isset($_GET['type'])) {
	$usertype = ($_GET['type']);
}
$courselocationId = isset($_GET['courselocationId']) ? $_GET['courselocationId'] : '';
$rotationLocationId = isset($_GET['rotationLocationId']) ? $_GET['rotationLocationId'] : '';

if (isset($_GET['clockin'])) {
	$clockin = $_GET['clockin'];
	$bedCrumTitle = 'ClockIn-Location';
	$page_title = 'ClockIn-Location';
	if ($usertype == 'clinician') {
		$heading = 'Clinician Clock-In Location Details';
	} else {
		$heading = 'Student Clock-In Location Details';
	}
}
if (isset($_GET['clockout'])) {
	$clockout = $_GET['clockout'];
	$bedCrumTitle = 'ClockOut-Location';
	$page_title = 'ClockOut-Location';
	if ($usertype == 'clinician') {
		$heading = 'Clinician Clock-Out Location Details';
	} else {
		$heading = 'Student Clock-Out Location Details';
	}
}
if (isset($_GET['studentId'])) {
	$currentstudentId = DecodeQueryData($_GET['studentId']);
}
if (isset($_GET['clinicianId'])) {
	$currentclinicianId = DecodeQueryData($_GET['clinicianId']);
}
if (isset($_GET['attendanceId'])) {
	$attendanceId = DecodeQueryData($_GET['attendanceId']);
}
if ($usertype == 'clinician') {

	$objClinicianAttendance = new clsClinicianAttendance();
	$totalAttendance = 0;
	$rowsAttendance = $objClinicianAttendance->GetSingleClinicianAttendanceDetails($hospitalsiteId, $currentclinicianId, $attendanceId);
	$hospitalsitename = $rowsAttendance['title'];

	// $rotationId = stripslashes($rowsAttendance['rotationId']);
	// $courselocationId = $rowsAttendance['locationId'];
	// $parentRotationId = stripslashes($rowsAttendance['parentRotationId']);
	// $rotationLocationId = stripslashes($rowsAttendance['rotationLocationId']);
} else {

	$objAttendance = new clsAttendance();
	$totalAttendance = 0;
	$rowsAttendance = $objAttendance->GetSingleStudentAttendanceDetails($rotationId, $currentstudentId, $attendanceId);
	$rotationname = $rowsAttendance['title'];

	$rotationId = stripslashes($rowsAttendance['rotationId']);
	$courselocationId = $rowsAttendance['locationId'];
	$parentRotationId = stripslashes($rowsAttendance['parentRotationId']);
	$rotationLocationId = stripslashes($rowsAttendance['rotationLocationId']);
}
$locationId = 0;
if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
	if ($parentRotationId > 0) {
		if (!$rotationLocationId)
			$locationId = $objrotation->GetLocationByRotation($rotationId);
		else
			$locationId  = $rotationLocationId;
	}
} else {
	$locationId  = $courselocationId;
}

//Get Time Zone By Rotation 
$objLocation = new clsLocations();
$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
unset($objLocation);
if ($TimeZone == '')
	$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

if (isset($_GET['clockout'])) {
	$clockOutDateTime = ($rowsAttendance['clockOutDateTime']);
	$clockOutDateTime = converFromServerTimeZone($clockOutDateTime, $TimeZone);
	$clockInDateTime = ($rowsAttendance['clockInDateTime']);
	$clockInDateTime = converFromServerTimeZone($clockInDateTime, $TimeZone);
} else {
	//$DateTime=$rowsAttendance['clockInDateTime'];
	$clockInDateTime = ($rowsAttendance['clockInDateTime']);
	$clockInDateTime = converFromServerTimeZone($clockInDateTime, $TimeZone);
}

unset($objClinicianAttendance);
unset($objAttendance);
$objStudent = new clsStudent();
$StudentName = $objStudent->GetSingleStudent($currentSchoolId, $currentstudentId);
$studentfullname = $StudentName ? ($StudentName['firstName'] . ' ' . $StudentName['lastName']) : '';
$Ranktitle = $StudentName ? $StudentName['title'] : '';
unset($objStudent);

$objClinician = new clsClinician();
$ClinicianName = $objClinician->GetSingleClinician($currentSchoolId, $currentclinicianId);
$clinicianfullname = $ClinicianName ? ($ClinicianName['firstName'] . ' ' . $ClinicianName['lastName']) : '';
$RoleName = isset($ClinicianName['title']) ? $ClinicianName['title'] : '';
unset($objClinician);
unset($objrotation);

?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($page_title); ?></title>
	<?php include('includes/headercss.php'); ?>

</head>

<body>
	<?php include('includes/header.php'); ?>
	<style>
		#map {
			height: 300px;
			width: 100%;
		}
	</style>
	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
					<?php if ($usertype == 'clinician') { ?>
						<li><a href="clinicianattendancelist.html">Clinician List</a></li>
					<?php } else { ?>
					<li><a href="clinical.html">Clinical</a></li>
					<?php } ?>
					<?php if ($usertype == 'clinician') { ?>
						<li><a href="attendancelist.html?clinicianId=<?php echo EncodeQueryData($currentclinicianId); ?>"><?php echo ($clinicianfullname); ?></a></li>
						<li><a href="attendancelist.html?clinicianId=<?php echo EncodeQueryData($currentclinicianId); ?>">Attendance</a></li>
					<?php } else { ?>
						<li><a href="Attendance.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>"><?php echo ($studentfullname); ?></a></li>
						<li><a href="Attendance.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Attendance</a></li>
					<?php } ?>
					<li class="active"><?php echo ($bedCrumTitle); ?></li>
				</ol>
			</div>

		</div>
	</div>

	<div class="container">
		<div class="row">
			<div class="col-md-6">
				<div class="form-group">
					<div>
						<div id="map"></div>
					</div>
				</div>
			</div>
			<div class="col-md-6">
				<h4 align="center"><?php echo ($heading); ?></h4><br>
				<div class="form-horizontal">
					<?php if ($usertype == 'clinician') { ?>
						<div class="form-group">
							<label class="col-md-4 control-label text-left " style="padding-top:0px;">Clinician Name:</label>
							<div class="col-md-6"><?php echo ($clinicianfullname); ?>
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label text-left " style="padding-top:0px;">Role:</label>
							<div class="col-md-6"><?php echo ($RoleName); ?>
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label text-left " style="padding-top:0px;">Hospital Site:</label>
							<div class="col-md-6"><?php echo ($hospitalsitename); ?>
							</div>
						</div>
						<?php if (isset($_GET['clockout'])) { ?>
							<div class="form-group">
								<label class="col-md-4 control-label text-left " style="padding-top:0px;">Clock In Date Time:</label>
								<div class="col-md-6"><?php echo ($clockInDateTime); ?>
								</div>
							</div>
							<div class="form-group">
								<label class="col-md-4 control-label text-left " style="padding-top:0px;">Clock Out Date Time:</label>
								<div class="col-md-6"><?php echo ($clockOutDateTime); ?>
								</div>
							</div>
						<?php } else { ?>
							<div class="form-group">
								<label class="col-md-4 control-label text-left " style="padding-top:0px;">Clock In Date Time:</label>
								<div class="col-md-6"><?php echo ($clockInDateTime); ?>
								</div>
							</div>
						<?php } ?>
					<?php } else { ?>
						<div class="form-group">
							<label class="col-md-4 control-label text-left " style="padding-top:0px;">Student Name:</label>
							<div class="col-md-6"><?php echo ($studentfullname); ?>
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label text-left " style="padding-top:0px;">Rank:</label>
							<div class="col-md-6"><?php echo ($Ranktitle); ?>
							</div>
						</div>
						<div class="form-group">
							<label class="col-md-4 control-label text-left " style="padding-top:0px;">Rotation:</label>
							<div class="col-md-6"><?php echo ($rotationname); ?>
							</div>
						</div>
						<?php if (isset($_GET['clockout'])) { ?>
							<div class="form-group">
								<label class="col-md-4 control-label text-left " style="padding-top:0px;">Clock In Date Time:</label>
								<div class="col-md-6"><?php echo ($clockInDateTime); ?>
								</div>
							</div>
							<div class="form-group">
								<label class="col-md-4 control-label text-left " style="padding-top:0px;">Clock Out Date Time:</label>
								<div class="col-md-6"><?php echo ($clockOutDateTime); ?>
								</div>
							</div>
						<?php } else { ?>
							<div class="form-group">
								<label class="col-md-4 control-label text-left " style="padding-top:0px;">Clock In Date Time:</label>
								<div class="col-md-6"><?php echo ($clockInDateTime); ?>
								</div>
							</div>
						<?php } ?>
					<?php } ?>
				</div>
			</div>

		</div>
	</div>

	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript">
		var map;

		function initMap() {
			//var latitude = 16.6948; // YOUR LATITUDE VALUE
			//var longitude = 74.2228; // YOUR LONGITUDE VALUE

			var latitude = <?php echo ($latti); ?>;
			var longitude = <?php echo ($long); ?>;
			var myLatLng = {
				lat: latitude,
				lng: longitude
			};

			map = new google.maps.Map(document.getElementById('map'), {
				center: myLatLng,
				zoom: 14
			});

			var marker = new google.maps.Marker({
				position: myLatLng,
				map: map,
				//title: 'Hello World'              
				// setting latitude & longitude as title of the marker
				// title is shown when you hover over the marker
				title: latitude + ', ' + longitude
			});
		}
	</script>
	<script src="https://maps.googleapis.com/maps/api/js?key=AIzaSyAYOMVZkvVaT8XlkfVKrYWLsSQygRnmE2I&callback=initMap" async defer></script>
</body>

</html>