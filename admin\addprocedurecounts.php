<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');   
    include('../class/clsProcedureCategory.php');
	include('../setRequest.php'); 

    
       $currentSchoolId;
	   $title = '';
	   $proceduteCountName = '';
	   $procedureCountsCode = '';
	   $procedure = '';
       $title ="Add Procedure Count - ".$currenschoolDisplayname;  

    $page_title ="Add Procedure Count";
    $proceduteCountId = 0;	
    $procedureCategoryId = 0;	
    $bedCrumTitle = 'Add';
	 $objProcedureCategory = new clsProcedureCategory();
    if(isset($_GET['id'])) //Edit Mode
	{
        $proceduteCountId = DecodeQueryData($_GET['id']);
	    $page_title ="Edit Procedure Count";
        $bedCrumTitle = 'Edit';

       
		$row = $objProcedureCategory->GetAllproceduteCountByproceduteCountId($proceduteCountId);
       
        if($row=='')
        {
            header('location:addprocedurecounts.html');
            exit;
        }

        $proceduteCountName  = stripslashes($row['proceduteCountName']);        
        $procedureCountsCode  = stripslashes($row['procedureCountsCode']);        
        $procedureCategoryId  = stripslashes($row['procedureCategoryId']);        
        $procedure  = stripslashes($row['procedures']);        
	}
	$Category = $objProcedureCategory->GetAllCategory();
		unset($objProcedureCategory);
	
		
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    

</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li><a href="viewprocedurecounts.html">Procedure Count</a></li>
                    <li class="active"><?php echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

         <form id="frmProcedureCount" data-parsley-validate class="form-horizontal" method="POST" action="submitprocedurecounts.html?id=<?php echo(EncodeQueryData($proceduteCountId)); ?>" >

            <div class="row">
                <div class="col-md-6">

                    <!-- Text input-->
					
					<div class="form-group">
                        <label class="col-md-12 control-label" for="cboCategory">Procedure Category</label>
                        <div class="col-md-12 flex-col-reverse">
                            <select id="cboCategory" name="cboCategory"  
							 class="form-control input-md required-input select2_single" placeholder="Select Hospital site"  required>
                            <option value="" selected>Select</option>
								<?php
                                if($Category!="")
                                {
                                    while($row = mysqli_fetch_assoc($Category))
                                    {
                                         $selCategoryId  = $row['procedureCategoryId'];
                                         $name  = stripslashes($row['categoryName']);

                                         ?>
										<option value="<?php echo ($selCategoryId); ?>" <?php if($procedureCategoryId==$selCategoryId){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
                                        
										<?php

                                    }
                                }
                            ?>
                            </select>				
                       
						</div>
                    </div>
                </div>
                <div class="col-md-6">

					
					
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="ProcedureCountsCode">Procedure Counts ID</label>
                        <div class="col-md-12">
                            <input id="ProcedureCountsCode"  name="ProcedureCountsCode" value="<?php echo($procedureCountsCode); ?>" required type="text"  class="form-control input-md required-input">

                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
            <div class="col-md-6">
					
					 <div class="form-group">
                        <label class="col-md-12 control-label" for="Procedure">Procedure</label>
                        <div class="col-md-12">
                            <input id="Procedure"  name="Procedure" value="<?php echo($procedure); ?>" required type="text"  class="form-control input-md required-input">

                        </div>
                    </div>
            </div>
            <div class="col-md-6">

					<div class="form-group">
                        <label class="col-md-12 control-label" for="ProcedureCounts">Procedure Description</label>
                        <div class="col-md-12">
                            <textarea id="ProcedureCounts"  name="ProcedureCounts"  required type="text"  cols="5" rows="5" class="form-control input-md required-input"><?php echo($proceduteCountName); ?></textarea>

                        </div>
                    </div>

					
					
                     
                </div>
            </div>
            
				<div class="form-group">
                        <!-- <label class="col-md-2 control-label"></label> -->
                        <div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                            <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                            <a type="button" href="viewprocedurecounts.html" class="btn btn-default">Cancel</a>
						</div>
                </div>
        </form>


    </div>

    <?php include('includes/footer.php');?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>



    <script type="text/javascript">
 
		$(".select2_single").select2();
         $('#select2-cboCategory-container').addClass('required-select2');
        $(window).load(function(){

             $('#frmProcedureCount').parsley().on('field:validated', function() {
			    var ok = $('.parsley-error').length === 0;
            })
            .on('form:submit', function() {
                ShowProgressAnimation();									  
                return true; // Don't submit form for this demo
            });


        });	

    </script>
</body>

</html>