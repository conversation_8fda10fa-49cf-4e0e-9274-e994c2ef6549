<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsStudentRankMaster.php');
include('../class/clsLocations.php');
include('../class/PasswordHash.php');

if ($_SERVER['REQUEST_METHOD'] == "POST") {
	$type = isset($_POST['type']) ? $_POST['type'] : '';

	ini_set('upload_max_filesize', '50M');
	ini_set('post_max_size', '50M');
	ini_set('max_input_time', 300000);
	ini_set('max_execution_time', 300000);

	if (isset($_FILES['file'])) {
		// Check if there was an upload error
		if ($_FILES['file']['error'] !== UPLOAD_ERR_OK) {
			$errorMessage = getFileUploadErrorMessage($_FILES['file']['error']);
			header('location:studentIdsList.html?status=Error&message=' . urlencode($errorMessage));
			exit();
		}

		// Check if file is empty
		if ($_FILES['file']['size'] == 0) {
			header('location:studentIdsList.html?status=Error&message=' . urlencode('The uploaded file is empty. Please upload a valid CSV file.'));
			exit();
		}

		processFile($_FILES['file'], $type);
	} else {
		header('location:studentIdsList.html?status=Error&message=' . urlencode('No file was uploaded. Please select a CSV file to import.'));
	}
}

function processFile($file, $type)
{
	$filename = $file["tmp_name"];
	$ext = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));

	// Validate file extension
	if ($ext != "csv") {
		header('location:studentIdsList.html?status=Error&message=' . urlencode('Invalid file format. Please upload a CSV file.'));
		exit();
	}

	// Check if file exists and is readable
	if (!file_exists($filename) || !is_readable($filename)) {
		header('location:studentIdsList.html?status=Error&message=' . urlencode('Unable to read the uploaded file. Please try again.'));
		exit();
	}

	if ($file["size"] > 0) {
		// Try to open the file
		$fileHandle = fopen($filename, "r");
		if ($fileHandle !== false) {
			// Validate CSV structure
			$firstRow = fgetcsv($fileHandle, 10000, ",");
			if ($firstRow === false || count($firstRow) < 5) {
				fclose($fileHandle);
				header('location:studentIdsList.html?status=Error&message=' . urlencode('Invalid CSV format. The file must contain at least 5 columns.'));
				exit();
			}

			// Reset file pointer to beginning
			rewind($fileHandle);

			// Process the file
			handleFile($fileHandle, $type);
			fclose($fileHandle);
		} else {
			header('location:studentIdsList.html?status=Error&message=' . urlencode('Failed to open the uploaded file. Please try again.'));
			exit();
		}
	} else {
		header('location:studentIdsList.html?status=Error&message=' . urlencode('The uploaded file is empty. Please upload a valid CSV file.'));
		exit();
	}
}

/**
 * Process the CSV file based on the import type
 *
 * @param resource $fileHandle File handle for the CSV file
 * @param string $type Type of import ('accessToken' or other)
 */
function handleFile($fileHandle, $type)
{
	$counter = 0;
	$row = 1;
	$studentArray = [];
	$encounteredValues = [];
	$objDB = new clsDB();

	// For access token imports
	$successCount = 0;
	$errorCount = 0;
	$skippedCount = 0;

	while (($getData = fgetcsv($fileHandle, 10000, ",")) !== false) {
		// Skip header row
		if ($row == 1) {
			$row++;
			continue;
		}

		// Process based on import type
		if ($type == 'accessToken') {
			$result = handleAccessTokenRow($getData, $objDB);
			if ($result) {
				$successCount++;
			} else {
				$errorCount++;
			}
		} else {
			// For student ID imports
			$studentArray[] = $getData;
			handleStudentRow($getData, $encounteredValues, $objDB, $counter);
		}
		$counter++;
	}

	// Handle results based on import type
	if ($type == 'accessToken') {
		// Handle access token import results
		if ($successCount > 0) {
			$messageText = 'Imported';
			$statusMessage = "Successfully imported $successCount access token(s)";
			if ($errorCount > 0) {
				$statusMessage .= ", failed to import $errorCount record(s)";
			}
		} else if ($errorCount > 0) {
			$messageText = 'Error';
			$statusMessage = "Failed to import access tokens. Please check your data and try again.";
		} else {
			$messageText = 'Error';
			$statusMessage = "No access tokens were processed. Please check your CSV file and try again.";
		}

		header('location:studentIdsList.html?status=' . $messageText . '&message=' . urlencode($statusMessage) . '&success=' . $successCount . '&errors=' . $errorCount);
		exit();
	} else {
		// Process student ID imports
		importStudentData($studentArray, $objDB);
	}
}

/**
 * Handle a row from the CSV file for access token import
 *
 * @param array $getData The CSV row data
 * @param object $objDB Database object
 * @return bool True if the update was successful, false otherwise
 */
function handleAccessTokenRow($getData, $objDB)
{
	// Extract and clean data from CSV row
	$accessToken = cleanInput($getData[0]);
	$firstName = cleanInput($getData[1]); // Not used but kept for documentation
	$lastName = cleanInput($getData[2]);  // Not used but kept for documentation
	$email = cleanInput($getData[3]);
	$schoolCode = cleanInput($getData[4]);

	// Validate required fields
	if (empty($accessToken) || empty($email) || empty($schoolCode)) {
		return false;
	}

	// Look up school ID
	$schoolId = $objDB->GetSingleColumnValueFromTable('schools', 'schoolId', 'code', $schoolCode);
	if (!$schoolId) {
		// School not found
		return false;
	}

	// Look up student ID
	$studentId = $objDB->GetSingleColumnValueFromTable('student', 'studentId', 'email', $email, 'schoolId', $schoolId);
	if (!$studentId) {
		// Student not found
		return false;
	}

	// Update the student's canvas access token
	$result = $objDB->UpdateSingleColumnValueToTable('student', 'canvasAccessToken', $accessToken, 'studentId', $studentId, 'schoolId', $schoolId);
	return $result ? true : false;
}

function handleStudentRow($getData, &$encounteredValues, $objDB, $counter)
{
	$recordIdNumber = cleanInput($getData[0]);
	$schoolCode = cleanInput($getData[4]);
	$email = cleanInput($getData[3]);

	$schoolId = $objDB->GetSingleColumnValueFromTable('schools', 'schoolId', 'code', $schoolCode);
	if (!$schoolId) return;

	$studentId = $objDB->GetSingleColumnValueFromTable('student', 'studentId', 'email', $email, 'schoolId', $schoolId);
	if (!$studentId) return;

	if ($recordIdNumber != '' && in_array($recordIdNumber, $encounteredValues)) {
		handleDuplicateRecordId($recordIdNumber, $counter);
	}

	$encounteredValues[] = $recordIdNumber;
	if ($recordIdNumber) {
		$existingRecordId = $objDB->GetSingleColumnValueFromTable('student', 'studentId', 'recordIdNumber', $recordIdNumber, 'schoolId', $schoolId);
		if ($existingRecordId && $studentId != $existingRecordId) {
			handleDuplicateRecordId($recordIdNumber, $counter);
		}
	}
}

function handleDuplicateRecordId($recordIdNumber, $counter)
{
	$rowNumber = $counter + 2; // Adding 2 to account for header row and 0-based index
	$errorMessage = "Duplicate Record ID '$recordIdNumber' found at row $rowNumber. Please ensure all Record IDs are unique.";
	header('location:studentIdsList.html?status=Error&message=' . urlencode($errorMessage) . '&row=' . $rowNumber . '&value=' . urlencode($recordIdNumber));
	exit();
}

function importStudentData($studentArray, $objDB)
{
	$studentCount = count($studentArray);
	$successCount = 0;
	$errorCount = 0;
	$skippedCount = 0;
	$lastStudentId = 0;

	foreach ($studentArray as $students) {
		$recordIdNumber = cleanInput($students[0]);
		$firstName = cleanInput($students[1]);
		$lastName = cleanInput($students[2]);
		$email = cleanInput($students[3]);
		$schoolCode = cleanInput($students[4]);

		// Validate school code
		$schoolId = $objDB->GetSingleColumnValueFromTable('schools', 'schoolId', 'code', $schoolCode);
		if (!$schoolId) {
			$skippedCount++;
			continue;
		}

		// Validate student email
		$studentId = $objDB->GetSingleColumnValueFromTable('student', 'studentId', 'email', $email, 'schoolId', $schoolId);
		if (!$studentId) {
			$skippedCount++;
			continue;
		}

		// Update student record
		if ($firstName || $lastName || ($studentId && $recordIdNumber && $schoolId)) {
			$result = $objDB->UpdateSingleColumnValueToTable('student', 'recordIdNumber', $recordIdNumber, 'studentId', $studentId, 'schoolId', $schoolId);
			if ($result) {
				$successCount++;
				$lastStudentId = $studentId; // Store the last successfully updated student ID for audit log
			} else {
				$errorCount++;
			}
		} else {
			$skippedCount++;
		}
	}

	// Determine the message based on the results
	if ($successCount > 0) {
		$messageText = 'Imported';
		$statusMessage = "Successfully imported $successCount student record(s)";
		if ($skippedCount > 0) {
			$statusMessage .= ", skipped $skippedCount record(s)";
		}
		if ($errorCount > 0) {
			$statusMessage .= ", failed to import $errorCount record(s)";
		}

		// Create audit log for successful imports
		$objLog = new clsLogger();
		$action = $objLog::EDIT;
		$type = "studentId Import";
		$userType = $objLog::ADMIN;
		$IsMobile = 0;

		$objStudent = new clsStudent();
		$objStudent->saveStudentAuditLog($lastStudentId, $_SESSION['loggedUserId'], $userType, $action, $IsMobile, 0, $type, $studentCount);
		unset($objStudent);
		unset($objLog);
	} else if ($errorCount > 0) {
		$messageText = 'Error';
		$statusMessage = "Failed to import student records. Please check your data and try again.";
	} else if ($skippedCount > 0) {
		$messageText = 'Warning';
		$statusMessage = "No records were imported. All $skippedCount record(s) were skipped due to invalid data.";
	} else {
		$messageText = 'Error';
		$statusMessage = "No records were processed. Please check your CSV file and try again.";
	}

	header('location:studentIdsList.html?status=' . $messageText . '&message=' . urlencode($statusMessage) . '&success=' . $successCount . '&skipped=' . $skippedCount . '&errors=' . $errorCount);
	exit();
}

function cleanInput($input)
{
	return trim(str_replace(" ", "", $input));
}

/**
 * Get a human-readable error message for file upload errors
 *
 * @param int $errorCode PHP file upload error code
 * @return string Human-readable error message
 */
function getFileUploadErrorMessage($errorCode)
{
	switch ($errorCode) {
		case UPLOAD_ERR_INI_SIZE:
			return 'The uploaded file exceeds the upload_max_filesize directive in php.ini.';
		case UPLOAD_ERR_FORM_SIZE:
			return 'The uploaded file exceeds the MAX_FILE_SIZE directive specified in the HTML form.';
		case UPLOAD_ERR_PARTIAL:
			return 'The uploaded file was only partially uploaded.';
		case UPLOAD_ERR_NO_FILE:
			return 'No file was uploaded.';
		case UPLOAD_ERR_NO_TMP_DIR:
			return 'Missing a temporary folder.';
		case UPLOAD_ERR_CANT_WRITE:
			return 'Failed to write file to disk.';
		case UPLOAD_ERR_EXTENSION:
			return 'A PHP extension stopped the file upload.';
		default:
			return 'Unknown upload error.';
	}
}
