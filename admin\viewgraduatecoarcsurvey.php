<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsStudent.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsSectionStudentName.php');
include('../class/clsClinician.php');
include('../class/clsGraduateCoarc.php');


$schoolId = 0;
$rotationId = 0;
$currentstudentId = 0;
$graduateCoarcMasterId = 0;
$clinicianId = 0;
$currentstudentId = 0;
$coARCSatelliteOptionProgramId = 0;
$graduateCoarcSubmitedMasterId = 0;
$display_to_date = date('m/d/Y');


if (isset($_GET['studentId'])) {
	$currentstudentId = DecodeQueryData($_GET['studentId']);
}
if (isset($_GET['graduatecoarcId'])) {
	$graduatecoarcId = DecodeQueryData($_GET['graduatecoarcId']);
}

//FOR EDIT JRCERT Survey
if (isset($_GET['graduateCoarcMasterId']) && ($_GET['studentId'])) {
	$graduateCoarcMasterId = DecodeQueryData($_GET['graduateCoarcMasterId']);
	$studentId = DecodeQueryData($_GET['studentId']);

	$schoolId = $currentSchoolId;
	$page_title = "View Graduate JRCERT Survey ";
	$bedCrumTitle = 'View';

	$objGraduateCoarc = new clsGraduateCoarc();
	$rowCoarc = $objGraduateCoarc->GetGraduateStudentCoarcSurveyDetails($graduateCoarcMasterId, $studentId);

	//unset($objGraduateCoarc);
	if ($rowCoarc == '') {
		header('location:viewgraduatecoarcsurvey.html');
		exit;
	}

	$graduateCoarcSubmitedMasterId = ($rowCoarc['graduateCoarcMasterId']);
	$clinicianId = ($rowCoarc['clinicanId']);
	$evaluationDate = ($rowCoarc['evaluationDate']);
	$evaluationDate = date('m/d/Y', strtotime($evaluationDate));
	$currentstudentId = ($rowCoarc['studentId']);
	$sponsoringInstitution = ($rowCoarc['sponsoringInstitution']);
	$coARCSatelliteOptionProgramId = ($rowCoarc['coARCSatelliteOptionProgramId']);
	$coARCEntryBaseProgramId = ($rowCoarc['coARCEntryBaseProgramId']);
	$GradMonth = ($rowCoarc['GradMonth']);
	$GradYear = ($rowCoarc['GradYear']);
	$jobTitle = ($rowCoarc['jobTitle']);
	$employeeEvalutionYear = ($rowCoarc['employeeEvalutionYear']);
	$employeeEvalutionMonth = ($rowCoarc['employeeEvalutionMonth']);
	$TimeOfEvalution = ($rowCoarc['TimeOfEvalution']);
	$enrolledName = ($rowCoarc['enrolledName']);
	$CredentialStatus = ($rowCoarc['CredentialStatus']);
	$satelliteLocation = ($rowCoarc['satelliteLocation']);
	$specialistCertificate = ($rowCoarc['specialistCertificate']);
	$OverAllRating = ($rowCoarc['OverAllRating']);
} else {
	$schoolId = $currentSchoolId;
	$page_title = "View Graduate JRCERT Survey";
	$bedCrumTitle = 'View';
}


//----------------------------//
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByStudent($currentstudentId);
unset($objClinician);
//--------------------------//
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($currentstudentId, $rotationId);
unset($objStudent);
//--------------------------//
$objSectionStudentName = new clsSectionStudentName();
$getStudentName = $objSectionStudentName->GetSectionStudentName($schoolId, $currentstudentId);
unset($objSectionStudentName);
///----------------------//

$objGraduateCoarc = new clsGraduateCoarc();
$totalSection = 0;
$CoarcSection = $objGraduateCoarc->GetSections($schoolId);
if ($CoarcSection != '') {
	$totalSection = mysqli_num_rows($CoarcSection);
}

//For Student Record Id
$objDB = new clsDB();
$studentRecordId = $objDB->GetSingleColumnValueFromTable('student', 'recordIdNumber', 'studentId', $currentstudentId);
$studentRecordId = ($studentRecordId) ? $studentRecordId : '';
unset($objDB);


?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($page_title); ?></title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

	<style type="text/css">
		.some-class {
			float: left;
			clear: none;
		}

		.collapsible {
			cursor: pointer;
			/* padding: 15px; */
			/* border: 1px solid #181818; */
			/* background-color: #f9f9f9; */
			display: flex;
			justify-content: space-between;
			align-items: center;
			/* border-radius: 14px; */
		}

		.collapsible p {
			margin: 0;
		}

		.collapsible-arrow {
			font-size: 18px;
			transition: transform 0.3s ease;
		}

		.content {
			display: none;
			padding: 10px 0;
			/* border-top: 1px solid #ececec; */
		}

		.content.active {
			display: block;
		}

		.active.collapsible-arrow {
			transform: rotate(180deg);
		}

		.row-delete-icon {
			position: absolute;
			top: -82px;
			right: 20px;
		}

		.panel-heading {
			width: 100%;
			display: flex;
			justify-content: space-between;
			align-items: center;
		}

		.arrow-icon {
			transition: transform 0.3s;
		}

		.collapsible.expanded .arrow-icon {
			transform: rotate(180deg);
		}
	</style>


</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
						 <li><a href="dashboard.html">Setting</a></li>
                    <li><a href="graduatecoarcsurvey.html">Graduate JRCERT Survey</a></li>
                    <li class="active"><?php echo($bedCrumTitle);?></li>
                </ol>
            </div>

		</div>
	</div>

    <div class="container">
			 <?php 
			 if (isset($_GET["status"]))
				{
					 if($_GET["status"] =="updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> JRCERT Survey updated successfully.
                </div>
                <?php 
					} 
					
				}  ?>   
         <form id="frmgraduatecoarcsurvey" data-parsley-validate class="form-horizontal" method="POST" 
		  action="graduatecoarcsurveysubmit.html?graduateCoarcMasterId=<?php echo(EncodeQueryData($graduateCoarcMasterId)); ?>
		&studentId=<?php echo (EncodeQueryData($currentstudentId));?>&graduatecoarcId=<?php echo (EncodeQueryData($graduatecoarcId));?>" >

            <div class="row">                
				
		
                <div class="col-md-6">
						<div class="form-group">					
							<label class="col-md-12 control-label" for="evaluationDate">Evaluation Date</label>
								<div class="col-md-12 col-sm-12 col-xs-12">	
									<div class='input-group date w-full' id='evaluationDate'>
									
										<input type='text' name="evaluationDate"  id="evaluationDate" class="form-control input-md required-input rotation_date" value="<?php  echo ($display_to_date); ?>" required data-parsley-errors-container="#error-txtDate"/>
											<span class="input-group-addon calender-icon">										
												<span class="glyphicon glyphicon-calendar"></span>
											</span>
									</div>
										
								</div>		
						</div>			
					
				</div>
				<div class="col-md-6">
					<div class="form-group">					
							<label class="col-md-12 control-label" for="coarcEntryBaseProgramId">JRCERT Entry Base Program ID#:</label>
								<div class="col-md-12 col-sm-12 col-xs-12">	
									<div class='input-group date w-full' id='coarcEntryBaseProgramId'>
									
										<input type='text' name="coarcEntryBaseProgramId"  id="coarcEntryBaseProgramId" class="form-control input-md required-input" <?php if(isset($_GET['graduateCoarcMasterId'])) 
										{ ?>value="<?php echo ($coARCEntryBaseProgramId);?>" <?php } else { ?> value="" <?php } ?> required />
											
									</div>
										
								</div>		
						</div>	
				</div>


			</div>



			<div class="row">
				<div class="col-md-6">
					<div class="form-group">					
							<label class="col-md-12 control-label" for="spomsoringInstitudeName">Sponsoring Institution/
Consortium Name:</label>
								<div class="col-md-12 col-sm-12 col-xs-12">	
									<div class='input-group date w-full' id='spomsoringInstitudeName'>
									
									<input type='text' name="spomsoringInstitudeName" id="spomsoringInstitudeName" class="form-control input-md required-input" <?php if (isset($_GET['graduateCoarcMasterId'])) { ?>value="<?php echo ($sponsoringInstitution); ?>" <?php } else { ?> value="" <?php } ?> required />
											
									</div>
										
								</div>		
						</div>		
				</div>

				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-12 control-label" for="studentRecordId">Student Id:</label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class='input-group date w-full' id='studentRecordId'>

								<input type='text' name="studentRecordId" id="recordIdNumber" class="form-control input-md required-input" maxlength="18" value="<?php echo ($studentRecordId); ?>" oninput="numberOnly(this.id);" onchange="validateData('recordId',<?php echo $currentSchoolId ?>,<?php echo $currentstudentId ?>);" required />

							</div>

						</div>
					</div>
				</div>

			</div>
			<div class="row">
				<!-- <div class="col-md-2"></div> -->
				<div class="col-md-12">
				<label><b>NOTE: Completion of this survey is required as part of outcomes assessment by the program's accreditation body (JRCERT).</b></label>	
				</div>
			</div>


			<div class="row">
				<div class="col-md-12">
							<div class="form-group">
								<!-- <label class="col-md-2 control-label" ></label> -->
									<div class="col-md-12 col-sm-12 col-xs-12">
										<div class="panel panel-default border-14">
											<div class="panel-body">
												
											<label>The purpose of this survey is to help faculty evaluate the Program’s success in preparing graduates to function as competent
respiratory therapists. Compiled data from all returned surveys will be used to evaluate program quality; data from individual
surveys will be held in strict confidence.</label>
													
												
											</div>
										</div>
									</div>						
							</div>
						</div>
					</div>

			<div class="row mt-3">
    <div class="col-md-12">
        <div class="form-group">
            <div class="col-md-12 col-sm-12 col-xs-12">
                <div class="panel panel-default border-14 p-3">
                    <div class="panel-body">
                        <label class="font-weight-bold" style="margin-left: 10px;"><b>BACKGROUND INFORMATION:</b></label>
                        
                        <div class="row mt-3">
                            <div class="col-md-6 mt-10">
                                <label>Grad Month</label>
                                <select class="form-control" id="GradMonth" name="GradMonth">
								<option value="" selected>Select</option>
																<option value="1" <?php echo ($GradMonth =='1')?"selected":"" ;?> >1</option>
																<option value="2" <?php echo ($GradMonth =='2')?"selected":"" ;?> >2</option>
																<option value="3" <?php echo ($GradMonth =='3')?"selected":"" ;?> >3</option>
																<option value="4" <?php echo ($GradMonth =='4')?"selected":"" ;?> >4</option>
																<option value="5" <?php echo ($GradMonth =='5')?"selected":"" ;?> >5</option>
																<option value="6" <?php echo ($GradMonth =='6')?"selected":"" ;?> >6</option>
																<option value="7" <?php echo ($GradMonth =='7')?"selected":"" ;?> >7</option>
																<option value="8" <?php echo ($GradMonth =='8')?"selected":"" ;?> >8</option>
																<option value="9" <?php echo ($GradMonth =='9')?"selected":"" ;?> >9</option>
																<option value="10" <?php echo ($GradMonth =='10')?"selected":"" ;?> >10</option>
																<option value="11" <?php echo ($GradMonth =='11')?"selected":"" ;?> >11</option>
																<option value="12" <?php echo ($GradMonth =='12')?"selected":"" ;?> >12</option>
                                </select>
                            </div>
                            <div class="col-md-6 mt-10">
                                <label>Grad Year</label>
                                <input type="text" class="form-control" name="GradYear" maxlength="4" value="<?php echo ($GradYear); ?>">
                            </div>
                        </div>

                        <div class="row mt-10">
                            <div class="col-md-6 mt-10">
                                <label>Job Title</label>
                                <input type="text" class="form-control" name="jobTitle" value="<?php echo ($jobTitle); ?>">
                            </div>
                        </div>

                        <div class="row">
    <div class="col-md-6 mt-10 d-flex align-items-center">
        <label class="me-2  ">Length of employment at time of evaluation</label>
        <input type="text" class="form-control w-auto" name="employeeEvalutionYear" maxlength="2" value="<?php echo ($employeeEvalutionYear); ?>" placeholder="Years">
    </div>
    <div class="col-md-6 mt-10 d-flex align-items-center">
        <label class="me-2 mt-10 mr-5" >Months</label>
        <input type="text" class="form-control w-auto" name="employeeEvalutionMonth" maxlength="2" value="<?php echo ($employeeEvalutionMonth); ?>">
    </div>
</div>


                        <div class="row mt-10">
                            <div class="col-md-12 ">
                                <label>Type of employment at time of evaluation</label><br>
                                <div class="d-flex flex-wrap">
                                    <label class="mr-3" style="margin-left: 10px;"><input type="radio" name="checkEvalutionTime" value="Full-Time" <?php echo ($TimeOfEvalution=='Full-Time')?"checked":"" ;?>> Full-Time</label>
                                    <label class="mr-3 " style="margin-left: 10px;"><input type="radio" name="checkEvalutionTime" value="Part-Time" <?php echo ($TimeOfEvalution=='Part-Time')?"checked":"" ;?>> Part-Time</label>
                                    <label><input type="radio" style="margin-left: 10px; name="checkEvalutionTime" value="Per-Diem" <?php echo ($TimeOfEvalution=='Per-Diem')?"checked":"" ;?>> Per-Diem</label>
                                </div>
                            </div>
                        </div>

                        <div class="row mt-10">
                            <div class="col-md-6">
                                <label>Name (while enrolled in the Program)</label>
                                <input type="text" class="form-control" name="enrolledName" value="<?php echo ($enrolledName); ?>">
                            </div>
                        </div>

                        <div class="row mt-10">
                            <div class="col-md-12 mb-3">
                                <label>Credential Status (check all that apply)</label><br>
                                <div class="d-flex flex-wrap gap-13">
                                    <?php 
                                    $credentials = ["CRT", "CPFT", "RPFT", "CRT-SDS", "CRRT-SDSRT", "RRT", "NPS", "RPSGT", "Other"];
                                    foreach ($credentials as $credential) { ?>
                                        <label class="mr-3" style="margin-left: 10px;" >
                                            <input type="radio" name="checkCredentialStatus" value="<?php echo $credential; ?>" <?php echo ($CredentialStatus == $credential) ? "checked" : ""; ?>> <?php echo $credential; ?>
                                        </label>
                                    <?php } ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


			<div class="row mb-10">
			<!-- <div class="col-md-2"></div> -->
				<div class="col-md-12">
					<label><b>Were you a student at the program’s satellite location?</b></label>
					<input type="radio" name="checkLocation" value="Yes" <?php echo ($satelliteLocation == 'Yes') ? "checked" : ""; ?>> Yes
					<input type="radio" name="checkLocation" value="No" <?php echo ($satelliteLocation == 'No') ? "checked" : ""; ?>> No
					<input type="radio" name="checkLocation" value="N/A" <?php echo ($satelliteLocation == 'N/A') ? "checked" : ""; ?>> N/A
				</div>
			</div>
			<div class="row mb-10">
			<!-- <div class="col-md-2"></div> -->
				<div class="col-md-12">
					<label><b>Were you a student in the program’s sleep specialist certificate?</b></label>
					<input type="radio" name="checkcertificate" value="Yes" <?php echo ($specialistCertificate == 'Yes') ? "checked" : ""; ?>> Yes
					<input type="radio" name="checkcertificate" value="No" <?php echo ($specialistCertificate == 'No') ? "checked" : ""; ?>> No
					<input type="radio" name="checkcertificate" value="N/A" <?php echo ($specialistCertificate == 'N/A') ? "checked" : ""; ?>> N/A
				</div>
			</div>

			<div class="row">
				<!-- <div class="col-md-2"></div> -->
				<div class="col-md-12">
				<div class="panel panel-default border-14" style="padding: 10px;">
					
					<label >5 = Excellent 4 = Above Average 3 = Average <span style="color:red"> 2 = Below Average 1 = Poor </span> </label>
									
				</div>
				</div>
			</div>

			<div class="row mb-10">
				<!-- <div class="col-md-3"></div> -->
				<div class="col-md-12">
					<label><b>YOUR OVERALL RATING OF THE PROGRAM:</b></label>
					5 <input  type="radio" name="OverAllRating" value="5"  <?php echo ($OverAllRating =='5')?"checked":"" ;?> >
					4 <input type="radio" name="OverAllRating" value="4" <?php echo ($OverAllRating =='4')?"checked":"" ;?> >
					3 <input type="radio" name="OverAllRating" value="3" <?php echo ($OverAllRating =='3')?"checked":"" ;?> >
					2 <input type="radio" name="OverAllRating" value="2" <?php echo ($OverAllRating =='2')?"checked":"" ;?> >
					1 <input type="radio" name="OverAllRating" value="1" <?php echo ($OverAllRating =='1')?"checked":"" ;?> >

				</div>
			</div>

			<div class="row">
				<div class="col-md-12">
							<div class="form-group">
								<!-- <div class="col-md-2"></div> -->
									<div class="col-md-12 col-sm-12 col-xs-12">
										<div class="panel panel-default border-14">
											<div class="panel-body">
													<label><u>INSTRUCTIONS:</u></label>
													Consider each item separately and rate each item independently of all others. Check the rating that indicates the extent to which you agree with each statement. Please do not skip any rating.<br> 
													<b style="margin-left:20px"> 5 = Strongly Agree 4 = Generally Agree 3 = Neutral (acceptable)<span style="color:red"> 2 = Generally Disagree 1 = Strongly Disagree</span>
														<br>
														<span style="color:red;margin-left:50px">NOTE: Please provide detailed comments for any item rated below 3.</span>
														(Relevant Standard is in parentheses)</b>
												
											</div>
										</div>
									</div>						
							</div>
						</div>

			<!---- 1st SECTION div start --------->
			<div class="">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="instructions:"></label>
						<div class="col-md-12 col-sm-12 col-xs-12">
							<div class="panel-group" id="posts">
								<div class="panel panel-default">
									<div class="panel-heading">
										<h4 class="panel-title">
											<a href="#CoarcSection" data-toggle="collapse" data-parent="#posts">Clinical Trac JRCERT Student Program Survey</a>
										</h4>
									</div>

									<div id="CoarcSection" class="panel-collapse collapse in">

										<?php while ($row = mysqli_fetch_array($CoarcSection)) {
											$sectionMasterId = $row['sectionMasterId'];
											$title = $row['title'];
											//$subTitle = $row['subTitle'];
										?>
											<div class="panel-body"><?php echo '<b>' . $title . '</b>'; ?></div>
											<?php
											// for question
											$totalCoarc = 0;
											$Coarcquestion = $objGraduateCoarc->GetAllGraduateCoarcSurveyQuestionMaster($schoolId, $sectionMasterId);

											if ($Coarcquestion != '') {
												$totalCoarc = mysqli_num_rows($Coarcquestion);
											}

											if ($totalCoarc > 0) {
												while ($row = mysqli_fetch_array($Coarcquestion)) {
													if (isset($_GET['graduateCoarcMasterId'])) {
														$graduateCoarcMasterId = DecodeQueryData($_GET['graduateCoarcMasterId']);
													} else {
														$graduateCoarcMasterId = 0;
													}


													$schoolGraduateCoarcQuestionId = $row['schoolGraduateCoarcQuestionId'];
													$questionText = $row['questionText'];
													$schoolGraduateCoarcQuestionType = $row['schoolGraduateCoarcQuestionType'];
													$qhtml = GetGraduateCoarcQuestionHtml($schoolGraduateCoarcQuestionId, $schoolGraduateCoarcQuestionType, $graduateCoarcSubmitedMasterId, $currentSchoolId);
											?>
													<div class="panel-body">
														<?php echo ($questionText); ?> <br /><br />
														<?php echo $qhtml; ?>
													</div>
										<?php
												}
											}
											//}													
										}
										?>
									</div>
								</div>

							</div>


						</div>

					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-md-6"></div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="control-label"></label>
						<div class="col-md-6">
							<!--button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                            <button id="btncompletecoarc" name="btncompletecoarc" class="btn btn-success">Submit Survey</button>
                            <!---a type="button" href="coarcsurvey.html?studentId=<?php //echo EncodeQueryData($currentstudentId); 
																					?>" class="btn btn-default">Cancel</a---->
						</div>
					</div>
				</div>
			</div>
		</form>


	</div>

	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/common.js"></script>




	<script type="text/javascript">
		alertify.defaults.transition = "slide";
		alertify.defaults.theme.ok = "btn btn-success";
		alertify.defaults.theme.cancel = "btn btn-danger";
		alertify.defaults.theme.input = "form-control";

		$(window).load(function() {


			$('#frmgraduatecoarcsurvey').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {
					ShowProgressAnimation();
					return true; // Don't submit form for this demo
				});

			$('#evaluationDate').datetimepicker({
				format: 'MM/DD/YYYY',
				maxDate: new Date()
			});


			//for searching dropdown
			$(".select2_single").select2();
			$('#select2-cborotation-container').addClass('required-select2');
			$('#select2-cboclinician-container').addClass('required-select2');

			<?php if (isset($_GET['graduateCoarcMasterId']) && ($_GET['studentId'])) { ?>
				$('#cbostudent').prop('disabled', true);
			<?php } ?>
		});
	</script>
</body>

</html>