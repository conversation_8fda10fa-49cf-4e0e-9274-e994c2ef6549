<?php 
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');   
    include('../class/clsRotation.php');  
    include('../class/clsVolunteerEval.php');  
    include('../class/clsLocations.php');  
    include('../setRequest.php'); 
    include('../class/clsStudent.php'); 


    $schoolId = 0;
	$rotationtitle ='';
    $transchooldisplayName = '';
    $DefaultrotationId=0;
    $totalVolunteerEval = 0;
    $TotalpointsAwarded=0;
    $TotaltimeSpent=0;
    $TotatlHours=0;
    $Totalminutes =0;
    $studentId = 0;
    $TotalHoursInMinutes=0;
	$TimeZone=  $_SESSION["loggedUserSchoolTimeZone"];
	$loggedUserId=$_SESSION["loggedUserId"];
    $loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0; 
    $canvasStatus= isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';
    $type = isset($_GET['type']) ? $_GET['type'] : '';
	if($type == 'canvas')
		$canvasStatus =1;

    if(isset($_GET['schoolId'])) //Edit Mode
	{
		$schoolId = $_GET['schoolId'];
        $schoolId = DecodeQueryData($schoolId);
    }
    else
    {
        $schoolId = $currentSchoolId;
        $transchooldisplayName=$currenschoolDisplayname;
    }

    $title ="Volunteer Evalution";

	$rotationId=0;
    $encodedRotationId = '';
	if(isset($_GET['rotationId'])) 
	{
        $encodedRotationId = $_GET['rotationId'];
		$DefaultrotationId = $_GET['rotationId'];
        $rotationId = DecodeQueryData($DefaultrotationId);
        $bedCrumTitle = 'Rotation';
    }

    //For Student Side
    $encodedStudentId = '';
	if(isset($_GET['studentId'])) 
	{
        $encodedStudentId = $_GET['studentId'];
		$studentId = $_GET['studentId'];
        $studentId = DecodeQueryData($studentId);
        $bedCrumTitle = 'Clinical';
    }
    
    //Get Rotation Name 
	$objrotation = new clsRotation();
    $rowsrotation=$objrotation->GetrotationTitleForInteraction($rotationId,$schoolId);
	$rotationtitle = isset($rowsrotation['title']) ? $rowsrotation['title'] : '';
    unset($objrotation);

    //Get All VolunteerEval
	$objVolunteerEval = new clsVolunteerEval();
	$rowsVolunteerEval = $objVolunteerEval->GetAllVolunteerEval($schoolId,$rotationId,$studentId,$canvasStatus);
	if($rowsVolunteerEval !='')
	{
		$totalVolunteerEval =mysqli_num_rows($rowsVolunteerEval);
	}
    unset($objVolunteerEval);
    
    //rotation
    $objrotation = new clsRotation();
	$rotation = $objrotation->GetRotation($schoolId,$studentId);
    unset($objrotation);

    //For Student Name
	$objStudent = new clsStudent();
	$rowsStudents = $objStudent->GetSingleStudent($schoolId,$studentId);
	$studentfullname=$rowsStudents ? ($rowsStudents['firstName'] . ' ' . $rowsStudents['lastName']) : '';
	unset($objStudent);	
?>
    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo($title); ?></title>
        <?php include('includes/headercss.php');?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
        <?php include("includes/datatablecss.php") ?>

		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css"> 

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <?php if($type == 'canvas') { ?>
							<li><a href="dashboard.html">Home</a></li>
							<li><a href="settings.html">Settings</a></li>
							<li class="active">Volunteer Evaluation</li>
						<?php } else { ?>
                            <li><a href="dashboard.html">Home</a></li>
                            <?php if($rotationId > 0) { ?>
                            <li class="active"><a href="rotations.html"><?php echo $bedCrumTitle; ?></a></li>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                            <?php } else{ ?>
                            <li class="active"><a href="clinical.html">Clinical</a></li>
                            <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li> 
                            <?php } ?>
                            <li class="active">Volunteer</li>
                        <?php } ?>
                    </ol>
                </div>
         
            </div>
        </div>

        <div class="container">
		<?php 
             if (isset($_GET["status"]))
                    {
                        if($_GET["status"] =="Added")
                        {
                            ?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                                </button> Volunteer added successfully.
                    </div>
                    <?php 
                        }
                        else if($_GET["status"] =="Updated")
                        {
                            ?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                                </button> Volunteer updated successfully.
                    </div>
                    
                   <?php 
                        }
                        else if($_GET["status"] =="Deleted")
                        {
                            ?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                                </button> Volunteer deleted successfully.
                    </div>
                    <?php 
                        }
                         else if($_GET["status"] =="Error")
                        {
                            ?>
                    <div class="alert alert-danger alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                                </button> Error occurred.
                    </div>
                    <?php 
                        }
    
                    }
                  ?>

				  
                <div id="divTopLoading" >Loading...</div>

                <?php if($type !='canvas') { ?>
                    <?php if($rotationId > 0 ) { ?>    
                    <div class="col-md-2 pull-right padding_right_zero mb-10">
                        <select id="cborotation" name="cborotation" class="form-control input-md required-input select2_single"  >
                        <!-- <option value="" selected>Select</option> -->
                            <?php
                            if($rotation!="")
                            {
                                while($row = mysqli_fetch_assoc($rotation))
                                {
                                    $selrotationId  = $row['rotationId'];
                                    $name  = stripslashes($row['title']);

                                    ?>
                                    <option value="<?php echo(EncodeQueryData($selrotationId)); ?>" <?php if($rotationId==$selrotationId){ ?>  selected="true" <?php }?>><?php echo($name); ?></option>
                                    <?php
                                }
                            }
                        ?>
                        </select>
                    </div>
                    
                    <label class=" control-label  pull-right" for="cborotation" style="margin-top:8px">Rotation:</label>
                    <br/> <br>
                        <?php } ?>
                    
                    <?php if($isActiveCanvas) { ?>
                    <div class="row margin_bottom_ten">
                        <div class="col-md-8"></div>
                        <div class="col-md-4">
                            <div class="form-group">
                                <label class="col-md-6 control-label text-right" for="" style="margin-top:8px">Canvas Status</label>
                                <div class="col-md-6 padding_right_zero padding_left_zero">
                                    <select id="canvasStatus" name="canvasStatus" class="form-control input-md required-input select2_single"  >
                                        <option value="" selected>All</option>
                                        <option value="1" <?php if($canvasStatus==1) { ?> selected="true" <?php } ?>>Sent</option>
                                        <option value="0" <?php if($canvasStatus==0 && $canvasStatus!='' ) { ?> selected="true" <?php } ?>>Not Sent</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php } ?>  
                
                <?php } ?>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th class="text-center">Volunteer Date </th>
                            <?php if($type == 'canvas') { ?>
                                <th class="text-center">Student<br>First Name </th>
                                <th class="text-center">Student<br>Last Name </th>
                                <th class="text-center">Rank</th>
                                <th>Rotation</th>
                            <?php } elseif ($studentId){ ?>
                            <th>Rotation</th>
                            <?php } else { ?>
                            <th class="text-center">Student<br>First Name </th>
                            <th class="text-center">Student<br>Last Name </th>
                            <th class="text-center">Rank</th>
                            <?php } ?>
                            <th>Organization</th>
                            <th class="text-center">Time Spent<br>(hh:mm)</th>                          
                            <th class="text-center">Clinician<br>Signature</th>     
                            <th class="text-center">School<br>Signature</th>
                            <th class="text-center">School<br>Response</th> 
                            <?php if($type != 'canvas') { ?>           
                                <th class="text-center">Action</th>
                            <?php } ?>
                            <?php if($isActiveCanvas && $type !='canvas') { ?>
                                <th class="text-center">Canvas Status</th>
                            <?php } ?>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
						 $objrotation = new clsRotation();
                        if($totalVolunteerEval>0)
                        {
                            while($row = mysqli_fetch_array($rowsVolunteerEval))
                            {
								$volunteerEvalId = $row['volunteerEvalId'];
								$volunteerEvalDate = $row['volunteerEvalDate'];
								$rotationId = stripslashes($row['rotationId']);
								$courselocationId = $row['locationId'];
								$parentRotationId = stripslashes($row['parentRotationId']);
								$rotationLocationId = stripslashes($row['rotationLocationId']);
								
								$locationId = 0;
								if($rotationLocationId != $courselocationId && $parentRotationId > 0)
								{
									if($parentRotationId > 0)
									{
										if(!$rotationLocationId)
											$locationId = $objrotation->GetLocationByRotation($rotationId);
										else
											$locationId  = $rotationLocationId;
									}
								}
								else
								{	
										$locationId  = $courselocationId;
								}
									
								//Get Time Zone By Rotation 
								$objLocation = new clsLocations();
								$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
								unset($objLocation);
								if($TimeZone =='')
									$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
								$volunteerEvalDate = converFromServerTimeZone($volunteerEvalDate,$TimeZone);
                                $volunteerEvalDate =date("m/d/Y",strtotime($volunteerEvalDate));
                                $orgnizationName = $row['orgnizationName'];
                                $DBstudentId = $row['studentId'];
                                $studentfirstName = $row['studentfirstName'];
                                $studentlastName = $row['studentlastName'];
                                $rotationname = $row['rotationname'];
                                $rank = $row['rank'];
                                $timeSpent = $row['timeSpent'];
                                
                                $schoolSummary = $row['schoolSummary'];
								$shortSummarylen=strlen($schoolSummary);
                                if($shortSummarylen > 80)
								{
									 $Summary=substr($schoolSummary,0,80);
									$Summary .= '...';
								}
								else
								{
									$Summary=$schoolSummary;
                                }

                                $clinicianDate = $row['clinicianDate'];
                                if($clinicianDate)
                                {
                                    $clinicianDate = converFromServerTimeZone($clinicianDate,$TimeZone);
                                    $clinicianDate =date("m/d/Y",strtotime($clinicianDate));
                                }
                                else{
                                    $clinicianDate = '-';
                                }
                                $schoolDate = $row['schoolDate'];
                                if($schoolDate)
                                {
                                    $schoolDate = converFromServerTimeZone($schoolDate,$TimeZone);
                                    $schoolDate =date("m/d/Y",strtotime($schoolDate));
                                }
                                else {
                                    $schoolDate = "-";
                                }
                                $Totalminutes +=$timeSpent;

                                // For Canvas
								$isSendToCanvas  = isset($row['isSendToCanvas']) ? $row['isSendToCanvas'] : 0;

                                $isSendToCanvasClass ='isSendRecordToCanvas';
                                $isSentToCanvasClass ='hide';
                                if($isSendToCanvas)
                                {
                                    $isSendToCanvasClass ='hide';
                                    $isSentToCanvasClass ='';
                                }

								$isUserCanSendCompletedRecordToCanvas = 0;
								if($schoolDate !='-' || $clinicianDate !='-')
									$isUserCanSendCompletedRecordToCanvas = 1;
                                
                                /// End Canvas ///

                                ?>
                            <tr>
                                <td class="text-center"><?php echo ($volunteerEvalDate);?></td>
                                <?php if($type == 'canvas') { ?>
                                    <td><?php echo $studentfirstName; ?></td>
                                    <td><?php echo $studentlastName;?> </td>
                                    <td><?php echo $rank;?> </td>
                                    <td><?php echo $rotationname;?> </td>

                                <?php } elseif ($studentId){ ?>
                                <td><?php echo $rotationname;?> </td>
                                <?php } else { ?>
                                <td><?php echo $studentfirstName; ?></td>
                                <td><?php echo $studentlastName;?> </td>
                                <td><?php echo $rank;?> </td>
                                <?php } ?>
                                <td><?php echo ($orgnizationName); ?></td>
                                <td class="text-center"><small> <?php echo ($timeSpent); ?></small></td>
                                <td class="text-center"><?php echo ($clinicianDate); ?></td>
                                <td class="text-center"><?php echo ($schoolDate); ?></td>
                                <td title="<?php echo ($schoolSummary); ?>">
                                   <small> <?php echo ($Summary); ?></small>
                                </td> 
                                
                                <?php if($type !='canvas') { ?>
                                    <td class="text-center">
                                    <?php
									$rotationStatus = checkRotationStatus($rotationId);
									$viewParam = ($rotationStatus) ? '&view=V' : ''; // Add '&view=V' if $rotationStatus is true
									$linkText = ($rotationStatus) ? 'View' : 'Edit'; // Change link text based on $rotationStatus
									?>
                                        <a href="addVolunteerEval.html?volunteerEvalId=<?php echo(EncodeQueryData($volunteerEvalId)); ?><?php if ($studentId){?>&studentId=<?php echo (EncodeQueryData($studentId));}else { ?>&rotationId=<?php echo EncodeQueryData($DefaultrotationId); }?> <?php echo $viewParam; ?>"><?php echo $linkText; ?></a> 
                                        | <a  href="javascript:void(0);" class="deleteAjaxRow" volunteerEvalId="<?php echo EncodeQueryData($volunteerEvalId); ?>" >Delete</a>
                                    </td>
                                <?php } ?>
                                <?php if($isActiveCanvas && $type !='canvas') 
										{ 
											if($loggedUserSendRecordToCanvas && $isUserCanSendCompletedRecordToCanvas) 
											{ ?>
											<td class="text-center">
												<a href="javascript:void(0);" id="isSendToCanvas_<?php echo $volunteerEvalId; ?>" class="<?php echo $isSendToCanvasClass;?>" 
                                                    
                                                    volunteerEvalDate="<?php echo $volunteerEvalDate; ?>"
													orgnizationName="<?php echo $orgnizationName; ?>"
													timeSpent="<?php echo $timeSpent; ?>"
													clinicianDate="<?php echo $clinicianDate; ?>"
													schoolDate="<?php echo $schoolDate; ?>"
                                                    rotation="<?php echo $rotationname; ?>" 
													studentfullname="<?php echo $studentfirstName.' '.$studentlastName; ?>"
													studentId="<?php echo $DBstudentId; ?>" 
													volunteerEvalId="<?php echo $volunteerEvalId; ?>"
													>
													Send to Canvas
												</a>
												<label for=""  class="isSentToCanvas_<?php echo $volunteerEvalId; ?> <?php echo $isSentToCanvasClass; ?>">Sent</label>
												
											</td>
									
									<?php } 
											else
											{ ?>
											<td class="text-center"><label for=""  class=""> - 
												<?php //if($isSendToCanvas>0) { echo 'Sent'; } else { echo 'Send to canvas'; } ?>
											</label></td>
												
										<?php } 
										
									}
								?> 
                            </tr>
                            <?php
                            }
                        }
						unset($objrotation);
                    ?>

                    </tbody>
                    <tfoot>
                            <tr>
                                <?php if ($type == 'canvas'){ ?>
                                    <td colspan="6" align="right" ><b>Total:</b></td>
                                
                                <?php } elseif ($studentId){ ?>
                                    <td colspan="3" align="right" ><b>Total:</b></td>
                                <?php } else { ?>
                                <td colspan="5" align="right" ><b>Total:</b></td>
                                <?php } ?>
                                <td align="center">
                                <?php 
                                  echo sprintf("%02d:%02d", floor($Totalminutes/60), $Totalminutes%60); ?>
                                </td>
                                <td align="center"></td>
                                <td>&nbsp;</td>
                                <?php if($type !='canvas') { ?>
                                <td>&nbsp;</td>
                                
                                <td>&nbsp;</td>
                                <?php } ?>
                                <?php if($isActiveCanvas && $type !='canas') { ?>
                                    <td>&nbsp;</td>
                                <?php } ?>
                            </tr>
                        </tfoot>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
        <script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

        <script type="text/javascript">

        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
                $(".select2_single").select2();

                $("#cborotation").change(function(){
					var rotationId = $(this).val();
					if(rotationId)
					{
						window.location.href = "volunteerEvalList.html?rotationId="+rotationId;
					}
					else{
						window.location.href = "volunteerEvalList.html";
					}
				});

            });

            

            var current_datatable = $("#datatable-responsive").DataTable({
                "ordering": true,
                "order": [[0, "desc" ]],
                "aoColumns": [{
                    "sWidth": "15%"
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                },
                {
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter",
					// "bSortable": false
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                }]
                
            });

            // ajax call for deleteAjaxRow
			
			// ajax call for delete
		  function ShowwarningMessage()
            {
                alertify.alert('Warning','Wating for approved. You can\'t delete this.');
            }    

		 
                      
             $(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var volunteerEvalId = $(this).attr('volunteerEvalId');
                
                
                alertify.confirm('Volunteer Evalution: ', 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: volunteerEvalId,
                            type: 'volunteerEval'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });

            //Send Records To Canvas
			$(document).on('click', '.isSendRecordToCanvas', function() {

                var that = this;
                var volunteerEvalDate = $(this).attr('volunteerEvalDate');
                var orgnizationName = $(this).attr('orgnizationName');
                var timeSpent = $(this).attr('timeSpent');
                var clinicianDate = $(this).attr('clinicianDate');
                var schoolDate = $(this).attr('schoolDate');
                var rotation = $(this).attr('rotation');
                var schoolId = "<?php echo $currentSchoolId; ?>";
                var studentfullname = $(this).attr('studentfullname');
                var studentId = $(this).attr('studentId');
                var volunteerEvalId = $(this).attr('volunteerEvalId');

                alertify.confirm('Volunteer Evalution ', 'Continue with send record to Canvas?', function() {
                    $(that).text('Loading..');
                    $(that).prop('disabled',true);

                    $.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_record_to_canvas.html",
                        data: {
                            
                            volunteerEvalDate: volunteerEvalDate,
                            orgnizationName: orgnizationName,
                            timeSpent: timeSpent,
                            clinicianDate: clinicianDate,
                            schoolDate: schoolDate,
                            rotation: rotation,
                            studentFullName: studentfullname,
                            studentId: studentId,
                            schoolId: schoolId,
                            volunteerEvalId: volunteerEvalId,
                            type: 'VolunteerEval'
                        },
                        success: function(response) {
                            if(response == 'Success')
                            {
                                $(that).addClass('hide');
                                $('.isSentToCanvas_'+volunteerEvalId).removeClass('hide')
                                alertify.success('Record Successfully Sent to Canvas.');
                            }
                            else
                            {
                                alertify.success(response);
                            }
                            
                        }
                    });
                }, function() {});

            });

            $("#canvasStatus").change(function(){
                var canvasStatus=$(this).val();
                var studentId='<?php echo $encodedStudentId; ?>';
                var rotationId='<?php echo $encodedRotationId; ?>';

                if(studentId !='')
                {
                    if(canvasStatus !='')
                        window.location.href = "summativelist.html?studentId="+studentId+"&canvasStatus="+canvasStatus;
                    else
                        window.location.href = "summativelist.html?studentId="+studentId;
                }
                else if(rotationId !='')
                {
                    if(canvasStatus !='')
                        window.location.href = "summativelist.html?summativerotationid="+rotationId+"&canvasStatus="+canvasStatus;
                    else
                        window.location.href = "summativelist.html?summativerotationid="+rotationId;
                }
            });
			
			
        </script>


    </body>

    </html>