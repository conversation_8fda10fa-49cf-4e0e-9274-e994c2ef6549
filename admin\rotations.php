<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsCourses.php');
include('../class/clsRotation.php');
include('../class/clsSystemUser.php');

$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = '';
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

//For Check Checkoff 
$isActiveCheckoff = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];

//echo '$TimeZone->'.$TimeZone;
$title = "Rotations  ";
$schoolId = 0;
$courseId = 0;
$locationId = 0;
$rotationId = 0;
$from_date = '';
$to_date = '';
$rowsRotations = '';

$loggedUserLocationId = $_SESSION["loggedUserLocation"];
$transchooldisplayName = '';
$schoolId = $currentSchoolId;
$transchooldisplayName = $currenschoolDisplayname;

$display_from_date = '';
$display_to_date = '';

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSearch'])) {
    $display_from_date = $_POST['fromDate'];
    $display_from_date = date("m/d/Y", strtotime($display_from_date));
    $display_to_date = $_POST['toDate'];
    $display_to_date = date("m/d/Y", strtotime($display_to_date));
}

if (isset($_GET['courseId'])) {
    $courseId = $_GET['courseId'];
    $courseId = DecodeQueryData($courseId);
}
//GetAllrotation
$objRotation = new clsRotation();
$totalRotations = 0;
if (isset($_GET['type'])) {
    $rowsRotations = $objRotation->GetAllDisplayrotation($currentSchoolId, $locationId, $courseId, $rotationId);
    if ($rowsRotations != '') {
        $totalRotations = mysqli_num_rows($rowsRotations);
    }
} else {
    $rowsRotations = $objRotation->GetAllActiverotationByAdmin($currentSchoolId, $locationId, $courseId, $rotationId, $display_from_date, $display_to_date);
    if ($rowsRotations != '') {
        $totalRotations = mysqli_num_rows($rowsRotations);
    }
}



// unset($objRotation);
//GetCouses
$objCourses = new clsCourses();
$courses = $objCourses->GetCousesByLocation($currentSchoolId, $loggedUserLocationId, $courseId);
unset($objCourses);


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <style>
        .scheduleClass {
            color: #449d44 !important;
            font-weight: bold;
        }

        /* .dt-bootstrap {
            overflow-x: auto;
        } */

        thead tr th {
            text-align: center;
        }
    </style>

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if ($courseId > 0) {  ?>
                        <li><a href="courses.html">Course</a></li>
                        <li class="active">Rotations</li>
                    <?php } else { ?>
                        <li class="active">Rotations</li>
                    <?php  } ?>
                </ol>
            </div>

            <div class="pull-right">
                <?php
                if (isset($_GET['courseId'])) { ?>
                    <a class="btn btn-link" href="addrotations.html?courseId=<?php echo (EncodeQueryData($courseId)); ?>">Add</a>

                <?php  } else { ?>
                    <a class="btn btn-link" href="addrotations.html">Add</a>
                <?php } ?>
            </div>


        </div>
    </div>

    <div class="custom-container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>

        <div class="row" style="margin: 0 !important;">
            <form name="rotationlist" id="rotationbtn" method="POST" action="rotations.html">
                <a href="rotations.html?type=all" id="btnSearch" name="btnSearch" class="btn btn-success pull-right" style="margin-right: 16px;">Display All</a>
            </form>
            <div class="col-md-8">

                <form name="rotationlist" id="rotationlist" method="POST" action="rotations.html">

                    <div class="col-md-6 margin_bottom_ten">
                        <div class="form-group">
                            <label class="col-md-12 px-0 control-label" for="fromDate">From Date</label>
                            <div class="col-md-12 px-0">
                                <div class='input-group date w-full relative' name="fromDate" id='fromDate'>
                                    <input type='text' name="fromDate" id="fromDate" value="<?php echo ($display_from_date); ?>" class="dateInputFormat form-control input-md r rotation_date" data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>

                    <div class="col-md-5 margin_bottom_ten">
                        <div class="form-group">
                            <label class="col-md-12 px-0 control-label" for="toDate">To Date</label>
                            <div class="col-md-12 px-0">
                                <div class='input-group date w-full relative' id='toDate'>
                                    <input type='text' name="toDate" id="toDate" class="dateInputFormat form-control input-md  rotation_date" value="<?php echo ($display_to_date); ?>" data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
                                    <span class="input-group-addon calender-icon">
                                        <span class="glyphicon glyphicon-calendar"></span>
                                    </span>
                                </div>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                    <div class="col-md-1  margin_bottom_ten">
                        <div class="form-group">
                            <label class="col-md-12 control-label" for="toDate" style="visibility: hidden;height: 25px;"></label>
                            <div class="col-md-12">
                                <button id="btnSearch" name="btnSearch" class="btn btn-success">Search</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>


            <div class="col-md-4 pull-right margin_bottom_ten">
                <div class="form-group">
                    <div class="pull-right padding_right_zero" style="width:53%;margin-top: 9px;">
                        <select id="cbocourses" name="cbocourses" class="form-control input-md required-input select2_single" required>
                            <option value="" selected>Select</option>
                            <?php
                            if ($courses != "") {
                                while ($row = mysqli_fetch_assoc($courses)) {
                                    $selcourseId  = $row['courseId'];
                                    $name  = stripslashes($row['title']);

                            ?>
                                    <option value="<?php echo (EncodeQueryData($selcourseId)); ?>" <?php if ($courseId == $selcourseId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                            <?php

                                }
                            }
                            ?>
                        </select>
                    </div>
                    <label class="control-label  pull-right" for="cbocourses" style="margin-top:20px; margin-right: 10px;">Courses:</label>

                </div>
            </div>
        </div>
        <div class="col-md-12" style="overflow-x: auto;">
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th>Rotation</th>
                        <th style="text-align: left">Course</th>
                        <th style="text-align: center">Start Date</th>
                        <th style="text-align: center">End Date</th>
                        <th style="text-align: center">Duration</th>
                        <th style="text-align: center">Students</th>
                        <th style="text-align: center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($totalRotations > 0) {
                        while ($row = mysqli_fetch_array($rowsRotations)) {
                            $rotationId = $row['rotationId'];
                            $courseTitle = $row['courseTitle'];
                            $locationTitle = $row['locationTitle'];
                            //$studentId = $row['studentId'];					
                            $startDate = $row['startDate'];
                            $startDate = converFromServerTimeZone($startDate, $TimeZone);
                            $endDate = $row['endDate'];
                            $endDate = converFromServerTimeZone($endDate, $TimeZone);
                            $title = stripslashes($row['title']);
                            $isDelete = stripslashes($row['isDelete']);
                            $deleteDate = stripslashes($row['deleteDate']);
                            $deletedUserId = stripslashes($row['deletedUserId']);
                            $courseHours = $row['courseHours'];
                            //Get deleted user name
                            if ($deletedUserId != '') {
                                $objSystemUser = new clsSystemUser();
                                $rowSystemUser = $objSystemUser->GetSchoolSystemUsersName($deletedUserId);
                                $firstName = $rowSystemUser['firstName'];
                                $lastName = $rowSystemUser['lastName'];
                                $fullName =  $firstName . ' ' . $lastName;
                            }
                            unset($objSystemUser);
                            $rotationStudentCount = $objRotation->GetCountRotationByStudent($rotationId);
                            $scheduleCount = $objRotation->GetCountSchedule($rotationId);

                            $scheduleClass = ($scheduleCount) ? 'scheduleClass' : '';
                            $subratoncount = $row['subratoncount'];
                            $duration = $row['duration'];
                            $hospitalSite = $row['hospitalSite'];
                            $hospitalSiteDispName = strlen($hospitalSite) > 30 ? (substr($hospitalSite, 0, 27)) . '...' : $hospitalSite;

                            $startDateTimestamp = strtotime($startDate);
                            $endDateTimestamp = strtotime($endDate);

                    ?>
                            <tr>
                                <?php if ($isDelete == '0') { ?>

                                    <td>
                                        <?php

                                        echo '<span class = ' . $scheduleClass . '>' . ($title) . '</span>' . '<br><div><small><label title="' . $hospitalSite . '">Hospital: ' . $hospitalSiteDispName . '</label></div>';
                                        if ($subratoncount > 0 && $scheduleCount > 0) {
                                        ?>
                                            <a title='View Hospital Sites' href="addsubrotation.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>">Schedules</a>
                                        <?php
                                        } else if ($subratoncount > 0) {
                                        ?>
                                            <a title='View Schedules' href="addsubrotation.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>">Hospital Sites</a>
                                        <?php
                                        }
                                        ?>
                                    </td>
                                <?php } else { ?>
                                    <td> <span style="color:red;"> <?php echo ($title); ?> </span> <br>
                                        <div><small><label title="<?php echo ($hospitalSite); ?>">Hospital: <?php echo ($hospitalSiteDispName); ?></label></div>
                                        <?php if ($subratoncount > 0) {
                                        ?>
                                            <a title='View Hospital Sites' href="addsubrotation.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>">Hospital Sites</a>
                                        <?php
                                        }
                                        ?>
                                    </td>
                                <?php } ?>
                                <td style="text-align: left">
                                    <?php echo $courseTitle; ?><br>
                                    <small>Location: </small><?php echo '<small>' . $locationTitle . '</small>'; ?>
                                    <?php if ($courseHours != '') { ?>
                                        <br><small>Required Hours: </small><?php echo '<small>' . $courseHours . 'Hrs </small>'; ?>
                                    <?php } ?>
                                </td>

                                <td style="text-align: center">
                                    <?php echo (date('m/d/Y', $startDateTimestamp)); ?>
                                </td>
                                <td style="text-align: center">
                                    <?php echo (date('m/d/Y', $endDateTimestamp)); ?>
                                </td>
                                <td style="text-align: center">
                                    <small>From </small> <?php echo (date('h:i A', $startDateTimestamp)); ?>
                                    <small> To </small> <?php echo (date('h:i A', $endDateTimestamp)); ?>
                                </td>
                                <td style="text-align: center">
                                    <?php
                                    $displayText = 'Assign';

                                    if ($rotationStudentCount > 0)
                                        $displayText = $rotationStudentCount;

                                    if ($subratoncount > 0) {

                                    ?>
                                        -
                                    <?php } else { ?>
                                        <a title="Add students to rotation" href="assignstudents.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>"><?php echo $displayText; ?></a>
                                    <?php } ?>
                                </td>
                                <td style="text-align: center">
                                    <?php
                                    if ($subratoncount > 0) {
                                        if (isset($_GET['courseId'])) { ?>
                                            <a href="addrotations.html?courseId=<?php echo (EncodeQueryData($courseId)); ?>&id=<?php echo (EncodeQueryData($rotationId)); ?>">Edit</a>
                                            <?php } else {
                                            if ($isDelete == '1' && is_numeric($deleteDate) != 'NULL') { ?>
                                                <span><?php if ($deletedUserId != '') {
                                                            echo $fullName; ?> | <?php } ?></span>
                                                <span>Deleted | </span>
                                                <span><?php echo date('m/d/Y h:i A', strtotime($deleteDate)) ?>|</span>
                                                <a href="javascript:void(0);" class="undeleteAjax" deletedUserId="<?php echo EncodeQueryData($deletedUserId); ?>" rotationId="<?php echo EncodeQueryData($rotationId); ?>" rotationName="<?php echo ($title); ?>">Undelete</a>
                                            <?php } else { ?>

                                                <a href="copyrotations.html?id=<?php echo (EncodeQueryData($rotationId)); ?>&copyrotationId=<?php echo (EncodeQueryData($rotationId)); ?>">Copy</a>
                                                |<a href="addrotations.html?id=<?php echo (EncodeQueryData($rotationId)); ?>">Edit</a>
                                            <?php  }
                                        }
                                        $currentDate = date('Y-m-d 00:00:00');
                                        if ($isDelete != '1') {
                                            if (strtotime($currentDate) > strtotime($startDate)) {
                                            ?>
                                                | <a onclick="javascript:ShowCountMessage();" href="javascript:void(0);" class="text-muted">Delete</a>
                                            <?php
                                            } else { ?>
                                                | <a href="javascript:void(0);" class="deleteAjaxRow" rotationId="<?php echo EncodeQueryData($rotationId); ?>" rotationName="<?php echo ($title); ?>">Delete</a>
                                        <?php
                                            }
                                        }
                                    } elseif ($isDelete == '1' && is_numeric($deleteDate) != 'NULL') {

                                        ?>
                                        <span><?php if ($deletedUserId != '') {
                                                    echo $fullName; ?> | <?php } ?></span>
                                        <span>Deleted | </span>
                                        <span><?php echo date('m/d/Y h:i A', strtotime($deleteDate)) ?> |</span>
                                        <a href="javascript:void(0);" class="undeleteAjax" deletedUserId="<?php echo EncodeQueryData($deletedUserId); ?>" rotationId="<?php echo EncodeQueryData($rotationId); ?>" rotationName="<?php echo ($title); ?>"> Undelete</a>
                                        <?php } else {
                                        if ($_SESSION["loggedUserRoleType"] == 'P' || $_SESSION["loggedUserRoleType"] == 'p' || $_SESSION["loggedUserRoleType"] == 'C' || $_SESSION["loggedUserRoleType"] == 'c') { ?>
                                            <a href="journallist.html?rotationId=<?php echo EncodeQueryData($rotationId) ?>">Daily Journal</a> |
                                            <a href="interaction.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Doctor Interaction</a>
                                        <?php } else { ?>
                                            <?php if ($schoolId != 145) { ?>

                                                <a href="journallist.html?rotationId=<?php echo EncodeQueryData($rotationId) ?>">Daily Journal</a> |
                                            <?php } ?>
                                            <!-- <a href="incident.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>">Incident</a> -->
                                            <!-- |<a href="interaction.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Dr. Interaction</a> | -->
                                            <a href="procedurecountsNew.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Procedure Count</a>
                                            <?php if ($isActiveCheckoff == 1) { ?>
                                                |<a href="checkoff.html?rotationId=<?php echo EncodeQueryData($rotationId) ?>&Type=R"> Comps</a>
                                            <?php } elseif ($isActiveCheckoff == 2) { ?>
                                                |<a href="checkoffusaf.html?rotationId=<?php echo EncodeQueryData($rotationId) ?>&Type=R"> Comps</a>
                                            <?php } else { ?>
                                                |<a href="checkoffs.html?rotationId=<?php echo EncodeQueryData($rotationId) ?>&Type=R"> Comps</a>
                                            <?php } ?>

                                            | <a href="pevaluationlist.html?pevaluationrotationid=<?php echo EncodeQueryData($rotationId); ?>">T Evaluation</a>|
                                            <a href="siteevaluationlist.html?siteevaluationrotationid=<?php echo EncodeQueryData($rotationId); ?>">Site Evaluation</a>|
                                            <a href="midtermperformancelist.html?jrmidtermrotationid=<?php echo EncodeQueryData($rotationId); ?>">JPE </a>|
                                            <a href="srmidtermprofessionalList.html?srMidTermRotationId=<?php echo EncodeQueryData($rotationId); ?>" title="">SPE </a>|


                                            <!-- Performance Evaluation -->
                                            <a href="performanceEvaluationList.html?performanceRotationId=<?php echo EncodeQueryData($rotationId); ?>">Performance Eval </a>|
                                            <!-- Clinical Evaluation -->
                                            <!-- <a href="clinicalEvaluationList.html?clinicalRotationId=<?php echo EncodeQueryData($rotationId); ?>">Clinical Evaluation</a>| -->

                                            <a href="orientationevallist.html?orientationrotationid=<?php echo EncodeQueryData($rotationId); ?>">Orientation Checklist </a>|
                                            <!-- Clinical Pre-comp Evaluation -->
                                            <!-- <a href="clinicalPrecompList.html?clinicalPrecompRotationId=<?php echo EncodeQueryData($rotationId); ?>">Clinical Pre-Comp</a>| -->

                                            <?php if ($isActiveCheckoff == 1) { ?>
                                                <a href="volunteerEvalList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Volunteer Evaluation</a>|
                                            <?php } ?>
                                            <?php if ($currentSchoolId == 75 || $currentSchoolId == 121) { ?>
                                                <a href="pefList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">PEF Evaluation</a>|
                                            <?php } ?>
                                            <?php if ($currentSchoolId == 75 || $currentSchoolId == 127) { ?>
                                                <a href="floorTherapyAndICUEvaluationlist.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>">Floor Therapy And ICU Evaluation</a>|
                                            <?php } ?>
                                            <?php if ($schoolId != 145) { ?>

                                                <a href="equipmentlist.html?equipmentrotationid=<?php echo EncodeQueryData($rotationId); ?>">Equipment List</a>|
                                            <?php } ?>
                                            <?php if (isset($_GET['courseId'])) { ?>
                                                <a href="addrotations.html?courseId=<?php echo (EncodeQueryData($courseId)); ?>&id=<?php echo (EncodeQueryData($rotationId)); ?>">Edit</a>
                                                <?php } else {
                                                if ($isDelete == '1' && is_numeric($deleteDate) != 'NULL') { ?>
                                                    <span><?php if ($deletedUserId != '') {
                                                                echo $fullName; ?> | <?php } ?></span>
                                                    <span>Deleted | </span>
                                                    <span><?php echo date('m/d/Y h:i A', strtotime($deleteDate)) ?></span>
                                                <?php } else { ?>
                                                    <a href="copyrotations.html?id=<?php echo (EncodeQueryData($rotationId)); ?>&copyrotationId=<?php echo (EncodeQueryData($rotationId)); ?>">Copy</a>
                                                    |<a href="addrotations.html?id=<?php echo (EncodeQueryData($rotationId)); ?>">Edit</a>
                                                <?php  }
                                            }
                                            $currentDate = date('Y-m-d 00:00:00');
                                            if (strtotime($currentDate) > strtotime($startDate)) {
                                                ?>
                                                | <a onclick="javascript:ShowCountMessage();" href="javascript:void(0);" class="text-muted">Delete</a>
                                            <?php
                                            } else { ?>
                                                | <a href="javascript:void(0);" class="deleteAjaxRow" rotationId="<?php echo EncodeQueryData($rotationId); ?>" rotationName="<?php echo ($title); ?>">Delete</a>
                                    <?php
                                            }
                                        }
                                    }

                                    ?>
                                </td>
                            </tr>
                    <?php
                        }
                    }
                    ?>
                </tbody>
            </table>
        </div>
    </div>
    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();

            $("#cbocourses").change(function() {
                var courseId = $(this).val();

                if (courseId) {
                    window.location.href = "rotations.html?courseId=" + courseId;
                } else {
                    window.location.href = "rotations.html";
                }
            });

            $(function() {
                $("#fromDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            $(function() {
                $("#toDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

        });

        <?php if ($isActiveCheckoff == "2") { ?>

            var current_datatable = $("#datatable-responsive").DataTable({

                "ordering": true,
                "order": [
                    [2, "desc"]
                ],
                "aoColumns": [{
                        "sWidth": "15%"
                    }, {
                        "sWidth": "10%",
                        "sClass": "alignCenter",
                        "bSortable": true
                    }, {
                        "sWidth": "10%",
                        "sClass": "alignCenter"

                    },
                    {
                        "sWidth": "10%",
                        "sClass": "alignCenter"

                    }, {
                        "sWidth": "10%",
                        "sClass": "alignCenter",
                        // "bSortable": false
                    }, {
                        "sWidth": "5%",
                        "sClass": "alignCenter",
                        // "bSortable": false
                    }, {
                        "sWidth": "10%",
                        "sClass": "alignCenter",
                        // "bSortable": false
                    }
                ]
            });
        <?php } else { ?>
            var current_datatable = $("#datatable-responsive").DataTable({

                "ordering": true,
                "aoColumns": [{
                        "sWidth": "15%"
                    }, {
                        "sWidth": "10%",
                        "sClass": "alignCenter",
                        // "bSortable": false
                    }, {
                        "sWidth": "10%",
                        "sClass": "alignCenter"
                    },
                    {
                        "sWidth": "10%",
                        "sClass": "alignCenter"
                    }, {
                        "sWidth": "10%",
                        "sClass": "alignCenter",
                        "bSortable": false
                    }, {
                        "sWidth": "5%",
                        "sClass": "alignCenter",
                        "bSortable": false
                    }, {
                        "sWidth": "10%",
                        "sClass": "alignCenter",
                        "bSortable": false
                    }
                ]
            });
        <?php } ?>

        // ajax call for deleteAjaxRow
        function ShowCountMessage() {
            alertify.alert('Warning', 'This rotation is already started. You can\'t delete this.');
        }

        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var rotationId = $(this).attr('rotationId');
            var title = $(this).attr('rotationName');
            var loggedDeletedUserId = <?php echo ($_SESSION["loggedUserId"]); ?>;

            alertify.confirm('Rotation: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: rotationId,
                        type: 'rotation',
                        loggedDeletedUserId: loggedDeletedUserId,
                        title: title
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                        location.reload();
                    }
                });
            }, function() {});

        });


        // undelete rotation
        $(document).on('click', '.undeleteAjax', function() {

            var rotationId = $(this).attr('rotationId');
            var loggedDeletedUserId = $(this).attr('deletedUserId');
            var title = $(this).attr('rotationName');

            alertify.confirm('Rotation: ' + title, 'Continue with undelete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_undelete.html",
                    data: {
                        id: rotationId,
                        loggedDeletedUserId: loggedDeletedUserId
                    },
                    success: function() {

                        alertify.success('Undeleted');
                        location.reload();
                    }
                });
            }, function() {});

        });
    </script>


</body>

</html>