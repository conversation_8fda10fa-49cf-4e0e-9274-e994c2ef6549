<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsJuniorMidtermPerformanceEval.php');
include('../class/clsQuestionOption.php');

$studentJrMidtermEvalId = 0;
$evaluationDate = date('Y-m-d');
// echo '<pre>';
// print_r($_POST);
// exit;
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
	$rotationId = 0;

	if (isset($_GET['studentOrientationEvalId'])) {
        $studentOrientationEvalId = $_GET['studentOrientationEvalId'];
        $studentOrientationEvalId = DecodeQueryData($studentOrientationEvalId);
    }
    if (isset($_GET['rotationId'])) {
        $rotationId = $_GET['rotationId'];
        $rotationId = DecodeQueryData($rotationId);
    }

    $studentOrientationEvalId = isset($_GET['studentOrientationEvalId']) ? DecodeQueryData($_GET['studentOrientationEvalId']) : 0;
    $status = ($studentOrientationEvalId > 0) ? 'updated' : 'added';

    // $cboclinician  = isset($_POST['cboclinician']) ? $_POST['cboclinician'] : 0;
    // $rotationId  = isset($_POST['cboTerm']) ? $_POST['cboTerm'] : 0;
    // $cboClinicalsites  = isset($_POST['cboclinicalsites']) ? $_POST['cboclinicalsites'] : 0;
    // $cboStudent  = isset($_POST['cboStudent']) ? $_POST['cboStudent'] : 0;

    $studentsignitureDate  = isset($_POST['studentsignitureDate']) ? $_POST['studentsignitureDate'] : 0;
    // $evaluationDate = GetDateStringInServerFormat($_POST['evaluationDate']);

    $studentsignitureDate = GetDateStringInServerFormat($_POST['studentsignitureDate']);
    $studentsignitureDate = str_replace('00:00:00', '12:00 PM', $studentsignitureDate);
    $studentsignitureDate = date('Y-m-d H:i', strtotime($studentsignitureDate));
    // exit;
    $objOrientationEval = new clsOrientationChecklist();
    $objOrientationEval->rotationId = $rotationId;
    // $objOrientationEval->schoolId = $currentSchoolId;
    // $objOrientationEval->hospitalSiteId = $cboClinicalsites;
    // $objOrientationEval->studentId = $cboStudent;
    // $objOrientationEval->evaluationDate = $evaluationDate;
    $objOrientationEval->dateOfStudentSignature = $studentsignitureDate;

    $objOrientationEval->createdBy = $_SESSION['loggedStudentId'];;

    $retOrientationEvalId = $objOrientationEval->SaveClinicianOrientationEval($studentOrientationEvalId);
    if ($retOrientationEvalId > 0) {
        $objOrientationEval->DeleteStudentOrientationEvalkDetails($retOrientationEvalId);

        foreach ($_POST as $id => $value) {

            if (strpos($id, 'questionoptionst_') === 0) {
                //$id = explode("_", $id)[1];
                $id =    str_replace('questionoptionst_', '', $id);
                $objOrientationEval->studentOrientationEvalId = $retOrientationEvalId;
                $objOrientationEval->studentQuestionId = $id;
                $objOrientationEval->studentoptionvalue = '';
                $objOrientationEval->studentOptionAnswerText = $value[0];
                $objOrientationEval->clinicianId = '';

                $studentDailyDetailId = $objOrientationEval->SaveOrientationEvalDetails($retOrientationEvalId);
            }
        }

        foreach ($_POST as $id => $value) {

            if (strpos($id, 'cbostage_') === 0) {
                $queid = str_replace('cbostage_', '', $id);
                $objOrientationEval->studentOrientationEvalId = $retOrientationEvalId;
                $objOrientationEval->studentQuestionId = $queid;
                $objOrientationEval->studentoptionvalue = '';
                $objOrientationEval->studentOptionAnswerText = '';
                $objOrientationEval->clinicianId = isset($_POST['cbostage_' . $queid]) ? $_POST['cbostage_' . $queid] : 0;
                $objOrientationEval->completionDate = isset($_POST['datestage_' . $queid]) ? $_POST['datestage_' . $queid] : 0;
                $studentDailyDetailId = $objOrientationEval->SaveOrientationEvalDetails($retOrientationEvalId);
            }
        }
    }
    unset($objOrientationEval);

    if ($retOrientationEvalId > 0) {
        if (isset($_GET['studentOrientationEvalId']))
            header('location:orientationEvalList.html?orientationrotationid=' . EncodeQueryData($rotationId) . '&studentOrientationEvalId=' . EncodeQueryData($studentOrientationEvalId) . '&status=' . $status);
        else
            header('location:orientationEvalList.html?orientationrotationid=' . EncodeQueryData($rotationId) . '&status=' . $status);
        exit();
    } else {
        header('location:addorientationeval.html?status=error');
    }
} {
    header('location:orientationEvalList.html');
    exit();
}
