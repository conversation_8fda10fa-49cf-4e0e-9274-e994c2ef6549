<?php 
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsBriefcase.php');   	
    include('../setRequest.php'); 
    
    $schoolId = 0;
    $briefcaseId=0;
    $transchooldisplayName = '';
	$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
	
    if(isset($_GET['schoolId'])) //Edit Mode
	{
		$schoolId = $_GET['schoolId'];
        $schoolId = DecodeQueryData($schoolId);
    }
    else
    {
        $schoolId = $currentSchoolId;
        $transchooldisplayName=$currenschoolDisplayname;
    }

	if(isset($_GET['briefcaseId'])) //Edit Mode
	{
		$briefcaseId = $_GET['briefcaseId'];
        $briefcaseId = DecodeQueryData($briefcaseId);
    }
    $title ="Briefcase ";

    //For Immunization List 
	$objBriefcase = new clsBriefcase();
    $totalBriefcase = 0;
	$rowsBriefcase = $objBriefcase->GetAllBriefcase($currentSchoolId);
	if($rowsBriefcase !='')
	{
		$totalBriefcase =mysqli_num_rows($rowsBriefcase);
	}
     unset($objBriefcase);
	 
?>
    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo($title); ?></title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Briefcase </li>
                    </ol>
                </div>
				<div class="pull-right">
                    <a class="btn btn-link" href="pouch.html">Pouch</a>
                    <a class="btn btn-link" href="addbriefcase.html">Add</a>
                </div>
         
            </div>
        </div>

        <div class="container">

            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="Added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Briefcase Added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Briefcase updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Deleted")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Briefcase deleted successfully.
                </div>
                <?php 
					}
                     else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
                  <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                         <thead>
                            <tr>
       							<th style="text-align: left">Title</th>
                                <th style="text-align: center">Date</th>
                                <th style="text-align: center">Action</th>
                            </tr>
                        </thead>
                        <tbody>
                             <?php
                            if($totalBriefcase>0)
                            {
                                while($row = mysqli_fetch_array($rowsBriefcase))
                                {
                                     $briefcaseId = $row['briefcaseId'];								 
                                     $fileTitle = $row['fileTitle'];								 
                                     $fileName = $row['fileName'];								 
                                     $createdDate = $row['createdDate'];	
									 $createdDate = converFromServerTimeZone($createdDate,$TimeZone);
									  $createdDate = date('m/d/Y H:i A',strtotime($createdDate));
									  
									 $documentPath = GetBriefcaseDocumentPath($schoolId,$fileName);
                                    ?>
                                <tr class="dataTableRowSelected">
                                    <td >
                                       <input type="text" name="fileTitle" value="<?php echo ($fileTitle);?>" style="width:100%;" class="updateAjaxRow" briefcaseId="<?php echo EncodeQueryData($briefcaseId); ?>" fileTitle="<?php echo ($fileTitle); ?>" >
                                    </td>
                                    <td style="text-align: center">
									<?php echo ($createdDate);?><br>
                                    </td>
                                    <td style="text-align: center">
                                        <a href="<?php echo $documentPath;?>" target="_blank">View</a>
                                       |<a  href="javascript:void(0);" class="deleteAjaxRow" briefcaseId="<?php echo EncodeQueryData($briefcaseId); ?>" filePath="<?php echo ($documentPath); ?>" > Delete</a>
                                    </td>
                                </tr>
                                <?php
                                }
                            }
                        ?>
                      </tbody>
                    </table>   
              </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
		
		<script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

		$(window).load(function(){
			$("#divTopLoading").addClass('hide');
		});

		var current_datatable = $("#datatable-responsive").DataTable({
			"aoColumns": [
				{
					"sWidth": "70%",
					"bSortable": false
				},
				{
					"sWidth": "15%"
				},
				{
					"sWidth": "15%",
					"bSortable": false
				}
			],
		
		});
		
		// ajax call for update Title      
		 $(document).on('change', '.updateAjaxRow', function() {
			var briefcaseId = $(this).attr('briefcaseId');
			var fileTitle = $(this).val();
		
				$.ajax({
					type: "GET",
					url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_briefcase.html",
					data: {
						id: briefcaseId,
						fileTitle: fileTitle,
					},
					success: function() {
						alertify.success('Updated');
					}
				});
		});


		// ajax call for deleteAjaxRow      
		 $(document).on('click', '.deleteAjaxRow', function() {

			var current_datatable_row = current_datatable.row($(this).parents('tr'));
			var briefcaseId = $(this).attr('briefcaseId');
			var filePath = $(this).attr('filePath');
            var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';
            var isUser = 1; //for Admin
		   
			alertify.confirm('Briefcase ', 'Continue with delete?', function() {
				$.ajax({
					type: "GET",
					url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
					data: {
						id: briefcaseId,
						type: 'Briefcase',
						filePath : 'filePath',
                        userId: userId,
                        isUser: isUser
					},
					success: function() {
						current_datatable.row(current_datatable_row).remove().draw(false);
						alertify.success('Deleted');
					}
				});
			}, function() {});

		});

     </script> 
    </body>
    </html>