<?php 
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php');   
    include('../class/clsClinicianRoleMaster.php'); 
    include('../setRequest.php'); 
    
    $schoolId = 0;
    $transchooldisplayName = '';

    if(isset($_GET['schoolId'])) //Edit Mode
	{
		$schoolId = $_GET['schoolId'];
        $schoolId = DecodeQueryData($schoolId);
    }
    else
    {
        $schoolId = $currentSchoolId;       
        $transchooldisplayName=$currenschoolDisplayname;
    }

    $title ="Evaluator Roles| ".$transchooldisplayName;

    //For Clinician Role List
    $totalCompanyRoles = 0;
	$objClinicianRoleMaster = new clsClinicianRoleMaster();
	$rowsCompanyRoles = $objClinicianRoleMaster->GetAllClinicianRolesBySchool($schoolId);
	if($rowsCompanyRoles !='')
	{
		$totalCompanyRoles =mysqli_num_rows($rowsCompanyRoles);
	}
?>
    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title><?php echo($title); ?></title>
        <?php include('includes/headercss.php');?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
        <?php include("includes/datatablecss.php") ?>

		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Evaluator Roles</li>
                    </ol>
                </div>
         
               <div class="pull-right">
                     <a class="btn btn-link" href="addclinicianroles.html">Add</a>
               </div>
         

            </div>
        </div>

        <div class="container">

            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="Added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Evaluator Role added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Evaluator Role updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Deleted")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Evaluator Role deleted successfully.
                </div>
                <?php 
					}
                     else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Evaluator Role </th>
                            <th style="text-align: center">Evaluator Type </th>
                            <th style="text-align: center">Total Users</th>
                            <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalCompanyRoles>0)
                        {
                            while($row = mysqli_fetch_array($rowsCompanyRoles))
                            {
								
								$clinicianRoleId = $row['clinicianRoleId'];                                			
                                $title = stripslashes($row['title']);                                
                                $type = stripslashes($row['type']);                                
                                $totalUserCount = 0;    
                                $totalUserCount = $objClinicianRoleMaster->GetClinicianCountByRole($clinicianRoleId,$schoolId);
?>
                            <tr>
                                <td>
                                    <?php echo($title); ?>
                                </td>
								<td style="text-align: center">
                                    <?php echo($type); ?>
                                </td>
                                 <td style="text-align: center">
                                    <?php echo($totalUserCount); ?>
                                </td>
                                
                                <td style="text-align: center">
   
                                    <a href="addclinicianroles.html?id=<?php echo(EncodeQueryData($clinicianRoleId)); ?>">Edit</a> 
                                   | <a href="javascript:void(0);" class="deleteAjaxRow"
															clinicianRoleId="<?php echo EncodeQueryData($clinicianRoleId); ?>" schoolclinicianFullName="<?php echo($title); ?>" >Delete</a>
												<?php   
                                            
                                    ?>                           
                                    
                                </td>
                            </tr>
                            <?php
                            }
                        }
                        unset($objClinicianRoleMaster);
                    ?>
                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
        <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            });

            var current_datatable = $("#datatable-responsive").DataTable({
                "aoColumns": [{
                    "sWidth": "20%"
                },{
                    "sWidth": "5%"
                },
				{
                    "sWidth": "5%",
                    "sClass": "alignCenter",
					"bSortable": false
                },{
                    "sWidth": "5%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }]
            });

            // ajax call for deleteAjaxRow
			
			  
             $(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var clinicianRoleId = $(this).attr('clinicianRoleId');
                var title = $(this).attr('schoolclinicianFullName');
                var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';
                var isUser = 1; //for Admin

                
                alertify.confirm('Evaluator Role: '+ title, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: clinicianRoleId,
                            userId: userId,
                            isUser: isUser,
                            type: 'clinicianrole'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });
        </script>
    </body>
    </html>