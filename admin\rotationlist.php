<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');       
    include('../class/clsRotation.php');       
    include('../class/clsStudent.php');       
    include('../setRequest.php'); 	
	
	$selrotationId=0;
	$rotationId=0;	
	$currentSchoolId;
	$studentId =0;
	if(isset($_GET['studentId']))
	{
		$currentstudentId=DecodeQueryData($_GET['studentId']);
	}
	
	if(isset($_GET['CE']))
	{
		$CE=DecodeQueryData($_GET['CE']);
	}
	if(isset($_GET['SIE']))
	{
		$SIE=DecodeQueryData($_GET['SIE']);
	}
	if(isset($_GET['EL']))
	{
		$EL=DecodeQueryData($_GET['EL']);
	}
	if(isset($_GET['FE']))
	{
		$FE=DecodeQueryData($_GET['FE']);
	}
	if(isset($_GET['SE']))
	{
		$SE=DecodeQueryData($_GET['SE']);
	}
	if(isset($_GET['ME']))
	{
		$ME=DecodeQueryData($_GET['ME']);
	}
	$objRotation = new clsRotation();			
	$rowsrotation = $objRotation->GetRotationByStudent($currentSchoolId,$currentstudentId);
	$totalrotation = 0;
	if($rowsrotation !='')
	{
		$totalrotation = mysqli_num_rows($rowsrotation);
	}	
    unset($objRotation);
	$objStudent=new clsStudent();
	$student=$objStudent->GetSingleStudent($currentSchoolId,$currentstudentId);
	$studentname= $student ? ($student['firstName']. ' ' .$student['lastName']) : '';
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Rotation List</title>  
     </head>
 <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
    <body>
       

        <div class="container">
			<div class="" tabindex="-1" role="dialog">
				<div class="modal-dialog" role="document">
					<div class="modal-content">
						<div class="modal-header">
						<button type="button" class="close" data-dismiss="modal" aria-label="Close"><span aria-hidden="true">&times;</span></button>
								<h4 class="modal-title">Student: <?php echo ($studentname); ?></h4>
						</div>
						<div class="modal-body">
							<div class="modal-body">
                
									<table id="rotationlist_table" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
										<thead>
											<tr>										                             
												<th>Rotation</th>												
												<th>Action</th>
												
											</tr>
										</thead>
										<tbody>
											<?php
											if($totalrotation > 0)
											{
												while($row = mysqli_fetch_array($rowsrotation))
												{								
													$rotationId = ($row['rotationId']);                                
													$title = ($row['title']);                          
													
													
												   ?>
												<tr>																				
													<td><?php echo ($title); ?></td>
													<?php if(isset($_GET['FE'])){ ?>
													<td><a href="formative.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Add</a></td>							
													<?php }  else if(isset($_GET['SE'])){ ?>
													<td><a href="summative.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Add</a></td>							
													<?php } else if(isset($_GET['SIE'])){ ?>
													<td><a href="siteevaluation.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Add</a></td>							
													<?php } else if(isset($_GET['EL'])){ ?>
													<td><a href="equipment.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Add</a></td>							
													<?php } else if(isset($_GET['CE'])){ ?>
													<td><a href="cievaluationlist.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Add</a></td>							
													<?php } else if(isset($_GET['ME'])){ ?>
													<td><a href="midtermlist.html?midtermrotationid=<?php echo EncodeQueryData($rotationId); ?>&studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Add</a></td>							
													<?php } ?>
												</tr>
												<?php
												}
											}
										?>
										</tbody>
									</table>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div> 
	
         <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
		
    </body>
    </html>