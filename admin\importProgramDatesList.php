<?php

    include('includes/validateUserLogin.php'); 
    include('../includes/config.php');
	include('../includes/commonfun.php');
    include('../class/clsDB.php');
	include('../setRequest.php');  
    include('../class/clsStudent.php');
    include('../class/clsStudentRankMaster.php');
    include('../class/clsLocations.php');
	include('../class/PasswordHash.php'); 
	include('../class/clsAccreditation.php');
	
	@session_start();
    $systemUserId= $_SESSION['loggedUserId'];
	

	if($_SERVER['REQUEST_METHOD'] == "POST")
	{	
		ini_set('upload_max_filesize', '50M');
		ini_set('post_max_size', '50M');
		ini_set('max_input_time', 300000);
		ini_set('max_execution_time', 300000);

		$notifyMessage = 'Imported';
		$row = 1;
		$reaccreditationId=0;
		$isExistAccreditationId= 0;
		if(isset($_FILES['file']))
		{
			$filename=$_FILES["file"]["tmp_name"];
			$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));
			
			if($ext !="csv")
			{
				$error = "Upload only .csv file.";
				header('location:studentIdsList.html?status=Importerror');
				exit();
			}
			
			if($_FILES["file"]["size"] > 0)
			{
				$file = fopen($filename, "r");
				$counter = 0;

				$retStudentId = 0;
				while (($getData = fgetcsv($file, 10000, ",")) !== FALSE)
				{	//print_r($getData);
					if($row == 1){ $row++; continue; }
					$ProgramEnrollmentDate  = str_replace(" ", "", $getData[0]);
					$OntimeGraduationDate = str_replace(" ", "", $getData[1]);
					$ActualGraduationDate = str_replace(" ", "", $getData[2]);
					$TMCDate = str_replace(" ", "", $getData[3]);
					$CSEDate = str_replace(" ", "", $getData[4]);
					$ProgramDropDate = str_replace(" ", "", $getData[5]);
					$ProgramEnrollmentDate = $ProgramEnrollmentDate != '' ? date('Y-m-d',strtotime($ProgramEnrollmentDate)) : 'NULL'; 
					$OntimeGraduationDate = $OntimeGraduationDate != '' ? date('Y-m-d',strtotime($OntimeGraduationDate)) : 'NULL'; 
					$ActualGraduationDate = $ActualGraduationDate != '' ? date('Y-m-d',strtotime($ActualGraduationDate)) : 'NULL'; 
					$TMCDate = $TMCDate != '' ? date('Y-m-d',strtotime($TMCDate)) : 'NULL'; 
					$CSEDate = $CSEDate != '' ? date('Y-m-d',strtotime($CSEDate)) : 'NULL'; 
					$ProgramDropDate = $ProgramDropDate != '' ? date('Y-m-d',strtotime($ProgramDropDate)) : 'NULL'; 
					$Email = str_replace(" ", "", $getData[6]);
					$SchoolCode = str_replace(" ", "", $getData[7]);
					
					// $RecordIdNumber = trim($RecordIdNumber);
					// if(!is_numeric(".$RecordIdNumber.")) //Check Number is Numberic Or Not
					// 	echo "hi";exit;
						// echo "ProgramDropDate ".$ProgramDropDate;
					$Email = trim($Email);
					$SchoolCode = trim($SchoolCode);
					{	

					$objDB = new clsDB();

					//Check School is Exist Or not 
					$isExistSchoold = $objDB->GetSingleColumnValueFromTable('schools', 'schoolId','code',$SchoolCode);
					if(!$isExistSchoold)
						continue;

					//Check Record is Exist Or Not
					$isExistStudentId = $objDB->GetSingleColumnValueFromTable('student', 'studentId','email',$Email,'schoolId',$isExistSchoold);
					if(!$isExistStudentId)
						continue;
											
					if($isExistSchoold)
						//Check accreditation is Exist Or Not
						$isExistAccreditationId = $objDB->GetSingleColumnValueFromTable('accreditation', 'accreditationId','studentId',$isExistStudentId);
						$objAccreditation = new clsAccreditation();
						$reaccreditationId=$objAccreditation->SaveAccreditationImportDates($isExistStudentId,$ProgramEnrollmentDate,$OntimeGraduationDate,$ActualGraduationDate,$systemUserId,$isExistAccreditationId,$TMCDate,$CSEDate,$ProgramDropDate);

					}
				}	
				// exit;
				fclose($file);

				$messageText = $reaccreditationId ? 'Imported' : 'Error';

				if($messageText == 'Imported')
				{
					//Audit Log Start
					// Instantiate the Logger class
					$objLog = new clsLogger();
					
					// Determine the action type (EDIT or ADD) based on the presence of a journal ID
					$action =  $objLog::ADD;
					$userType = $objLog::ADMIN; // User type is set to ADMIN
					$type ="programdatesImport";
					$IsMobile = 0;
					
					$objAccreditation = new clsAccreditation();
					$objAccreditation->saveStudentAccreditationAuditLog($isExistAccreditationId, $_SESSION['loggedUserId'], $userType, $action, $IsMobile,$type);
					
					unset($objLog);
					//Audit Log End
				}

				header('location:programDatesList.html?status='.$messageText);
				exit();
			}
		}	 
	}	
?>