<?php
require '../vendor/autoload.php'; // PhpSpreadsheet autoload

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

// Fetch data
$objStudents = new clsStudent();    
$individual_student = unserialize($individual_student);

$rowsSchoolStudents = $objStudents->GetAllSchoolStudentsForReport($currentSchoolId, $rotationId, $individual_student, $student_rank, $school_location, $hospital_site, $AscDesc, $startDate, $endDate, $AscDesc, $sordorder);

// Create a new Spreadsheet
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();
$sheet->setTitle('Student Reports');

// Set document properties
$spreadsheet->getProperties()
    ->setCreator($schoolname)
    ->setLastModifiedBy('JCC')
    ->setTitle('Reports')
    ->setSubject('School Report')
    ->setDescription('All School Reports');

// Print heading
$headerStyleArray = [
    'font' => ['bold' => true, 'size' => 16],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'E0E0E0']
	],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]

];

$sheet->mergeCells("B2:J2");
$sheet->setCellValue('B2', $schoolname);
$sheet->getStyle('B2:J2')->applyFromArray($headerStyleArray);

// Report title
$sheet->mergeCells("B4:J4");
$sheet->setCellValue('B4', 'Student Report');
$sheet->getStyle('B4:J4')->applyFromArray($headerStyleArray);

// Table headings
$tableHeaderStyle = [
    'font' => ['bold' => true, 'size' => 10],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'E0E0E0']
	],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]

];

$headers = ['First Name', 'Last Name', 'Rank', 'Rotation', 'Attendance', 'Procedures', 'Journals', 'Interactions', 'Equipments'];
$columns = range('B', 'J');
foreach ($headers as $key => $header) {
    $sheet->setCellValue("{$columns[$key]}6", $header);
    $sheet->getStyle("{$columns[$key]}6")->applyFromArray($tableHeaderStyle);
}

// Add data rows
$dataRowStyle = [
    'font' => ['size' => 10],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]

];

$printStartRowCounter = 7;

if ($rowsSchoolStudents) {
    while ($row = mysqli_fetch_array($rowsSchoolStudents)) {
        $sheet->setCellValue("B{$printStartRowCounter}", stripslashes($row['firstName']))
              ->setCellValue("C{$printStartRowCounter}", stripslashes($row['lastName']))
              ->setCellValue("D{$printStartRowCounter}", stripslashes($row['rank']))
              ->setCellValue("E{$printStartRowCounter}", stripslashes($row['rotationname']))
              ->setCellValue("F{$printStartRowCounter}", stripslashes($row['orignalhours']))
              ->setCellValue("G{$printStartRowCounter}", stripslashes($row['ProcedureCountTotal']))
              ->setCellValue("H{$printStartRowCounter}", stripslashes($row['JournalCount']))
              ->setCellValue("I{$printStartRowCounter}", stripslashes($row['InteractionCount']))
              ->setCellValue("J{$printStartRowCounter}", stripslashes($row['EquipmentsCounts']));
        
        // Apply row style
        $sheet->getStyle("B{$printStartRowCounter}:J{$printStartRowCounter}")->applyFromArray($dataRowStyle);
        $printStartRowCounter++;
    }
}

// Apply borders
$borderStyle = [
	'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
];
$sheet->getStyle("B6:J{$printStartRowCounter}")->applyFromArray($borderStyle);

// Auto size columns
foreach (range('B', 'J') as $columnID) {
    $sheet->getColumnDimension($columnID)->setAutoSize(true);
}
$sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows

// Save spreadsheet
$reportname = 'StudentsReport_';

?>
