<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
	include('../class/clsState.php');
    include('../class/clsSchool.php');
    include('../class/clsClinicianCertificationLog.php');
    include('../setRequest.php'); 
	
	$AgingState='';
	$ChildAbuseState='';
	$BackgroundState='';
	
	 if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
          $cliniciancertificationlogId=0;

		
		$objCertificationLog = new clsClinicianCertificationLog();

		 if(isset($_GET['clinicianId'])) 
	   {
		 $clinicianId = $_GET['clinicianId'];
         $clinicianId = DecodeQueryData($clinicianId);
		 $objCertificationLog->DeleteClinicianCertificationLog($clinicianId);
	   }
	   
		$status = ($cliniciancertificationlogId > 0) ? 'updated' : 'Added';
		//print_r($_POST);
		
		$licenseNumber  = ($_POST['txtLicenseNumber']);
		$palsNumber  = ($_POST['txtpalsNumber']);
		$bclsStatus  = ($_POST['bclsStatus']);
		$palsStatus  = ($_POST['palsStatus']);

		$drugScreeningDate  = $_POST['drugScreeningDate'] ? date("Y-m-d",strtotime($_POST['drugScreeningDate'])) : 'NULL';
		$criminalBackgroundCheck  = $_POST['criminalBackgroundCheck'] ? date("Y-m-d",strtotime($_POST['criminalBackgroundCheck'])) : 'NULL';
		$healthCertificationDate  = $_POST['healthCertificationDate'] ? date("Y-m-d",strtotime($_POST['healthCertificationDate'])) : 'NULL';
		$aclsCertificationDate  = $_POST['aclsCertificationDate'] ? date("Y-m-d",strtotime($_POST['aclsCertificationDate'])) : 'NULL';
		$licenseExpiration  = $_POST['licenseExpiration'] ? date("Y-m-d",strtotime($_POST['licenseExpiration'])) : 'NULL';
		$aclsExpirationDate  = $_POST['aclsExpirationDate'] ? date("Y-m-d",strtotime($_POST['aclsExpirationDate'])) : 'NULL';
		$bclsCertificationDate  = $_POST['bclsCertificationDate'] ? date("Y-m-d",strtotime($_POST['bclsCertificationDate'])) : 'NULL';
		$bclsExpirationDate  = $_POST['bclsExpirationDate'] ? date("Y-m-d",strtotime($_POST['bclsExpirationDate'])) : 'NULL';
		$palsCertificationDate  = $_POST['palsCertificationDate'] ? date("Y-m-d",strtotime($_POST['palsCertificationDate'])) : 'NULL';
		$palsExpirationDate  = $_POST['palsExpirationDate'] ? date("Y-m-d",strtotime($_POST['palsExpirationDate'])) : 'NULL';
		$AARCExpirationDate  = $_POST['AARCExpirationDate'] ? date("Y-m-d",strtotime($_POST['AARCExpirationDate'])) : 'NULL';
		$CSRCExpirationDate  = $_POST['CSRCExpirationDate'] ? date("Y-m-d",strtotime($_POST['CSRCExpirationDate'])) : 'NULL';
		$NRPCerificationDate  = $_POST['NRPCerificationDate'] ? date("Y-m-d",strtotime($_POST['NRPCerificationDate'])) : 'NULL';
		$NRPExpirationdate  = $_POST['NRPExpirationdate'] ? date("Y-m-d",strtotime($_POST['NRPExpirationdate'])) : 'NULL';

		$aclsNumber  = ($_POST['txtaclsNumber']);
		$licenseCredentials  = ($_POST['txtlicenseCredentials']);
		$bclsNumber  = ($_POST['txtbclsNumber']);
		$aclsStatus  = ($_POST['aclsStatus']);
		$AARCMembershipNumber  = ($_POST['AARCMembershipNumber']);
		$txtCSRCMembershipNumber  = ($_POST['txtCSRCMembershipNumber']);
		$covid1Date  = $_POST['covid1Date'] ? date("Y-m-d",strtotime($_POST['covid1Date'])) : 'NULL';
		$covid2Date  = $_POST['covid2Date'] ? date("Y-m-d",strtotime($_POST['covid2Date'])) : 'NULL';
		$covidBoosterDate  = $_POST['covidBoosterDate'] ? date("Y-m-d",strtotime($_POST['covidBoosterDate'])) : 'NULL';
		$systemUserId= $_SESSION['loggedUserId'];

		$objCertificationLog->drugScreeningDate = $drugScreeningDate;
		$objCertificationLog->clinicianId = $clinicianId;
		$objCertificationLog->criminalBackgroundCheck = $criminalBackgroundCheck;
		$objCertificationLog->healthCertificationDate = $healthCertificationDate;
		$objCertificationLog->licenseNumber = $licenseNumber;
		$objCertificationLog->licenseExpiration = $licenseExpiration;
		$objCertificationLog->licenseCredentials = $licenseCredentials;
		$objCertificationLog->aclsCertificationDate = $aclsCertificationDate;
		$objCertificationLog->aclsNumber = $aclsNumber;
		$objCertificationLog->aclsExpirationDate = $aclsExpirationDate;
		$objCertificationLog->aclsStatus = $aclsStatus;
		$objCertificationLog->bclsCertificationDate = $bclsCertificationDate;
		$objCertificationLog->bclsNumber = $bclsNumber;
		$objCertificationLog->bclsExpirationDate = $bclsExpirationDate;
		$objCertificationLog->bclsStatus = $bclsStatus;
		$objCertificationLog->palsCertificationDate = $palsCertificationDate;
		$objCertificationLog->palsNumber = $palsNumber;
		$objCertificationLog->palsExpirationDate = $palsExpirationDate;
		$objCertificationLog->palsStatus = $palsStatus;
		$objCertificationLog->AARCMembershipNumber = $AARCMembershipNumber;
		$objCertificationLog->CSRCMembershipNumber = $txtCSRCMembershipNumber;
		$objCertificationLog->AARCExpirationDate = $AARCExpirationDate;
		$objCertificationLog->CSRCExpirationDate = $CSRCExpirationDate;
		$objCertificationLog->NRPCerificationDate = $NRPCerificationDate;
		$objCertificationLog->NRPExpirationdate = $NRPExpirationdate;
		$objCertificationLog->covid1Date = $covid1Date;
		$objCertificationLog->covid2Date = $covid2Date;
		$objCertificationLog->covidBoosterDate = $covidBoosterDate;
		$objCertificationLog->createdBy = $systemUserId;
		
		$cliniciancertificationlogId=$objCertificationLog->SaveClinicianCertificationLog($cliniciancertificationlogId);
		
		unset($objCertificationLog);
		   
		if($cliniciancertificationlogId > 0)
		{
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = ($cliniciancertificationlogId > 0) ? $objLog::EDIT : $objLog::ADD;
			$userType = $objLog::ADMIN; // User type is set to ADMIN
			$IsMobile = 0;

			$objCertificationLog = new clsClinicianCertificationLog();
			$objCertificationLog->saveClinicianCertificationAuditLog($cliniciancertificationlogId, $_SESSION['loggedUserId'], $userType, $action, $IsMobile);
			unset($objCertificationLog);

			unset($objLog);
			//Audit Log Ends

			
			header('location:schoolclinicians.html?status='.$status); 
		   
		}
		else
		{
			header('location:schoolclinicians.html?status=error');
			exit;
		}
		
	}
	
	{
		header('location:schoolclinicians.html');
		exit();
	}


?>