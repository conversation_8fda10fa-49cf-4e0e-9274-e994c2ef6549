<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php'); 
    include('../class/clscheckoff.php'); 
	include('../class/clsUsafMasterCheckoffQuestion.php');
	include('../class/clsMasterCheckoffQuestion.php'); 
	include('../setRequest.php'); 

     $currentSchoolId;
   $questionType='';
   if(isset($_GET['questionId']))
   {
		$questionId=$_GET['questionId'];
		$bedCrumTitle="Edit";	   

		$objQuestion = new clsUsafMasterCheckoffQuestion();
		
		$rowsquistionoption= $objQuestion->GetUsafQuestionOptionDetails($questionId);
		
		$rowsquistion= $objQuestion->GetUsafQuestionDetails($questionId);

		$marks = $rowsquistion['marks']; 
		$questionTitle = $rowsquistion['questionTitle']; 
		$questionId = $rowsquistion['questionId']; 
		$questionType = $rowsquistion['questionType']; 
		$sortOrder = $rowsquistion['sortOrder']; 
		$questionType = $rowsquistion['questionType']; 	

		$objType = new clsMasterCheckoffQuestion();
		$GetQuestionType = $objType->GetQuestionType();
		unset($objType);
		unset($objQuestion);
   } 
	$objQuestion = new clsMasterCheckoffQuestion();
    $GetQuestionType = $objQuestion->GetQuestionType();
    unset($objQuestion);
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Edit Steps</title>
    <?php include('includes/headercss.php');?>
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">


    

</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
                  <li><a href="settings.html">Setting</a></li>
                  <li><a href="usafmasterviewquestions.html">Steps</a></li>
                  <li><?php echo ($sortOrder); ?></li>
                  <li class="active"><?php echo($bedCrumTitle);?></li>
               </ol>
            </div>

        </div>
    </div>

    <div class="container">

         <form id="frmupdatequestion" name="frmupdatequestion" data-parsley-validate class="form-horizontal" method="POST" action="usafmasterquestionupdatesubmit.html?questionId=<?php echo ($questionId); ?>">

            <div class="row">
			
				<div class="col-md-6">
				
				<div class="form-group">
                        <label class="col-md-12 control-label" for="cboQuestionType">Question Type</label>
                        <div class="col-md-12">
                            <select id="cboQuestionType" name="cboQuestionType" class="form-control input-md required-input select2_single quetype" required>
                            <option value="" selected>Select</option>
								<?php
                                if($GetQuestionType!="")
                                {
                                    while($row = mysqli_fetch_assoc($GetQuestionType))
                                    {
                                         $selquestionTypeId  = $row['questionTypeId'];
                                         $selquestionType  = $row['questionType'];
                                         $name  = stripslashes($row['title']);

                                         ?>
										<option value="<?php echo($selquestionType); ?>" <?php if($questionType==$selquestionType){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
										<?php

                                    }
                                }
                            ?>
							</select>  
							 <input type="hidden" name="questionType" id="questionType" value="<?php echo ($questionType); ?>">
						</div>
                    </div>
				<div class="form-group" style="display: none;">
					<label class="col-md-12 control-label" for="Note">Read</label>
					<div class="col-md-12">
						<div class="clsyesno" style="display: none;">
								<label for="yesnoans">Answser of type Yes/No</label>
							</div>
							<div class="clslongans" style="display: none;">
								<label for="longans" >Answer is of text type</label>
							</div>
							<div class="clssingle" style="display: none;">
								<label for="singleans">Multiple choices with only one correct answer</label>
							</div>
							
					</div>
				</div>
				</div>
		
                <div class="col-md-6">
					<div class="form-group">
                        <label class="col-md-12 control-label" for="question">Question Title</label>
                        <div class="col-md-12">
                            <textarea id="question"  name="question" class="form-control input-md required-input" required><?php echo ($questionTitle); ?></textarea>

                        </div>
                    </div>
				</div>
			</div>
			<div class="row">
                <div class="col-md-6">
					<div class="form-group">
                        <label class="col-md-12 control-label" for="Marks">Marks</label>
                        <div class="col-md-12">
                            <input type="text" id="Marks"  name="Marks" value="<?php echo ($marks); ?>" class="form-control input-md">

                        </div>
                    </div>
				</div>
		
                <div class="col-md-6">
					<div class="form-group">
                        <label class="col-md-12 control-label" for="SortOrder">Sort Order</label>
                        <div class="col-md-12">
                            <input type="text" id="SortOrder"  name="SortOrder" class="form-control input-md required-input" value="<?php echo ($sortOrder); ?>" required>

                        </div>
                    </div>
				</div>
			</div>
			<div class="row">
							
			<div class="col-md-6">			
				<div class="form-group clsyesno">
						  <?php if($questionType == 1) { ?>
						<div class="yesnoboxdiv">
						   <!----div class="form-group"------------->
						 
                     <label class="col-md-12 control-label">Default Options:</label>
                     <?php 	 
                        while($row = mysqli_fetch_array($rowsquistionoption))
                        			{ 
                        		 $optionText = $row['optionText']; 
                        		 $optionValue = $row['optionValue']; 
                        		  
                        ?>
                     <label class="col-md-12 control-label"></label>
                     <div class="col-md-12 col-sm-4 col-xs-12 ">
                        <div class="form-group" >
                           <div class="col-md-8 col-sm-4 col-xs-12 yesnoboxdiv">
                              <input type="text" class="form-control input-md required-input" name="txtyesno[]" value="<?php echo ($optionText); ?>" >
                              <input type="radio" id="yesno" class="yesnochoice clsyesnoans" name="yesno[]" value="0" <?php if($optionValue == 1) { ?> checked <?php } ?> >
                              <input type="hidden"  id="yesnoanswers" class="hid_anser" name="yesnoanswers[]" value="<?php echo ($optionValue); ?>">
                              <label class="control-label" for="yesno"></label>
                           </div>
                        </div>
                     </div>
					
					<?php  
                     }
                     	 ?>
                 <!--- </div>---->
						</div>
						<?php } ?>
					
					</div>		
						
					<div class=" clslongans">
					<?php   if($questionType == 5){ ?>
						<div class="form-group">
							<label class="col-md-12 control-label" for="Answer">Default Answer </label>
							<div class="col-md-12">
							 <textarea id="longans" name="longans" class="form-control input-md" rows="4"></textarea>
							</div>
							
						</div>
						<?php } ?>
					</div>		
					
					<div class="form-group clssingle">
						<!----div class="form-group"----->
								<?php   if($questionType == 2){ ?>
							  <div class="col-md-12 control-label">Default Answer</div>
							  <div class="col-md-12">
							
									<div class="row">
										<div class="col-md-12 textboxDiv" >
										<?php 
										
									while($row = mysqli_fetch_array($rowsquistionoption))
															{ 
																 $optionText = $row['optionText']; 
																 $optionValue = $row['optionValue'];
																 $choiceAnswer = $row['isCorrectAnswer'];	
                          ?> 
											<div class="singlechoiceboxdiv">
												<input type="radio"  id="singlechoice" name="singlechoice[]"  class="choicebox singlechoice" value="0" <?php if($optionValue == 1) { ?> checked <?php } ?>>
												<input type="hidden" id="hid_anser" class="hid_anser"  name="answers[]" value="<?php echo ($optionValue); ?>">
												<input type="text" id="txtsinglechoice" value="<?php echo ($optionText); ?>" class="form-control input-md required-input choicebox" name="txtsinglechoice[]" placeholder="Add an answer" required><br>
												
												<input type="text" id="txtsinglechoicemarks" value="<?php echo ($choiceAnswer); ?>" class="form-control input-md required-input choicebox" name="txtsinglechoicemarks[]" placeholder="Add marks" required>
												<span id="Remove" style="color:red;" class="glyphicon glyphicon-trash Remove"></span>
											</div>
											<?php 
															}	
										
												 ?>
										</div>
										<div class="col-md-2">
											<button type="button" id="Add" class="btn btn-success">Add</button>
										</div>
										<div class="col-md-2">
										</div>
										
									</div>
							
							  </div>
							  <?php } ?>
						<!----</div>---->
						
					</div>
					
				</div>
				
				
			</div>
				<div class="form-group">
                        <!-- <label class="col-md-2 control-label"></label> -->
						<div class="col-md-12" style="display: flex; justify-content: center; margin: 10px 0;gap: 15px;"> 
								<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
								<a type="button" href="usafmasterviewquestions.html" class="btn btn-default">Cancel</a>
						</div>
				</div>
				
		</form>
            </div>
        


    </div>

    <?php include('includes/footer.php');?>
   
	
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>	
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript">
 
           $(".select2_single").select2();
           $('#select2-cboQuestionType-container').addClass('required-select2');

	   $(window).load(function(){

            /* $('#frmupdatequestion').parsley().on('field:validated', function() {
			    var ok = $('.parsley-error').length === 0;
            })
            .on('form:submit', function() {
                ShowProgressAnimation();									  
                return true; // Don't submit form for this demo
            });*/
		});
		$(function() {
		$('#cboQuestionType').change(function() {							
							var x = $('#cboQuestionType option:selected').val();							
							$('#questionType').val(x);							
						 });
						 
					});
					
		$( document ).ready(function() {
			$("select").change(function(){
				if($(this).val() == 1)
					$('.clsyesno').show();
				else
					$('.clsyesno').hide();
				document.getElementById("yesno").required = false;
			});
		});
		$( document ).ready(function() {
			$("select").change(function(){
				//alert($(this).val());
				if($(this).val() == 5)
				{
					//alert('1');
					$('.clslongans').show();
				}
				else
				{
					//alert('2');
					$('.clslongans').hide();
				document.getElementById("longans").required = false;
				}
			});
		});
		$( document ).ready(function() {
			$("select").change(function(){
				if($(this).val() == 2)
					$('.clssingle').show();
				else
					$('.clssingle').hide();
				document.getElementById("singlechoice").required = false;
				document.getElementById("txtsinglechoice").required = false;
				document.getElementById("txtsinglechoicemarks").required = false;
			});
		});
		
		$(document).ready(function() {	
			
            $("#Add").on("click", function() {  
			
                $(".textboxDiv").append("<div class='singlechoiceboxdiv'><br><input type='radio'  class='choicebox singlechoice' id='singlechoice' value='0' name='singlechoice[]' '><input type='text' class='form-control input-md required-input choicebox' id='txtsinglechoice' name='txtsinglechoice[]' placeholder='Add an answer' required /><input type='hidden'  id='hid_anser' class='hid_anser' name='answers[]'  value='0'/><br><input type='text' id='txtsinglechoicemarks'  class='form-control input-md required-input choicebox' name='txtsinglechoicemarks[]' placeholder='Add marks' required><span id='Remove' style='color:red;' class='glyphicon glyphicon-trash Remove'></span></div>");  
                 
            });  
           // $(".Remove").on("click", function() {  
            $(document).on("click", ".Remove", function() {  			
				$(this).closest('.singlechoiceboxdiv').remove();
            });  
        });  
	
	
	$(document).on("click", ".singlechoice", function(event){
			console.log($(event.target).hasClass('singlechoice'));
			if($(event.target).hasClass('singlechoice')){
				$('input[id=hid_anser]').val(0);
				//$(this).closest(".textboxDiv").find('input[class=hid_anser]').val($(this).is(":checked"));
			$(this).closest(".singlechoiceboxdiv").find('input[id=hid_anser]').val('1');
				
			}
		});
		
	
	$(document).on("click", ".clsyesnoans", function(event){
			console.log($(event.target).hasClass('clsyesnoans'));
			if($(event.target).hasClass('clsyesnoans')){
				$('input[id=yesnoanswers]').val(0);
			$(this).closest(".yesnoboxdiv").find('input[id=yesnoanswers]').val('1');
				
			}
		});
		
	
	$(document).on('click', '.choicebox', function(e) {	
		
			if(this.checked) {
				//var inputboxvalue = $(this).closest('.clssingle').find('input[name="singlechoice[]"]').val();
				var txtinputboxvalue = $(this).closest('.singlechoiceboxdiv').find('input[name="txtsinglechoice[]"]').val();
					
				if(txtinputboxvalue == '')
				 {					  
					alertify.alert('Warning', 'Please write answer to selected option!');
					 $('#btnsubmit').addClass('disabled');
				 }
				 else
				 {
					 $('#btnsubmit').removeClass('disabled');
				 }
			}
		});
		
		
		
		
		$(document).ready(function(){
			var questionType = $('#questionType').val();			
			if(questionType == 1)			
				$('.clsyesno').show();		
			else		
				$('.clsyesno').hide();			
			document.getElementById("yesno").required = false;
			
		});
		
		$(document).ready(function(){
			var questionType = $('#questionType').val();			
			if(questionType == 5)			
				$('.clslongans').show();		
			else		
				$('.clslongans').hide();			
			
			
		});
		
		$(document).ready(function(){
			var questionType = $('#questionType').val();				
			if(questionType == 2)	
				$('.clssingle').show();	
				else
				$('.clssingle').hide();
				document.getElementById("singlechoice").required = false;
				document.getElementById("txtsinglechoice").required = false;
				document.getElementById("txtsinglechoicemarks").required = false;
			});
			
		$('#cboQuestionType').prop('disabled', true);
		
		
    </script>

</body>

</html>