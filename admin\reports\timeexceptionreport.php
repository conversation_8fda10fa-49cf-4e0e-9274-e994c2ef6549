<?php
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

$objAttendance = new clsAttendance();
$individual_student = unserialize($individual_student);
$subcborotation = $subcborotation ? explode(' ', $subcborotation) : '';
$rowsAttendance = $objAttendance->GetTimeExceptionDetailsForreport($currentSchoolId, $rotationId, $individual_student, $student_rank, $evaluator, $school_location, $hospital_site, $startDate, $endDate, $AscDesc, $sordorder, $subcborotation);

$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set document properties
$spreadsheet->getProperties()
    ->setCreator('Jacson Community College')
    ->setLastModifiedBy('JCC')
    ->setTitle('Reports')
    ->setSubject('School Report')
    ->setDescription('All School Reports');

// Set sheet title
$sheet->setTitle('Time Exception Reports');

// Print Heading
$sheet->mergeCells("B2:K2");
$sheet->setCellValue('B2', $schoolname);
$sheet->getStyle('B2')->getFont()->setBold(true)->setSize(16);
$sheet->getStyle('B2')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
$sheet->getStyle('B2')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('E0E0E0');
$sheet->getStyle('B2:K2')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

$sheet->mergeCells("B4:K4");
$sheet->setCellValue('B4', 'Time Exception Report');
$sheet->getStyle('B4')->getFont()->setBold(true)->setSize(12);
$sheet->getStyle('B4')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
$sheet->getStyle('B4')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('E0E0E0');
$sheet->getStyle('B4:K4')->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

// Make Table Heading
$headers = ['First Name', 'Last Name', 'Rank', 'Rotation', 'Hospital Site', 'Evaluator', 'Date', 'Original', 'Adjusted', 'Reason'];
$col = 'B';
foreach ($headers as $header) {
    $sheet->setCellValue($col . '6', $header);
    $sheet->getStyle($col . '6')->getFont()->setBold(true)->setSize(10);
    $sheet->getStyle($col . '6')->getAlignment()->setHorizontal(Alignment::HORIZONTAL_CENTER);
    $col++;
}
$sheet->getStyle('B6:K6')->getFill()->setFillType(Fill::FILL_SOLID)->getStartColor()->setRGB('E0E0E0');

$printStartRowCounter = 7;
if ($rowsAttendance) {
    while ($row = mysqli_fetch_array($rowsAttendance)) {
        $sheet->setCellValue('B' . $printStartRowCounter, stripslashes($row['firstName']));
        $sheet->setCellValue('C' . $printStartRowCounter, stripslashes($row['lastName']));
        $sheet->setCellValue('D' . $printStartRowCounter, stripslashes($row['Rankname']));
        $sheet->setCellValue('E' . $printStartRowCounter, stripslashes($row['rotationname']));
        $sheet->setCellValue('F' . $printStartRowCounter, stripslashes($row['Hospitalname']));
        $sheet->setCellValue('G' . $printStartRowCounter, stripslashes($row['clinicianfname']) . ' ' . stripslashes($row['clinicianlname']));
        
        $createdDate = date('m/d/Y', strtotime($row['updatedDate']));
        $sheet->setCellValue('H' . $printStartRowCounter, ($createdDate == '0000-00-00 00:00:00') ? '' : $createdDate);
        $sheet->setCellValue('I' . $printStartRowCounter, stripslashes($row['orignalhours']));
        $sheet->setCellValue('J' . $printStartRowCounter, stripslashes($row['approvedhours']));
        $sheet->setCellValue('K' . $printStartRowCounter, stripslashes($row['comment']));
        
        $printStartRowCounter++;
    }
}

// Make Border
$printStartRowCounter--;
$sheet->getStyle("B6:K" . $printStartRowCounter)->getBorders()->getAllBorders()->setBorderStyle(Border::BORDER_THIN);

// Auto size columns
foreach (range('B', 'K') as $columnID) {
    $sheet->getColumnDimension($columnID)->setAutoSize(true);
}

$sheet->setSelectedCell('A1'); // Set focus to A1

$reportname = 'TimeExceptionReport_';

$writer = new Xlsx($spreadsheet);
$writer->save($reportname . '.xlsx');
?>