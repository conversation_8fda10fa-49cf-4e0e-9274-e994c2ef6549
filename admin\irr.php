<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsIrr.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../setRequest.php');

$display_from_date = date('m/d/Y', strtotime('-15 days'));
$display_to_date = date('m/d/Y');
$currentSchoolId;
$irrMasterId = 0;
$CompletedDate = null;
$CompletionDate = null;

$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

if (isset($_GET['irrMasterId'])) {
    $irrMasterId = DecodeQueryData($_GET['irrMasterId']);
}

//For Irr List
$objIrr = new clsIrr();
$objRotation = new clsRotation();
$rowsIrrData = $objIrr->GetAllIRRAssignmentsDetails($currentSchoolId);
$totalIrrCount = 0;
if ($rowsIrrData != '') {
    $totalIrrCount = mysqli_num_rows($rowsIrrData);
}


?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>IRR Assignments</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <style>
        .bootstrap-datetimepicker-widget {
            position: absolute;
            z-index: 999;
            margin: 11px 9px 0px -28px;
            background: beige;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Setting</a></li>
                    <li class="active">IRR Assignments</li>
                </ol>
            </div>
            <div class="pull-right">
                <ol class="breadcrumb">
                    <a href="addirr.html">Add</a>
                </ol>
            </div>
        </div>
    </div>

    <div class="container">


        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>IRR added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> IRR updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>

                    <th>IRR Title</th>
                    <th>PEF Checkoff</th>
                    <th>Clinicians</th>
                    <th>IRR Student</th>
                    <th>Start</th>
                    <th>End</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalIrrCount > 0) {
                    while ($row = mysqli_fetch_array($rowsIrrData)) {
                        $irrMasterId = ($row['irrMasterId']);
                        $Irrtitle = ($row['title']);
                        $irrStartDate = stripslashes($row['irrStartDate']);
                        $rotationId = isset(($row['rotationId'])) ? stripslashes($row['rotationId']) : 0;
                        $courselocationId = isset($row['locationId']) ? stripslashes($row['locationId']) : 0;
                        $parentRotationId = isset($row['parentRotationId']) ? stripslashes($row['parentRotationId']) : 0;
                        $rotationLocationId = isset($row['rotationLocationId']) ? stripslashes($row['rotationLocationId']) : 0;

                        $locationId = 0;
                        if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
                            if ($parentRotationId > 0) {
                                if (!$rotationLocationId)
                                    $locationId = $objRotation->GetLocationByRotation($rotationId);
                                else
                                    $locationId  = $rotationLocationId;
                            }
                        } else {
                            $locationId  = $courselocationId;
                        }

                        //Get Time Zone By Rotation 
                        $objLocation = new clsLocations();
                        $TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
                        unset($objLocation);
                        if ($TimeZone == '')
                            $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
                        $irrStartDate = converFromServerTimeZone($irrStartDate, $TimeZone);
                        $StartDate = strtotime($irrStartDate);

                        $irrEndDate = stripslashes($row['irrEndDate']);
                        $irrEndDate = converFromServerTimeZone($irrEndDate, $TimeZone);
                        $EndDate = strtotime($irrEndDate);

                        $CompletionDate = stripslashes($row['clinicianCompletionDate']);
                        $CompletedDate = converFromServerTimeZone($CompletionDate, $TimeZone);

                        $clinicianId = stripslashes($row['clinicianId']);
                        $CheckoffTitle = stripslashes($row['CheckoffTitle']);
                        $firstName = stripslashes($row['firstName']);

                        $lastName = stripslashes($row['lastName']);
                        $ClinicianName = $firstName . ' ' . $lastName;
                        $cliniciancountrow = $objIrr->GetClinicianCount($irrMasterId);

                        $cliniciancount = stripslashes($cliniciancountrow['clinicianId']);

                ?>
                        <tr>
                            <td><?php echo ($Irrtitle); ?>
                            </td>
                            <td title="<?php echo $CheckoffTitle;  ?>"><?php
                                                                        $shortTitlelen = strlen($CheckoffTitle);
                                                                        if ($shortTitlelen >  40) {

                                                                            $checkoffTitleCountName = substr($CheckoffTitle, 0, 40);
                                                                            $checkoffTitleCountName .= '...';
                                                                        } else {
                                                                            $checkoffTitleCountName = $CheckoffTitle;
                                                                        }
                                                                        echo ($checkoffTitleCountName); ?></td>
                            <td><a title="View To Clinicians" href="clinicianlist.html?irrMasterId=<?php echo (EncodeQueryData($irrMasterId)); ?>" class="cliniciandetails"><?php echo ($cliniciancount); ?></a></td>
                            <td> <?php echo ('IRR Student'); ?> </td>
                            <td><?php echo (date('m/d/Y', $StartDate)); ?></td>
                            <td>
                                <!-- <div class='input-group date' id='editDate'>
                                    
                                        <input type='text' name="editDate" id="editDate_<?php //echo $irrMasterId; 
                                                                                        ?>" irrMasterId="<?php echo $irrMasterId; ?>" onblur="editDate(<?php echo $irrMasterId; ?>);" value="<?php echo (date('m/d/Y h:m A', $EndDate)); ?>" class="form-control input-md required-input rotation_date"/>
                                         <span class="input-group-addon" irrMasterId="<?php //echo $irrMasterId; 
                                                                                        ?>">
                                            <span class="glyphicon glyphicon-calendar"></span>
                                        </span>
                                    </div> -->
                                <?php echo (date('m/d/Y', $EndDate)); ?>
                            </td>

                            <td style="text-align: center">
                                <?php if (isset($_SESSION["loggedAsBackUserId"])) { ?>
                                    <a href="addirr.html?irrMasterId=<?php echo EncodeQueryData($irrMasterId); ?>">Edit</a>
                                    |
                                <?php } ?>
                                <a href="javascript:void(0);" class="deleteAjaxRow" irrMasterId="<?php echo EncodeQueryData($irrMasterId); ?>" IrrName="<?php echo ($Irrtitle); ?>">Delete</a>
                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objIrr);
                unset($objRotation);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $('.cliniciandetails').magnificPopup({
            'type': 'ajax',
            // modal:true,
            closeOnBgClick: false,
            // closeBtnInside: true, 
            closeOnContentClick: false
        });

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();
            $("input[name='editDate']").attr("disabled", true);

            $(function() {
                $("#fromDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    defaultDate: moment().subtract(15, 'days'),
                    useCurrent: false
                });
            });

            $(function() {
                $("#toDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    defaultDate: moment(),
                    useCurrent: false
                });
            });


        });

        $('.input-group-addon').click(function() {
            // $("input[name='editDate']").attr("disabled",false);
            var irrmasterid = $(this).attr("irrmasterid");
            $("#editDate_" + irrmasterid).datetimepicker({
                format: "MM/DD/YYYY hh:mm A",
                inline: true,
                useCurrent: false

            });
        });

        function editDate(irrMasterId) {

            var editDate = $("input[name='editDate']").val();
            $.ajax({
                url: '<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_update_editdate.html',
                data: 'id=' + irrMasterId + '&date=' + editDate + '&changeDate=irr',
                type: "POST",
                success: function(json) {
                    $("input[name='editDate']").attr("disabled", true);
                    console.log("Update Date Successfully");
                    // location.reload();
                }
            });
        }

        var current_datatable = $("#datatable-responsive").DataTable({

            "aoColumns": [{
                "sWidth": "10%"
            }, {
                "sWidth": "26%"
            }, {
                "sWidth": "6%"
            }, {
                "sWidth": "8%"
            }, {
                "sWidth": "20%"
            }, {
                "sWidth": "20%"
            }, {
                "sWidth": "10%",
                "sClass": "alignCenter",
                "bSortable": false
            }]

        });

        //delete student
        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var irrMasterId = $(this).attr('irrMasterId');
            var IrrName = $(this).attr('IrrName');

            alertify.confirm('IRR Assignment Name: ' + IrrName, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: irrMasterId,
                        type: 'Irr_Assignment'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $(document).on('click', '.resendSms', function() {
            var irrdetailId = $(this).attr('irrdetailId');
            $.ajax({
                type: "POST",
                url: "../ajax/send_standard_checkoff_sms_to_preceptor.html",
                data: {
                    irrdetailId: irrdetailId

                },
                success: function(data) {
                    alertify.success('Sent');
                }
            });
        });

        //copy link
        $(document).on('click', '.copyLink', function() {

            event.preventDefault();

            var irrdetailId = $(this).attr('irrDetailId');
            var preceptorId = $(this).attr('preceptorId');
            var preceptorNum = $(this).attr('preceptorNum');
            var schoolTopicId = $(this).attr('schoolTopicId');
            var irrMasterId = $(this).attr('irrMasterId');
            var evaluationType = $(this).attr('evaluationType');



            // $.ajax({
            //     type: "POST",
            //     url: "../ajax/ajax_copy_link.html",
            //     data: {
            //         irrdetailId: irrdetailId,
            //         irrMasterId: irrMasterId,
            //         preceptorId: preceptorId,
            //         preceptorNum: preceptorNum,
            //         schoolTopicId: schoolTopicId,
            //         evaluationType: 'irr'

            //     },
            //     success: function(data) {
            //         console.log(data);
            //         var linkURL = data;

            //         if (linkURL != '') {
            //             navigator.clipboard.writeText(linkURL)
            //                 .then(function() {
            //                     alertify.success('URL copied to clipboard');
            //                 })

            //         }

            //     }
            // });

            $.ajax({
                type: "POST",
                url: "../ajax/ajax_copy_link.html",
                data: {
                    preceptorId: preceptorId,
                    preceptorNum: preceptorNum,
                    evaluationType: evaluationType,
                    schoolTopicId: schoolTopicId,
                    irrdetailId: irrdetailId,
                    irrMasterId: irrMasterId

                },
                success: function(data) {
                    var linkURL = data;
                    if (linkURL != '') {
                        // Display a prompt for manual copying
                        // var success = false;
                        $('#genrateLink').text('URL: ' + linkURL);
                        $('#genrateLink').show();
                        try {
                            navigator.clipboard.writeText(linkURL)
                                .then(function() {
                                    success = true;
                                    // alertify.success('URL copied to clipboard');

                                })
                            var textArea = document.createElement('textarea');
                            textArea.value = linkURL;
                            document.body.appendChild(textArea);
                            textArea.select();
                            success = document.execCommand('copy');
                            document.body.removeChild(textArea);
                        } catch (err) {
                            console.error('Error copying to clipboard manually: ', err);
                        }

                        if (success) {
                            alertify.success('URL copied to clipboard');
                        }
                       
                    }
                }
            });
        });

        
    </script>
</body>

</html>