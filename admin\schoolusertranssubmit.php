<?php

	include('includes/validateUserLogin.php');
    include('../includes/config.php');
    include('../class/clsDB.php');
    include('../class/clsSystemUser.php'); 
	include('../class/clsSchool.php'); 
	include('../includes/commonfun.php');
	include('../setRequest.php');

	if( isset($_GET['id'])) //Edit Mode
	{
		if(isset($_GET['schoolId'])) //Edit Mode
		{
			$schoolId = $_GET['schoolId'];
			$schoolId = DecodeQueryData($schoolId);
		}
		else
		{
			$schoolId=$currentSchoolId;
		}

		$userId = $_GET['id'];
		$userId = DecodeQueryData($userId);
		$type = $_GET['type'];
		if($type == 'status')
		{
			//Create object
			$objSystemUser = new clsSystemUser();
			$newStatus = $_GET['newStatus'];
			$objSystemUser->SetSystemUserStatus($userId,$newStatus,$schoolId);

			$loggeduserId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
			$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

			// $isUser 1 for Admin and 2 for Clinician and 3 for Student
			// Instantiate the Logger class
			$objLog = new clsLogger();
			$logAction = ($newStatus == 0) ? $objLog::INACTIVE : $objLog::ACTIVE;
			if ($isUser == 1) {
				$userType = $objLog::ADMIN;
			} else if ($isUser == 3) {
				$userType = $objLog::STUDENT;
			} else if ($isUser == 2) {
				$userType = $objLog::EVALUATOR;
			}

			$objSystemUser->saveSchoolAdminAuditLog($userId, $loggeduserId, $userType, $logAction);



			unset($objSystemUser);
			header('location:schoolusers.html?status=StatusUpdated');
		}
		else
		{
				$objSystemUser = new clsSystemUser();
				$newblockStatus = $_GET['newblockStatus'];
				$objSystemUser->SetSystemUserBlockUnblock($userId,$newblockStatus,$schoolId);

				$loggeduserId = isset($_GET['userId']) ? ($_GET['userId']) : 0;
				$isUser = isset($_GET['isUser']) ? ($_GET['isUser']) : 0;

				// $isUser 1 for Admin and 2 for Clinician and 3 for Student
				// Instantiate the Logger class
				$objLog = new clsLogger();
				$logAction = ($newblockStatus == 0) ? $objLog::UNLOCK : $objLog::LOCK;
				if ($isUser == 1) {
					$userType = $objLog::ADMIN;
				} else if ($isUser == 3) {
					$userType = $objLog::STUDENT;
				} else if ($isUser == 2) {
					$userType = $objLog::EVALUATOR;
				}

				$objSystemUser->saveSchoolAdminAuditLog($userId, $loggeduserId, $userType, $logAction);



				unset($objSystemUser);
				header('location:schoolusers.html?status=blockStatusUpdated');
		}

		
	}
	else
	{
		exit();
	}
?>