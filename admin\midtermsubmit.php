<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
	include('../setRequest.php');
    include('../class/clsMidterm.php');
    include('../class/clsQuestionOption.php');
	
	 $studentMidtermMasterId=0;	 
	 if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
			$rotationId=0;
			$rotation=0;
			$midtermrotationid=0;
			$schoolIncidentQuestionId='';
			$schoolIncidentQuestionType='';
			
			if(isset($_GET['studentMidtermMasterId'])) 
			{
				$studentMidtermMasterId = $_GET['studentMidtermMasterId'];
				$studentMidtermMasterId = DecodeQueryData($studentMidtermMasterId);
			}
			if(isset($_GET['midtermrotationid'])) 
			{
					$midtermrotationid = $_GET['midtermrotationid'];
					$midtermrotationid = DecodeQueryData($midtermrotationid);			
			}
		
			$studentMidtermMasterId = isset($_GET['studentMidtermMasterId']) ? DecodeQueryData($_GET['studentMidtermMasterId']) : 0;
			$status = ($studentMidtermMasterId > 0) ? 'updated' : 'added';
			if(isset($_GET['studentId'])) 
				{
					$student = $_GET['studentId'];
					$student = DecodeQueryData($student);
				
				}
				else
				{ 
					$student=isset($_POST['cbostudent']) ? $_POST['cbostudent'] : 0;
					
				}
				
			$cboclinician  = isset($_POST['cboclinician']) ? $_POST['cboclinician'] : 0;	
			$evaluationDate=GetDateStringInServerFormat($_POST['evaluationDate']);
			$evaluationDate = str_replace('00:00:00','12:00 PM',$evaluationDate);
			$evaluationDate = date('Y-m-d H:i',strtotime($evaluationDate));
			//$InstructorDate=GetDateStringInServerFormat($_POST['InstructorDate']);			
			$rotation  =isset($_POST['cborotation']) ? $_POST['cborotation'] : 0;
			$Absence=isset($_POST['Absence']) ? $_POST['Absence'] : 0;
			$DaysTardy=isset($_POST['DaysTardy']) ? $_POST['DaysTardy'] : 0;
			$MidtermEval=isset($_POST['MidtermEval']) ? $_POST['MidtermEval'] : 0;
			
			$objMidterm = new clsMidterm();
			$objMidterm->rotationId =$midtermrotationid ? $midtermrotationid : $rotation;
			$objMidterm->clinicianId =$cboclinician;				
			$objMidterm->schoolId =$currentSchoolId;
			$objMidterm->studentId =$student;
			$objMidterm->OverAllRating =$MidtermEval;
			$objMidterm->absence =$Absence;
			$objMidterm->daysTardy =$DaysTardy;
			$objMidterm->evaluationDate = $evaluationDate;						
			$objMidterm->updatedBy =$_SESSION["loggedUserId"];		
			$retMidtermId = $objMidterm->SaveAdminMidterm($studentMidtermMasterId);	
			
			$objMidterm->DeleteStudentMidtermDetails($retMidtermId);
			
				foreach($_POST as $id=>$value)
			{
			
				if (strpos($id, 'questionoptions_') === 0) 
				{			
				$id = explode("_", $id)[1];
				$objMidterm->studentMidtermMasterId = $retMidtermId;
				$objMidterm->studentQuestionId = $id;		 		
				$objMidterm->studentoptionvalue = $value[0];
				$objMidterm->studentOptionAnswerText ='';	
				$studentMidtermDetailId=$objMidterm->SaveAdminMidtermDetail($retMidtermId);
				}
			}
			foreach($_POST as $id=>$value)
			{
			
				if (strpos($id, 'questionoptionst_') === 0) 
				{			
				$id = explode("_", $id)[1];
				
				$objMidterm->studentMidtermMasterId = $retMidtermId;
				$objMidterm->studentQuestionId = $id;		 		
				$objMidterm->studentoptionvalue ='';
				$objMidterm->studentOptionAnswerText =$value[0];	
				$studentMidtermDetailId=$objMidterm->SaveAdminMidtermDetail($retMidtermId);
				}
			}
			
			unset($objMidterm);
			
			if($retMidtermId > 0)
			{
				if(isset($_GET['studentId'])) 
				{
					header('location:midtermlist.html?studentId='.EncodeQueryData($student).'&studentMidtermMasterId='.EncodeQueryData($studentMidtermMasterId).'&midtermrotationid='.EncodeQueryData($midtermrotationid).'&status='.$status);
					exit();
				}
				else
				{
					header('location:midtermlist.html?studentMidtermMasterId='.EncodeQueryData($studentMidtermMasterId).'&midtermrotationid='.EncodeQueryData($midtermrotationid).'&status='.$status);	
					exit();
				}
			}
			else
			{
				header('location:midterm.html?status=error');
			}
		 
	}
	{
		header('location:midtermlist.html');
		exit();
	}	
?>