<?php
	include('includes/validateUserLogin.php');
    include('../includes/config.php');
    include('../class/clsDB.php');
    include('../includes/commonfun.php');
    include('../class/clsStudentCertificationLog.php');	
    include('../class/clsCountryStateMaster.php');	
    include('../class/clsStudent.php'); 
    include('../setRequest.php');
	require '../vendor/autoload.php';  // Include PhpSpreadsheet's autoloader

	use PhpOffice\PhpSpreadsheet\Spreadsheet;
	use PhpOffice\PhpSpreadsheet\IOFactory;
		
		
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnStudentExport']))		
	
	$accreditationId=0; 
	$totalStudentCertificationLogCount=0; 
	$currentSchoolId;
	
	// Object
	$objStudentCertificationLog = new clsStudentCertificationLog();

	$allStudentCertificationLog = $objStudentCertificationLog->GetAllStudentCertificationLog($currentSchoolId);
	if($allStudentCertificationLog !='')
	{
		$totalStudentCertificationLogCount = mysqli_num_rows($allStudentCertificationLog);
	}
	$title='Student Certification Log Information List';			
	date_default_timezone_set('Asia/Kolkata');
	$today= (date('m/d/Y, H:i A'));
		
	$spreadsheet = new Spreadsheet();
	// Set document properties
	$spreadsheet->getProperties()->setCreator('Schools')
								 ->setLastModifiedBy('JCC')
								 ->setTitle('Reports')
								 ->setSubject('Student Certification Log Information List')
								 ->setDescription('All School Reports');
								 
	//Active Sheet
	$spreadsheet->setActiveSheetIndex(0);
	// $spreadsheet->getActiveSheet()->setTitle();				
	
	$spreadsheet->getActiveSheet()->mergeCells("B1:AH1");
	
	//Print Heading	
	$headerstyleArray = array('font'  => array('bold'  => true,'size'  => 16));
	
	$spreadsheet->getActiveSheet()->mergeCells("B2:AH2");
	$spreadsheet->getActiveSheet()->setCellValue('B2', $title);
	$spreadsheet->getActiveSheet()->getStyle('B2')->applyFromArray($headerstyleArray);
	$spreadsheet->getActiveSheet()->getStyle('B2')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()
				 ->getStyle('B2')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
		
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B2:AE2')->applyFromArray($styleBorderArray);
	
	$styleBorderArray = ['borders' => ['allBorders' => ['borderStyle' => 'thin']]];
	$spreadsheet->getActiveSheet()->getStyle('B4:AH4')->applyFromArray($styleBorderArray);
	$spreadsheet->getActiveSheet()
				 ->getStyle('B4:AH4')
				 ->getFill()
				 ->setFillType(\PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID)
				 ->getStartColor()
				 ->setRGB('E0E0E0');
				 
	//Make Table Heading
	$styleArray = array('font'  => array('bold'  => true,'size'  => 10));
	
	$spreadsheet->getActiveSheet()->setCellValue('B4', 'Student Name');
	$spreadsheet->getActiveSheet()->getStyle('B4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('B4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getDefaultColumnDimension('B4')->setWidth('30');
	
	$spreadsheet->getActiveSheet()->setCellValue('C4', 'Drug Screening Date');
	$spreadsheet->getActiveSheet()->getStyle('C4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('C4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getDefaultColumnDimension('C4')->setWidth('30');
	
	$spreadsheet->getActiveSheet()->setCellValue('D4', 'Criminal Background Check Date');
	$spreadsheet->getActiveSheet()->getStyle('D4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('D4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('E4', 'Health Certification Date');
	$spreadsheet->getActiveSheet()->getStyle('E4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('E4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('F4', 'ACLS Certification Date');
	$spreadsheet->getActiveSheet()->getStyle('F4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('F4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('G4', 'FBI Fingerprinting');
	$spreadsheet->getActiveSheet()->getStyle('G4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('G4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('H4', 'State Background Check');
	$spreadsheet->getActiveSheet()->getStyle('H4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('H4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('I4', 'State Background Check Date');
	$spreadsheet->getActiveSheet()->getStyle('I4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('I4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('J4', 'Child Abuse State');
	$spreadsheet->getActiveSheet()->getStyle('J4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('J4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('K4', 'Child Abuse Date');
	$spreadsheet->getActiveSheet()->getStyle('K4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('K4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('L4', 'License Expiration');
	$spreadsheet->getActiveSheet()->getStyle('L4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('L4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('M4', 'License Number');
	$spreadsheet->getActiveSheet()->getStyle('M4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('M4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('N4', 'PALS Number');
	$spreadsheet->getActiveSheet()->getStyle('N4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('N4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('O4', 'BCLS Status');
	$spreadsheet->getActiveSheet()->getStyle('O4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('O4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('P4', 'Insurance Carrier');
	$spreadsheet->getActiveSheet()->getStyle('P4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('P4')->getAlignment()->applyFromArray(array('horizontal' => 'center'));

	$spreadsheet->getActiveSheet()->setCellValue('Q4', 'ACLS Expiration Date');
	$spreadsheet->getActiveSheet()->getStyle('Q4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('Q4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	$spreadsheet->getActiveSheet()->getDefaultColumnDimension('Q4')->setWidth('30');
	
	$spreadsheet->getActiveSheet()->setCellValue('R4', 'BCLS Certification Date');
	$spreadsheet->getActiveSheet()->getStyle('R4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('R4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('S4', 'BCLS Expiration Date');
	$spreadsheet->getActiveSheet()->getStyle('S4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('S4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('T4', 'COVID 19 I Date');
	$spreadsheet->getActiveSheet()->getStyle('T4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('T4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('U4', 'COVID 19 II Date');
	$spreadsheet->getActiveSheet()->getStyle('U4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('U4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('V4', 'COVID Booster Date');
	$spreadsheet->getActiveSheet()->getStyle('V4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('V4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('W4', 'PALS Certification Date');
	$spreadsheet->getActiveSheet()->getStyle('W4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('W4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('X4', 'PALS Expiration Date');
	$spreadsheet->getActiveSheet()->getStyle('X4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('X4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('Y4', 'Department of Aging State');
	$spreadsheet->getActiveSheet()->getStyle('Y4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('Y4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('Z4', 'Department of Aging Date');
	$spreadsheet->getActiveSheet()->getStyle('Z4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('Z4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AA4', 'NRP Certification Date');
	$spreadsheet->getActiveSheet()->getStyle('AA4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AA4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AB4', 'NRP Expiration Date');
	$spreadsheet->getActiveSheet()->getStyle('AB4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AB4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AC4', 'ACLS Number');
	$spreadsheet->getActiveSheet()->getStyle('AC4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AC4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AD4', 'License Credentials');
	$spreadsheet->getActiveSheet()->getStyle('AD4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AD4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->setCellValue('AE4', 'BCLS Number');
	$spreadsheet->getActiveSheet()->getStyle('AE4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AE4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('AF4', 'Insurance Number');
	$spreadsheet->getActiveSheet()->getStyle('AF4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AF4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('AG4', 'ACLS Status');
	$spreadsheet->getActiveSheet()->getStyle('AG4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AG4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));

	$spreadsheet->getActiveSheet()->setCellValue('AH4', 'PALS Status');
	$spreadsheet->getActiveSheet()->getStyle('AH4')->applyFromArray($styleArray);
	$spreadsheet->getActiveSheet()->getStyle('AH4')->getAlignment()->applyFromArray(array('horizontal' => 'left'));
	
	$spreadsheet->getActiveSheet()->mergeCells("B3:AH3");
	
	$printStartRowCounter = 5;
	$objCountryStateMaster = new clsCountryStateMaster();
	if($totalStudentCertificationLogCount!="")
    {
		while($row = mysqli_fetch_assoc($allStudentCertificationLog))
		{
			$studentId=stripslashes($row['studentId']);
			$objStudent = new clsStudent();
			$rowStudent = $objStudent->GetStudentDetails($studentId);
			unset($objStudent);
			$firstName=$rowStudent['firstName'];
			$lastName=$rowStudent['lastName'];
			$fullName=$firstName.' '.$lastName;

			$drugScreeningDate = '';
		if($row['drugScreeningDate'] != '0000-00-00' && isset($row['drugScreeningDate']))
			$drugScreeningDate = date('m/d/Y', strtotime($row['drugScreeningDate']));

		$criminalBackgroundCheck = '';
		if($row['criminalBackgroundCheck'] != '0000-00-00' && isset($row['criminalBackgroundCheck']))
			$criminalBackgroundCheck = date('m/d/Y', strtotime($row['criminalBackgroundCheck']));

		$healthCertificationDate = '';
		if($row['healthCertificationDate'] != '0000-00-00' && isset($row['healthCertificationDate']))
			$healthCertificationDate = date('m/d/Y', strtotime($row['healthCertificationDate']));
		
		
		$licenseExpiration = '';
		if($row['licenseExpiration'] != '0000-00-00' && isset($row['licenseExpiration']))
			$licenseExpiration = date('m/d/Y', strtotime($row['licenseExpiration']));
		
		
		$aclsCertificationDate = '';
		if($row['aclsCertificationDate'] != '0000-00-00' && isset($row['aclsCertificationDate']))
			$aclsCertificationDate = date('m/d/Y', strtotime($row['aclsCertificationDate']));

		$bclsCertificationDate = '';
		if($row['bclsCertificationDate'] != '0000-00-00' && isset($row['bclsCertificationDate']))
			$bclsCertificationDate = date('m/d/Y', strtotime($row['bclsCertificationDate']));

		$bclsExpirationDate = '';
		if($row['bclsExpirationDate'] != '0000-00-00' && isset($row['bclsExpirationDate']))
			$bclsExpirationDate = date('m/d/Y', strtotime($row['bclsExpirationDate']));

		$palsCertificationDate = '';
		if($row['palsCertificationDate'] != '0000-00-00' && isset($row['palsCertificationDate']))
			$palsCertificationDate = date('m/d/Y', strtotime($row['palsCertificationDate']));

		$NRPCerificationDate = '';
		if($row['NRPCerificationDate'] != '0000-00-00' && isset($row['NRPCerificationDate']))
			$NRPCerificationDate = date('m/d/Y', strtotime($row['NRPCerificationDate']));

		$NRPExpirationdate = '';
		if($row['NRPExpirationdate'] != '0000-00-00' && isset($row['NRPExpirationdate']))
			$NRPExpirationdate = date('m/d/Y', strtotime($row['NRPExpirationdate']));

		
		$palsExpirationDate = '';
		if($row['palsExpirationDate'] != '0000-00-00' && isset($row['palsExpirationDate']))
			$palsExpirationDate = date('m/d/Y', strtotime($row['palsExpirationDate']));

		$aclsExpirationDate = '';
		if($row['aclsExpirationDate'] != '0000-00-00' && isset($row['aclsExpirationDate']))
			$aclsExpirationDate = date('m/d/Y', strtotime($row['aclsExpirationDate']));

		$FBIfingerprinting = '';
		if($row['FBIfingerprinting'] != '0000-00-00' && isset($row['FBIfingerprinting']))
			$FBIfingerprinting = date('m/d/Y', strtotime($row['FBIfingerprinting']));

		$StateBackGroundCheckdate = '';
		if($row['StateBackGroundCheckdate'] != '0000-00-00' && isset($row['StateBackGroundCheckdate']))
			$StateBackGroundCheckdate = date('m/d/Y', strtotime($row['StateBackGroundCheckdate']));

		$DepartmentOfAgingDate = '';
		if($row['DepartmentOfAgingDate'] != '0000-00-00' && isset($row['DepartmentOfAgingDate']))
			$DepartmentOfAgingDate = date('m/d/Y', strtotime($row['DepartmentOfAgingDate']));

		$ChildAbusedate = '';
		if($row['ChildAbusedate'] != '0000-00-00' && isset($row['ChildAbusedate']))
			$ChildAbusedate = date('m/d/Y', strtotime($row['ChildAbusedate']));

		$StateBackGroundCheck = $row['BackgroundStateIds'];
		
		$covid1Date = '';
		if($row['covid1Date'] != '0000-00-00' && isset($row['covid1Date']))
			$covid1Date = date('m/d/Y', strtotime($row['covid1Date']));
		
		$covid2Date = '';
		if($row['covid2Date'] != '0000-00-00' && isset($row['covid2Date']))
			$covid2Date = date('m/d/Y', strtotime($row['covid2Date']));
		
		$covidBoosterDate = '';	
		if($row['covidBoosterDate'] != '0000-00-00' && isset($row['covidBoosterDate']))
			$covidBoosterDate = date('m/d/Y', strtotime($row['covidBoosterDate']));
			
			
			$BackgroundState  = isset($row['BackgroundState']) ? ($row['BackgroundState']) : 0;
			// Get BackgroundStates
	
			$StudentCertificationLog = $objStudentCertificationLog->GetStudentCertificationLog($studentId, $BackgroundState);

			$BackgroundStateIds  = $StudentCertificationLog['BackgroundStateIds'];
			$BackgroundStateIds = explode(',',$BackgroundStateIds);
			$BackgroundStateNames = array();
			$BackgroundStateNameArray = '';
			if(count($BackgroundStateIds))
			{
				foreach($BackgroundStateIds as $BackgroundStateId)
				{
					$BackgroundStateName = $objCountryStateMaster->GetSingleState($BackgroundStateId);
					$BackgroundStateNames[] = $BackgroundStateName;
				}
			}
			if(count($BackgroundStateIds))
			{
				$BackgroundStateNameArray = implode(',',$BackgroundStateNames);
			}
			 
			
			
			$ChildAbuseState = ($row['ChildAbuseState']);
			$ChildAbuseStateName = $objCountryStateMaster->GetSingleState($ChildAbuseState);
			
			$insuranceCarrier=stripslashes($row['insuranceCarrier']);
			$licenseNumber=stripslashes($row['licenseNumber']);
			$insuranceNumber=stripslashes($row['insuranceNumber']);
			
			$licenseCredentials=stripslashes($row['licenseCredentials']);
			
			$aclsNumber=stripslashes($row['aclsNumber']);
		
			$aclsStatus=stripslashes($row['aclsStatus']);
			
			$bclsNumber=stripslashes($row['bclsNumber']);
			
			$bclsStatus=stripslashes($row['bclsStatus']);
			
			$palsNumber=stripslashes($row['palsNumber']);
			
			$palsStatus=stripslashes($row['palsStatus']);
			

			$spreadsheet->getActiveSheet()->setCellValue('B'.$printStartRowCounter, $fullName);
			$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('B'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('C'.$printStartRowCounter, $drugScreeningDate);
			$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('C'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('D'.$printStartRowCounter, $criminalBackgroundCheck);
			$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('D'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('E'.$printStartRowCounter, $healthCertificationDate);
			$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('E'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('F'.$printStartRowCounter, $aclsCertificationDate);
			$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('F'.$printStartRowCounter)->applyFromArray($styleArray);
					
			$spreadsheet->getActiveSheet()->setCellValue('G'.$printStartRowCounter, $FBIfingerprinting);
			$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('G'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('H'.$printStartRowCounter, $BackgroundStateNameArray);
			$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('H'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('I'.$printStartRowCounter, $StateBackGroundCheckdate);
			$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('I'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('J'.$printStartRowCounter, $ChildAbuseStateName);
			$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('J'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('K'.$printStartRowCounter, $ChildAbusedate);
			$spreadsheet->getActiveSheet()->getStyle('K'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('K'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('L'.$printStartRowCounter, $licenseExpiration);
			$spreadsheet->getActiveSheet()->getStyle('L'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('L'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('M'.$printStartRowCounter, $licenseNumber);
			$spreadsheet->getActiveSheet()->getStyle('M'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('M'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('N'.$printStartRowCounter, $palsNumber);
			$spreadsheet->getActiveSheet()->getStyle('N'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('N'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('O'.$printStartRowCounter, $bclsStatus);
			$spreadsheet->getActiveSheet()->getStyle('O'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('O'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('P'.$printStartRowCounter, $insuranceCarrier);
			$spreadsheet->getActiveSheet()->getStyle('P'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('P'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('Q'.$printStartRowCounter, $aclsExpirationDate);
			$spreadsheet->getActiveSheet()->getStyle('Q'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('Q'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('R'.$printStartRowCounter, $bclsCertificationDate);
			$spreadsheet->getActiveSheet()->getStyle('R'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('R'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('S'.$printStartRowCounter, $bclsExpirationDate);
			$spreadsheet->getActiveSheet()->getStyle('S'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('S'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('T'.$printStartRowCounter, $covid1Date);
			$spreadsheet->getActiveSheet()->getStyle('T'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('T'.$printStartRowCounter)->applyFromArray($styleArray);

			$spreadsheet->getActiveSheet()->setCellValue('U'.$printStartRowCounter, $covid2Date);
			$spreadsheet->getActiveSheet()->getStyle('U'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('U'.$printStartRowCounter)->applyFromArray($styleArray);

			$spreadsheet->getActiveSheet()->setCellValue('V'.$printStartRowCounter, $covidBoosterDate);
			$spreadsheet->getActiveSheet()->getStyle('V'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('V'.$printStartRowCounter)->applyFromArray($styleArray);

			$spreadsheet->getActiveSheet()->setCellValue('W'.$printStartRowCounter, $palsCertificationDate);
			$spreadsheet->getActiveSheet()->getStyle('W'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('W'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('X'.$printStartRowCounter, $palsExpirationDate);
			$spreadsheet->getActiveSheet()->getStyle('X'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('X'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('Y'.$printStartRowCounter, $ChildAbuseStateName);
			$spreadsheet->getActiveSheet()->getStyle('Y'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('Y'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('Z'.$printStartRowCounter, $DepartmentOfAgingDate);
			$spreadsheet->getActiveSheet()->getStyle('Z'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('Z'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AA'.$printStartRowCounter, $NRPCerificationDate);
			$spreadsheet->getActiveSheet()->getStyle('AA'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AA'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AB'.$printStartRowCounter, $NRPExpirationdate);
			$spreadsheet->getActiveSheet()->getStyle('AB'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AB'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AC'.$printStartRowCounter, $aclsNumber);
			$spreadsheet->getActiveSheet()->getStyle('AC'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AC'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AD'.$printStartRowCounter, $licenseCredentials);
			$spreadsheet->getActiveSheet()->getStyle('AD'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AD'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AE'.$printStartRowCounter, $bclsNumber);
			$spreadsheet->getActiveSheet()->getStyle('AE'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AE'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AF'.$printStartRowCounter, $insuranceNumber);
			$spreadsheet->getActiveSheet()->getStyle('AF'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AF'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AG'.$printStartRowCounter, $aclsStatus);
			$spreadsheet->getActiveSheet()->getStyle('AG'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AG'.$printStartRowCounter)->applyFromArray($styleArray);
			
			$spreadsheet->getActiveSheet()->setCellValue('AH'.$printStartRowCounter, $palsStatus);
			$spreadsheet->getActiveSheet()->getStyle('AH'.$printStartRowCounter)->getAlignment()->applyFromArray(array('horizontal' => 'left'));
			$spreadsheet->getActiveSheet()->getStyle('AH'.$printStartRowCounter)->applyFromArray($styleArray);
			
			
			$printStartRowCounter++;
			
		}	
	}	
	
	// Auto size columns for each worksheet
	foreach ($spreadsheet->getAllSheets() as $sheet) 
	{
		for ($col = 0; $col <= \PhpOffice\PhpSpreadsheet\Cell\Coordinate::columnIndexFromString($sheet->getHighestDataColumn()); $col++) {
			$sheet->getColumnDimensionByColumn($col)->setAutoSize(true);
		}
	}	
	$reportname='StudentCertificationLogAllListReport_';	
	
	$spreadsheet->setActiveSheetIndex(0);
	
	$currentDate = date('m_d_Y_h_i');
	
	header('Content-type: application/vnd.ms-excel; charset=UTF-8');
	header('Content-Disposition: attachment;filename="'.$reportname.$today.'.xls"');		
	header("Pragma: no-cache");
	header("Expires: 0");
	
	$objWriter = IOFactory::createWriter($spreadsheet, 'Xls');
	$objWriter->save('php://output');
?>