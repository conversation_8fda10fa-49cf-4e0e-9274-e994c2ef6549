<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsCheckoffQuestionMaster.php');
include('../setRequest.php');
include('../class/clscheckoff.php');

//print_r($_POST);exit;

$schoolQuestionId = 0;
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {

	$schoolQuestionId = isset($_GET['questionId']) ? ($_GET['questionId']) : 0;
	$status = ($schoolQuestionId > 0) ? 'updated' : 'added';
	//echo 'schoolQuestionId->'.$schoolQuestionId;exit;

	$questionType  = ($_POST['questionType']);
	$question  = ($_POST['question']);
	$Marks  = ($_POST['Marks']);
	$SortOrder  = ($_POST['SortOrder']);



	$objQuestionMaster = new clsCheckoffQuestionMaster();
	$objQuestionMaster->schoolQuestionTitle = $question;
	$objQuestionMaster->schoolQuestionType = $questionType;
	$objQuestionMaster->schoolId = $currentSchoolId;
	$objQuestionMaster->sortOrder = $SortOrder;
	$objQuestionMaster->marks = $Marks;
	$RetQuestionId = $objQuestionMaster->UpdateNewQuestionsMaster($schoolQuestionId);

	$DeleteQuestionId = $objQuestionMaster->DeleteNewQuestionsMaster($RetQuestionId);


	switch ($questionType) {
		case "1":
			$yesnoanswers  = ($_POST['yesnoanswers']);
			$txtyesno  = ($_POST['txtyesno']);
			foreach ($txtyesno as $keys => $value) {

				$objQuestionMaster->schoolQuestionId = $RetQuestionId;
				$objQuestionMaster->schoolOptionText = $value;
				$objQuestionMaster->schoolOptionValue = $yesnoanswers[$keys];
				$objQuestionMaster->choiceAnswer = 0;
				$QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
			}
			break;

		case "2":
			$txtsinglechoice  = ($_POST['txtsinglechoice']);
			$txtsinglechoicemarks  = ($_POST['txtsinglechoicemarks']);
			$answersid  = ($_POST['answers']);
			foreach ($txtsinglechoice as $key => $value) {

				$objQuestionMaster->schoolQuestionId = $RetQuestionId;
				$objQuestionMaster->schoolOptionText = $value;
				$objQuestionMaster->schoolOptionValue = $answersid[$key];
				$objQuestionMaster->choiceAnswer = $txtsinglechoicemarks[$key];
				$QuestionDetailId = $objQuestionMaster->SaveNewQuestionsDetails($RetQuestionId);
			}
			break;
	}
	unset($objQuestionMaster);

	if ($RetQuestionId > 0) {
		//Audit Log
		// Instantiate the Logger class
		$objLog = new clsLogger();
		$type = "step";
		$action = $objLog::EDIT;
		$isSuperAdmin = isset($_SESSION['isCurrentSchoolSuperAdmin']) ? ($_SESSION['isCurrentSchoolSuperAdmin']) : 0;
		$userType = ($isSuperAdmin) ? $objLog::SUPERADMIN : $objLog::ADMIN;


		$objCheckoff = new clscheckoff();
		$objCheckoff->saveCompEvaluationAuditLog($RetQuestionId, $_SESSION['loggedUserId'], $userType, $action,  0, $type, $isSuperAdmin);
		unset($objCheckoff);

		unset($objLog);

		header('location:viewquestions.html?questionId=' . EncodeQueryData($questionId) . '&status=' . $status);

		exit();
	} else {
		header('location:editquestion.html?status=error');
	}
}
