<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsCheckoffSectionMaster.php');
include('../class/clsCheckoffQuestionMaster.php');
include('../class/clsCheckoffTopicMaster.php');
include('../class/clsClinicalEval.php');

if ($_SERVER['REQUEST_METHOD'] == "POST") {

    $objDB = new clsDB();

    $type = isset($_POST['type']) ? $_POST['type'] : '';


    ini_set('upload_max_filesize', '50M');
    ini_set('post_max_size', '50M');
    ini_set('max_input_time', 300000);
    ini_set('max_execution_time', 300000);

    $notifyMessage = 'Imported';
    $row = 1;
    $isExistmidTermPerformanceSectionId = 0;
    $isExistdfMidtermPerformanceQuestionId = 0;
    if (isset($_FILES['file'])) {
        $filename = $_FILES["file"]["tmp_name"];
        $ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));

        if ($ext != "csv") {
            $error = "Upload only .csv file.";
            header('location:settings.html?status=Importerror');
            exit();
        }

        if ($_FILES["file"]["size"] > 0) {

            $file = fopen($filename, "r");
            $counter = 0;
            // echo '<pre>';
            $retStudentId = 0;
            while (($getData = fgetcsv($file, 10000, ",")) !== FALSE) {
                // echo $type;exit; 
                if ($row == 1) {
                    $row++;
                    continue;
                }

                // print_r($getData);exit;
                //   echo  
                $sectionTitle = trim($getData[1]);
                //   echo "<br>";
                //   echo 
                $optionText = trim($getData[2]);
                //   echo "<br>";
                //   echo 
                $discription = trim($getData[3]);
                //   echo "<br>";
                //   echo 
                $optionType = trim($getData[4]);
                //   echo "<br>";
                //   echo  
                $sortOrder = trim($getData[5]);
                // $optionValues = trim($getData[5]);
                $questionDescription = trim($getData[6]);
                $optionValues = trim($getData[7]);
                $optionSortOrder = trim($getData[8]);
                //   echo "<br>";
                //   echo 
                $sectionSortOrder = trim($getData[9]);
                $topic = trim($getData[10]);
                $isTechnologist = trim($getData[11]);
                $checkoffTitleId = trim($getData[12]);
                $topicId = $objDB->GetSingleColumnValueFromTable('defaultadvancetopicmaster', 'defaultTopicId', 'title', $topic, 'checkoffTitleId', $checkoffTitleId);

                $isExistSectionId = $objDB->GetSingleColumnValueFromTable('defaultclinicalevalsectionmaster', 'defaultClinicalSectionId', 'title', addslashes($sectionTitle), 'topicId', $topicId);
            //   exit;
                $objClinicalEval = new clsClinicalEval();
                if ($sectionTitle != '') {
                    if (!$isExistSectionId) {

                        $objClinicalEval->title = $sectionTitle;
                        $objClinicalEval->sortOrder = $sectionSortOrder;
                        $objClinicalEval->topicId = $topicId;
                        $objClinicalEval->isTechnologist = $isTechnologist;
                        $isExistSectionId = $objClinicalEval->SaveDefaultClinicalevaluationSection(0);
                    }
                }
 
                $isExistQuestionId = $objDB->GetSingleColumnValueFromTable('defaultclinicalevalquestionmaster', 'defaultClinicalQuestionId', 'questionText', ($optionText));

                if (!$isExistQuestionId) {
                    $objClinicalEval->questionText = $optionText;
                    $objClinicalEval->defaultQuestionType = $optionType;
                    $objClinicalEval->sectionMasterId = $isExistSectionId;
                    $objClinicalEval->sortOrder = $sortOrder;
                    $objClinicalEval->description = $questionDescription;
                    $isExistQuestionId = $objClinicalEval->SaveDefaultEvaluationQuestion(0);
                }

                if ($isExistQuestionId) {
                   
                    $isExistDetailQuestionId = $objClinicalEval->GetQuestionIdFromDetail($isExistQuestionId);

                    if ($isExistDetailQuestionId < 9) {
                       
                        $objClinicalEval->questionId = $isExistQuestionId;
                        $objClinicalEval->optionText = $optionValues;
                        $objClinicalEval->description = $questionDescription;
                        $objClinicalEval->sortOrder = $optionSortOrder;
                        $objClinicalEval->SaveDefaultEvaluationQuestionOptions(0);
                    }
                }
                    
                $isExistDetailsQuestionId = $objDB->GetSingleColumnValueFromTable('defaultclinicalevalsectiondetails', 'defaultQuestionId', 'defaultQuestionId', $isExistQuestionId, 'defaultTopicId', $topicId);
                    
                if (!$isExistDetailsQuestionId) {
                    $objClinicalEval->topicId = $topicId;
                    $objClinicalEval->sectionId = $isExistSectionId;
                    $objClinicalEval->questionId = $isExistQuestionId;
                    $objClinicalEval->SaveDefaultSectionDetails(0);
                }
                $result = 1;
            }

            fclose($file);

            $messageText = $result ? 'Imported' : 'Error';
            header('location:settings.html?status=' . $messageText);

            exit();
        }
    }
}
