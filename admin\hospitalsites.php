<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../class/clsHospitalSite.php');
include('../includes/commonfun.php');
include('../setRequest.php');

$totalHospitalSite = 0;
//For Hospital Site List
$isActiveHospital = 0;

// $isActiveHospital = 1;

if (isset($_GET['active']))
    $isActiveHospital = DecodeQueryData($_GET['active']);


if ($isActiveHospital == 1)
    $clsBtnActiveAll = "active";
elseif ($isActiveHospital == 0)
    $clsBtnActive = "active";
elseif ($isActiveHospital == 2) {
    $clsBtnInActive = "active";
} else
    $clsBtnActive = "active";


$objHospitalSite = new clsHospitalSite();
$rowsHospitalSite = $objHospitalSite->GetAllHospitalSite($currentSchoolId, $isActiveHospital);
if ($rowsHospitalSite != '') {
    $totalHospitalSite = mysqli_num_rows($rowsHospitalSite);
}
// unset($objHospitalSite);
$loggedUserId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Hospital Sites</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">Hospital Sites</li>
                </ol>
            </div>
            <div class="pull-right">
                <a class="btn btn-link" href="addhospitalsites.html">Add</a>
            </div>
        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Hospital Site added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Hospital Site updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "StatusUpdated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Hospital Site status updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>

        <div class="row margin_bottom_ten">
            <div class="btn-group pull-right" style="margin-right: 14px;" role="group" aria-label="First group">
                <a role="button" class="btn btn-primary <?php echo $clsBtnActive; ?>" href="hospitalsites.html?schoolId=<?php echo EncodeQueryData($currentSchoolId); ?>&active=<?php echo EncodeQueryData(0); ?>">Active</a>
                <a role="button" class="btn btn-primary  <?php echo $clsBtnInActive; ?>" href="hospitalsites.html?schoolId=<?php echo EncodeQueryData($currentSchoolId); ?>&active=<?php echo EncodeQueryData(2); ?>">Inactive</a>
                <a role="button" class="btn btn-primary <?php echo $clsBtnActiveAll; ?>" href="hospitalsites.html?schoolId=<?php echo EncodeQueryData($currentSchoolId); ?>&active=<?php echo EncodeQueryData(1); ?>">All</a>
            </div>
        </div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Hospital Site</th>
                    <th>State</th>
                    <th>City</th>
                    <th>Contact Person</th>
                    <th>Contact Info</th>
                    <th class="text-center">Hospital</th>
                    <th class="text-center">SMS</th>

                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalHospitalSite > 0) {
                    while ($row = mysqli_fetch_array($rowsHospitalSite)) {

                        $hospitalSiteId = $row['hospitalSiteId'];
                        $RotationhospitalSiteId = $row['RotationhospitalSiteId'];
                        $CIhospitalSiteId = $row['CIhospitalSiteId'];
                        $title = stripslashes($row['title']);
                        $email = stripslashes($row['email']);
                        $phone = stripslashes($row['phone']);
                        $city = stripslashes($row['city']);
                        $state = stripslashes($row['name']);
                        $contactPerson = stripslashes($row['contactPerson']);

                        $buttoncss = "btn-primary";

                        $DBisHospital = $row['isHospital'];
                        $isHospitalStatusLabel = ($DBisHospital == 1) ? 'Active' : 'Inactive';

                        $DBisSms = $row['isSms'];
                        $isSMSStatusLabel = ($DBisSms == 1) ? 'Active' : 'Inactive';

                        $isActive = $row['isActive'];
                        $isActiveLabel = ($isActive == 0) ? 'Active' : 'Inactive';
                        $isActiveRotations = $objHospitalSite->GetRotationsCountByHospitalSites($currentSchoolId, $hospitalSiteId);
                ?>
                        <tr>
                            <td>
                                <?php echo ($title); ?>
                            </td>
                            <td>
                                <?php echo ($state); ?>
                            </td>
                            <td>
                                <?php echo ($city); ?>
                            </td>
                            <td>
                                <?php echo ($contactPerson); ?>
                            </td>
                            <td>
                                Email: <a href="mailto:<?php echo ($email); ?>">
                                    <?php echo ($email); ?>
                                </a> <br>
                                Phone: <a href="tel:<?php echo ($phone); ?>">
                                    <?php echo ($phone); ?>
                                </a>
                            </td>
                            <td class="text-center">
                                <a href="javascript:void(0);" onclick="updateHospitalStatus(this)" hospitalSiteId="<?php echo $hospitalSiteId; ?>" type="H" isHospitalStatus="<?php echo $DBisHospital; ?>"><?php echo $isHospitalStatusLabel; ?></a>
                            </td>
                            <td class="text-center">
                                <a href="javascript:void(0);" onclick="updateHospitalStatus(this)" hospitalSiteId="<?php echo $hospitalSiteId; ?>" type="S" isSMSStatus="<?php echo $DBisSms; ?>"><?php echo $isSMSStatusLabel; ?></a>
                            </td>
                            <td style="text-align: center">
                                <a class="<?php echo ($buttoncss); ?>" href="schoolclinicianstransubmit.html?id=<?php echo ($hospitalSiteId); ?>&newStatus=<?php //echo($updateStatus); 
                                                                                                                                                            ?>">

                                </a>
                                <?php //if($isShowemail==1){
                                ?>

                                <a href="addhospitalsites.html?id=<?php echo (EncodeQueryData($hospitalSiteId)); ?>">Edit</a>
                                | <a href="calendar.html?id=<?php echo (EncodeQueryData($hospitalSiteId)); ?>">Edit Calendar</a>
                                | <a href="addhospitalsites.html?id=<?php echo (EncodeQueryData($hospitalSiteId)); ?>&isCopy=<?php echo (EncodeQueryData(1)); ?>">Copy</a>
                                | <a href="javascript:void(0);" onclick="updateHospitalStatus(this)" hospitalSiteId="<?php echo $hospitalSiteId; ?>" isActiveRotations="<?php echo $isActiveRotations; ?>" type="A" isHospitalStatus="<?php echo $isActive; ?>"><?php echo $isActiveLabel; ?></a>
                                <?php if ($title == 'Peer To Peer') { ?>

                                <?php } elseif ($CIhospitalSiteId > 0 || $RotationhospitalSiteId > 0) { ?>
                                    | <a id="warningAjax" class="text-muted" href="javascript:void(0);" hospitalSiteId="<?php echo EncodeQueryData($hospitalSiteId); ?>" hospitaltitle="<?php echo ($title); ?>">Delete</a>
                                <?php } else { ?>

                                    | <a id="" class="text-muted deleteAjaxRow" href="javascript:void(0);" hospitalSiteId="<?php echo EncodeQueryData($hospitalSiteId); ?>" hospitaltitle="<?php echo ($title); ?>">Delete</a>

                                <?php }  ?>


                            </td>
                        </tr>
                <?php
                    }
                }
                ?>



            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
        });


        var current_datatable = $("#datatable-responsive").DataTable({
            // "responsive": false,

            "aoColumns": [{
                    "sWidth": "30%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%"
                }, {
                    "sWidth": "10%"
                },
                {
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
                {
                    "sWidth": "10%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
                {
                    "sWidth": "5%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
            ]
        });

        $(document).on('click', '#warningAjax', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));


            alertify.confirm('Warning!', 'This Hospital site already assigned, you cant delete it!', function() {}, function() {});
        });



        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var hospitalSiteId = $(this).attr('hospitalSiteId');
            var hospitaltitle = $(this).attr('hospitaltitle');
            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1; //for Admin

            alertify.confirm('Hospital Site: ' + hospitaltitle, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: hospitalSiteId,
                        userId: userId,
                        isUser: isUser,
                      type: 'hospitalSite'
                    },
                    success: function() {
                        // current_datatable.row(current_datatable_row).remove().draw(false);

                        alertify.success('Deleted');
                        window.location.reload();
                    }
                });
            }, function() {});

        });

        function updateHospitalStatus(eleObj) {
            var hospitalSiteId = $(eleObj).attr('hospitalSiteId');
            var isHospitalStatus = $(eleObj).attr('isHospitalStatus');
            var isSMSStatus = $(eleObj).attr('isSMSStatus');
            var type = $(eleObj).attr('type');
            var userId = '<?php echo EncodeQueryData($loggedUserId); ?>';
            var isUser = 1; //for Admin
           if (type == 'H') {
                if (isHospitalStatus == 0)
                    isStatus = 1;
                else
                    isStatus = 0;
            } else if (type == 'S') {
                if (isSMSStatus == 0)
                    isStatus = 1;
                else
                    isStatus = 0;
            }

            if (type == 'A') {
                if (isHospitalStatus == 0)
                    isStatus = 1;
                else
                    isStatus = 0;
                var isActiveRotations = $(eleObj).attr('isActiveRotations');
                if (isActiveRotations > 0) {
                    alertify.alert('Warning!', 'The hospital site has assigned for active rotations and cannot be made inactive.');
                    return false;
                }

            }

            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_update_hospital_status.html",
                data: {
                    id: hospitalSiteId,
                    isStatus: isStatus,
                    userId: userId,
                    isUser: isUser,
                    type: type
                },
                success: function() {

                    if (type == 'H') {
                        $(eleObj).attr('isHospitalStatus', isStatus);
                        var isHospitalStatusLabel = (isStatus == 1) ? 'Active' : 'Inactive';

                        $(eleObj).text(isHospitalStatusLabel);
                    } else if (type == 'S') {
                        $(eleObj).attr('isSMSStatus', isStatus);
                        var isSMSStatusLabel = (isStatus == 1) ? 'Active' : 'Inactive';
                        $(eleObj).text(isSMSStatusLabel);
                    } else if (type == 'A') {
                        $(eleObj).attr('isHospitalStatus', isStatus);
                        var isHospitalStatusLabel = (isStatus == 0) ? 'Active' : 'Inactive';

                        $(eleObj).text(isHospitalStatusLabel);
                    }
                    alertify.success('Updated');
                }
            });
        }
    </script>


</body>

</html>