<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsAccreditation.php');
include('../class/clsStudent.php');
include('../setRequest.php');

require '../vendor/autoload.php'; // PhpSpreadsheet autoload

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Style\Alignment;

// Check POST request
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnStudentExport'])) {
    $accreditationId = 0;
}

$currentSchoolId = $_GET['currentSchoolId'];
$currentSchoolId = DecodeQueryData($currentSchoolId);
$studentId = $_GET['studentId'];
$studentId = DecodeQueryData($studentId);

// Get Post Data
$recordNo  = ($_POST['txtRecordNumber']);
$coarcNo  = ($_POST['txtCoARCNumber']);
$dateOfBirth  = ($_POST['DateofBirth']);
$programEnrollmentDate  = ($_POST['ProgramEnrollmentDate']);
$ontimeGreduationDate  = ($_POST['OntimeGraduationDate']);
$wrrtCompletionDate  = ($_POST['WRRTCompletionDate']);
$actualGraduationDate  = ($_POST['ActualGraduationDate']);
$crtCompletionDate  = ($_POST['CRTCompletionDate']);
$programDropDate  = ($_POST['ProgramDropDate']);
$dropDateReason  = ($_POST['Reason']);

// Student Details
$objStudent = new clsStudent();
$rowStudent = $objStudent->GetStudentDetails($studentId);
unset($objStudent);

$firstName = $rowStudent['firstName'];
$lastName = $rowStudent['lastName'];
$fullName = $firstName . ' ' . $lastName;

$title = 'Student Accreditation List';
date_default_timezone_set('Asia/Kolkata');
$today = (date('m/d/Y, H:i A'));

// Create new Spreadsheet object
$spreadsheet = new Spreadsheet();
$sheet = $spreadsheet->getActiveSheet();

// Set document properties
$spreadsheet->getProperties()
    ->setCreator('Schools')
    ->setLastModifiedBy('JCC')
    ->setTitle('Reports')
    ->setSubject('Student Accreditation List')
    ->setDescription('All School Reports');

// Set Header
$headerStyleArray = [
    'font' => ['bold' => true, 'size' => 16],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'E0E0E0']
    ],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
];

$sheet->mergeCells("B2:K2");
$sheet->setCellValue('B2', $title);
$sheet->getStyle('B2:K2')->applyFromArray($headerStyleArray);

$sheet->mergeCells("B4:K4");
$sheet->setCellValue('B4', $fullName);
$sheet->getStyle('B4:K4')->applyFromArray($headerStyleArray);

// Table Header
$tableHeaderStyle = [
    'font' => ['bold' => true, 'size' => 12],
    'fill' => [
        'fillType' => Fill::FILL_SOLID,
        'startColor' => ['rgb' => 'E0E0E0']
    ],
    'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
    'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]]
];

$tableHeaders = [
    'B6' => 'Student ID',
    'C6' => 'CoARC Number',
    'D6' => 'Date of Birth',
    'E6' => 'Program Enrollment Date',
    'F6' => 'On time Graduation Date',
    'G6' => 'Actual Graduation Date',
    'H6' => 'CSE Completion Date',
    'I6' => 'TMC Completion Date',
    'J6' => 'Program Drop Date',
    'K6' => 'Drop Date Reason',
];

foreach ($tableHeaders as $cell => $text) {
    $sheet->setCellValue($cell, $text);
    $sheet->getStyle($cell)->applyFromArray($tableHeaderStyle);
}

// Table Data
$dataStyle = ['alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT]];
$printStartRowCounter = 7;

$data = [
    'B' . $printStartRowCounter => $recordNo,
    'C' . $printStartRowCounter => $coarcNo,
    'D' . $printStartRowCounter => $dateOfBirth,
    'E' . $printStartRowCounter => $programEnrollmentDate,
    'F' . $printStartRowCounter => $ontimeGreduationDate,
    'G' . $printStartRowCounter => $actualGraduationDate,
    'H' . $printStartRowCounter => $wrrtCompletionDate,
    'I' . $printStartRowCounter => $crtCompletionDate,
    'J' . $printStartRowCounter => $programDropDate,
    'K' . $printStartRowCounter => $dropDateReason,
];

foreach ($data as $cell => $value) {
    $sheet->setCellValue($cell, $value);
    $sheet->getStyle($cell)->applyFromArray($dataStyle);
}

// Auto size columns
foreach (range('B', 'K') as $columnID) {
    $sheet->getColumnDimension($columnID)->setAutoSize(true);
}

$sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows

// Generate Excel File
$reportname = 'StudentAccreditationListReport_';
$currentDate = date('m_d_Y_h_i');
$filename = $reportname . $currentDate . '.xls';

header('Content-Type: application/vnd.ms-excel');
header('Content-Disposition: attachment;filename="' . $filename . '"');
header('Cache-Control: max-age=0');

$writer = new Xls($spreadsheet);
$writer->save('php://output');
exit;
?>