<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include('../class/clsSystemUser.php');

$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$status = isset($_GET['status'])?$_GET['status']:'';
?>


<!doctype html>

<html lang="en">

<head>
  <meta charset="utf-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
  <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
  <title>Email</title>
  <?php include('includes/headercss.php'); ?>
  
  <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
  <!-- <script type="text/javascript" src="<?php //echo ($dynamicOrgUrl); ?>/assets/chartistjs/chartist.min.js"></script>
  <link rel="stylesheet" href="<?php //echo ($dynamicOrgUrl); ?>/assets/chartistjs/chartist.min.css" type="text/css"> -->

  <!-- <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/css/bootstrap.min.css" integrity="sha384-xOolHFLEh07PJGoPkLv1IbcEPTNtaed2xpHsD9ESMhqIYd0nLMwNLD69Npy4HI+N" crossorigin="anonymous">

     <script src="https://cdn.jsdelivr.net/npm/jquery@3.5.1/dist/jquery.slim.min.js" integrity="sha384-DfXdz2htPH0lsSSs5nCTpuj/zy4C+OGpamoFVy38MVBnE+IbbVYUew+OrCXaRkfj" crossorigin="anonymous"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.2/dist/js/bootstrap.bundle.min.js" integrity="sha384-Fy6S3B9q64WdZWQUiU+q4/2Lc9npb8tCaSX9FK7E8HnRr0Jz8D6OP9dO5Vg3Q9ct" crossorigin="anonymous"></script> -->
<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

  <style>
    .multiple-checkbox {
      padding-left: 20px;
    }

    .multiple-checkbox li {
      margin-top: 4px;
    }

    .multiple-checkbox .custom-control-label {
      font-weight: 600;
      color: #666;
    }

    .custom-control-label::before {
      border-radius: 3px;
    }

    .custom-control-input:checked~.custom-control-label::after {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 8 8'%3e%3cpath fill='%23fff' d='M6.564.75l-3.59 3.612-1.538-1.55L0 4.26 2.974 7.25 8 2.193z'/%3e%3c/svg%3e")
    }

    .custom-control-input:indeterminate~.custom-control-label::after {
      background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 4 4'%3e%3cpath stroke='%23fff' d='M0 2h4'/%3e%3c/svg%3e") !important;
      border-radius: 3px;
      color: #fff;
      border-color: #007bff;
      background-color: #007bff;
    }

    .list-style {
      list-style: none;
    }

    .submit-btn {
      width: 10%;
      color: #fff;
      background-color: rgba(45, 105, 182, 1);
    }

    .submit-btn:hover {
      color: #fff;
      background-color: rgba(45, 105, 182, 1);
    }

    .submit-btn:active {
      color: #fff;
      background-color: rgba(45, 105, 182, 1);
    }
    .scrollY{
      max-height: 300px;
      overflow-y: scroll;

    }
  </style>

</head>

<body>
  <?php include('includes/header.php'); ?>
  <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">Email</li>
                </ol>
            </div>
            <div class="pull-right">
              <a href="emailReport.html"><input type="submit" name="btnExport" id="btnExport" class="btn btn-link" value="Email Report" style="background-color: transparent !important;"></a>
            </div>
        </div>
  </div>
  <div id="divTopLoading" >Loading...</div>
  <div class="container my-3" style="margin-top: 20px; margin-bottom: 10px;">
    <form action="sendEmailRecords.html" method="POST" >
      <div class="col-md-12 col-sm-12 col-xs-12">
        <input type="hidden" value="" id="hideAllSchools" name="hideAllSchools">
        <input type="hidden" value="" id="hideAllStudents" name="hideAllStudents">
        <input type="hidden" value="" id="hideAllClinicians" name="hideAllClinicians">
        <input type="hidden" value="" id="hideAllUsers" name="hideAllUsers">
        <div class="row">
          <div class="col-md-6">
        <label for="exampleInputEmail1">Email Subject </label><br>
        <input type="text" class="form-control" id="exampleInputEmail1" name="subject" aria-describedby="emailHelp"  required> <br>
        </div>
          <div class="col-md-6 col-sm-6 col-xs-12">
      <div class="form-group">
        <!-- <div class="col-md-10 col-sm-6 col-xs-12"> -->
        <label for="exampleFormControlTextarea1">Email Body </label> <br>
          <textarea class="form-control " id="message" name="message" rows="3"  required></textarea> 
        <!-- </div> -->
      </div>
          </div>
        </div>
        

      <div class="row">
        <div class="col-md-6">
          <div class="card common-table-card mb-4">
            <!-- <div class="card-header text-muted"><strong>Check list</strong></div> -->
            <label for="exampleFormControlTextarea1">Email Send to </label> <br>
            <div class="card-body">
              <ul class="multiple-checkbox list-style">
                <!--List Step-1 -->
                <li class="">
                  <input type="checkbox" class="custom-control-input" name="all-1" id="all">
                  <label class="custom-control-label collapsed" for="#"  class="" ><a href = "javascript:void(0);">All</a></label>

                  <div >
                    <ul class="list-style">
                      <!--List Step-2 -->
                      <li>
                        <input type="checkbox" class="custom-control-input" name="all-1" id="all-1">
                        <label class="custom-control-label collapsed" for="#" data-toggle="collapse" href="#collapseStandrd" aria-expanded="false" aria-controls="collapseStandrd "><a href="javascript:void(0);">Standard</a>  </label>
                        <div id="collapseStandrd" class="panel-collapse collapse" role="tabpanel" aria-labelledby="collapseListGroupHeading1">
                          <ul class="list-style">
                            <!--List Step-3 -->
                            <?php
                            $objSchool = new clsSchool();
                            // GET ALL SCHOOLS
                            $totalSchool = 0;
                            $rowsSchool = $objSchool->GetSchoolsByPlatform(1);
                            if ($rowsSchool != '') {
                              $totalSchool = mysqli_num_rows($rowsSchool);
                            }
                            // echo $totalSchool;
                            if ($totalSchool > 0) {
                              while ($row = mysqli_fetch_array($rowsSchool)) {
                                $schoolId = $row['schoolId'];
                                $schoolName = stripslashes($row['title']);

                            ?>
                                <li>
                                  <input type="checkbox" class="custom-control-input" id="all-2-1" name="Schools" value="<?php echo $schoolId; ?>">
                                  <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseSchool_<?php echo $schoolId; ?>" aria-expanded="false" aria-controls="collapseSchool_<?php echo $schoolId; ?> "> <a href="javascript:void(0);"><?php echo $schoolName; ?> </a></label>
                                  <div id="collapseSchool_<?php echo $schoolId; ?>" class="panel-collapse collapse" role="tabpanel" aria-labelledby="collapseListGroupHeading1">
                                    <ul class="list-style">
                                      <!--List Step-3 -->
                                      <li>
                                        <input type="checkbox" class="custom-control-input" id="all-2-1">
                                        <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseAdminList_<?php echo $schoolId; ?>" aria-expanded="false" aria-controls="collapseAdminList_<?php echo $schoolId; ?> "> <a href="javascript:void(0);">Users </a></label>
                                        <div id="collapseAdminList_<?php echo $schoolId; ?>" class="panel-collapse collapse" role="tabpanel" aria-labelledby="collapseListGroupHeading1">
                                          <ul class="list-style">
                                            <!--List Step-3 -->
                                            <?php

                                            $totalSchoolUser = '';
                                            $objSystemUsers = new clsSystemUser();
                                            $rowsSchoolUsers = $objSystemUsers->GetSchoolSystemUsers($schoolId);
                                            unset($objSystemUsers);
                                            if ($rowsSchoolUsers != '') {
                                              $totalSchoolUser = mysqli_num_rows($rowsSchoolUsers);
                                            }
                                            if ($totalSchoolUser > 0) {
                                              while ($row = mysqli_fetch_array($rowsSchoolUsers)) {

                                                $systemUserMasterId = $row['systemUserMasterId'];
                                                $firstName = stripslashes($row['firstName']);
                                                $lastName = stripslashes($row['lastName']);
                                                $fullName = $firstName . ' ' . $lastName;
                                            ?>
                                                <li>
                                                  <input type="checkbox" class="custom-control-input" id="all-2-1" name="UserIds" value="<?php echo $systemUserMasterId; ?>">
                                                  <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseAdmin_<?php echo $systemUserMasterId; ?>" aria-expanded="false" aria-controls="collapseAdmin_<?php echo $systemUserMasterId; ?> "> <?php echo $fullName; ?> </label>
                                                </li>
                                            <?php
                                              }
                                            }
                                            ?>

                                          </ul>
                                        </div>
                                      </li>
                                      <li>
                                        <input type="checkbox" class="custom-control-input" id="all-2-2" name="clinicians" value="<?php echo $schoolId; ?>">
                                        <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseClinicians" aria-expanded="false" aria-controls="collapseClinicians "> Clinicians</label>
                                      </li>
                                      <li>
                                        <input type="checkbox" class="custom-control-input" id="all-2-3" name="students" value="<?php echo $schoolId; ?>">
                                        <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseStudents" aria-expanded="false" aria-controls="collapseStudents "> Students </label>
                                      </li>

                                    </ul>
                                <?php
                              }
                            }
                            unset($objSchool);
                                ?>
                          </ul>
                        </div>
                      </li>
                      <li>
                        <input type="checkbox" class="custom-control-input" id="list2" name="all-1">
                        <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseAdvancedList" aria-expanded="false" aria-controls="collapseAdvancedList "><a href="javascript:void(0);">Advanced</a> </label>
                        <div id="collapseAdvancedList" class="panel-collapse collapse" role="tabpanel" aria-labelledby="collapseListGroupHeading1">
                        <ul class="list-style">
                            <!--List Step-3 -->
                            <?php
                            $objSchool = new clsSchool();
                            // GET ALL SCHOOLS
                            $totalSchool = 0;
                            $rowsSchool = $objSchool->GetSchoolsByPlatform(0);
                            if ($rowsSchool != '') {
                              $totalSchool = mysqli_num_rows($rowsSchool);
                            }
                            // echo $totalSchool;
                            if ($totalSchool > 0) {
                              while ($row = mysqli_fetch_array($rowsSchool)) {
                                $schoolId = $row['schoolId'];
                                $schoolName = stripslashes($row['title']);

                            ?>
                                <li>
                                  <input type="checkbox" class="custom-control-input" id="all-2-1" name="Schools" value="<?php echo $schoolId; ?>">
                                  <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseSchool_<?php echo $schoolId; ?>" aria-expanded="false" aria-controls="collapseSchool_<?php echo $schoolId; ?> "> <a href="javascript:void(0);"><?php echo $schoolName; ?></a> </label>
                                  <div id="collapseSchool_<?php echo $schoolId; ?>" class="panel-collapse collapse" role="tabpanel" aria-labelledby="collapseListGroupHeading1">
                                    <ul class="list-style">
                                      <!--List Step-3 -->
                                      <li>
                                        <input type="checkbox" class="custom-control-input" id="all-2-1">
                                        <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseAdminList_<?php echo $schoolId; ?>" aria-expanded="false" aria-controls="collapseAdminList_<?php echo $schoolId; ?> "> <a href="javascript:void(0);">Users</a> </label>
                                        <div id="collapseAdminList_<?php echo $schoolId; ?>" class="panel-collapse collapse" role="tabpanel" aria-labelledby="collapseListGroupHeading1">
                                          <ul class="list-style">
                                            <!--List Step-3 -->
                                            <?php

                                            $totalSchoolUser = '';
                                            $objSystemUsers = new clsSystemUser();
                                            $rowsSchoolUsers = $objSystemUsers->GetSchoolSystemUsers($schoolId);
                                            unset($objSystemUsers);
                                            if ($rowsSchoolUsers != '') {
                                              $totalSchoolUser = mysqli_num_rows($rowsSchoolUsers);
                                            }
                                            if ($totalSchoolUser > 0) {
                                              while ($row = mysqli_fetch_array($rowsSchoolUsers)) {

                                                $systemUserMasterId = $row['systemUserMasterId'];
                                                $firstName = stripslashes($row['firstName']);
                                                $lastName = stripslashes($row['lastName']);
                                                $fullName = $firstName . ' ' . $lastName;
                                            ?>
                                                <li>
                                                  <input type="checkbox" class="custom-control-input" id="all-2-1" name="UserIds" value="<?php echo $systemUserMasterId; ?>">
                                                  <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseAdmin_<?php echo $systemUserMasterId; ?>" aria-expanded="false" aria-controls="collapseAdmin_<?php echo $systemUserMasterId; ?> "> <?php echo $fullName; ?> </label>
                                                </li>
                                            <?php
                                              }
                                            }
                                            ?>

                                          </ul>
                                        </div>
                                      </li>
                                      <li>
                                        <input type="checkbox" class="custom-control-input" id="all-2-2" name="clinicians" value="<?php echo $schoolId; ?>">
                                        <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseClinicians" aria-expanded="false" aria-controls="collapseClinicians "> Clinicians</label>
                                      </li>
                                      <li>
                                        <input type="checkbox" class="custom-control-input" id="all-2-3" name="students" value="<?php echo $schoolId; ?>">
                                        <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseStudents" aria-expanded="false" aria-controls="collapseStudents "> Students </label>
                                      </li>

                                    </ul>
                                <?php
                              }
                            }
                            unset($objSchool);
                                ?>
                          </ul>
                        </div>
                      </li>
                      <li>
                        <input type="checkbox" class="custom-control-input platform" name="all-1" id="all-11">
                        <label class="custom-control-label collapsed" for="#" data-toggle="collapse" href="#collapseMilitary" aria-expanded="false" aria-controls="collapseMilitary "> <a href="javascript:void(0);">Military</a> </label>
                        <div id="collapseMilitary" class="panel-collapse collapse" role="tabpanel" aria-labelledby="collapseListGroupHeading1">
                          <ul class="list-style">
                            <!--List Step-3 -->
                            <?php
                            $objSchool = new clsSchool();
                            // GET ALL SCHOOLS
                            $totalSchool = 0;
                            $rowsSchool = $objSchool->GetSchoolsByPlatform(2);
                            if ($rowsSchool != '') {
                              $totalSchool = mysqli_num_rows($rowsSchool);
                            }
                            // echo $totalSchool;
                            if ($totalSchool > 0) {
                              while ($row = mysqli_fetch_array($rowsSchool)) {
                                $schoolId = $row['schoolId'];
                                $schoolName = stripslashes($row['title']);

                            ?>
                                <li>
                                  <input type="checkbox" class="custom-control-input Schools" id="all-2-1" name="Schools" value = "<?php echo $schoolId; ?>" >
                                  <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseSchool_<?php echo $schoolId; ?>" aria-expanded="false" aria-controls="collapseSchool_<?php echo $schoolId; ?> "> <a href="javascript:void(0);"><?php echo $schoolName; ?></a> </label>
                                  <div id="collapseSchool_<?php echo $schoolId; ?>" class="panel-collapse collapse" role="tabpanel" aria-labelledby="collapseListGroupHeading1">
                                    <ul class="list-style">
                                      <!--List Step-3 -->
                                      <li>
                                        <input type="checkbox" class="custom-control-input types" id="all-2-1" >
                                        <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseAdminList_<?php echo $schoolId; ?>" aria-expanded="false" aria-controls="collapseAdminList_<?php echo $schoolId; ?> "> <a href="javascript:void(0);">Users</a> </label>
                                        <div id="collapseAdminList_<?php echo $schoolId; ?>" class="panel-collapse collapse" role="tabpanel" aria-labelledby="collapseListGroupHeading1">
                                          <ul class="list-style">
                                            <!--List Step-3 -->
                                            <?php

                                            $totalSchoolUser = '';
                                            $objSystemUsers = new clsSystemUser();
                                            $rowsSchoolUsers = $objSystemUsers->GetSchoolSystemUsers($schoolId);
                                            unset($objSystemUsers);
                                            if ($rowsSchoolUsers != '') {
                                              $totalSchoolUser = mysqli_num_rows($rowsSchoolUsers);
                                            }
                                            if ($totalSchoolUser > 0) {
                                              while ($row = mysqli_fetch_array($rowsSchoolUsers)) {

                                                $systemUserMasterId = $row['systemUserMasterId'];
                                                $firstName = stripslashes($row['firstName']);
                                                $lastName = stripslashes($row['lastName']);
                                                $fullName = $firstName . ' ' . $lastName;
                                            ?>
                                                <li>
                                                  <input type="checkbox" class="custom-control-input"  id="all-2-1" name="UserIds" value="<?php echo $systemUserMasterId; ?>">
                                                  <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseAdmin_<?php echo $systemUserMasterId; ?>" aria-expanded="false" aria-controls="collapseAdmin_<?php echo $systemUserMasterId; ?> "> <?php echo $fullName; ?> </label>
                                                </li>
                                            <?php
                                              }
                                            }
                                            ?>

                                          </ul>
                                        </div>
                                      </li>
                                      <li>
                                        <input type="checkbox" class="custom-control-input types" id="all-2-2" name="clinicians" value="<?php echo $schoolId; ?>">
                                        <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseClinicians"  aria-expanded="false" aria-controls="collapseClinicians "> Clinicians</label>
                                      </li>
                                      <li>
                                        <input type="checkbox" class="custom-control-input types" id="all-2-3" name="students" value="<?php echo $schoolId; ?>">
                                        <label class="custom-control-label" for="#" class="collapsed" data-toggle="collapse" href="#collapseStudents" aria-expanded="false" aria-controls="collapseStudents "> Students </label>
                                      </li>

                                    </ul>
                                <?php
                              }
                            }
                            unset($objSchool);
                                ?>
                          </ul>
                        </div>
                      </li>
                    </ul>

                  </div>
                </li>

              </ul>
            </div>
          </div>
        </div>
      </div>
      <div style="display: flex; justify-content: center; margin: 30px 0;gap: 15px;">

      <button type="submit" class="btn btn-success" id="btnSend">Send</button>
      </div>
      </div>

    </form>
  </div>
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.3/jquery.min.js"></script>
  <script src="https://maxcdn.bootstrapcdn.com/bootstrap/3.3.4/js/bootstrap.min.js"></script>
  <script src="https://use.fontawesome.com/df678b889c.js"></script>
  <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
  <!-- <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/ckeditor.js"></script> -->
</body>
<script>

  // ClassicEditor
 	// 		.create(document.querySelector('#message'))
 	// 		.catch(error => {
 	// 			console.error(error);
 	// 		});						
  $(document).on('change', 'input[type="checkbox"]', function(e) {
    var checked = $(this).prop("checked");
    var container = $(this).parent();
    var siblings = container.siblings();

    container.find('input[type="checkbox"]').prop({
      indeterminate: false,
      checked: checked
    });

    function checkSiblings(el) {

      var parent = el.parent().parent();
      var all = true;

      el.siblings().each(function() {
        // selectedSchools();
        return all = ($(this).children('input[type="checkbox"]').prop("checked") === checked);
      });

      if (all && checked) {
        // console.log('hi')
        parent.children('input[type="checkbox"]').prop({
          indeterminate: false,
          checked: checked
        });

        // parent.find('input[type="checkbox"]').prop({
        //   indeterminate: false,
        //   checked: checked
        // });

        checkSiblings(parent);

      } else if (all && !checked) {
        // console.log('hey')
        // console.log(parent)
        parent.children('input[type="checkbox"]').prop("checked", checked);
        parent.children('input[type="checkbox"]').prop("indeterminate", (parent.find('input[type="checkbox"]:checked').length > 0));
        checkSiblings(parent);

      } else {
        // console.log('bye')

        el.parents("li").children('input[type="checkbox"]').prop({
          indeterminate: true,
          checked: false
        });

      }

    }

    checkSiblings(container);
    getschools()

  });
  
function getschools() 
{
  // $(".Schools").trigger('change');
  var array = $.map($('input[name="Schools"]:checked'), function(c){return c.value; })
  $('#hideAllSchools').val(array)
  // not(:checked)
  var studentsArray = $.map($('input[name="students"]:checked'), function(c){return c.value; })
  var cliniciansArray = $.map($('input[name="clinicians"]:checked'), function(c){return c.value; })
  var UserIdsArray = $.map($('input[name="UserIds"]:checked'), function(c){return c.value; })
  $('#hideAllStudents').val(studentsArray)
  $('#hideAllClinicians').val(cliniciansArray)
  $('#hideAllUsers').val(UserIdsArray)
  // console.log('studentsArray '+studentsArray)
  // console.log('cliniciansArray '+cliniciansArray)
  // console.log('UserIdsArray '+UserIdsArray)
  // console.log('hi')
};

$(window).load(function(){
  $("#divTopLoading").addClass('hide');
  var status = '<?php echo $status;?>'
  if(status == 'success')
  alertify.success('E-Mail send scheduled successfully.');

				 });
  // military
</script>

</html>