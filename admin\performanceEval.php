<?php
// Redirect from mobile
$IsMobile = isset($_GET['IsMobile']) ? ($_GET['IsMobile']) : 0;

include('../class/clsExternalPreceptors.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');

// echo '<pre>';
// print_r($_POST);
// @session_start();
// print_r($_SESSION);
// exit;
// $isSendToExternalPreceptor = isset($_GET['isSendToExternalPreceptor']) ? ($_GET['isSendToExternalPreceptor']) : 0;
$preceptorNum = isset($_GET['preceptorNum']) ? DecodeQueryData($_GET['preceptorNum']) : '';
// $isCompletion = isset($_GET['isCompletion']) ? DecodeQueryData($_GET['isCompletion']) : 0;
$preceptorId = isset($_GET['preceptorId']) ? DecodeQueryData($_GET['preceptorId']) : 0;
$isRegister = isset($_GET['isRegister']) ? ($_GET['isRegister']) : 0;

$preceptorFullName = '';
$externalPreceptorFirstName = '';

$standardPreceptors = '';
$objExternalPreceptors = new clsExternalPreceptors();

if ($preceptorNum) {
    $isPreceptorExist = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId, $preceptorNum);
    $externalPreceptorFirstName = isset($isPreceptorExist['firstName']) ? $isPreceptorExist['firstName'] : '';
    $externalPreceptorLastName = isset($isPreceptorExist['lastName']) ? $isPreceptorExist['lastName'] : '';
    $externalPreceptorEmail = isset($isPreceptorExist['email']) ? $isPreceptorExist['email'] : '';
    $preceptorId = isset($isPreceptorExist['id']) ? $isPreceptorExist['id'] : '';
    $preceptorFullName = $externalPreceptorFirstName . ' ' . $externalPreceptorLastName;
}
$preceptorsignature = $preceptorFullName;

if (!$preceptorNum)
    include('includes/validateUserLogin.php');

include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsCIevaluation.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsClinician.php');
include('../class/clsQuestionOption.php');
include('../class/clsPerformance.php');

$schoolId = 0;
$rotationId = 0;
$studentId = 0;
$performanceEvaluationMasterId = 0;
$studentId = isset($_SESSION['loggedStudentId']) ? $_SESSION['loggedStudentId'] : 0;
$performanceRotationId = 0;
$patientCareAdultAreaId = 0;
$patientCarePediatricAreaId = 0;
$patientCareNeonatalaAreaId = 0;
$hospitalSiteId = 0;
$courselocationId = 0;
$totalSection = 0;
$parentRotationId = 0;
$rotationLocationId = 0;
$display_to_date = date('m/d/Y');
$evaluationDate = '';
$evaluatorDate = $studentDate = '';
$currentDate = date('m/d/Y');
$view = '';
$weeks = 0;
$clinicianId = 0;
$startdatetime = '';
$preceptorSignatureDate = '';
$evaluatorSignatureDate = '';
$objDB = new clsDB();
$getTimezoneId = $objDB->GetSingleColumnValueFromTable('schools', 'timeZoneId', 'schoolId', $currentSchoolId);
$TimeZone = $objDB->GetSingleColumnValueFromTable('timezonemaster', 'timezone', 'timeZoneId', $getTimezoneId);
unset($objDB);

// echo '<pre>';
//  print_r($_GET);
// exit;

//object
$objRotation = new clsRotation();
$objDB = new clsDB();
$objPerformance = new clsPerformance();

//Post Checkoff Deatil         
if ($_SERVER['REQUEST_METHOD'] == "POST") {

    $startdatetime  = ($_POST['startdatetime']);
    if ($startdatetime != '' && $startdatetime != '0000-00-00 00:00:00') {
        $startdatetime = converFromServerTimeZone($startdatetime, $TimeZone);
        $startdatetime = date("m/d/Y", strtotime($startdatetime));
    } else
        $startdatetime = '';

    $rotationId  = DecodeQueryData($_POST['cborotation']);
    $standardPreceptors = isset($_POST['standardPreceptors']) ? ($_POST['standardPreceptors']) : '';
    // $standardPreceptors = count($standardPreceptors) ? serialize($standardPreceptors) : '';
}

if (isset($_GET['performanceRotationId']))
    $performanceRotationId = DecodeQueryData($_GET['performanceRotationId']);

//For Edit CI Evaluation
if (isset($_GET['performanceEvaluationMasterId'])) {
    $performanceEvaluationMasterId = DecodeQueryData($_GET['performanceEvaluationMasterId']);

    if ($performanceEvaluationMasterId) {
        // $schoolId = $currentSchoolId;
        $page_title = "Edit Performance Evaluation ";
        $bedCrumTitle = 'Edit';

        //Get CI Evalution Details

        $rowEvaluation = $objPerformance->GetPerformanceEvaluation($currentSchoolId, $performanceEvaluationMasterId);

        if ($rowEvaluation == '') {
            header('location:performanceEvaluationList.html');
            exit;
        }
        // echo '<pre>';
        // print_r($rowEvaluation);
        $performanceEvaluationMasterId = $rowEvaluation['performanceEvaluationMasterId'];
        $clinicianId = $rowEvaluation['clinicianId'];
        $studentId = $rowEvaluation['studentId'];
        $clinicianName = $rowEvaluation['clinicianName'];
        $studentName = $rowEvaluation['studentName'];
        $rotationId = $rowEvaluation['rotationId'];
        $rotationName = $rowEvaluation['rotationName'];
        $totalPoints = $rowEvaluation['totalPoints'];
        $percentage = $rowEvaluation['percentage'];
        $weeks = $rowEvaluation['weeks'];
        $preceptorId = ($preceptorNum == '') ? $rowEvaluation['preceptorId'] : $preceptorId;

        if ($preceptorId > 0) {
            $objExternalPreceptors = new clsExternalPreceptors();
            $externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
            $preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
            $preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
            $preceptorsignature = $preceptorFirstName . ' ' . $preceptorLastName;
        }

        $evaluationDate = isset($rowEvaluation['evaluationDate']) ? stripslashes($rowEvaluation['evaluationDate']) : '';
        if ($evaluationDate != '' && $evaluationDate != '0000-00-00 00:00:00') {
            $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
            $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
        } else
            $evaluationDate = '';

        $evaluatorSignatureDate = isset($rowEvaluation['evaluatorSignatureDate']) ? stripslashes($rowEvaluation['evaluatorSignatureDate']) : '';
        if ($evaluatorSignatureDate != '' && $evaluatorSignatureDate != '0000-00-00 00:00:00') {
            $evaluatorSignatureDate = converFromServerTimeZone($evaluatorSignatureDate, $TimeZone);
            $evaluatorDate = $evaluatorSignatureDate = date("m/d/Y", strtotime($evaluatorSignatureDate));
        } else
            $evaluatorDate = "";

        $studentSignatureDate = isset($rowEvaluation['studentSignatureDate']) ? stripslashes($rowEvaluation['studentSignatureDate']) : '';
        if ($studentSignatureDate != '' && $studentSignatureDate != '0000-00-00 00:00:00') {
            $studentSignatureDate = converFromServerTimeZone($studentSignatureDate, $TimeZone);
            $studentDate = $studentSignatureDate = date("m/d/Y", strtotime($studentSignatureDate));
        } else
            $studentDate = "";

        $preceptorSignatureDate = isset($rowEvaluation['preceptorSignatureDate']) ? stripslashes($rowEvaluation['preceptorSignatureDate']) : '';
        if ($preceptorSignatureDate != '' && $preceptorSignatureDate != '0000-00-00 00:00:00') {
            $preceptorSignatureDate = converFromServerTimeZone($preceptorSignatureDate, $TimeZone);
            $preceptorSignatureDate = date("m/d/Y", strtotime($preceptorSignatureDate));
        } else
            $preceptorSignatureDate = "";
    }
} else {
    $schoolId = $currentSchoolId;
    $page_title = "Add Performance Evaluation";
    $bedCrumTitle = 'Add';
}

//----------------------------//
//Get Clinician Names
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, $performanceRotationId);
unset($objClinician);

///----------------------//

$PerformanceSection = $objPerformance->GetSections($currentSchoolId);
if ($PerformanceSection != '') {
    $totalSection = mysqli_num_rows($PerformanceSection);
}

//Get Hospital Site
// $objHospitalSite = new clsHospitalSite();
// $hospitalSite = $objHospitalSite->GetAllHospitalSite($currentSchoolId);
// unset($objHospitalSite);

//Get Rotation Name
$RotationName = $objRotation->GetrotationDetails($performanceRotationId, $currentSchoolId);

$rotationtitle = $RotationName['title'] ?? '';
$endDate = $RotationName['endDate'] ?? '';

//For Schedule
$isSchedule = $RotationName['isSchedule'] ?? '';
$parentsRotationId = $RotationName['parentRotationId'] ?? '';
// if ($isSchedule)
//     $endDate = $objDB->GetSingleColumnValueFromTable('rotation', 'endDate', 'rotationId', $parentsRotationId);
// //-----------------
// $endDate = date('m/d/Y', strtotime($endDate));

$view = isset($_GET['view']) ? $_GET['view'] : '';
$bedCrumTitle = ($view) ? 'View' : $bedCrumTitle;

//For Student
$objStudent = new clsStudent();
$totalstudent = 0;
$rowsstudent = $objStudent->GetStudentsByRotation($currentSchoolId, $performanceRotationId);
if ($rowsstudent != '') {
    $totalstudent = mysqli_num_rows($rowsstudent);
}
unset($objStudent);
unset($objDB);

?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($page_title); ?></title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    <style type="text/css">
        .some-class {
            float: left;
            clear: none;
        }

        .input-group-addon {
            position: absolute;
            right: 7px;
            /* width: 100%; */
            z-index: 99;
            width: 35px;
            margin: auto;
            top: 5px;
            border-left: 1px solid #ccc;
            border-radius: 50% !important;
            padding: 10px -2px;
            height: 35px;
            /* background: #2e3192; */
            /* color: #fff; */
            color: #555;
            background: #f6f6ff;
            border: none;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .panel-default>.panel-heading {
            background-color: transparent !important;
        }

        .btn-success,
        .btn-default {
            padding: 8px 25px;
            border-radius: 10px;
        }

        .panel {
            border-radius: 14px !important;
        }

        .select2-container--default .select2-selection--single .select2-selection__rendered {
            line-height: 45px !important;
        }

        .required-select2 {
            border-left: solid 3px red !important;
            border-radius: 12px !important;
        }

        .select2-container--default .select2-selection--single {
            background-color: #f6f6ff !important;
            cursor: default !important;
            height: 45px !important;
            border-radius: 10px !important;
        }

        .select2-container--default .select2-selection--single {
            border: none !important;
        }

        .panel,
        .form-group {
            margin-bottom: 10px;
        }

        .bootstrap-datetimepicker-widget {
            border-radius: 12px !important;
        }

        .form-control {
            height: 45px;
        }

        /* Style for the collapsible content */
        .panel-collapse {
            display: none;
            /* Hidden by default */
            /* padding: 10px;
            border: 1px solid #ddd;
            background-color: #f9f9f9; */
        }

        /* Style for the collapsible button */
        .collapsible {
            /* background-color: #3498db;
            color: white;
            padding: 10px;
            border: none; */
            width: 100%;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            /* Align content horizontally */
        }

        .panel-heading {
            width: 100%;
        }

        /* Style for the arrow icons */
        .arrow-icon {
            transition: transform 0.3s;
        }

        .collapsible.expanded .arrow-icon i {
            transform: rotate(180deg);
        }

        @media screen and (max-width: 500px) {
            .panel-body ol {
                padding-left: 20px;
            }

            .panel-default>.panel-heading {
                padding-left: 5px;
            }

        }

        @media screen and (max-width: 575px) {
            .boxli ul li {
                width: 100%;
            }
        }
    </style>

</head>

<body>
    <?php if ($IsMobile == 0) { ?>

        <?php include('includes/header.php');
        ?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <?php
                        if ($performanceRotationId != '') { ?>
                            <li><a href="rotations.html?active=1">Rotations</a></li>
                            <?php if ($rotationtitle != '') { ?>
                                <li class="active"><a href="rotations.html?active=1"><?php echo ($rotationtitle); ?></a></li>
                            <?php } ?>
                            <li><a href="performanceEvaluationList.html?performanceRotationId=<?php echo EncodeQueryData($performanceRotationId); ?>">Performance Evaluation</a></li>
                        <?php } else { ?>
                            <li><a href="performanceEvaluationList.html">Performance Evaluation</a></li>
                        <?php } ?>
                        <li class="active"><?php echo ($bedCrumTitle); ?></li>
                    </ol>
                </div>

            </div>
        </div>
    <?php  } else  ?>

    <div class="container">

        <form id="frmevaluation" data-parsley-validate class="form-horizontal" method="POST" <?php if (isset($_GET['isSendToExternalPreceptor'])) { ?>action=" <?php echo $dynamicOrgUrl ?>/school/<?php echo $schoolSlug; ?>/clinician/performanceEvalsubmit.html?performanceEvaluationMasterId= <?php echo (EncodeQueryData($performanceEvaluationMasterId)); ?> &performanceRotationId=<?php echo EncodeQueryData($performanceRotationId); ?>" <?php } else {
                                                                                                                                                                                                                                                                                                                                                                                                                                                    if (isset($_GET['performanceRotationId'])) { ?> action="performanceEvalsubmit.html?performanceEvaluationMasterId=<?php echo (EncodeQueryData($performanceEvaluationMasterId)); ?> &performanceRotationId=<?php echo (EncodeQueryData($performanceRotationId)); ?>" <?php } else {  ?> action="performanceEvalsubmit.html?performanceEvaluationMasterId=<?php echo (EncodeQueryData($performanceEvaluationMasterId)); ?>" <?php }
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        } ?>>

            <div class="row">
                <!-- Mobile redirect -->
                <input type="hidden" name="IsMobile" id="IsMobile" value="<?php echo $IsMobile; ?>">

                <input type="hidden" name="preceptorId" id="preceptorId" value="<?php echo $preceptorId; ?>">
                <input type="hidden" name="preceptorNum" id="preceptorNum" value="<?php echo $preceptorNum; ?>">


                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="evaluationDate">Evaluation Date</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='evaluationDate' style="position: relative;">

                                <input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date evaluationDate disabledClass" value="<?php if ($startdatetime) {
                                                                                                                                                                                                echo $startdatetime;
                                                                                                                                                                                            } else {
                                                                                                                                                                                                echo ($evaluationDate);
                                                                                                                                                                                            } ?>" required data-parsley-errors-container="#error-txtDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="weeks">Weeks</label>
                        <div class="col-md-12">
                            <select id="weeks" name="weeks" class="form-control input-md  select2_single">
                                <option value="" selected>Select</option>
                                <option value="2" <?php if ($weeks == 2) echo 'selected="true"'; ?>>2</option>
                                <option value="4" <?php if ($weeks == 4) echo 'selected="true"'; ?>>4</option>
                                <option value="6" <?php if ($weeks == 6) echo 'selected="true"'; ?>>6</option>
                                <option value="8" <?php if ($weeks == 8) echo 'selected="true"'; ?>>8</option>

                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="Adult">Evaluator</label>
                        <div class="col-md-12">
                            <select id="cboclinician" name="cboclinician" class="form-control input-md required-input select2_single " required data-parsley-errors-container="#cboclinician-err">
                                <option value="" selected>Select</option>
                                <?php
                                if ($Clinician != "") {
                                    while ($row = mysqli_fetch_assoc($Clinician)) {
                                        $selClinicianId  = $row['clinicianId'];
                                        $firstname  = stripslashes($row['firstName']);
                                        $lastname  = stripslashes($row['lastName']);

                                        $name = $firstname . ' ' . $lastname;


                                ?>
                                        <option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>

                                <?php

                                    }
                                }

                                ?>
                            </select>
                            <div id="cboclinician-err"></div>
                        </div>
                    </div>

                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="evaluatorDate">Evaluator Signature Date</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='evaluatorDate' style="position: relative;">

                                <input type='text' name="evaluatorDate" id="evaluatorDate" class="form-control input-md required-input rotation_date evaluatorDate disabledClass" value="<?php echo ($evaluatorDate); ?>" data-parsley-errors-container="#error-evaluatorDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-evaluatorDate"></div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">

                <div class="col-md-6">

                    <div class="form-group">
                        <label class="col-md-12 control-label" for="cboStudent">Student</label>
                        <div class="col-md-12">
                            <select id="cboStudent" name="cboStudent" class="form-control input-md required-input select2_single disabledClass" required data-parsley-errors-container="#error-cboStudent">
                                <option value="" selected>Select</option>
                                <?php
                                if ($totalstudent > 0) {

                                    while ($row = mysqli_fetch_array($rowsstudent)) {
                                        $selstudentId = $row['studentId'];
                                        $firstName = $row['firstName'];
                                        $lastName = $row['lastName'];
                                        $rank = $row['rank'];
                                        $fullName = $firstName . ' ' . $lastName;

                                ?>
                                        <option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($fullName); ?></option>
                                <?php

                                    }
                                }
                                ?>
                            </select>
                            <div id="error-cboStudent"></div>
                        </div>
                    </div>

                </div>
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="studentDate">Student Signature Date</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' fpr='studentDate' style="position: relative;">

                                <input type='text' name="studentDate" id="studentDate" class="form-control input-md required-input rotation_date studentDate disabledClass" value="<?php echo ($studentDate); ?>" data-parsley-errors-container="#error-studentDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-studentDate"></div>
                        </div>
                    </div>
                </div>

            </div>
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="preceptorsignature">Technologist Signature</label>
                        <div class="col-md-12">
                            <div id='preceptorsignature'>
                                <input type='text' name="preceptorsignature" id="preceptorsignature" class="form-control input-md rotation_date disabledClass" value="<?php echo ($preceptorsignature);
                                                                                                                                                                        ?>" data-parsley-errors-container="#error-preceptorsignature" />
                            </div>
                            <div id="error-preceptorsignature"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="preceptorSignatureDate">Date Of Technologist Signature</label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class='input-group date w-full' id='preceptorSignatureDate' style="position: relative;">

                                <input type='text' name="preceptorSignatureDate" id="preceptorSignatureDate" class="form-control input-md required-input rotation_date preceptorSignatureDate disabledClass" value="<?php echo ($preceptorSignatureDate); ?>" data-parsley-errors-container="#error-preceptorSignatureDate" placeholder="MM-DD-YYYY" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-preceptorSignatureDate"></div>
                        </div>
                    </div>
                </div>
            </div>

            <input type="hidden" name="rotationid" id="rotationid" value="<?php echo ($rotationId) ?>">
            <input type="hidden" name="startdatetime" id="startdatetime" value="<?php echo ($startdatetime) ?>">
            <input type="hidden" name="standardPreceptors" id="standardPreceptors" value='<?php echo $standardPreceptors; ?>'>

            <!-- 1st SECTION div start -->
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <label class="col-md-12 control-label" for="instructions:"></label>
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class="panel panel-default border-12  mt-10 mb-10">
                                <div class="panel-body">
                                    <p>The clinician's ratings are based on the following categories. The rating scale for each category is<br>
                                        <b>10-Exceeds Expectations, 8-Meets Expectations, 7-Needs Improvement, 0-Failing.</b>
                                    </p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <div class="form-group">
                        <!-- <label class="col-md-12 control-label" for="instructions:"></label> -->
                        <div class="col-md-12 col-sm-12 col-xs-12">
                            <div class="panel-group" id="posts">
                                <?php
                                while ($row = mysqli_fetch_array($PerformanceSection)) {
                                    // print_r($row);exit;
                                    $sectionMasterId = $row['performanceSectionId'];
                                    $title = $row['title'];
                                    $sortOrder = $row['sortOrder'];
                                    $isTechnologist = $row['isTechnologist'];
                                    $sectionDiv = ($isTechnologist) ? 'technologistDiv' : 'evaluatorDiv';

                                ?>
                                    <div class="panel panel-default <?php echo $sectionDiv; ?>">
                                        <a class="collapsible" style="color: #000; text-decoration: none;" href="#CIevaluationSection" data-toggle="collapse" data-parent="#posts" id="collapse-link">
                                            <div class="panel-heading" style="display: flex; justify-content: space-between; align-items: center;">
                                                <h4 class="panel-title">
                                                    <?php echo  $title; ?>
                                                    <!-- <b>Radiologic Technology Program</b> -->
                                                </h4>
                                                <span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
                                            </div>
                                        </a>
                                        <div id="<?php echo $sectionMasterId; ?>" class="panel-collapse collapse boxli">

                                            <!-- <div class="panel-body"><?php echo '<b>' . $title . '</b>'; ?></div> -->
                                            <?php
                                            // for question
                                            $totalPerformanceevaluation = 0;
                                            $evaluationquestion = $objPerformance->GetAllPerformanceEvaluationQuestionMaster($currentSchoolId, $sectionMasterId);

                                            if ($evaluationquestion != '')
                                                $totalPerformanceevaluation = mysqli_num_rows($evaluationquestion);

                                            if ($totalPerformanceevaluation > 0) {
                                                while ($rowQuestion = mysqli_fetch_array($evaluationquestion)) {
                                                    // print_r($rowQuestion);exit;
                                                    // if (isset($_GET['performanceEvaluationMasterId']))
                                                    //     $performanceEvaluationMasterId = DecodeQueryData($_GET['performanceEvaluationMasterId']);
                                                    // else
                                                    //     $performanceEvaluationMasterId = 0;

                                                    $performanceQuestionId = $rowQuestion['performanceQuestionId'];
                                                    $questionText = $rowQuestion['questionText'];
                                                    $performanceQuestionType = $rowQuestion['performanceQuestionType'];
                                                    $qhtml = GetPerformanceEvaluationQuestionHtml($performanceQuestionId, $performanceQuestionType, $performanceEvaluationMasterId, $currentSchoolId);

                                                    $questionComment = '';

                                                    if ($performanceEvaluationMasterId > 0) {
                                                        if (!isset($objDB) || !is_object($objDB))
                                                            $objDB = new clsDB();

                                                        $questionComment = $objDB->GetSingleColumnValueFromTable('performanceevaluationdetails', 'comment', 'evaluationMasterId', $performanceEvaluationMasterId, 'schoolEvaluationQuestionId', $performanceQuestionId);
                                                        unset($objDB);
                                                    }


                                            ?>
                                                    <?php if ($sortOrder == 1) { ?>
                                                        <div class="panel-body allRadioForFirst">

                                                            <b> <?php echo ($questionText); ?> </b><br /><br />
                                                            <?php echo $qhtml; ?>
                                                            <?php //if ($performanceQuestionType == 2) { 
                                                            ?>
                                                            <br /><br />
                                                            <b> Comment: </b><br /><br />

                                                            <textarea name="textarea_<?php echo $performanceQuestionId; ?>" id="textarea_<?php echo $performanceQuestionId; ?>" rows="4" class="form-control"><?php echo $questionComment; ?></textarea>
                                                            <?php //} 
                                                            ?>
                                                        </div>
                                                    <?php } elseif ($sortOrder == 2) { ?>
                                                        <div class="panel-body allRadioForSecond">

                                                            <b> <?php
                                                                // Define the array of specific questionText values
                                                                $specialQuestions = [
                                                                    'Clinical Expenctations <br> CourseKey <br> Audit sheets <br> Canvas papers'
                                                                ];

                                                                // Check if the questionText is in the array of special questions
                                                                if (in_array($questionText, $specialQuestions)) {
                                                                    // Split the questionText by <br> and add bullets
                                                                    $parts = explode('<br>', $questionText);
                                                                    // Check if there is at least one part
                                                                    if (count($parts) > 0) {
                                                                        // Display the first part without bullet
                                                                        echo "<b>" . trim(array_shift($parts)) . "</b><br><br>";

                                                                        // Display the remaining parts with bullets
                                                                        if (count($parts) > 0) {
                                                                            echo "<ul>";
                                                                            foreach ($parts as $part) {
                                                                                echo  "<li>" . trim($part) . "</li>";
                                                                            }
                                                                            echo "</ul>";
                                                                        }
                                                                    }
                                                                } else {
                                                                    echo "<b> $questionText </b>";
                                                                }
                                                                ?> </b><br /><br />

                                                            <?php echo $qhtml; ?>
                                                            <?php //if ($performanceQuestionType == 2) { 
                                                            ?>
                                                            <br /><br />
                                                            <b> Comment: </b><br /><br />

                                                            <textarea name="textarea_<?php echo $performanceQuestionId; ?>" id="textarea_<?php echo $performanceQuestionId; ?>" rows="4" class="form-control"><?php echo $questionComment; ?></textarea>
                                                            <?php //} 
                                                            ?>
                                                        </div>
                                                    <?php } elseif ($sortOrder == 3) { ?>

                                                        <div class="panel-body allRadioForSecond">

                                                            <b> <?php
                                                                // Define the array of specific questionText values
                                                                $specialQuestions = [
                                                                    'Clinical Expenctations <br> CourseKey <br> Audit sheets <br> Canvas papers'
                                                                ];

                                                                // Check if the questionText is in the array of special questions
                                                                if (in_array($questionText, $specialQuestions)) {
                                                                    // Split the questionText by <br> and add bullets
                                                                    $parts = explode('<br>', $questionText);
                                                                    // Check if there is at least one part
                                                                    if (count($parts) > 0) {
                                                                        // Display the first part without bullet
                                                                        echo "<b>" . trim(array_shift($parts)) . "</b><br><br>";

                                                                        // Display the remaining parts with bullets
                                                                        if (count($parts) > 0) {
                                                                            echo "<ul>";
                                                                            foreach ($parts as $part) {
                                                                                echo  "<li>" . trim($part) . "</li>";
                                                                            }
                                                                            echo "</ul>";
                                                                        }
                                                                    }
                                                                } else {
                                                                    echo "<b> $questionText </b>";
                                                                }
                                                                ?> </b><br /><br />

                                                            <?php echo $qhtml; ?>
                                                            <?php //if ($performanceQuestionType == 2) { 
                                                            ?>
                                                            <br /><br />
                                                            <b> Comment: </b><br /><br />

                                                            <textarea name="textarea_<?php echo $performanceQuestionId; ?>" id="textarea_<?php echo $performanceQuestionId; ?>" rows="4" class="form-control"><?php echo $questionComment; ?></textarea>
                                                            <?php //} 
                                                            ?>
                                                        </div>
                                                    <?php } ?>
                                            <?php
                                                }
                                            }
                                            ?>
                                        </div>
                                    </div>
                                <?php
                                }

                                ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row m-0">
                <!-- <label class="col-md-2 control-label" for="instructions:"></label> -->
                <div class="col-md-12 p-0">

                    <input type="hidden" name="totalAvg" class="totalAvg" value="<?php echo ($totalAvg); ?>">
                    <input type="hidden" name="totalPoints" class="totalPoints" value="<?php echo ($totalPoints); ?>">
                    <input type="hidden" name="totalTechnologistScore" class="totalTechnologistScore" value="">
                    <input type="hidden" name="totalEvaluatorScore" class="totalEvaluatorScore" value="">

                    <div class="grid-layout">

                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										/ background: #EEF5FF; /
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/person.png" alt="briefcase">
                                </div>
                                <p class="card-title">
                                    Technologist Score
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="totalTechnologistScore">0</span><span class="card-count-span">&nbsp;/&nbsp;70</span>
                                </p>
                            </div>
                        </div>


                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/person.png" alt="briefcase">
                                </div>
                                <p class="card-title">
                                    Evaluator Score
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="totalEvaluatorScore">0</span><span class="card-count-span">&nbsp;/&nbsp;30</span>
                                </p>
                            </div>
                        </div>

                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										/ background: #EEF5FF; /
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/trachestomy-care.png" alt="briefcase">
                                </div>
                                <p class="card-title">
                                    Total Eval Points
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="totalPoints">0</span><span class="card-count-span">&nbsp;/&nbsp;100</span>
                                </p>
                            </div>
                        </div>

                        <div class="item card-body">
                            <div style="display: flex; align-items: center;">
                                <div class="card-image-section" style="
										/ background: #EEF5FF; /
										background: rgba(1, 167, 80, 0.08);">
                                    <img src="<?php echo ($dynamicOrgUrl); ?>/student/assets/images/HFOV.png" alt="award">
                                </div>
                                <p class="card-title">
                                    Total Percentage
                                </p>
                            </div>
                            <div>
                                <p class="card-count">
                                    <span class="card-count-span" id="totalAvg">0</span>
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row" style="margin: 0;">
                <div class="col-md-12">
                    <div class="form-group">
                        <!-- <label class="col-md-2 control-label"></label> -->
                        <div class="col-md-12 col-sm-12 col-xs-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
                            <?php //if ((strtotime($currentDate)) < (strtotime($endDate))) {
                            if ($view == '') {
                            ?>
                                <button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
                            <?php }
                            //} 
                            ?>
                            <?php if ($IsMobile) { ?>
                                <a type="button" href="<?php echo BASE_PATH ?>/webRedirect.html?status=Cancel&type=performanceEval" class="btn btn-default">Cancel</a>
                            <?php } elseif ($performanceRotationId > 0) { ?>
                                <a type="button" href="performanceEvaluationList.html?performanceRotationId=<?php echo EncodeQueryData($performanceRotationId); ?>" class="btn btn-default">Cancel</a>
                            <?php } else { ?>
                                <a type="button" href="performanceEvaluationList.html" class="btn btn-default">Cancel</a>
                            <?php } ?>
                        </div>
                    </div>
                </div>
            </div>
        </form>


    </div>

    <?php include('includes/footer.php'); ?>

    <?php //print_r($_SESSION); 
    ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.cascadingdropdown.js"></script>

    <script type="text/javascript">
        $(window).load(function() {

            $(".isAllRadioButton").trigger('click');

            $('#frmevaluation').parsley().on('field:validated', function() {
                    var ok = $('.parsley-error').length === 0;
                })
                .on('form:submit', function() {
                    ShowProgressAnimation();
                    $('#frmevaluation').find(':disabled').removeAttr('disabled');
                    //for edit
                    if (evaluatorSignatureDate !== '' && technologistSignatureDate !== '') {
                        $('#studentDate').removeClass('disabledClass');
                    }
                    return true; // Don't submit form for this demo
                });

            $('#evaluationDate').datetimepicker({
                format: 'MM/DD/YYYY',
                defaultDate: moment()
            });
            $('#studentDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });
            $('#evaluatorDate').datetimepicker({
                format: 'MM/DD/YYYY'
            });

            //for searching dropdown
            $(".select2_single").select2();
            $('#select2-cbohospitalsites-container').addClass('required-select2');
            $('#select2-cboclinician-container').addClass('required-select2');
            $('#select2-cboStudent-container').addClass('required-select2');

            $("input[type=radio]").attr('disabled', true);
            $("textarea").attr('disabled', true);
            $(".disabledClass").attr('disabled', true);

            //Remove required for technologist and Clinician Sections
            $('.evaluatorDiv input, .evaluatorDiv textarea, .evaluatorDiv select').prop('required', false);
            $('.technologistDiv input, .technologistDiv textarea, .technologistDiv select, .evaluatorDiv select').prop('required', false);

            var view = '<?php echo $view; ?>';
            if (view == 'V') {
                $('#frmevaluation input, #frmevaluation textarea, #frmevaluation select').prop('disabled', true);
            }
        });

        $(document).ready(function() {
            //For Edit
            var bedCrumTitle = '<?php echo $bedCrumTitle; ?>';
            console.log(bedCrumTitle);
            if (bedCrumTitle == 'Edit') {
                $('#weeks').addClass('disabledClass');
                $('#cboclinician').addClass('disabledClass');

            }

            var evaluatorSignatureDate = '<?php echo $evaluatorSignatureDate; ?>';
            var technologistSignatureDate = '<?php echo $preceptorSignatureDate; ?>';
            if (evaluatorSignatureDate !== '' && technologistSignatureDate !== '') {
                $('#studentDate').removeClass('disabledClass');
            }
        });

        $(document).ready(function() {

            //For First Section
            $(document).ready(function() {

                $(".allRadioForFirst").click(function() {
                    var sumCheckedButton = 0;
                    var checkedNACount = 0;
                    var avgAttendance = 0;

                    $(".allRadioForFirst input[type=radio]:checked").each(function() {
                        var checkedText = $(this).parent().text().trim();
                        // Extract the numeric part from the text (assuming the numeric part is at the beginning)
                        var checkedRadio = parseInt(checkedText.match(/^\d+/));

                        if (checkedRadio != 10 && checkedRadio != 8 && checkedRadio != 7 && checkedRadio != 0) {
                            checkedRadio = 0;
                            checkedNACount++;
                        }
                        sumCheckedButton += parseInt(($.trim(checkedRadio)));
                    });

                    $("#totalTechnologistScore").text(sumCheckedButton); // Update total score
                    $(".totalTechnologistScore").val(sumCheckedButton);
                });
            });
            $(window).ready(function() {
                $(".allRadioForFirst").trigger('click');
            });

            //For Second Section
            $(document).ready(function() {

                $(".allRadioForSecond").click(function() {
                    var sumCheckedButton1 = 0;
                    var checkedNACount = 0;
                    var avgAttendance = 0;

                    $(".allRadioForSecond input[type=radio]:checked").each(function() {
                        var checkedText = $(this).parent().text().trim();
                        // Extract the numeric part from the text (assuming the numeric part is at the beginning)
                        var checkedRadio = parseInt(checkedText.match(/^\d+/));

                        if (checkedRadio != 10 && checkedRadio != 8 && checkedRadio != 7 && checkedRadio != 0) {
                            checkedRadio = 0;
                            checkedNACount++;
                        }
                        sumCheckedButton1 += parseInt(($.trim(checkedRadio)));
                    });

                    $("#totalEvaluatorScore").text(sumCheckedButton1); // Update total score
                    $(".totalEvaluatorScore").val(sumCheckedButton1);
                });
            });
            $(window).ready(function() {
                $(".allRadioForSecond").trigger('click');
            });

            //For All Total and Avg
            $(document).ready(function() {
                $("#frmevaluation").click(function() {
                    var sumCheckedButton = 0;
                    var checkedNACount = 0;
                    var avgAttendance = 0;
                    $("#frmevaluation input[type=radio]:checked").each(function() {
                        var checkedText = $(this).parent().text().trim();
                        var checkedRadio = parseInt(checkedText.match(/^\d+/));

                        if (checkedRadio != 10 && checkedRadio != 8 && checkedRadio != 7 && checkedRadio != 0) {
                            checkedRadio = 0;
                            checkedNACount++;
                        }
                        sumCheckedButton += parseInt(($.trim(checkedRadio)));
                    });
                    var allradioButtons = $("#frmevaluation input[type='radio']:checked").length;
                    allcheckedCount = allradioButtons - checkedNACount;
                    if (allcheckedCount > 0)
                        avgAttendance = Math.round((sumCheckedButton / (10 * allcheckedCount)) * 100);

                    $("#totalPoints").text(sumCheckedButton);
                    $(".totalPoints").val(sumCheckedButton);
                    $("#totalAvg").text(avgAttendance + ' %');
                    $(".totalAvg").val(avgAttendance);
                });
            });

            $(window).ready(function() {
                $("#frmevaluation").trigger('click');
            });
        });
    </script>

    <script>
        // Get all collapsible button elements
        var buttons = document.querySelectorAll(".collapsible");
        var contents = document.querySelectorAll(".panel-collapse");

        // Add click event listeners to all buttons
        buttons.forEach(function(button, index) {
            button.addEventListener("click", function() {
                // Check if the content is currently expanded
                var isExpanded = contents[index].style.display === "block";

                // Close all sections
                contents.forEach(function(content) {
                    content.style.display = "none";
                });

                // Reset the "expanded" class for all buttons
                buttons.forEach(function(btn) {
                    btn.classList.remove("expanded");
                });

                // Toggle the content for the clicked section
                if (!isExpanded) {
                    contents[index].style.display = "block";
                    button.classList.add("expanded");
                }
            });
        });
    </script>
</body>

</html>