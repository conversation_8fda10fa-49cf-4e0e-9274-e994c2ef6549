<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsSchool.php');
include('../class/clsCourses.php');
include('../class/clsHospitalSite.php');
include('../class/clsStudent.php');
include('../class/clsRotation.php');
include('../class/clsRotationDetails.php');

include('../setRequest.php');
$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = '';
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

$schoolId = 0;
$courseId = 0;
$locationId = 0;
$rotationId = 0;
$loggedUserLocationId = $_SESSION["loggedUserLocation"];
$transchooldisplayName = '';
$default_rotationId = '';
if (isset($_GET['schoolId'])) //Edit Mode
{
    $schoolId = $_GET['schoolId'];
    $schoolId = DecodeQueryData($schoolId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

if (isset($_GET['courseId'])) {
    $courseId = $_GET['courseId'];
    $courseId = DecodeQueryData($courseId);
}

$isScheduleRotation  = isset($_GET['isSchedule']) ? DecodeQueryData($_GET['isSchedule'])  : 0;
$tableThTitle = $isScheduleRotation ? 'Schedule' : 'Subrotation';
$title = "Student Shedule | " . $transchooldisplayName;

//CREATE OBJECT
$objRotation = new clsRotation();
$objRotationDetails = new clsRotationDetails();

if (isset($_GET['rotationId']))
    $default_rotationId = DecodeQueryData($_GET['rotationId']);

$totalRotations = 0;
// $rowsRotations = $objRotationDetails->GetAllAssignSubRotationStudents($default_rotationId);
$rowsRotations = $objRotation->GetAllSubRotation($currentSchoolId, $locationId = 0, $courseId = 0, $default_rotationId);


if ($rowsRotations != '') {
    $totalRotations = mysqli_num_rows($rowsRotations);
}
$rowsrotation = $objRotation->GetrotationTitleForInteraction($default_rotationId, $schoolId);

$rotationtitle = isset($rowsrotation['title']) ? $rowsrotation['title'] : '';


$reportType = "StudentRotationAssignList";

/*$objCourses = new clsCourses();
	$courses = $objCourses->GetCousesByLocation($currentSchoolId, $loggedUserLocationId);	
	unset($objCourses);*/

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/fullcalendar/fullcalendar_main.css" />

    <!-- <link href="https://cdn.jsdelivr.net/npm/fullcalendar@5.5.0/main.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/jquery@3.6.0/dist/jquery.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/fullcalendar@5.5.0/main.js"></script> -->


    <style>
        a {
            text-decoration: none !important;
        }

        .fc-direction-ltr {
            border: 1px solid #8080802b;
            padding: 8px;
            border-radius: 10px;
            margin-top: 20px;
            box-shadow: rgba(17, 17, 26, 0.05) 0px 1px 0px, rgba(17, 17, 26, 0.1) 0px 0px 8px;
        }

        .fc {
            font-size: 1em;
        }

        .fc .fc-toolbar-title {
            font-size: 1.2em;
            margin: 0;
            color: rgb(45, 105, 182);
            font-weight: 600;
        }

        .fc .fc-button-primary:disabled,
        .fc .fc-button {
            color: #fff;
            background-color: rgb(45, 105, 182) !important;
            border-color: rgb(45, 105, 182);
        }

        .fc .fc-button-primary:hover {
            border: none;
        }

        .fc .fc-button {
            font-weight: 400;
            padding: 0.4em 0.65em;
            font-size: .8em;
            line-height: 1.5;
            border-radius: 0.25em;
            background: transparent;
        }

        .fc-theme-standard td,
        .fc-theme-standard th,
        .fc-theme-standard .fc-scrollgrid {
            border: none;
        }

        .fc th {
            /* text-align: left; */
            text-align: center;
        }

        .fc-toolbar-chunk:nth-child(3) {
            display: flex;
        }

        .fc .fc-scrollgrid-section-body table,
        .fc .fc-scrollgrid-section-footer table,
        .fc .fc-daygrid-body,
        .fc .fc-scrollgrid-section table {
            width: 100% !important;
        }

        .fc .fc-view-harness {
            height: 162.074px !important;
        }

        .fc-direction-ltr {
            background: #ffffff !important;
        }

        .fc .fc-button-primary {
            border: none;
        }

        .fc .fc-bg-event,
        .fc .fc-non-business,
        .fc .fc-highlight {
            left: 4px;
        }

        .fc .fc-col-header-cell-cushion {
            color: #337ab7;
            background-color: transparent;
            padding: 2px 0px;
            font-weight: 600;
        }

        .fc .fc-daygrid-day-number {
            position: relative;
            z-index: 4;
            padding: 4px;
        }

        .fc .fc-bg-event {
            background: rgb(143, 223, 130);
            background: var(--fc-bg-event-color, rgb(143, 223, 130));
            opacity: 0.3;
            /* / opacity: var(--fc-bg-event-opacity, 0.3); / */
            color: #ffffff;
            border-radius: 50%;
            width: 28px;
            height: 28px;
            /* / display: flex; /
            / justify-content: end; / */
            float: right;
        }

        .fc .fc-scroller-liquid-absolute,
        .fc-scroller {
            overflow: hidden !important;
        }

        .fc .fc-daygrid-day-frame {
            position: relative;
            min-height: 100%;
            height: 28px;
        }

        .fc .fc-daygrid-day-top {
            display: flex;
            flex-direction: row;
            justify-content: center;
        }
    </style>

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="rotations.html">Rotation</a></li>
                    <li class="active"><?php echo ($rotationtitle); ?></li>
                    <li class="active">Schedule</li>
                </ol>
            </div>
            <!-- <div class="pull-right">
                <form id="frmexportreport" data-parsley-validate class="form-horizontal" method="POST" action="<?php if ($isScheduleRotation) { ?>exportStudentScheduleList.html?Type=<?php echo ($reportType); ?>&rotationId=<?php echo (EncodeQueryData($default_rotationId));
                                                                                                                                                                                                                            } else { ?>exportStudentRotationAssignList.html?Type=<?php echo ($reportType); ?>&rotationId=<?php echo (EncodeQueryData($default_rotationId));
                                                                                                                                                                                                                                                                                                                        } ?>" enctype="multipart/form-data">
                    <input type="hidden" name="cboreporttype" value="<?php echo ($reportType); ?>">
                    <input type="submit" name="btnStudentExport" id="btnStudentExport" class="btn btn-link" value="Export to Excel">
                </form>
            </div> -->

        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Rotation deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>



        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th style="text-align: center">Student Name</th>
                    <th style="text-align: center"><?php echo $tableThTitle ?></th>
                    <th style="text-align: center">Course </th>
                    <th style="text-align: center">Start Date</th>
                    <th style="text-align: center">Start Time</th>
                    <th style="text-align: center">End Date </th>
                    <th style="text-align: center">End Time </th>
                    <th style="text-align: center">Duration</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $scheduledatesArray = array();
                if ($totalRotations > 0) {

                    while ($row = mysqli_fetch_array($rowsRotations)) {

                        // $firstName = $row['firstName'];
                        $rotationId = $row['rotationId'];

                        // $lastName = $row['lastName'];
                        // $Rankname = $row['Rankname'];
                        $title = $row['title'];
                        $startDateTime = $row['startDate'];

                        $startDateTime = converFromServerTimeZone($startDateTime, $TimeZone);

                        $startDate = date('m/d/Y', strtotime($startDateTime));
                        $startTime = date('h:i A', strtotime($startDateTime));
                        $endDateTime = $row['endDate'];
                        $endDateTime = converFromServerTimeZone($endDateTime, $TimeZone);
                        $endDate = date('m/d/Y', strtotime($endDateTime));
                        $endTime = date('h:i A', strtotime($endDateTime));

                        $duration = $row['duration'];
                        $courseTitle = $row['courseTitle'];
                        $hospitalSite = $row['hospitalSite'];

                        $shortTitlelen = strlen($courseTitle);

                        if ($shortTitlelen > 15) {

                            $shortCourseTitle = substr($courseTitle, 0, 10);
                            $shortCourseTitle .= '...';
                        } else {
                            $shortCourseTitle = $courseTitle;
                        }
                        $objDB = new clsDB();
                        $isSchedule = $objDB->GetSingleColumnValueFromTable('rotation', 'isSchedule', 'rotationId', $rotationId);
                        unset($objDB);
                        $repeateddays = $objRotation->GetRotationRepeatDays($rotationId);
                        $scheduledates = '';

                        $rotationScheduleDates = $objRotation->GetScheduleDateById($rotationId);
                        if ($rotationScheduleDates != '') {
                            $scheduledates = explode(',', $rotationScheduleDates);
                            $scheduledatesArray["calendar_" . $rotationId] = $scheduledates;
                        }
                        $rowsRotationsDetails = $objRotationDetails->GetAllAssignStudentsDetails($rotationId);
                        $rotationStudentCount = ($rowsRotationsDetails != '') ? mysqli_num_rows($rowsRotationsDetails) : 0;
                        if ($rotationStudentCount == 0) {
                            continue;
                        }

                ?>
                        <tr>


                            <td>

                                <table id="subdatatable" class="subdatatable table dt-responsive table-bordered nowrap table-hover" cellspacing="0" width="100%">
                                    <thead>
                                        <tr>

                                            <th style="text-align: center">First Name</th>
                                            <th style="text-align: center">Last Name </th>
                                            <th style="text-align: center">Rank</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php
                                        if ($rowsRotationsDetails != '') {
                                            while ($row = mysqli_fetch_array($rowsRotationsDetails)) {
                                                $firstName = $row['firstName'];
                                                $lastName = $row['lastName'];
                                                $Rankname = $row['Rankname'];



                                        ?>
                                                <tr>
                                                    <td><?php echo $firstName; ?></td>
                                                    <td><?php echo $lastName; ?></td>
                                                    <td><?php echo $Rankname; ?></td>
                                                </tr>
                                        <?php
                                            }
                                        }
                                        ?>
                                    <tbody>
                                </table>
                            </td>
                            <td style="white-space: normal; word-wrap: break-word;  width:100%;"><?php echo $title; ?> <br>
                                <small>Hospital: <?php echo $hospitalSite; ?></small> <br>
                                <?php if ($isSchedule) { ?>

                                <?php } else { ?>
                                    <small>Repeat Days: <?php echo $repeateddays; ?></small>
                                <?php } ?>
                                <div style="min-width: 255px;"  id="calendar_<?php echo $rotationId; ?>" isScheduleDates='<?php echo $rotationScheduleDates; ?>'></div>

                            </td>
                            <td title="<?php echo $courseTitle; ?>"><?php echo $shortCourseTitle; ?></td>
                            <td><?php echo $startDate; ?></td>
                            <td><?php echo $startTime; ?></td>
                            <td><?php echo $endDate; ?></td>
                            <td><?php echo $endTime; ?></td>
                            <td align="center"><?php echo $duration; ?></td>
                        </tr>
                <?php
                    }
                    $calendarIds = array_keys($scheduledatesArray);
                }
                ?>
            </tbody>
        </table>
    </div>
    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>

    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/fullcalendar/fullcalendar_main.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";



        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();


        });

        var current_datatable = $("#datatable-responsive").DataTable({

            "ordering": true,
            "order": [
                [1, "asc"]
            ],

            "aoColumns": [{
                "sWidth": "20%",
                "bSortable": true

            }, {
                "sWidth": "20%",
                "sClass": "alignCenter",
                "bSortable": true

            }, {
                "sWidth": "10%",
                "sClass": "alignCenter",
                "bSortable": true

            }, {
                "sWidth": "10%",
                "sClass": "alignCenter",
                "bSortable": true

            }, {
                "sWidth": "10%",
                "sClass": "alignCenter",
                "bSortable": true

            }, {
                "sWidth": "10%",
                "sClass": "alignCenter",
                "bSortable": true

            }, {
                "sWidth": "10%",

            }, {
                "sWidth": "10%",
                "sClass": "alignCenter",
                "bSortable": false
            }]


        });
        var subdatatable = $(".subdatatable").DataTable({
            "searching": false,
            "paging": false,
            "info": false

        });


        // Wait for the DOM content to be loaded before executing the script
        document.addEventListener('DOMContentLoaded', function() {
            // Call the renderData function when the DOM is fully loaded
            renderData();
        });

        // Function to render calendar events
        function renderData() {
            // Use setTimeout to delay execution to ensure all elements are loaded
            setTimeout(function() {
                // Get all elements in the DOM
                var allElements = document.getElementsByTagName("*");
                var calendarIds = []; // Array to store calendar IDs

                // Loop through all elements in the DOM
                for (var i = 0; i < allElements.length; i++) {
                    var element = allElements[i];
                    var id = element.id;

                    // Check if the element has an ID and if it starts with "calendar_"
                    if (id && id.startsWith("calendar_")) {
                        calendarIds.push(id); // Add calendar ID to the array
                        var calendarEl = document.getElementById(id); // Get calendar element by ID
                        var dates = '';
                        var dates = $('#' + id).attr('isScheduleDates'); // Get schedule dates attribute
                        // Check if schedule dates attribute is not empty
                        var eventData = []; // Array to store event data for FullCalendar
                        if (dates != '') {
                            var datesList = dates.split(','); // Split schedule dates into an array

                            // Loop through each date in the datesList array
                            datesList.forEach(function(date) {
                                var parts = date.split('/'); // Split date by '/'
                                // Rearrange parts to form the ISO format (YYYY-MM-DD)
                                var isoDate = parts[2] + '-' + parts[0].padStart(2, '0') +
                                    '-' + parts[1].padStart(2, '0');
                                // Push event data (start date) to eventData array
                                eventData.push({
                                    start: isoDate
                                });
                            });

                        }
                        // Create FullCalendar instance with specified options
                        var calendar = new FullCalendar.Calendar(calendarEl, {
                            initialView: 'dayGridMonth', // Initial view of the calendar
                            events: eventData, // Event data to be displayed on the calendar
                            eventDisplay: 'background', // Display events as background
                            eventBackgroundColor: '#2e3192', // Background color of events
                            selectable: false, // Disable date selection
                            // Callback function when a date is selected
                            select: function(info) {
                                // alert('Selected Date: ' + info.startStr); // Show selected date in alert
                            }
                        });
                        calendar.render(); // Render the calendar

                    }
                }
            }, 100); // Set timeout to ensure all elements are loaded before execution
        }

        // render calendar event after datatable event changed
        $('#datatable-responsive').on('draw.dt', function() {
            renderData();
        });
    </script>


</body>

</html>