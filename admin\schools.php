<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../setRequest.php');
include_once('../class/clsSchool.php');
include_once('../class/clsCountryStateMaster.php');
include('../class/clsSystemUserRolePermission.php');

//CREATE OBJECT
$objSchool = new clsSchool();
$totalSchool = 0;
$activebtn = 1;
$clsBtnActive = '';
$clsBtnInActive = '';
$clsBtnActive = '';

$userPermissions = '';
$schoolsArray = $settingsArray = $headersArray = '';
$schools = $settings = $headers = '';

if (isset($_GET['active']))
    $activebtn = DecodeQueryData($_GET['active']);


if ($activebtn == 1)
    $clsBtnActive = "active";
elseif ($activebtn == 0)
    $clsBtnInActive = "active";
elseif ($activebtn == 2) {
    $clsBtnActiveAll = "active";
    $activebtn = '';
} else
    $clsBtnActive = "active";


if ($currentSchoolId == 1) {
    // Check if the user's role ID is available in the session
    $loggedUserRoleId = $_SESSION['loggedUserRoleId'];

    // Initialize a new instance of the clsSystemUserRolePermission class
    $objSystemUserRolePermission = new clsSystemUserRolePermission();

    // Get system user role permissions based on the user's role ID
    $userPermissions = $objSystemUserRolePermission->GetSystemUserRolePermissions($loggedUserRoleId);

    // Check if any user permissions were returned
    if ($userPermissions != '') {
        // Extract the user permissions for schools, headers, and settings
        $schools = $userPermissions['schools'];
        $headers = $userPermissions['headers'];
        $settings = $userPermissions['settings'];

        // Convert the user permissions for schools, headers, and settings into arrays
        $schoolsArray = explode(",", $schools);
        $headersArray = explode(",", $headers);
        $settingsArray = explode(",", $settings);
    }
}
$loggedUserId = $_SESSION["loggedUserId"];
$objDB = new clsDB();
$isPrimaryUser = 0;
$isPrimaryUser = $objDB->GetSingleColumnValueFromTable('systemusermaster', 'isPrimaryUser', 'systemUserMasterId', $loggedUserId);
unset($objDB);
if ($isPrimaryUser)
    $rowsSchool = $objSchool->GetAllSchools($activebtn);
else
    $rowsSchool = $objSchool->GetAllSchoolsBySchoolIds($activebtn, $schools);

if ($rowsSchool != '') {
    $totalSchool = mysqli_num_rows($rowsSchool);
}
unset($objSchool);
$reportType = 'School_Report';
$currentSchoolId;


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Schools</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active">Schools</li>
                </ol>
            </div>
            <?php if ($isPrimaryUser) { ?>
                <div class="pull-right">
                    <a class="btn btn-link" href="addschool.html">Add</a>
                </div>
                <div class="pull-right">
                    <form id="frmexportreport" data-parsley-validate class="form-horizontal" method="POST" action="exportschoolreport.html?Type=<?php echo ($reportType); ?>&currentSchoolId=<?php echo (EncodeQueryData($currentSchoolId)); ?>" enctype="multipart/form-data">
                        <input type="hidden" name="cboreporttype" value="<?php echo ($reportType); ?>">
                        <input type="submit" name="btnSchoolExport" id="btnSchoolExport" class="btn btn-link" value="Export to Excel">|
                    </form>
                </div>
            <?php } ?>
        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> School added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> School updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "StatusUpdated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> School status updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <div class="row margin_bottom_ten">
            <div class="btn-group pull-right" role="group" aria-label="First group">
                <a role="button" class="btn btn-primary <?php echo $clsBtnActive; ?>" href="schools.html?active=<?php echo EncodeQueryData(1); ?>">Active</a>
                <a role="button" class="btn btn-primary  <?php echo $clsBtnInActive; ?>" href="schools.html?active=<?php echo EncodeQueryData(0); ?>">Inactive</a>
                <a role="button" class="btn btn-primary <?php echo $clsBtnActiveAll; ?>" href="schools.html?active=<?php echo EncodeQueryData(2); ?>">All</a>
            </div>
        </div>

        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Name</th>
                    <th>Contact Person</th>
                    <th>Address</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalSchool > 0) {
                    $objLocation = new clsCountryStateMaster();
                    $objSchool = new clsSchool();
                    //$objSchoolUsers = new clsSchoolUsers();
                    //$objSchoolAccount = new clsSchoolAccount();
                    while ($row = mysqli_fetch_array($rowsSchool)) {
                        $schoolId = $row['schoolId'];
                        $schoolName = stripslashes($row['title']);

                        //$schoolType = stripslashes($row['schoolType']);
                        $contactPerson = stripslashes($row['contactPerson']);
                        $email = stripslashes($row['email']);
                        $phone = stripslashes($row['phone']);
                        $cellPhone = stripslashes($row['cellPhone']);
                        $address1 = stripslashes($row['address1']);
                        $address2 = stripslashes($row['address2']);
                        $stateId = stripslashes($row['stateId']);
                        $city = stripslashes($row['city']);
                        $zip = stripslashes($row['zip']);
                        $slug = stripslashes($row['slug']);
                        $displayName = stripslashes($row['displayName']);
                        $code = stripslashes($row['code']);
                        $isSuperAdmin = $row['isSuperAdmin'];
                        $isCopiedDefaultData = $row['isCopiedDefaultData'];
                        $isDefaultCopyProcessing = $row['isDefaultCopyProcessing'];
                        $isActiveCheckoffForStudent = $row['isActiveCheckoffForStudent'];
                        $contractStartDate = stripslashes($row['contractStartDate']);
                        $createdDate = stripslashes($row['createdDate']);
                        $schoolCheckoff = '';
                        if ($isActiveCheckoffForStudent == 0) {
                            $schoolCheckoff = 'Advanced';
                            $bgcolor = '#1a8cff';
                        } else if ($isActiveCheckoffForStudent == 1) {
                            $schoolCheckoff = 'Standard';
                            $bgcolor = '#11a8a8de';
                        } else if ($isActiveCheckoffForStudent == 2) {
                            $schoolCheckoff = 'Military';
                            $bgcolor = '#c44dff';
                        }
                        $dynamicLoginURL = BASE_PATH . '/school/' . $slug . '/admin/index.html';

                        //Get Total Users
                        $allSchoolUsers = $objSchool->GetAllSchoolUsersCount($schoolId);
                        $totalSchoolUserCount = 0;
                        $totalClinicianCount = 0;
                        $totalStudentCount = 0;


                        if ($allSchoolUsers != '') {
                            $totalSchoolUserCount = $allSchoolUsers['totalSchoolUserCount'];
                            $totalClinicianCount = $allSchoolUsers['ClinicianCount'];
                            $totalStudentCount = $allSchoolUsers['StudentCount'];
                        }

                        //Read State 
                        $state  = $objLocation->GetLocationName($stateId);

                        //Read Country
                        $countryId = $objLocation->GetParentIdFromChildId($stateId);
                        $country = $objLocation->GetLocationName($countryId);

                        $fullAddress = '';
                        if ($address1 != '') {
                            $fullAddress .= $address1;
                        }
                        if ($address2 != '') {
                            $fullAddress .= '<br>' . $address2;
                        }

                        if ($city != '') {
                            $fullAddress .= ', ' . $city;
                        }
                        if ($zip != '') {
                            $fullAddress .= ' - ' . $zip;
                        }
                        if ($state != '') {
                            $fullAddress .= '<br>' . $state;
                        }
                        if ($country != '') {
                            $fullAddress .= ' ' . $country;
                        }
                        $schoolcurrentstatus = '';
                        $IsPublished = $row['isActive'];
                        $displayStatus = "";
                        $updateStatus = "0";
                        $buttoncss = "btn-primary";
                        $backgroundcolor = "";

                        if ($IsPublished == "1") {
                            $displayStatus = "Active";
                            $updateStatus = "0";
                            $buttoncss = "text-primary";
                        } else {
                            $displayStatus = "Inactive";
                            $updateStatus = "1";
                            $buttoncss = "text-warning";
                        }
                        if ($isCopiedDefaultData == 1) {
                            $schoolcurrentstatus = "Completed";
                            $statuscss = "badge badge-success";
                            $backgroundcolor = "#009933";
                        } elseif ($isDefaultCopyProcessing == 1) {
                            $schoolcurrentstatus = "Inprogress";
                            $statuscss = "badge badge-warning";
                            $backgroundcolor = "#ff8000";
                        } elseif ($isCopiedDefaultData == 0 && $isDefaultCopyProcessing == 0) {
                            $schoolcurrentstatus = "Pending";
                            $statuscss = "badge badge-danger";
                            $backgroundcolor = "#f44141";
                        }


                ?>
                        <tr>
                            <td>
                                <?php echo ($displayName);
                                echo '<br>' . $code; ?><br>
                                <br><span class="<?php echo ($statuscss); ?>" style="background-color:<?php echo ($backgroundcolor); ?>"><?php echo ($schoolcurrentstatus); ?></span>
                                <span class="badge" style="float:right;background-color:<?php echo ($bgcolor); ?>"><?php echo ($schoolCheckoff); ?></span>
                            </td>
                            <td>
                                <?php echo ($contactPerson); ?>
                                <br>
                                Email:<a href="mailto:<?php echo ($email); ?>?subject=<?php echo ($displayName); ?> | Issue">
                                    <?php echo ($email); ?>
                                </a>
                                <br>Phone:<a href="tel:<?php echo ($phone); ?>">
                                    <?php echo ($phone); ?>
                                </a>
                                <br>Created Date: <?php echo date('m/d/Y', strtotime($createdDate)); ?>
                                <br>Contract Start Date: <?php echo date('m/d/Y', strtotime($contractStartDate)); ?>
                            </td>
                            <td style="white-space: normal; word-wrap: break-word;">
                                <?php echo ($fullAddress); ?>
                            </td>
                            <td style="text-align: center">
                                <a class="updateActiveStatus <?php echo ($buttoncss); ?>" href="javascript:void(0);" schoolId="<?php echo EncodeQueryData($schoolId); ?>" status="<?php echo ($updateStatus); ?>" title="<?php echo $displayName; ?>">
                                    <?php echo ($displayStatus); ?></a>
                                | <a href="displayschoolsystemuser.html?schoolId=<?php echo (EncodeQueryData($schoolId)); ?>">
                                    Users</a>
                                <?php
                                if ($totalSchoolUserCount > 0) {
                                ?><span class="badge"><?php echo ($totalSchoolUserCount); ?></span><?php
                                                                                                }
                                                                                                    ?>
                                | Evaluators
                                <?php
                                if ($totalClinicianCount > 0) {
                                ?><span class="badge"><?php echo ($totalClinicianCount); ?></span><?php
                                                                                                }
                                                                                                    ?>
                                | Students
                                <?php
                                if ($totalStudentCount > 0) {
                                ?><span class="badge"><?php echo ($totalStudentCount); ?></span><?php
                                                                                            }
                                                                                                ?>
                                | <a href="addschool.html?id=<?php echo (EncodeQueryData($schoolId)); ?>">Edit</a>
                                |
                                <?php if ($isSuperAdmin == 1) { ?>
                                    <span class="text-muted" title="Can not delete primary school">Delete</span>
                                <?php
                                } else {
                                ?>
                                    <a href="javascript:void(0);" class="deleteAjaxRow" schoolName="<?php echo ($schoolName); ?>" schoolId="<?php echo (EncodeQueryData($schoolId)); ?>">Delete</a>
                                <?php } ?>


                            </td>
                        </tr>
                <?php
                    }

                    unset($objSchoolAccount);
                    unset($objSchoolUsers);
                    unset($objLocation);
                }
                ?>
            </tbody>
        </table>
    </div>


    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>


    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();

            $("#schoolStatus").change(function() {
                var isactive = $(this).val();

                if (isactive) {
                    window.location.href = "schools.html?status=" + isactive;
                } else {
                    window.location.href = "schools.html";
                }
            });
        });


        var current_datatable = $("#datatable-responsive").DataTable({
            'iDisplayLength': 100,
            "aoColumns": [{
                    "sWidth": "25%"
                },
                {
                    "sWidth": "15%",
                    "bSortable": false
                }, {
                    "sWidth": "25%"
                }, {
                    "sWidth": "25%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
            ]
        });



        //  $(document).on('click', '.deleteAjaxRow', function() {
        $("#datatable-responsive").on("click", ".deleteAjaxRow", function() {
            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var schoolId = $(this).attr('schoolId');
            var schoolName = $(this).attr('schoolName');
            alertify.confirm('School: ' + schoolName, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: schoolId,
                        type: 'school'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });
        $(document).on('click', '.updateActiveStatus', function() {

            var schoolId = $(this).attr('schoolId');
            var status = $(this).attr('status');
            var title = $(this).attr('title');
            statusTitle = (status == 0) ? 'Inactive' : 'Active';
            alertify.confirm('School: ' + title, 'Continue with ' + statusTitle + ' school?', function() {
                var URL = 'schoolTransSubmit.html?id=' + schoolId + '&newStatus=' + status;
                window.location.href = URL;
            }, function() {});
        });
    </script>


</body>

</html>