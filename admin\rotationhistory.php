<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');
    include('../class/clsSchool.php');
    include('../class/clsStudent.php');    
    include('../class/clsRotation.php');    
    include('../setRequest.php'); 	
	
	$selrotationId=0;
	$rotationId=0;
	$rankId=0;
	$from_date='';
	$to_date = '';	
	$display_from_date= date('m/d/Y', strtotime('-15 days'));
	$display_to_date= date('m/d/Y');
	
	$objRotation = new clsRotation();
	
	$currentstudentId =0;
	if(isset($_GET['studentId']))
	{
		$currentstudentId= DecodeQueryData($_GET['studentId']);	
	}
	if(isset($_GET['rankId']))
	{
		$rankId = $_GET['rankId'];
        $rankId = DecodeQueryData($rankId); 
	}
	if(isset($_GET['rotationId'])) 
	{
		$rotationId = $_GET['rotationId'];
        $rotationId = DecodeQueryData($rotationId);
    }
	$rowsRotationData = $objRotation->GetRotationHistoryByStudent($currentSchoolId,$currentstudentId,$rotationId);
	$totalRotationCount = 0;
	if($rowsRotationData !='')
	{
		$totalRotationCount = mysqli_num_rows($rowsRotationData);
	}
	$objStudent=new clsStudent();
	$singlestudentname=$objStudent->GetSingleStudent($currentSchoolId,$currentstudentId);
	$firstName= $singlestudentname ? $singlestudentname['firstName'] : '';
	$lastName= $singlestudentname ? $singlestudentname['lastName'] : '';
	$fullname= $firstName . ' ' .$lastName;
	
	//$rotation = $objRotation->GetAllrotation($currentSchoolId, $locationId=0, $courseId=0, $selrotationId);
	
	$searchrotation = $objRotation->GetRotation($currentSchoolId,$currentstudentId);
	unset($objRotation);
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Rotation History</title>
        <?php include('includes/headercss.php');?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
        <?php include("includes/datatablecss.php") ?>
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css"> 
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="schoolstudents.html">Student</a></li>
					 <li><a href="schoolstudents.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>&rankId=<?php echo EncodeQueryData($rankId); ?>"><?php echo $fullname; ?></a></li>
                        <li class="active">Rotation History</li>
                    </ol>
                </div>
              
            </div>
        </div>

        <div class="container">           
		
                <div id="divTopLoading" >Loading...</div>			
				 <div class="col-md-2 pull-right padding_right_zero">
                                <select studentId=<?php echo (EncodeQueryData($currentstudentId));?> id="cborotation" name="cborotation" class="form-control input-md required-input select2_single"  >
                                <option value="" selected>Select</option>
                                     <?php
                                    if($searchrotation!="")
                                    {
                                        while($row = mysqli_fetch_assoc($searchrotation))
                                        {
                                             $selrotationId  = $row['rotationId'];
                                             $name  = stripslashes($row['title']);
    
                                             ?>
                                              <option value="<?php echo(EncodeQueryData($selrotationId)); ?>" <?php if($rotationId==$selrotationId){ ?>  selected="true" <?php }?>><?php echo($name); ?></option>
                                             <?php
    
                                        }
                                    }
                                ?>
                                </select>
                 </div>
                            <label class="pull-right" for="cborotation" style="margin-top:7px">Rotation:</label>
                            <br/> <br/> <br>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
							
                           <th>Semester</th>						   
                           <th>Rotation</th>
                            <th>Hospital Site</th>                            
                            <!-- <th>Clinician</th> -->
                            <th>Course</th>
                            <th style="text-align: center;">Start Date</th>
                            <th style="text-align: center;">End Date</th>
                            <th style="text-align: center;">Created</th>
                            <th style="text-align: center;">Modified</th>
                            
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalRotationCount > 0)
                        {
                            while($row = mysqli_fetch_array($rowsRotationData))
                            {
								
                                $rankname = ($row['rankname']);					
                                $fName = stripslashes($row['firstName']);
                                $lastName = stripslashes($row['lastName']);
                                $fullName =  $fName . ' ' . $lastName;
                                $rotationname = $row['title'];
                                $hospitalTitle = $row['hospitalTitle'];
                                $clinicianFName = $row['clinicianFName'];
                                $clinicianLName = $row['clinicianLName'];
                                $clinicianName=$clinicianFName.' '.$clinicianLName;
                                $courseTitle = $row['courseTitle'];
                                $startDate = $row['startDate'];
                                $endDate = $row['endDate'];
                                $createdDate = $row['createdDate'];
                                $updatedDate = $row['updatedDate'];
                                $Semestertitle = $row['Semestertitle'];
                                                         
                                                          
                               ?>
                            <tr>
								
								<td >
								
                                    <?php echo($Semestertitle); ?>
                                </td>                                
								 <td>
                                    <?php echo($rotationname); 				
									?>
                                </td>
								
								<td>
                                    <?php echo($hospitalTitle); ?>
                                </td>	
								<!-- <td>
                                    <?php echo($clinicianName); ?>
                                </td> -->
								<td>
                                    <?php echo($courseTitle); ?>
                                </td>
								<td style="text-align: center;">
                                    <?php echo (date('m/d/Y', strtotime ($startDate))); ?>
                                </td>
								<td style="text-align: center;">
                                    <?php echo (date('m/d/Y', strtotime ($endDate))); ?>
                                </td>
								<td style="text-align: center;">
                                    <?php echo (date('m/d/Y', strtotime ($createdDate))); ?>
                                </td>
								<td style="text-align: center;">
                                    <?php 
                                    if($updatedDate !='' && $updatedDate !='0000-00-00 00:00:00')
                                    {
                                    echo (date('m/d/Y', strtotime ($updatedDate))); 
                                    }
                                    else{
                                        echo "-";
                                    }
                                    ?>
                                </td>
                               
                                
                            </tr>
                            <?php


                            }
                        }
                    ?>
					</tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
		<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
		<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
			<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

            $(window).load(function(){
                $("#divTopLoading").addClass('hide');
				$(".select2_single").select2();
				
				
				
					$(function () {
							$("#fromDate").datetimepicker({
								format: "MM/DD/YYYY",
								defaultDate: moment().subtract(15, 'days'),
								useCurrent: false
							});
					});
					
					$(function () {
							$("#toDate").datetimepicker({
								format: "MM/DD/YYYY",
								defaultDate: moment(),
								useCurrent: false
							});
					});
				
				
            });
			
			$("#cborotation").change(function(){
					var rotationId = $(this).val();
					var studentId = $(this).attr('studentId');
					
					if(rotationId)
					{
						window.location.href = "rotationhistory.html?rotationId="+rotationId+"&studentId="+studentId;
																	 
					}
					else{
						window.location.href = "rotationhistory.html?studentId="+studentId;
					}
				});
			
                 var current_datatable = $("#datatable-responsive").DataTable({
                    "ordering": true,
                    "order": [[5, "desc" ]],

					 "aoColumns": [{
                    "sWidth": "5%",
					"bSortable": false
                },{
                    "sWidth": "5%",
                    "sClass": "alignCenter"
					
                },
				{
                    "sWidth": "5%",
                    "sClass": "alignCenter"
					
                },{
                    "sWidth": "5%",
                    "sClass": "alignCenter",
					"bSortable": false
                },{
                    "sWidth": "5%",
                    "sClass": "alignCenter",
                },{
                    "sWidth": "5%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },{
                    "sWidth": "5%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },{
                    "sWidth": "5%",
                    "sClass": "alignCenter",
                    "bSortable": false
                }]
				 });    

		//delete student
				$(document).on('click', '.deleteAjaxRow', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                var currentstudentId = $(this).attr('currentstudentId');
                var schoolStudentName = $(this).attr('schoolStudentName');
                
                alertify.confirm('Student Name: '+schoolStudentName, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: currentstudentId,
                            type: 'journal_student'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });

        </script>
    </body>
    </html>