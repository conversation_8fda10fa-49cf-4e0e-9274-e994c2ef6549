<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsCheckoffSectionMaster.php');
include('../class/clsCheckoffQuestionMaster.php');
include('../class/clsCheckoffTopicMaster.php');
include('../class/clsPerformance.php');

if ($_SERVER['REQUEST_METHOD'] == "POST") {

    $objDB = new clsDB();

    $type = isset($_POST['type']) ? $_POST['type'] : '';


    ini_set('upload_max_filesize', '50M');
    ini_set('post_max_size', '50M');
    ini_set('max_input_time', 300000);
    ini_set('max_execution_time', 300000);

    $notifyMessage = 'Imported';
    $row = 1;
    $isExistmidTermPerformanceSectionId = 0;
    $isExistdfMidtermPerformanceQuestionId = 0;
    if (isset($_FILES['file'])) {
        $filename = $_FILES["file"]["tmp_name"];
        $ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));

        if ($ext != "csv") {
            $error = "Upload only .csv file.";
            header('location:settings.html?status=Importerror');
            exit();
        }

        if ($_FILES["file"]["size"] > 0) {

            $file = fopen($filename, "r");
            $counter = 0;

            $retStudentId = 0;
            while (($getData = fgetcsv($file, 10000, ",")) !== FALSE) {
                // echo $type;exit; 
                if ($row == 1) {
                    $row++;
                    continue;
                }

                //   echo  
                $sectionTitle = trim($getData[1]);
                //   echo "<br>";
                //   echo 
                $optionText = trim($getData[2]);
                //   echo "<br>";
                //   echo 
                $discription = trim($getData[3]);
                //   echo "<br>";
                //   echo 
                $optionType = trim($getData[4]);
                //   echo "<br>";
                //   echo  
                $optionValues = trim($getData[5]);
                //   echo "<br>";
                //   echo 
                $sortOrder = trim($getData[6]);
                //   echo "<br>";
                //   echo
                $sectionSortOrder = trim($getData[7]);
                //   echo "<br>";
                //   echo "<br>";
                $isTechnologist = trim($getData[8]);


                $isExistSectionId = $objDB->GetSingleColumnValueFromTable('defaultperformanceevalsectionmaster', 'defaultPerformanceSectionId', 'title', addslashes($sectionTitle));
                $objPerformance = new clsPerformance();
                // echo '</br>$isExistSectionId = '.$isExistSectionId;
                if ($sectionTitle != '') {
                    if (!$isExistSectionId) {

                        $objPerformance->title = $sectionTitle;
                        $objPerformance->sortOrder = $sectionSortOrder;
                        $objPerformance->isTechnologist = $isTechnologist;
                        $isExistSectionId = $objPerformance->SaveDefaultPerformanceevaluationSection(0);
                    }
                }

                // if($optionText != 'Personal Appearance')
                // {    
                $isExistQuestionId = $objDB->GetSingleColumnValueFromTable('defaultperformanceevalquestionmaster', 'defaultPerformanceQuestionId', 'questionText', ($optionText));
                // }
                // echo '</br>$isExistQuestionId = '.$isExistQuestionId;

                if (!$isExistQuestionId) {


                    $objPerformance->questionText = $optionText;
                    $objPerformance->defaultQuestionType = $optionType;
                    $objPerformance->sectionMasterId = $isExistSectionId;
                    $objPerformance->sortOrder = $sortOrder;
                    $isExistQuestionId = $objPerformance->SaveDefaultPerformanceevaluationQuestion(0);


                    // $objPerformance->questionText = 'Comments';
                    // $objPerformance->defaultQuestionType = 9;
                    // $objPerformance->sectionMasterId = $isExistSectionId;
                    // $objPerformance->sortOrder = $sortOrder;
                    // $dfQuestionId = $objPerformance->SaveDefaultPerformanceevaluationQuestion(0);
                }

                if ($isExistQuestionId) {

                    $isExistQuestionId = ($optionValues == '') ? $dfQuestionId : $isExistQuestionId;
                    $optionValuesString = explode(",", $optionValues);

                    // Check if any value from the array exists in the comma-separated string
                    foreach ($optionValuesString as $optionValue) {
                        $objPerformance->questionId = $isExistQuestionId;
                        $objPerformance->optionText = $optionValue;
                        $objPerformance->optionValue = $optionValue;
                        $objPerformance->SaveDefaultPerformanceEvaluationQuestionOptions(0);
                    }
                }
                $result = 1;
            }
            // exit;

            fclose($file);

            $messageText = $result ? 'Imported' : 'Error';
            header('location:settings.html?status=' . $messageText);

            exit();
        }
    }
}
