<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsProcedureCategory.php');
include('../class/clsUsafProcedureCategory.php');
include('../class/clsStudent.php');
include('../class/clscheckoff.php');
include('../class/clsRotation.php');
include('../class/clsProcedureCount.php');
include('../class/clsUsafProcedureCount.php');
include('../class/clsCheckoffTopicMaster.php');
include('../setRequest.php');

$schoolId = 0;
$procedureCategoryId = 0;
$rotationId = 0;
$studentId = 0;
$currentstudentId = 0;
$rotationtitle = '';
$transchooldisplayName = '';
$checkoffId = 0;
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$loggedUserSendRecordToCanvas = isset($_SESSION["loggedUserSendRecordToCanvas"]) ? $_SESSION["loggedUserSendRecordToCanvas"] : 0;
$canvasStatus = isset($_GET['canvasStatus']) ? $_GET['canvasStatus'] : '';
$type = isset($_GET['type']) ? $_GET['type'] : '';
$loggedUserId = isset($_SESSION["loggedUserId"]) ? $_SESSION["loggedUserId"] : 0;

if ($type == 'canvas')
    $canvasStatus = 1;

$encodedRotationId = '';
$encodedStudentId = '';

//    $TimeZone = $_SESSION["loggedClinicianSchoolTimeZone"];
$objUsafProcedureCount = new clsUsafProcedureCount();
$objcheckoff = new clscheckoff();

if (isset($_GET['schoolId'])) //Edit Mode
{
    $schoolId = $_GET['schoolId'];
    $schoolId = DecodeQueryData($schoolId);
} else {
    $schoolId = $currentSchoolId;
    $transchooldisplayName = $currenschoolDisplayname;
}

//For Filter by Student
if (isset($_GET['studentId'])) {
    $encodedStudentId = $_GET['studentId'];
    $studentId = DecodeQueryData($_GET['studentId']);
    $currentstudentId = DecodeQueryData($_GET['studentId']);
}


//For Filter by Rotation
if (isset($_GET['rotationId'])) {
    $encodedRotationId = $_GET['rotationId'];
    $rotationId = $_GET['rotationId'];
    $rotationId = DecodeQueryData($rotationId);
    //$checkoffId = $objcheckoff->GetCheckoffByRotationId($rotationId);

}

//For View 
if (isset($_GET['view'])) {
    $view = $_GET['view'];
    $view = DecodeQueryData($view);
}

//For Filter by Procedure Category 
if (isset($_GET['procedureCategoryId'])) {
    $procedureCategoryId = $_GET['procedureCategoryId'];
    $procedureCategoryId = DecodeQueryData($procedureCategoryId);
}
$title = "Procedure Count ";



//For Rotation Title
$objrotation = new clsRotation();
$rotation = $objrotation->GetRotationBySchool($schoolId);
$rowsrotation = $objrotation->GetrotationTitleForInteraction($rotationId, $currentSchoolId);
$rotationtitle = $rowsrotation ? $rowsrotation['title'] : '';
unset($objrotation);

//For Student Name
$objStudent = new clsStudent();
$rowsStudents = $objStudent->GetSingleStudent($currentSchoolId, $currentstudentId);
$studentfullname = $rowsStudents ? ($rowsStudents['firstName'] . ' ' . $rowsStudents['lastName']) : '';
$Students = $objStudent->GetAllStudents($currentSchoolId);
unset($objStudent);

//For Procedure Category Name By Filter
$objProcedureCategory = new clsProcedureCategory();
if ($isActiveCheckoff == 1)
    $Category = $objProcedureCategory->GetAllCategory();
else
    $Category = $objProcedureCategory->GetAllAdvanceCategory();

//For Procedure
$objUsafProcedureCategory = new clsUsafProcedureCategory();

$usafSchoolId = ($currentSchoolId == 122) ? 122 : 0;

if ($isActiveCheckoff == 1)
    $ProcedureCategory = $objcheckoff->GetProcedureCategory($currentSchoolId);
elseif ($isActiveCheckoff == 2)
    $ProcedureCategory = $objUsafProcedureCategory->GetAllUsafCategory($usafSchoolId);
else
    $ProcedureCategory = $objcheckoff->GetAdvanceProcedureCategory($currentSchoolId);

if ($ProcedureCategory != '') {
    $totalSection = mysqli_num_rows($ProcedureCategory);
}
$rotationname = '';


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include("includes/datatablecss.php"); ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">

    <style>
        .table-datepicker .datepicker td {
            white-space: normal !important;
        }

        .collapsible {
            cursor: pointer;
            /* padding: 15px; */
            /* border: 1px solid #181818; */
            /* background-color: #f9f9f9; */
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* border-radius: 14px; */
        }

        .collapsible p {
            margin: 0;
        }

        .collapsible-arrow {
            font-size: 18px;
            transition: transform 0.3s ease;
        }

        .content {
            display: none;
            padding: 10px 0;
            /* border-top: 1px solid #ececec; */
        }

        .content.active {
            display: block;
        }

        .active.collapsible-arrow {
            transform: rotate(180deg);
        }

        .row-delete-icon {
            position: absolute;
            top: -82px;
            right: 20px;
        }

        .panel-heading {
            width: 100%;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .arrow-icon {
            transition: transform 0.3s;
        }

        .collapsible.expanded .arrow-icon {
            transform: rotate(180deg);
        }
        .procedureresponsive {
            width: 100%;
        }
        @media screen and (max-width: 1440px) {
            .procedureresponsive {
                width: 150px !important;
            }
        }
        .form-inline .input-group>.form-control {
            width: 180px;
        }

        .panel-body {
            overflow-x: unset !important;
        }


        .bootstrap-datetimepicker-widget.dropdown-menu {
            border-radius: 12px !important;
        }
        
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>
    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <?php if ($type == 'canvas') { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="settings.html">Settings</a></li>
                        <li class="active">Procedure Count</li>
                    <?php } else { ?>
                        <li><a href="dashboard.html">Home</a></li>
                        <?php if ($currentstudentId != 0) { ?>
                            <li class="active"><a href="clinical.html">Clinical</a></li>
                            <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
                            <li class="active">Procedure Count</li>

                        <?php } else { ?>
                            <li class="active"><a href="rotations.html"> Rotations </a></li>
                            <li class="active"><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                            <li class="active">Procedure Count</li>

                        <?php } ?>
                    <?php } ?>
                </ol>
            </div>


        </div>
    </div>
    <div id="contentText" class="custom-container">
        <?php
        if (isset($_GET["status"])) {

            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Procedure Count Added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>
        <div id="divTopLoading">Loading...</div>

        <?php if ($type != 'canvas') { ?>
            <div class="col-md-4 pull-right padding_zero  margin_zero">
                <div class="form-group">
                    <div class="col-md-3">
                        <label class="control-label" for="cbostudent" style="margin-top:8px">Student:</label>
                    </div>
                    <div class="col-md-9">
                        <select studentId="<?php echo EncodeQueryData($studentId); ?>" id="cbostudent" name="cbostudent"
                            procedureCategoryId="<?php echo EncodeQueryData($procedureCategoryId); ?>"
                            class="form-control input-md  select2_single">
                            <option value="" selected>Select</option>
                            <?php
                            if ($Students != "") {
                                while ($row = mysqli_fetch_assoc($Students)) {
                                    $selstudentId  = $row['studentId'];
                                    $firstName  = stripslashes($row['firstName']);
                                    $lastName  = stripslashes($row['lastName']);
                                    $studentname  = $firstName . ' ' . $lastName;

                            ?>
                                    <option value="<?php echo EncodeQueryData($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($studentname); ?></option>
                            <?php
                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
            </div>

            <!-- <div class="col-md-4 pull-right padding_zero  margin_zero" >
            <div class="form-group">
                <div class="col-md-3">
                <label class="control-label" for="cboCategory" style="margin-top:8px">Category:</label>
                </div>
                <div class="col-md-9">
                <select studentId="<?php echo EncodeQueryData($studentId); ?>" id="cboCategory" name="cboCategory"  
                class="form-control input-md  select2_single"  >
                 <option value="" selected>Select</option> 
                <?php
                if ($Category != "") {
                    while ($row = mysqli_fetch_assoc($Category)) {
                        $selCategoryId  = $row['procedureCategoryId'];
                        $name  = stripslashes($row['categoryName']);

                ?>
                <option value="<?php echo EncodeQueryData($selCategoryId); ?>" <?php if ($procedureCategoryId == $selCategoryId) { ?>  selected="true" <?php } ?>><?php echo ($name); ?></option>
                <?php
                    }
                }
                ?>
                </select>
                </div>
                </div>
            </div> -->


            <div class="col-md-2 pull-right padding_right_zero">
                <select studentId="<?php echo EncodeQueryData($studentId); ?>" id="cborotation" name="cborotation" class="form-control input-md select2_single">
                    <option value="" selected>Select</option>
                    <?php
                    if ($rotation != "") {
                        while ($row = mysqli_fetch_assoc($rotation)) {
                            $selrotationId  = $row['rotationId'];

                            $name  = stripslashes($row['title']);

                    ?>
                            <option value="<?php echo (EncodeQueryData($selrotationId)); ?>" <?php if ($rotationId == $selrotationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                    <?php
                        }
                    }
                    ?>
                </select>
            </div>
            <label class=" control-label  pull-right" for="cborotation" style="margin-top:8px">Rotation:</label> <br /> <br>
        <?php } ?>

        <form id="frmprocedurcount" data-parsley-validate class="form-horizontal" method="POST" <?php if ($currentstudentId != 0) { ?> action="addprocedurecountsubmit.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>"

            <?php  } else { ?> action="addprocedurecountsubmit.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>" <?php } ?>>
            <div class="row">
                <div class="col-md-12 mt-20">
                    <div class="form-group">

                        <div class="col-md-12 col-sm-6 col-xs-12">
                            <div class="panel-group mt-10" id="posts">
                                <?php

                                $objProcedureCount = new clsProcedureCount();
                                while ($rowProcedure = mysqli_fetch_array($ProcedureCategory)) {
                                    $procedureCategoryId = $rowProcedure['procedureCategoryId'];
                                    $title = $rowProcedure['categoryName'];

                                ?>

                                    <div class="panel panel-default">
                                        <a class="collapsible" style="color: #000; text-decoration: none;" href="#<?php echo $procedureCategoryId; ?>" data-toggle="collapse" data-parent="#posts" aria-expanded="false" id="collapse-link" onclick="displayProcedureCount(<?php echo ($procedureCategoryId) ? $procedureCategoryId : 0; ?>, <?php echo ($studentId) ? $studentId : 0; ?>, <?php echo ($rotationId) ? $rotationId : 0; ?>,<?php echo ($checkoffId) ? $checkoffId : 0; ?>,<?php echo ($isActiveCheckoff) ? $isActiveCheckoff : 0; ?> )">
                                            <div class="panel-heading">
                                                <h4 class="panel-title">
                                                    <?php echo  $title; ?>
                                                </h4>
                                                <span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
                                            </div>
                                        </a>
                                        <div id="<?php echo $procedureCategoryId; ?>" class="panel-collapse collapse">

                                            <div class="panel-body" id="data_id_<?php echo $procedureCategoryId; ?>">


                                            </div>

                                        </div>
                                    </div>
                                <?php
                                }
                                unset($objProcedureCount);
                                unset($objProcedureCategory);
                                unset($objUsafProcedureCount);

                                ?>
                            </div>


                        </div>

                    </div>
                </div>
            </div>

    </div>
    <!--<div class="form-group">
               <div class="col-md-12"> 
			    <?php  //if(!isset($_GET['view'])) { 
                ?>
                  <button id="btnSubmit" style="text-align:Center; width:130px;margin-top:10px" name="btnSubmit" class="btn btn-success">Save</button>
				<?php // } 
                ?>
			   </div>
            </div> -->


    </form>
    </div>
    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <?php include('../includes/procedureStepsModal.php'); ?>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";
        $('.procedureTopicSteps').magnificPopup({
            'type': 'ajax',
        });

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();


        });
        var current_datatable = $(".mytablecustom").DataTable({
            "pageLength": 50,
            "lengthMenu": [10, 20, 30, 50, 75],
            "fixedHeader": true,
            "responsive": true,
            "ordering": false


        });


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');

            <?php if (isset($_GET['view'])) { ?>
                $("input[type=text]").attr('disabled', true);
            <?php } ?>

            $('.procedurecountDate').datetimepicker({
                format: 'MM/DD/YYYY',
                defaultDate: new Date(),
                maxDate: 'now'
            });

            $("#cborotation").change(function() {
                var rotationId = $(this).val();
                var studentId = $(this).attr('studentId');

                if (rotationId) {
                    window.location.href = "procedurecountsNew.html?rotationId=" + rotationId + "&studentId=" + studentId;
                } else {
                    window.location.href = "procedurecountsNew.html";
                }
            });

            $("#cboCategory").change(function() {
                var procedureCategoryId = $(this).val();
                var studentId = "<?php echo EncodeQueryData($studentId); ?>";
                var rotationId = "<?php echo EncodeQueryData($rotationId); ?>";

                if (procedureCategoryId) {
                    window.location.href = "procedurecountsNew.html?procedureCategoryId=" + procedureCategoryId + "&studentId=" + studentId + "&rotationId=" + rotationId;
                } else {
                    window.location.href = "procedurecountsNew.html?studentId=" + studentId;
                }
            });

            $("#cbostudent").change(function() {
                var studentId = $(this).val();
                var procedureCategoryId = $(this).attr('procedureCategoryId');
                var rotationId = "<?php echo EncodeQueryData($rotationId); ?>";

                if (procedureCategoryId) {
                    window.location.href = "procedurecountsNew.html?procedureCategoryId=" + procedureCategoryId + "&studentId=" + studentId + "&rotationId=" + rotationId;
                } else {
                    window.location.href = "procedurecountsNew.html?studentId=" + studentId + "&rotationId=" + rotationId;
                }
            });

        });

        $("#cborotation").change(function() {
            var rotationId = $(this).val();

            if (rotationId) {
                window.location.href = "procedurecounts.html?rotationId=" + rotationId;
            } else {
                window.location.href = "procedurecounts.html";
            }
        });

        function displayProcedureCount(procedureCategoryId, studentId, rotationId, checkoffId, isActiveCheckoff) {
            let product_data_id = '#data_id_' + procedureCategoryId;
            var isUser = 1;
            var userId = '<?php echo $loggedUserId; ?>';
            $(product_data_id).html('Please wait...');
            $.ajax({
                url: "../ajax/ajax_student_procedure_count.html?procedureCategoryId=" + procedureCategoryId + "&studentId=" + studentId + "&rotationId=" + rotationId + "&checkoffId=" + checkoffId + "&isUser=" + isUser + "&isActiveCheckoff=" + isActiveCheckoff + "&userId=" + userId,
                type: 'GET',
                dataType: 'html', // added data type
                success: function(res) {
                    $(product_data_id).html(res);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });

        }
    </script>
    <script>
        // Get all collapsible button elements
        var buttons = document.querySelectorAll(".collapsible");
        var contents = document.querySelectorAll(".panel-collapse");

        // Add click event listeners to all buttons
        buttons.forEach(function(button, index) {
            button.addEventListener("click", function() {
                // Check if the content is currently expanded
                var isExpanded = contents[index].style.display === "block";

                // Close all sections
                contents.forEach(function(content) {
                    content.style.display = "none";
                });

                // Reset the "expanded" class for all buttons
                buttons.forEach(function(btn) {
                    btn.classList.remove("expanded");
                });

                // Toggle the content for the clicked section
                if (!isExpanded) {
                    contents[index].style.display = "block";
                    button.classList.add("expanded");
                }
            });
        });
    </script>
</body>

</html>