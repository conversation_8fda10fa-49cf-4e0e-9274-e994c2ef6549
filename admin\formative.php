<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsStudent.php');
include('../class/clsFormative.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsSectionStudentName.php');
include('../class/clsClinician.php');
include('../class/clsExternalPreceptors.php');


$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

$schoolId = 0;
$rotationId = 0;
$studentId = 0;
$currentstudentId = 0;
$studentFormativeMasterId = 0;
$clinicianId = 0;
$formativerotationid = 0;
$dateOfStudentSignature = '';
$evaluationDate = '';
$dateOfInstructorSignature = '';
$display_to_date = date('m/d/Y');
$schoolId = $currentSchoolId;

$objRotation = new clsRotation();

//For Rotation
if (isset($_GET['formativerotationid'])) {
	$formativerotationid = DecodeQueryData($_GET['formativerotationid']);
}

//For Student	
if (isset($_GET['studentId'])) {
	$currentstudentId = DecodeQueryData($_GET['studentId']);
}

if (isset($_GET['rotationId'])) {
	$formativerotationid = DecodeQueryData($_GET['rotationId']);
}
//For Edit Formative
if (isset($_GET['studentFormativeMasterId']) && ($_GET['formativerotationid'])) {
	$schoolId = $currentSchoolId;
	$page_title = "Edit Formative ";
	$bedCrumTitle = 'Edit';

	//For Formative Details
	$objFormative = new clsFormative();
	$studentFormativeMasterId = DecodeQueryData($_GET['studentFormativeMasterId']);
	$rowFormative = $objFormative->GetStudentFormativeDetails($studentFormativeMasterId);
	unset($objFormative);
	if ($rowFormative == '') {
		header('location:formative.html');
		exit;
	}
	$formativerotationid = ($rowFormative['rotationId']);
	$clinicianId = ($rowFormative['clinicanId']);
	$evaluationDate = ($rowFormative['evaluationDate']);
	$courselocationId = $rowFormative['locationId'];
	$parentRotationId = stripslashes($rowFormative['parentRotationId']);
	$rotationLocationId = stripslashes($rowFormative['rotationLocationId']);

	$locationId = 0;
	if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
		if ($parentRotationId > 0) {
			if (!$rotationLocationId)
				$locationId = $objRotation->GetLocationByRotation($formativerotationid);
			else
				$locationId  = $rotationLocationId;
		}
	} else {
		$locationId  = $courselocationId;
	}

	//Get Time Zone By Rotation 
	$objLocation = new clsLocations();
	$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
	unset($objLocation);
	if ($TimeZone == '')
		$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
	$evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
	$evaluationDate = (date('m/d/Y', strtotime($evaluationDate)));
	$studentId = ($rowFormative['studentId']);
	$studentSignature = ($rowFormative['studentSignature']);
	$dateOfStudentSignature = ($rowFormative['dateOfStudentSignature']);
	if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '0000-00-00 00:00:00') {
		$dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
		$dateOfStudentSignature = (date('m/d/Y', strtotime($dateOfStudentSignature)));
	}
	$dateOfInstructorSignature = ($rowFormative['dateOfInstructorSignature']);
	if ($dateOfInstructorSignature != '' && $dateOfInstructorSignature != '0000-00-00 00:00:00') {
		$dateOfInstructorSignature = converFromServerTimeZone($dateOfInstructorSignature, $TimeZone);
		$dateOfInstructorSignature = (date('m/d/Y', strtotime($dateOfInstructorSignature)));
	}
	$studentComment = strip_tags($rowFormative['studentComment']);
	//For Rotation List
	$rowstudentrotation = $objRotation->GetRotationByStudent($schoolId, $currentstudentId);
} else {

	$page_title = "Add Formative";
	$bedCrumTitle = 'Add';
	//For Rotation List
	$rowstudentrotation = $objRotation->GetCurrentRotationByStudent($schoolId, $currentstudentId);
}

//For Clinician Name
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, $formativerotationid);
unset($objClinician);

//For Student Name
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($currentSchoolId, $formativerotationid);
unset($objStudent);

$objSectionStudentName = new clsSectionStudentName();
$getStudentName = $objSectionStudentName->GetSectionStudentName($schoolId, $formativerotationid);
unset($objSectionStudentName);


$totalSection = 0;
$objFormative = new clsFormative();
$FormativeSection = $objFormative->GetSections($schoolId);
if ($FormativeSection != '') {
	$totalSection = mysqli_num_rows($FormativeSection);
}

//For Rotation Name

$RotationName = $objRotation->GetrotationDetails($formativerotationid, $schoolId);
$rotationtitle = $RotationName['title'];
unset($objRotation);

//For Student Full Name
$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $currentstudentId);
$studentfullname = $Rowstudent['firstName'] . ' ' . $Rowstudent['lastName'];
unset($objStudent);

$preceptorId = 0;
$isPreceptor = isset($_GET['isPreceptor']) ? DecodeQueryData($_GET['isPreceptor']) : '';
$preceptorFullName = '';
if ($isPreceptor > 0) {
	$preceptorId = $isPreceptor;
	$objExternalPreceptors = new clsExternalPreceptors();
	$externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
	$preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
	$preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
	$preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
}

$bedCrumTitle = (isset($_GET['view']) && $_GET['view'] == 'V') ? 'View' : $bedCrumTitle;
?>
<!DOCTYPE html>
<html lang="en">

<head>
	<meta charset="utf-8">
	<meta http-equiv="X-UA-Compatible" content="IE=edge">
	<!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
	<!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
	<title><?php echo ($page_title); ?></title>
	<?php include('includes/headercss.php'); ?>
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<style type="text/css">
		.some-class {
			float: left;
			clear: none;
		}
	</style>

</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
					<?php if ($currentstudentId > 0) { ?>
						<li><a href="clinical.html">Clinical</a></li>
						<li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
						<li><a href="formativelist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>">Formative Evaluation </a></li>
					<?php } else { ?>
						<li><a href="rotations.html">Rotations</a></li>
						<li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
						<li><a href="formativelist.html?formativerotationid=<?php echo EncodeQueryData($formativerotationid); ?>">Formative Evaluation </a></li>
					<?php } ?>
					<li class="active"><?php echo ($bedCrumTitle); ?></li>
				</ol>
			</div>

		</div>
	</div>

	<div class="container">

		<form id="frmformative" data-parsley-validate class="form-horizontal" method="POST" <?php if ($currentstudentId > 0) { ?>action="formativesubmit.html?studentId=<?php echo (EncodeQueryData($currentstudentId)); ?>&studentFormativeMasterId=<?php echo (EncodeQueryData($studentFormativeMasterId)); ?>" <?php } else { ?>action="formativesubmit.html?studentFormativeMasterId=<?php echo (EncodeQueryData($studentFormativeMasterId)); ?>
		 &formativerotationid=<?php echo (EncodeQueryData($formativerotationid)); ?>" <?php } ?>>

			<div class="row">
				<?php if ($isPreceptor || $preceptorId) { ?>
					<div class="col-md-6">
						<div class="form-group">
							<label class="col-md-4 control-label" for="cboclinician">Preceptor</label>
							<div class="col-md-8">
								<input type="text" name="" id="" value="<?php echo $preceptorFullName; ?>" class="form-control" disabled>
							</div>
						</div>
					</div>
				<?php } else { ?>
					<div class="col-md-6">

						<div class="form-group">
							<label class="col-md-4 control-label" for="cboclinician">Clinician</label>
							<div class="col-md-8">
								<select id="cboclinician" name="cboclinician" class="form-control input-md required-input select2_single">
									<option value="" selected>Select</option>
									<?php
									if ($Clinician != "") {
										while ($row = mysqli_fetch_assoc($Clinician)) {
											$selClinicianId  = $row['clinicianId'];
											$name  = stripslashes($row['firstName']);
											$lastName  = stripslashes($row['lastName']);
											$fullName = $name . ' ' . $lastName;
									?>
											<option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?> selected="true" <?php } ?>><?php echo ($fullName); ?></option>

									<?php

										}
									}
									?>
								</select>
							</div>
						</div>
						<!-- ROTATION DD END -->
					</div>
				<?php }  ?>


				<div class="col-md-6">

					<div class="form-group">
						<label class="col-md-4 control-label" for="cbostudent">Student</label>
						<div class="col-md-8">
							<select id="cbostudent" name="cbostudent" class="form-control input-md required-input select2_single">
								<option value="" selected>Select</option>
								<?php
								if ($Student != "") {
									while ($row = mysqli_fetch_assoc($Student)) {
										$selstudentId  = $row['studentId'];
										$firstName  = stripslashes($row['firstName']);
										$lastName  = stripslashes($row['lastName']);
										$name =	$firstName . ' ' . $lastName;
										if ($currentstudentId > 0) { ?>
											<option value="<?php echo ($selstudentId); ?>" <?php if ($currentstudentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
										<?php } else { ?>
											<option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
										<?php }  ?>
								<?php

									}
								}
								?>
							</select>

						</div>
					</div>

				</div>


			</div>



			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="evaluationDate">Evaluation Date</label>
						<div class="col-md-8">
							<div class='input-group date' id='evaluationDate'>

								<input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date" value="<?php echo ($evaluationDate); ?>" required data-parsley-errors-container="#error-txtDate" />
								<span class="input-group-addon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>

				<div class="col-md-6">
					<div class="form-group">
						<?php if ($isPreceptor || $preceptorId) { ?>
							<label class="col-md-4 control-label" for="InstructorDate">Date of Preceptor Signature</label>
						<?php } else { ?>
							<label class="col-md-4 control-label" for="InstructorDate">Date of Instructor Signature</label>
						<?php } ?> <div class="col-md-8">
							<div class='input-group date' id='InstructorDate'>

								<input type='text' name="InstructorDate" readonly id="InstructorDate" class="form-control input-md required-input rotation_date" value="<?php echo ($dateOfInstructorSignature); ?>" data-parsley-errors-container="#error-txtDate" />
								<span class="input-group-addon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<?php if ($currentstudentId > 0) { ?>
					<div class="col-md-6">
						<div class="form-group">
							<label class="col-md-4 control-label" for="cborotation">Rotation</label>
							<div class="col-md-8">
								<select id="cborotation" name="cborotation" class="form-control input-md required-input select2_single" required>
									<option value="" selected>Select</option>
									<?php
									if ($rowstudentrotation != "") {
										while ($row = mysqli_fetch_assoc($rowstudentrotation)) {
											echo 'hete';
											$selrotationId  = $row['rotationId'];
											$title  = stripslashes($row['title']);
									?>
											<option value="<?php echo ($selrotationId); ?>" <?php if ($formativerotationid == $selrotationId) { ?> selected="true" <?php } ?>><?php echo ($title); ?></option>
									<?php }
									}
									?>
								</select>

							</div>
						</div>
					</div>
				<?php } ?>
			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="studentsigniture">Student Signature</label>
						<div class="col-md-8">
							<input type='text' name="studentsigniture" readonly id="studentsigniture" class="form-control input-md required-input " value="<?php if (isset($_GET['studentFormativeMasterId']))  echo ($studentSignature); ?>" />
							<div id="error-txtDate"></div>
						</div>
					</div>

				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="studentsignitureDate">Date Of Student Signature</label>
						<div class="col-md-8 col-sm-4 col-xs-12">
							<div class='input-group date' id='studentsignitureDate'>

								<input type='text' name="studentsignitureDate" readonly id="studentsignitureDate" class="form-control input-md required-input rotation_date" value="<?php echo ($dateOfStudentSignature);  ?>" data-parsley-errors-container="#error-txtDate" />
								<span class="input-group-addon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="studentcomment">Student Comments</label>
						<div class="col-md-10 col-sm-6 col-xs-12">
							<textarea name="studentcomment" id="studentcomment" class="form-control input-md " rows="4" cols="100"><?php if (isset($_GET['studentFormativeMasterId']))  echo ($studentComment); ?></textarea>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="instructions:"><b>INSTRUCTIONS:</b></label>
						<div class="col-md-10 col-sm-6 col-xs-12">
							<div class="panel panel-default">
								<div class="panel-body">
									<ol>
										<b>
											<li>Please be frank and honest in reacting to the following statements regarding your opinion of the student's clinical performance.</li>
										</b>
										<b>
											<li>Note the scale describing the rating system.</li>
										</b>
										<b>
											<li>CHECK the most appropriate rating.</li>
										</b>

									</ol>
									<p class="margin_left_twenty">The clinical site ratings are based on the following categories. The rating scale for each category is
										<br><b>1-N/A, 2-Hardly Ever, 3-Not Often, 4-Minimally Okay, 5-Most of the Time, 6-Almost Always.<b>
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 1st SECTION div start -->
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="instructions:"></label>
						<div class="col-md-10 col-sm-6 col-xs-12">

							<div class="panel-group" id="posts">
								<?php

								if ($FormativeSection) {
									while ($row = mysqli_fetch_array($FormativeSection)) {
										$sectionMasterId = $row['sectionMasterId'];
										$title = $row['title'];
										//$firstName = $row['firstName'];
								?>

										<div class="panel panel-default">
											<div class="panel-heading">
												<h4 class="panel-title">
													<a href="#<?php echo $sectionMasterId; ?>" data-toggle="collapse" data-parent="#posts"><b><?php echo  $title; ?></b></a>
												</h4>
											</div>

											<div id="<?php echo $sectionMasterId; ?>" class="panel-collapse collapse">
												<?php
												// for question
												$totalFormative = 0;
												$Formativequestion = $objFormative->GetAllFormativeQuestionMaster($schoolId, $sectionMasterId);
												if ($Formativequestion != '') {
													$totalFormative = mysqli_num_rows($Formativequestion);
												}

												if ($totalFormative > 0) {
													while ($row = mysqli_fetch_array($Formativequestion)) {
														if (isset($_GET['studentFormativeMasterId'])) {
															$studentFormativeMasterId = DecodeQueryData($_GET['studentFormativeMasterId']);
														} else {
															$studentFormativeMasterId = 0;
														}

														$schoolFormativeQuestionId = $row['schoolFormativeQuestionId'];
														$schoolFormativeQuestionTitle = $row['optionText'];
														$schoolFormativeQuestionType = $row['schoolFormativeQuestionType'];
														$qhtml = GetFormativeQuestionHtml($schoolFormativeQuestionId, $schoolFormativeQuestionType, $studentFormativeMasterId, $currentSchoolId);

												?>
														<div class="panel-body">
															<b><?php echo ($schoolFormativeQuestionTitle); ?> </b><br /><br />
															<?php echo $qhtml; ?>
														</div>
												<?php
													}
												}
												?>
											</div>
										</div>
								<?php
									}
								}

								?>
							</div>



						</div>
					</div>
				</div>

				<div class="row">
					<div class="form-group">
						<label class="col-md-2 control-label"></label>
						<div class="col-md-10">
							<?php
							$rotationStatus = checkRotationStatus($formativerotationid);
							if ($rotationStatus == 0) {
							?>
								<button style="margin: 0 0 0 11px;" id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
							<?php } ?>
							<?php if ($currentstudentId > 0) { ?>
								<a type="button" href="formativelist.html?studentId=<?php echo EncodeQueryData($currentstudentId); ?>" class="btn btn-default">Cancel</a>
							<?php } else { ?>
								<a type="button" href="formativelist.html?formativerotationid=<?php echo EncodeQueryData($formativerotationid); ?>" class="btn btn-default">Cancel</a>
							<?php } ?>
						</div>
					</div>
				</div>
			</div>
		</form>
		<?php include('includes/footer.php'); ?>
		<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
		<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
		<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
		<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
		<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
		<!-- <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/ckeditor.js"></script> -->



		<script type="text/javascript">
			// ClassicEditor
 			// .create(document.querySelector('#studentcomment'))
 			// .catch(error => {
 			// 	console.error(error);
 			// });	
			$(window).load(function() {


				$('#frmformative').parsley().on('field:validated', function() {
						var ok = $('.parsley-error').length === 0;
					})
					.on('form:submit', function() {
						ShowProgressAnimation();
						return true; // Don't submit form for this demo
					});

				$('#evaluationDate').datetimepicker({
					format: 'MM/DD/YYYY',
					maxDate: moment()
				});
				var evaluationDate = '<?php echo $evaluationDate; ?>';
				$('#evaluationDate').val(evaluationDate);

				$('#studentsignitureDate').datetimepicker({
					format: 'MM/DD/YYYY'
				});
				$('#InstructorDate').datetimepicker({
					format: 'MM/DD/YYYY'
				});


				//for searching dropdown
				$(".select2_single").select2();
				$('#select2-cborotation-container').addClass('required-select2');
				$('#select2-cboclinician-container').addClass('required-select2');
				$('#select2-cbostudent-container').addClass('required-select2');

				<?php if (isset($_GET['studentFormativeMasterId']) && ($_GET['formativerotationid'])) { ?>
					$('#cbostudent').prop('disabled', true);
				<?php } ?>

				<?php if ($currentstudentId > 0) { ?>
					$('#cbostudent').prop('disabled', true);
				<?php }
				if ($formativerotationid > 0) { ?>
					document.getElementById("cborotation").required = false;
				<?php } ?>


			});
		</script>
</body>

</html>