<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsFormative.php');
include('../setRequest.php');

$sectionMasterId = 0;
$sectionId = 0;
$currentSchoolId;
$counter = 1;

if (isset($_GET['sectionMasterId'])) {
    $sectionMasterId = $_GET['sectionMasterId'];
    $sectionMasterId = DecodeQueryData($_GET['sectionMasterId']);
}

$totalFormativeevaluation = 0;
$objFormative = new clsFormative();
if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
    $Formativeevaluationquestion = $objFormative->GetAllDefaultFormativeEvaluationQuestionToSetting($sectionMasterId);
else
    $Formativeevaluationquestion = $objFormative->GetAllFormativeEvaluationQuestionToSetting($currentSchoolId, $sectionMasterId);

$totalFormativeevaluation = ($Formativeevaluationquestion != '') ? mysqli_num_rows($Formativeevaluationquestion) : 0;
$loggedAsBackUserId = isset($_SESSION["loggedAsBackUserId"]) ? $_SESSION["loggedAsBackUserId"] : 0;
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Formative Evaluation Steps</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Setting </a></li>
                    <li><a href="formativeEvaluationSectionList.html">Formative Evaluation Section</a></li>
                    <!-- <li><a title="<?php //echo ($TopicTitle); 
                                        ?>" href="checkoffsection.html?topicid=<?php //echo EncodeQueryData($schoolTopicId); 
                                                                                ?>&schoolSectionId=<?php //echo EncodeQueryData($schoolSectionId); 
                                                                                                                                ?>"><?php //echo substr($TopicTitle,0,21).(' - ' . $SingleSection); 
                                                                                                                                                                                ?>-PEF Checkoff Section</a></li> -->
                    <li class="active">Steps</li>
                </ol>
            </div>
            <?php
            if ($loggedAsBackUserId || $currentSchoolId == 1) {
            ?>
                <div class="pull-right">
                    <ol class="breadcrumb">
                        <a href="addformativeEvaluationQuestions.html?sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>">Add</a>
                    </ol>
                </div>
            <?php
            }
            ?>
        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Question added successfully.
                </div>
        <?php
            }
        } ?>
        <div id="divTopLoading">Loading...</div>

        <form name="checkoffquestion" id="checkoffquestion" data-parsley-validate method="POST" action="questionsubmit.html">
            <div class="row">
                <div class="col-md-10  margin_bottom_ten"></div>
                <div class="col-md-2  margin_bottom_ten">
                    <div class="form-group">

                    </div>
                </div>
            </div>
            <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                <thead>
                    <tr>
                        <th>Sr. No</th>
                        <?php if ($loggedAsBackUserId || $currentSchoolId != 1) { ?>
                            <th>Sort Order</th>
                        <?php } ?>

                        <th>Steps Title</th>
                        <th>Type</th>
                        <th style="text-align: center">Action</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    if ($totalFormativeevaluation > 0) {
                        while ($row = mysqli_fetch_array($Formativeevaluationquestion)) {
                            if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
                            {
                                $schoolFormativeQuestionId = $row['formativeQuestionId'];
                                $schoolFormativeQuestionType = $row['formativeQuestionType'];
                            } else //For school admin 
                            {
                                $schoolFormativeQuestionId = $row['schoolFormativeQuestionId'];
                                $schoolFormativeQuestionType = $row['questionType'];
                            }

                            $optionText = $row['optionText'];
                            $sortOrder = isset($row['sortOrder']) ? $row['sortOrder'] : '';
                            $actiontype = '';
                            if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
                                $assignedQuestions = $objFormative->GetAllDefaultFormativeEvaluationAssignQuestionToSection($sectionMasterId);
                            else //For school admin 
                                $assignedQuestions = $objFormative->GetAllFormativeEvaluationAssignQuestionToSection($currentSchoolId, $sectionMasterId);

                            if ($isCurrentSchoolSuperAdmin == 1) // For superadmin
                                $assignedQuestionId = $row['formativeQuestionId'];
                            else //For school admin
                                $assignedQuestionId = $row['schoolFormativeQuestionId'];

                            if ($assignedQuestionId > 0)
                                $actiontype = "false";
                            else
                                $actiontype = "true";

                            $shortTitlelen = strlen($optionText);

                            if ($shortTitlelen > 80) {
                                $schoolQuestionTitle = substr($optionText, 0, 80);
                                $schoolQuestionTitle .= '...';
                            } else {
                                $schoolQuestionTitle = $optionText;
                            }

                    ?>
                            <tr>
                                <td><?php echo ($counter); ?></td>
                                <?php if ($loggedAsBackUserId || $currentSchoolId != 1) { ?>
                                    <td><?php echo ($sortOrder); ?></td>
                                <?php } ?>
                                <!-- <td style="text-align: center"> 
                                <input schoolFormativeQuestionId="<?php echo EncodeQueryData($schoolFormativeQuestionId); ?>" SchoolId="<?php echo EncodeQueryData($currentSchoolId); ?>"   type="checkbox"  id="checkoffquestion" name="checkoffquestion[]" 
                                value=""  actiontype="<?php echo ($actiontype); ?>" sectionMasterId="<?php echo EncodeQueryData($sectionMasterId); ?>" <?php if ($assignedQuestionId == $schoolFormativeQuestionId) { ?>  checked  class="checkoffquestion sendrequest chkque"<?php  } else { ?> class="sendrequest chkque" <?php } ?>>
                                </td> -->
                                <td title="<?php echo ($optionText); ?>">
                                    <?php echo ($schoolQuestionTitle);
                                    ?>
                                </td>
                                <td><?php echo ($schoolFormativeQuestionType); ?></td>
                                <td style="text-align: center">
                                    <a href="addformativeEvaluationQuestions.html?editid=<?php echo EncodeQueryData($schoolFormativeQuestionId); ?>&sectionMasterId=<?php echo EncodeQueryData($sectionMasterId); ?>">Edit</a>
                                    <?php if (isset($_SESSION["loggedAsBackUserId"]) || $isCurrentSchoolSuperAdmin == 1) { ?>
                                        | <a href="javascript:void(0);" class="deleteAjaxRow" QuestionId="<?php echo EncodeQueryData($schoolFormativeQuestionId); ?>">Delete</a>

                                    <?php } ?>
                                </td>
                            </tr>
                    <?php
                            $counter++;
                        }
                    }
                    ?>
                </tbody>
            </table>
        </form>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            var TotalCheckboxCount = $('input[name="checkoffquestion[]"]').length;
            var CheckedCheckboxCount = $('input[name="checkoffquestion[]"]:checked').length;
            if (TotalCheckboxCount == CheckedCheckboxCount) {
                $('.selectall').prop('checked', true);

            } else {
                $('.selectall').prop('checked', false);

            }

            $("#divTopLoading").addClass('hide');

        });
        var current_datatable = $("#datatable-responsive").DataTable({
            "aoColumns": [{
                    "sWidth": "2%",
                    "bSortable": true
                },
                // {"sWidth": "3%","bSortable": false},
                {
                    "sWidth": "80%"
                },
                {
                    "sWidth": "1%"
                },
                {
                    "sWidth": "15%",
                    "bSortable": false
                }
            ],

            "aLengthMenu": [
                [100, 200, 300, 400, 500, -1],
                [100, 200, 300, 400, 500, "All"]
            ],
            "iDisplayLength": 100,
        });



        $('.chkque').click(function() {
            var TotalCheckboxCount = $('input[name="checkoffquestion[]"]').length;
            var CheckedCheckboxCount = $('input[name="checkoffquestion[]"]:checked').length;
            if (TotalCheckboxCount == CheckedCheckboxCount) {
                $('.selectall').prop('checked', true);

            } else {
                $('.selectall').prop('checked', false);

            }
        });

        $('.addCommentpopup').magnificPopup({
            'type': 'ajax',
        });

        $('#selectall').click(function() {
            if ($(this).is(':checked')) {
                $('input:checkbox').prop('checked', true);
                var ischeckall = 1;
            } else {
                //$('input:checkbox').prop('checked', false);
                $('input').filter(':checkbox').removeAttr('checked');
                var ischeckall = 0;
            }


            var schoolSectionId = '<?php echo ($sectionMasterId); ?>';
            var SchoolId = '<?php echo ($currentSchoolId); ?>';


            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/assign_questions.html",
                data: {
                    schoolSectionId: schoolSectionId,
                    SchoolId: SchoolId,
                    ischeckall: ischeckall,
                    type: 'assign_all_questions'
                },
                success: function() {
                    if (ischeckall == 1) { //Assigned 
                        alertify.success('Assigned');
                    } else if (ischeckall == 0) { //Removed
                        alertify.error('Removed');
                    }
                }
            });
        });

        //FOR SEND REQUEST 1 by 1
        $("#datatable-responsive").on("click", ".sendrequest", function() {


            var action;
            var thischeck = $(this);
            var schoolQuestionId = $(this).attr('schoolCIEvaluationQuestionId');
            var SchoolId = $(this).attr('SchoolId');
            var actiontype = $(this).attr('actiontype');
            var schoolSectionId = $(this).attr('sectionMasterId');

            $.ajax({
                type: "POST",
                url: "<?php echo ($dynamicOrgUrl); ?>/ajax/assign_questions.html",
                data: {
                    id: schoolQuestionId,
                    action: actiontype,
                    SchoolId: SchoolId,
                    schoolTopicId: schoolTopicId,
                    schoolSectionId: schoolSectionId,
                    type: 'assign_questions'
                },
                success: function() {
                    if (actiontype == 'true') { //Assigned 
                        alertify.success('Assigned');
                        thischeck.attr('actiontype', 'false');
                    } else if (actiontype == 'false') { //Removed
                        alertify.error('Removed');
                        thischeck.attr('actiontype', 'true');
                    }
                }
            });

        });

        //Delete Questions
        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var QuestionId = $(this).attr('QuestionId');
            var title = $(this).attr('title');
            var isCurrentSchoolSuperAdmin = "<?php echo $isCurrentSchoolSuperAdmin; ?>";

            alertify.confirm('Formative Evaluation Question: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: QuestionId,
                        isCurrentSchoolSuperAdmin: isCurrentSchoolSuperAdmin,
                        type: 'FormativeEvaluation_Question'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });
    </script>
</body>

</html>