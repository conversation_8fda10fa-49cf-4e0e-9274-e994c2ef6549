<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php'); 
    include('../class/clsMasterCheckoffQuestion.php');     
	include('../setRequest.php'); 

    $schoolId = 0;
    $questionTypeId = 0;
    $questionType = 0;
	
    $schoolId= $currentSchoolId;
    $title ="Add Question - ".$currenschoolDisplayname;
 

    $page_title ="Add Question";
    $QueId = 0;
    $title = '';
	$sordOrder ='';
    $bedCrumTitle = 'Add';
	if(isset($_GET['sectionId']))
	{
		$sectionId=DecodeQueryData($_GET['sectionId']);
	}
	if(isset($_GET['topicid']))
	{
		 $topicid=DecodeQueryData($_GET['topicid']);
	}
    if(isset($_GET['QueId'])) //Edit Mode
	{
        $QueId = DecodeQueryData($_GET['QueId']);
	    $page_title ="Edit Question";
        $bedCrumTitle = 'Edit';        
	}
	$objQuestion = new clsMasterCheckoffQuestion();
    $GetQuestionType = $objQuestion->GetQuestionType();
    unset($objQuestion);
?>    
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
		<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">


    	<style>
		.singlechoiceboxdiv {
			display: flex;
			align-items: center;
			gap: 15px;
			margin-bottom: 15px;
		}

		.defualtanswer-div{
			width: 100%;
			display: flex;
			align-items: center;
			gap: 15px;
		}

		/* .form-horizontal .control-label{
			margin-bottom: 0 !important;
		} */
		@media screen and (max-width: 600px) {
			.singlechoiceboxdiv {
				flex-wrap: wrap;
			}
           .mobile-flex-wrap{
			flex-wrap: wrap;
		   }
		}
	</style>

</head>

<body>
    <?php include('includes/header.php');?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>
                    <li><a href="dashboard.html">Setting</a></li>
                    <li><a href="usafmastercheckofftopic.html">Military Comps Topics</a></li>
                    <li><a href="usafmastercheckoffsection.html?sectionId=<?php echo EncodeQueryData($sectionId); ?>">Comps Section </a></li>
                    <li><a href="usafmasterquestion.html?sectionId=<?php echo EncodeQueryData($sectionId); ?>">Comps Steps</a></li>                   
                    <li class="active"><?php echo($bedCrumTitle);?></li>
                </ol>
            </div>

        </div>
    </div>

    <div class="container">

         <form id="frmaddquestion" name="frmaddquestion" data-parsley-validate class="form-horizontal" method="POST" <?php if(isset($_GET['QueId'])){?> action="usafmasterquestionsubmit.html?editid=<?php echo(EncodeQueryData($QueId)); ?>&topicid=<?php echo EncodeQueryData($topicid); ?>&sectionId=<?php echo EncodeQueryData($sectionId); ?>" <?php } else { ?> action="usafmasterquestionsubmit.html?topicid=<?php echo EncodeQueryData($topicid); ?>&sectionId=<?php echo EncodeQueryData($sectionId); ?> <?php } ?>" >

            <div class="row">
			
				<div class="col-md-6">
				
				<div class="form-group">
                        <label class="col-md-12 control-label" for="cboQuestionType">Question Type</label>
                        <div class="col-md-12 flex-col-reverse">
                            <select id="cboQuestionType" name="cboQuestionType" class="form-control input-md required-input select2_single quetype" required>
                            <option value="" selected>Select</option>
								<?php
                                if($GetQuestionType!="")
                                {
                                    while($row = mysqli_fetch_assoc($GetQuestionType))
                                    {
                                         $selquestionTypeId  = $row['questionTypeId'];
                                         $selquestionType  = $row['questionType'];
										 $name  = stripslashes($row['title']);
										 if($selquestionType == 3 || $selquestionType == 4 || $selquestionType == 6 || $selquestionType == 7 || $selquestionType == 8 ){
											continue;
											}

                                         ?>
										<option value="<?php echo($selquestionType); ?>" <?php if($questionType==$selquestionType){ ?>  selected="true" <?php } ?>><?php echo($name); ?></option>
										<?php

                                    }
                                }
                            ?>
							</select>  
							 <input type="hidden" value="" name="questionType" id="questionType">
						</div>
                    </div>
				</div>
				<div class="col-md-6">
				<div class="form-group">
					<label class="col-md-12 control-label clslongans" for="Note" style="display: none;">Read</label>
					<label class="col-md-12 control-label clssingle" for="Note" style="display: none;">Read</label>
					<label class="col-md-12 control-label clsyesno" for="Note" style="display: none;">Read</label>					
					<div class="col-md-12">
							<div class="clslongans" style="display: none;">
								<input class="form-control" readonly type="text" value="Answer is of text type">
							</div>
							<div class="clssingle" style="display: none;">
								<input class="form-control" readonly type="text" value="Multiple choices with only one correct answer">
							</div>
							<div class="clsyesno" style="display: none;">
								<input class="form-control" readonly type="text" value="Answser of type Yes/No">
							</div>
					</div>
				</div>
				</div>
			</div>
			<div class="row">
                <div class="col-md-6">
					<div class="form-group">
                        <label class="col-md-12 control-label" for="question">Question Title</label>
                        <div class="col-md-12">
                            <textarea id="question"  name="question" class="form-control input-md required-input" required></textarea>

                        </div>
                    </div>
				</div>
			
                <div class="col-md-6 clsmark">
					<div class="form-group">
                        <label class="col-md-12 control-label" for="Marks">Marks</label>
                        <div class="col-md-12">
                            <input type="text" id="Marks"  name="Marks" class="form-control input-md required-input" required>

                        </div>
                    </div>
				</div>
			</div>
			
			<div class="row">
                <div class="col-md-6">
					<div class="form-group">
                        <label class="col-md-12 control-label" for="SortOrder">Step Number</label>
                        <div class="col-md-12">
                            <input type="text" id="SortOrder"  name="SortOrder" class="form-control input-md required-input" required>

                        </div>
                    </div>
				</div>

							
			<div class="col-md-12">
				<div class="form-group clsyesno m-0" style="display: none;">
					
						<div class="form-group yesnoboxdiv">
						  <label class="col-md-12 control-label" for="yes">Default Answer</label>
							<div class="col-md-12">
								<input type="radio" class="clsyesnoans" id="yesno" name="yesno[]" value="0">
								<input type="hidden"  id="yesnoanswers" name="yesnoanswers[]" value="0">
								
								<input type="hidden"   name="txtyesno[]" value="Sat">
								<label class="control-label" for="yes">Sat</label>
							</div>
						</div>
						<div class="form-group yesnoboxdiv">
						  <label class="col-md-12 control-label" for="no"></label>
							<div class="col-md-12">
								<input type="radio" class="clsyesnoans" id="yesno" name="yesno[]" value="0">
								<input type="hidden"  id="yesnoanswers" name="yesnoanswers[]" value="0">
								<input type="hidden"   name="txtyesno[]" value="UnSat">

								<label class="control-label" for="no">UnSat</label>
							</div>
						</div>
						
						<div class="form-group yesnoboxdiv">
						  <label class="col-md-12 control-label" for="None"></label>
							<div class="col-md-12">
								<input type="radio" class="clsyesnoans" id="yesno" name="yesno[]" value="0">
								<input type="hidden"  id="yesnoanswers" name="yesnoanswers[]" value="0">
								<input type="hidden"   name="txtyesno[]" value="NI">

								<label class="control-label" for="None">NI</label>
							</div>
						</div>
					 
					</div>
					<div class="form-group clslongans  m-0" style="display: none;">
						<div class="form-group">
							<label class="col-md-12 control-label" for="Answer">Default Answer </label>
							<div class="col-md-12">
							 <textarea id="longans" name="longans" class="form-control input-md" rows="4" ></textarea>
							</div>
							
						</div>
						
					</div>
					
					<div class="form-group clssingle mx-0" style="display: none;">
						<div class="">						  
							  <div class="col-md-12 control-label px-0 ">Default Answer</div>
							  <div class="col-md-12 px-0">
									<div class="row mx-0">
										<div class="col-md-12 px-0 textboxDiv" >
											<div class="singlechoiceboxdiv">
											<div class="defualtanswer-div mobile-flex-wrap">
												<input type="radio"  id="singlechoice" name="singlechoice[]"  class="choicebox singlechoice" value="0">
												<input type="hidden" id="hid_anser" class="hid_anser"  name="answers[]" value="0">
												<input type="text" id="txtsinglechoice"  class="form-control input-md required-input choicebox" name="txtsinglechoice[]" placeholder="Add an answer" required>
												</div>
												<div class="defualtanswer-div">
												<input type="text" id="txtsinglechoicemarks"  class="form-control input-md required-input choicebox" name="txtsinglechoicemarks[]" placeholder="Add marks" required>
												<span id="Remove" style="color:red;" class="glyphicon glyphicon-trash Remove"></span>
												<!---button type="button" id="Remove" class="btn btn-danger Remove">Remove</button--->
											</div>
										</div>
										</div>
										
										<div class="col-md-12 mt-15" style="display: flex;justify-content: end;">
                                       <button type="button" id="Add" class="btn btn-success text_left">Add</button>
										</div>
										<div class="col-md-2">
										<!--button type="button" id="Remove" class="btn btn-danger">Remove</button--->
										</div>
										
									</div>
							  </div>
						</div>
						
					</div>
					
				</div>
				
				
			</div>
			
			
			<div class="form-group mx-0">
                        <!-- <label class="col-md-2 control-label"></label> -->
						<div class="col-md-12" style="display: flex; justify-content: center; margin: 20px 0;gap: 15px;">
						<button id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
									<a type="button" href="usafmasterquestion.html?sectionId=<?php echo  EncodeQueryData($sectionId); ?>" class="btn btn-default">Cancel</a>
						</div>
				</div>
				
		</form>
            </div>
        


    </div>

    <?php include('includes/footer.php');?>
   
	
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>	
	<script type="text/javascript" src="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
    <script type="text/javascript">
 
 
  $(".select2_single").select2();
         $('#select2-cboQuestionType-container').addClass('required-select2');
        $(window).load(function(){

             $('#frmaddquestion').parsley().on('field:validated', function() {
			    var ok = $('.parsley-error').length === 0;
            })
            .on('form:submit', function() {
                ShowProgressAnimation();									  
                return true; // Don't submit form for this demo
            });
			
			
			
		
        });
		$(function() {
		$('#cboQuestionType').change(function() {							
							var x = $('#cboQuestionType option:selected').val();							
							$('#questionType').val(x);							
						 });
						 
					});
					
		$( document ).ready(function() {
			$("select").change(function(){
				if($(this).val() == 1)
					$('.clsyesno').show();
				else
					$('.clsyesno').hide();
				document.getElementById("yesno").required = false;
			});
		});
		$( document ).ready(function() {
			$("select").change(function(){
				if($(this).val() == 5)
					$('.clslongans').show();
				else
					$('.clslongans').hide();
				document.getElementById("longans").required = false;
			});
		});
		$( document ).ready(function() {
			$("select").change(function(){
				if($(this).val() == 2)
					$('.clssingle').show();
				else
					$('.clssingle').hide();
				document.getElementById("singlechoice").required = false;
				document.getElementById("txtsinglechoice").required = false;
				document.getElementById("txtsinglechoicemarks").required = false;
			});
		});
		
		$( document ).ready(function() {
			$("select").change(function(){
				if($(this).val() != 2)
					$('.clsmark').show();
				else
					$('.clsmark').hide();
				document.getElementById("Marks").required = false;
			});
		});
		
		$(document).ready(function() { 
		
			
            $("#Add").on("click", function() {  
			
                $(".textboxDiv").append("<div class='singlechoiceboxdiv'><div class='defualtanswer-div mobile-flex-wrap'> <input type='radio'  class='choicebox singlechoice' id='singlechoice' value='0' name='singlechoice[]' '><input type='text' class='form-control input-md required-input choicebox' id='txtsinglechoice' name='txtsinglechoice[]' placeholder='Add an answer' required /><input type='hidden'  id='hid_anser' class='hid_anser' name='answers[]'  value='0'/></div><div class='defualtanswer-div'> <input type='text' id='txtsinglechoicemarks'  class='form-control input-md required-input choicebox' name='txtsinglechoicemarks[]' placeholder='Add marks' required><span id='Remove' style='color:red;' class='glyphicon glyphicon-trash Remove'></span></div></div>");  
                 
            });  
           // $(".Remove").on("click", function() {  
            $(document).on("click", ".Remove", function() {  			
				$(this).closest('.singlechoiceboxdiv').remove();
            });  
        });  
	
	
	$(document).on("click", ".singlechoice", function(event){
			console.log($(event.target).hasClass('singlechoice'));
			if($(event.target).hasClass('singlechoice')){
				$('input[id=hid_anser]').val(0);
				//$(this).closest(".textboxDiv").find('input[class=hid_anser]').val($(this).is(":checked"));
			$(this).closest(".singlechoiceboxdiv").find('input[id=hid_anser]').val('1');
				
			}
		});
		
	
	$(document).on("click", ".clsyesnoans", function(event){
			console.log($(event.target).hasClass('clsyesnoans'));
			if($(event.target).hasClass('clsyesnoans')){
				$('input[id=yesnoanswers]').val(0);
				//$(this).closest(".textboxDiv").find('input[class=hid_anser]').val($(this).is(":checked"));
			$(this).closest(".yesnoboxdiv").find('input[id=yesnoanswers]').val('1');
				
			}
		});
		
	
	$(document).on('click', '.choicebox', function(e) {	
		
			if(this.checked) {
				//var inputboxvalue = $(this).closest('.clssingle').find('input[name="singlechoice[]"]').val();
				var txtinputboxvalue = $(this).closest('.singlechoiceboxdiv').find('input[name="txtsinglechoice[]"]').val();
					
				if(txtinputboxvalue == '')
				 {					  
					alertify.alert('Warning', 'Please write answer to selected option!');
					 $('#btnsubmit').addClass('disabled');
				 }
				 else
				 {
					 $('#btnsubmit').removeClass('disabled');
				 }
			}
		});
		
	
		 $(document).ready(function(){
        $("#btnSubmit").click(function(){ 

		var questionType = $("select").val();
		if(questionType == 2 || questionType == 1)
		{
            if($("input[type=radio]:checked").length > 0)
			{
				//alert('success');
               return true;
            }
			else
			{
				 alert("please select correct answer");
				 return  false;
			}
		}
        });
        
    });
	
		
    </script>

</body>

</html>