<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsQuestionOption.php');
include('../class/clsClinician.php');
include('../class/clsIrr.php');
include('../class/clscheckoff.php');
include('../class/clsCoarc.php');
include('../class/clsGraduateCoarc.php');
include('../class/clsEmployerCoarcRequestMaster.php');

include('../class/clsEmployerCoarc.php');
include('../class/clsStudent.php');


$CoarcOptionAnswerText = '';
$loggedUserLocationId = '';
$currentSchoolId;
$tranSchoolDisplayname = $currenschoolDisplayname;
$schoolId = $currentSchoolId;
$title = '';
$selstudentId = 0;
$studentId = 0;
$AssignedcoarcSurveyMasterId = 0;
$TimeZone = $TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

if (isset($_GET['coarcSurveyMasterId'])) {
   $coarcSurveyMasterId = DecodeQueryData($_GET['coarcSurveyMasterId']);
}


if (isset($_GET['AssignedcoarcSurveyMasterId'])) {
   $AssignedcoarcSurveyMasterId = DecodeQueryData($_GET['AssignedcoarcSurveyMasterId']);
}

if (isset($_GET['studentId'])) {
   $studentId = DecodeQueryData($_GET['studentId']);
}

//For Student List
$objCoarc = new clsCoarc();
$rowsData = $objCoarc->GetStudentDetailsByCoarcSurvey($coarcSurveyMasterId, $currentSchoolId);
$totalCount = 0;
if ($rowsData != '') {
   $totalCount = mysqli_num_rows($rowsData);
}

$totalSection = 0;
$objEmployerCoarc = new clsEmployerCoarc();
$CoarcSection = $objEmployerCoarc->GetEmployeerSections($schoolId);
if ($CoarcSection != '') {
   $totalSection = mysqli_num_rows($CoarcSection);
}

//Student count
$rowsStudentData = $objCoarc->GetStudentListByCoarcSurvey($coarcSurveyMasterId, $currentSchoolId);
$totalStudentCount = 0;
if ($rowsStudentData != '') {
   $totalStudentCount = mysqli_num_rows($rowsStudentData);
}


//Get Corac Survey Title
$coarcSurveyTitle = $objCoarc->GetCoarcSurveyTitle($coarcSurveyMasterId);
$reportType = "EmployerReport";

$objEmployerCoarcRequestMaster = new clsEmployerCoarcRequestMaster();

?>
<!DOCTYPE html>
<html lang="en">

<head>
   <meta charset="utf-8">
   <meta http-equiv="X-UA-Compatible" content="IE=edge">
   <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
   <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
   <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
   <title>Coarc Report</title>
   <?php include('includes/headercss.php'); ?>
   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
   <?php include("includes/datatablecss.php") ?>
   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

   <style>
      .collapsible {
         cursor: pointer;
         /* padding: 15px; */
         /* border: 1px solid #181818; */
         /* background-color: #f9f9f9; */
         display: flex;
         justify-content: space-between;
         align-items: center;
         /* border-radius: 14px; */
      }

      .collapsible p {
         margin: 0;
      }

      .collapsible-arrow {
         font-size: 18px;
         transition: transform 0.3s ease;
      }

      .content {
         display: none;
         padding: 10px 0;
         /* border-top: 1px solid #ececec; */
      }

      .content.active {
         display: block;
      }

      .active.collapsible-arrow {
         transform: rotate(180deg);
      }

      .row-delete-icon {
         position: absolute;
         top: -82px;
         right: 20px;
      }

      .mobile-block {
         display: block;
      }
   </style>
</head>

<body>
   <?php include('includes/header.php'); ?>
   <div class="row margin_zero breadcrumb-bg">
      <div class="container">
         <div class="pull-left">
            <ol class="breadcrumb">
               <li><a href="dashboard.html">Home</a></li>
               <li><a href="settings.html">Settings</a></li>
               <li><a href="employeeCoarcSurveyReport.html"> Employer JRCERT Survey Report</a></li>
               <li class="active">Employer JRCERT Report</a></li>
            </ol>
         </div>
         <div class="pull-right">
            <form id="frmexportreport" data-parsley-validate class="form-horizontal" method="POST" action="exportEmployerServey.html?Type=<?php echo ($reportType); ?>&coarcSurveyMasterId=<?php echo (EncodeQueryData($coarcSurveyMasterId)); ?>&AssignedcoarcSurveyMasterId=<?php echo (EncodeQueryData($AssignedcoarcSurveyMasterId)); ?>&studentId=<?php echo (EncodeQueryData($studentId)); ?>" enctype="multipart/form-data">
               <input type="hidden" name="cboreporttype" value="<?php echo ($reportType); ?>">
               <input type="submit" name="btnStudentExport" id="btnStudentExport" class="btn btn-link" value="Export to Excel">

            </form>
         </div>
      </div>
   </div>
   <div class="container">
      <div class="row">
         <div class="col-md-8">
            <h4>Report Title: <?php echo $coarcSurveyTitle; ?></h4>
         </div>
         <div class="col-md-4 pull-right padding_zero  margin_zero">
            <div class="form-group">
               <div class="col-md-4 text-right">
                  <label class="control-label" for="cbostudent" style="margin-top:8px">Student:</label>
               </div>
               <div class="col-md-8">
                  <select id="cbostudent" name="cbostudent" class="form-control input-md  select2_single">
                     <option value="" selected>Select</option>
                     <?php
                     if ($totalCount > 0) {
                        while ($row = mysqli_fetch_array($rowsData)) {
                           $selstudentId = $row['studentId'];
                           $coarcSurveyMasterId = ($row['coarcSurveyMasterId']);
                           $firstName = stripslashes($row['firstName']);
                           $lastName = stripslashes($row['lastName']);
                           $name  = $firstName . ' ' . $lastName;

                     ?>
                           <option value="<?php echo EncodeQueryData($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                     <?php
                        }
                     }
                     ?>
                  </select>
               </div>
            </div>
         </div>
      </div>
      <br><br>
      <div class="row">
         <div class="col-md-12">
            <div class="panel-group" id="posts">
               <?php
               if ($totalSection > 0) {
                  while ($row = mysqli_fetch_array($CoarcSection)) {
                     $sectionMasterId = $row['sectionMasterId'];
                     $title = $row['title'];
                     //$subTitle = $row['subTitle'];
               ?>
                     <div class="panel panel-default">
                        <a class="collapsible" style="color: #000; text-decoration: none;" href="#collapse1" data-toggle="collapse" data-parent="#accordion" id="collapse-link">
                           <div class="panel-heading" style="width : 100%; display: flex; justify-content: space-between; align-items: center;">
                              <h4 class="panel-title">
                                 <?php echo $title; ?>
                              </h4>
                              <span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
                           </div>
                        </a>
                        <div class="panel-body panel-collapse panel-body collapse">
                           <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover mytablecustom" cellspacing="0" width="100%">
                              <thead>
                                 <tr>
                                    <th>Student Program Resource Survey</th>
                                    <th>Strongly Agree</th>
                                    <th>Generally Agree</th>
                                    <th>Neutral</th>
                                    <th>Generally Disagree</th>
                                    <th>Strongly Disagree</th>
                                 </tr>
                              </thead>
                              <tbody>
                                 <?php
                                 $totalCoarc = 0;
                                 $Coarcquestion = $objEmployerCoarc->GetAllEmployeerCoarcSurveyQuestionMaster($schoolId, $sectionMasterId);

                                 if ($Coarcquestion != '') {
                                    $totalCoarcquestion = mysqli_num_rows($Coarcquestion);
                                 }

                                 if ($totalCoarcquestion > 0) {
                                    while ($row = mysqli_fetch_array($Coarcquestion)) {
                                       $coarcQuestionTitle = stripslashes($row['questionText']);

                                       $schoolEmployerCoarcQuestionId = $row['schoolEmployerCoarcQuestionId'];
                                       $schoolEmployerCoarcQuestionType = $row['schoolEmployerCoarcQuestionType'];

                                       $getCalculation = $objEmployerCoarc->GetCalculationsforEmployerReport($schoolEmployerCoarcQuestionId, $studentId, $coarcSurveyMasterId, $AssignedcoarcSurveyMasterId);


                                       if ($getCalculation != '') {
                                          while ($GetCalrows = mysqli_fetch_array($getCalculation)) {
                                             $CountStronglyAgree = $GetCalrows['CountStronglyAgree'];
                                             $CountAgree = $GetCalrows['CountAgree'];
                                             $CountNeutral = $GetCalrows['CountNeutral'];
                                             $CountDisagree = $GetCalrows['CountDisagree'];
                                             $CountStronglyDisagree = $GetCalrows['CountStronglyDisagree'];
                                             $CoarcOptionAnswerText = $GetCalrows['schoolCoarcOptionAnswerText'];
                                             //echo 'CoarcOptionAnswerText->'.$CoarcOptionAnswerText.'<hr>';
                                             $StronglyAgreePer = 0;
                                             $AgreePer = 0;
                                             $NeutralPer = 0;
                                             $DisagreePer = 0;
                                             $StronglyDisagreePer = 0;
                                             $TotalCount = $CountStronglyAgree + $CountAgree + $CountNeutral + $CountDisagree + $CountStronglyDisagree;

                                             if ($TotalCount > 0) {
                                                $StronglyAgreePer = $CountStronglyAgree * 100 / $TotalCount;

                                                $AgreePer = $CountAgree * 100 / $TotalCount;

                                                $NeutralPer = $CountNeutral * 100 / $TotalCount;

                                                $DisagreePer = $CountDisagree * 100 / $TotalCount;

                                                $StronglyDisagreePer = $CountStronglyDisagree * 100 / $TotalCount;
                                             }


                                 ?>
                                             <tr>
                                                <td <?php if ($schoolEmployerCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo $coarcQuestionTitle; ?></td>
                                                <?php if ($schoolEmployerCoarcQuestionType == 5) { ?>
                                                   <td colspan="5" <?php if ($schoolEmployerCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo ($CoarcOptionAnswerText); ?></td>
                                                <?php } else { ?>
                                                   <td <?php if ($schoolEmployerCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo number_format((float)$StronglyAgreePer, 2, '.', '') . '%'; ?></td>
                                                   <td <?php if ($schoolEmployerCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo number_format((float)$AgreePer, 2, '.', '') . '%'; ?></td>
                                                   <td <?php if ($schoolEmployerCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo number_format((float)$NeutralPer, 2, '.', '') . '%'; ?></td>
                                                   <td <?php if ($schoolEmployerCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo number_format((float)$DisagreePer, 2, '.', '') . '%'; ?></td>
                                                   <td <?php if ($schoolEmployerCoarcQuestionType == 5) { ?> class="hide" <?php } ?>><?php echo number_format((float)$StronglyDisagreePer, 2, '.', '') . '%'; ?></td>
                                                <?php } ?>
                                             </tr>
                                 <?php
                                          }
                                       }
                                    }
                                 }
                                 ?>
                              </tbody>
                           </table>
                        </div>
                     </div>
               <?php
                  }
               }  ?>

               <?php
               if (!isset($_GET['studentId'])) {
               ?>
                  <div class="panel panel-default">
                     <a class="collapsible" style="color: #000; text-decoration: none;" href="#collapse1" data-toggle="collapse" data-parent="#accordion" id="collapse-link">
                        <div class="panel-heading" style="width : 100%; display: flex; justify-content: space-between; align-items: center;">
                           <h4 class="panel-title">
                              6. Clinical Trac JRCERT Survey Results
                           </h4>
                           <span class="arrow-icon"> <i class="fa fa-angle-down" aria-hidden="true" id="collapse-icon" style="font-size: 19px;"></i></span>
                        </div>
                     </a>
                     <div class="panel-body panel-collapse panel-body collapse">
                        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover mytablecustom" cellspacing="0" width="100%">
                           <thead>
                              <tr>
                                 <th style="text-align: center">Survey Start Date</th>
                                 <th style="text-align: center">Survey End Date</th>
                                 <th style="text-align: center">Number of Surveys Sent</th>
                                 <th style="text-align: center">Number of Surveys Completed</th>
                                 <th style="text-align: center">Number of Surveys Pending</th>
                                 <th style="text-align: center">Return Rate</th>
                              </tr>
                           </thead>
                           <tbody>
                              <?php
                              $completedStatus = 0;

                              if ($totalStudentCount > 0) {
                                 while ($row = mysqli_fetch_array($rowsStudentData)) {

                                    $studentId = stripslashes($row['studentId']);
                                    $startDate = $row['startDate'];
                                    $startDate = converFromServerTimeZone($startDate, $TimeZone);
                                    $endDate = $row['endDate'];
                                    $endDate = converFromServerTimeZone($endDate, $TimeZone);
                                    $startDateTimestamp = strtotime($startDate);
                                    $endDateTimestamp = strtotime($endDate);

                                    $rowEmployee = $objEmployerCoarcRequestMaster->GetEmployerCoarcSurveyByStudent($studentId, $coarcSurveyMasterId);
                                    // print_r($rowEmployee); 
                                    $status = 0;

                                    if ($rowEmployee != '') {
                                       $status = stripslashes($rowEmployee['status']);
                                    }

                                    if ($status > 0) {
                                       $completedStatus += $status;
                                    }
                                 }
                              }
                              $pendingStatus = $totalStudentCount - $completedStatus;
                              //For Return Rate
                              $returnRate = ($completedStatus / $totalStudentCount) * 100;
                              $returnRate = number_format((float)$returnRate, 2, '.', '') . '%';
 

                              ?>
                              <tr>

                                 <td style="text-align: center"><?php echo (date('m/d/Y', $startDateTimestamp)); ?></td>
                                 <td style="text-align: center"><?php echo (date('m/d/Y', $endDateTimestamp)); ?></td>
                                 <td style="text-align: center"><?php echo $totalStudentCount; ?></td>
                                 <td style="text-align: center"><?php echo $completedStatus; ?></td>
                                 <td style="text-align: center"><?php echo $pendingStatus; ?></td>
                                 <td style="text-align: center"><?php echo $returnRate; ?></td>

                              </tr>
                           <?php
                        }
                        unset($objEmployerCoarcRequestMaster);
                           ?>
                           </tbody>
                        </table>
                     </div>
                  </div>
            </div>
         </div>
      </div>
   </div>
   <?php include('includes/footer.php'); ?>
   <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
   <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
   <script type="text/javascript">
      alertify.defaults.transition = "slide";
      alertify.defaults.theme.ok = "btn btn-success";
      alertify.defaults.theme.cancel = "btn btn-danger";
      alertify.defaults.theme.input = "form-control";

      $(window).load(function() {

         $(".select2_single").select2();
      });

      <?php
      if ($studentId) { ?>
         $('.hide').removeClass('hide');
      <?php } ?>

      $("#cbostudent").change(function() {
         var studentId = $(this).val();
         var coarcSurveyMasterId = "<?php echo EncodeQueryData($coarcSurveyMasterId); ?>";
         var AssignedcoarcSurveyMasterId = "<?php echo EncodeQueryData($AssignedcoarcSurveyMasterId); ?>";

         if (studentId) {
            window.location.href = "employeerCoarcSurveyReportView.html?coarcSurveyMasterId=" + coarcSurveyMasterId + "&studentId=" + studentId + "&AssignedcoarcSurveyMasterId=" + AssignedcoarcSurveyMasterId;
         } else {
            window.location.href = "employeerCoarcSurveyReportView.html?coarcSurveyMasterId=" + coarcSurveyMasterId + "&AssignedcoarcSurveyMasterId=" + AssignedcoarcSurveyMasterId;
         }
      });
   </script>
   <script>
      // Get all collapsible button elements
      var buttons = document.querySelectorAll(".collapsible");
      var contents = document.querySelectorAll(".panel-collapse");

      // Add click event listeners to all buttons
      buttons.forEach(function(button, index) {
         button.addEventListener("click", function() {
            // Check if the content is currently expanded
            var isExpanded = contents[index].style.display === "block";

            // Close all sections
            contents.forEach(function(content) {
               content.style.display = "none";
            });

            // Reset the "expanded" class for all buttons
            buttons.forEach(function(btn) {
               btn.classList.remove("expanded");
            });

            // Toggle the content for the clicked section
            if (!isExpanded) {
               contents[index].style.display = "block";
               button.classList.add("expanded");
            }
         });
      });
   </script>
</body>

</html>