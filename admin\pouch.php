<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsStudentRankMaster.php');
include('../class/clsCanvas.php');
$currentDomainName = GetCurrentDomainNameFromURL();

$objCanvas = new clsCanvas();

//Create Course
// $acceToken = '7~ubtFTB7ZRv3JiVrQdxteK78ZvHqjcHfK8AxUF2iWtpFVl9QTAPGhSmGWZkRRsupT';
// $objCanvas->updateCourseByStudent($acceToken,'4743002','29792219',$description);
// exit;

//Student Registration Link
$registrationLink = $dynamicOrgUrl . '/school/' . $schoolSlug . '/admin/studentRegister.html';

$loggedUserId = $_SESSION["loggedUserId"];
$TimeZone = '';
$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];

$loggedUserRoleType = '';
$loggedUserRoleType = $_SESSION["loggedUserRoleType"];

$isActiveCheckoff = $_SESSION["isActiveCheckoff"];

//Get School Name
$rankId = 0;
//CREATE OBJECT
$objStudents = new clsStudent();
$totalSchoolStudents = 0;
$accreditationId = 0;
$currentstudentId  = 0;
$currentSchoolId;
$Type = 'C';
$isDeleted = '';
$studentImmunizationId = 0;
if (isset($_GET['rankId'])) {
    $rankId = $_GET['rankId'];
    $rankId = DecodeQueryData($rankId);
}
if (isset($_GET['isDeleted'])) {
    $isDeleted = $_GET['isDeleted'];
}
if (isset($_GET['studentId'])) {
    $currentstudentId = $_GET['studentId'];
    $currentstudentId = DecodeQueryData($currentstudentId);
}

$display_from_date = '';
$display_to_date = '';
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSearch'])) {
    $display_from_date = $_POST['fromDate'];
    if($display_from_date  != '')
        $display_from_date = date("m/d/Y", strtotime($display_from_date));
    $display_to_date = $_POST['toDate'];
    if($display_to_date  != '')
        $display_to_date = date("m/d/Y", strtotime($display_to_date));
}

//SchoolStudents
if (isset($_GET['view'])) {
    $rowsSchoolStudents = $objStudents->GetAllStudents($currentSchoolId);
    if ($rowsSchoolStudents != '') {
        $totalSchoolStudents = mysqli_num_rows($rowsSchoolStudents);
    }
} else {
    $rowsSchoolStudents = $objStudents->GetAllSchoolStudentList($currentSchoolId, $rankId, $currentstudentId, $display_from_date, $display_to_date);
    if ($rowsSchoolStudents != '') {
        $totalSchoolStudents = mysqli_num_rows($rowsSchoolStudents);
    }
}
if (isset($_GET['studentId'])) {
    $studentId = $_GET['studentId'];
    $studentId = ($studentId);
}
if (isset($_GET['reaccreditationId'])) {
    $reaccreditationId = $_GET['reaccreditationId'];
    $reaccreditationId = ($reaccreditationId);
}


//StudentRank
$objStudentRankMaster = new clsStudentRankMaster();
$ranks = $objStudentRankMaster->GetAllStudentRankBySchool($currentSchoolId);
unset($objStudentRankMaster);
?>

<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title>Pouch</title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">


    <style>
        .mt-1 {
            margin-top: 10px;
            padding-left: 55px;
        }
    </style>

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li class="active">Pouch</li>
                </ol>
            </div>
            <div class="pull-right">
                <form action="pouchUpload.html" method="POST" id="pouchForm">
                    <input type="hidden" id="hideStudentIds" name="hideStudentIds" value="">
                    <input style="background-color:none" type="button" class="btn btn-link" id="addPouch" value="Add">
                </form>
            </div>
        </div>
    </div>

    <div class="container">

        <?php

        if (isset($_GET["status"])) {

            if ($_GET["status"] == "added") {

        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Student added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Uploaded") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Document uploaded successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "StatusUpdated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Student status updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
            <?php
            } else if ($_GET["status"] == "datanotfound") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Certification log not available.
                </div>
            <?php
            } else if ($_GET["status"] == "Imported") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Student Imported
                </div>
            <?php
            } else if ($_GET["status"] == "Importerror") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button>Please Import .csv file
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <div class="row ">
            <!-- <div class="col-md-8">
                        <div class="form-group">
                            <label class="col-md-2 control-label margin_top_five padding_zero" for="">Registration Link:</label>
                            <div class="col-md-8">
                                <input id="copyText" type="text" class="form-control" value="<?php echo $registrationLink; ?>" readonly>
                            </div>
                            <button class="col-md-2 btn btn-primary" id="copyBtn" onclick="CopyToClipboard('copyText')">Click to copy</button>
        
                        </div>

                    </div>     -->
            <!-- <div class="col-md-12 text-right">
                        <a href="schoolstudents.html?view=all" class="btn btn-primary pull-right">Display All</a>
                    </div> -->
        </div>
        <div class="col-md-9">
<div class="row">
                <form name="rotationlist" id="rotationlist" method="POST" action="pouch.html">

                    <div class="col-md-5  margin_bottom_ten pl-0">
                        <div class="form-group">
                            <label class="col-md-4 control-label pl-0" for="fromDate" style="margin-top:8px;text-align:end">From Date</label>

                            <div class='input-group date col-md-7' name="fromDate" id='fromDate'>
                                <input type='text' name="fromDate" id="fromDate" value="<?php echo ($display_from_date); ?>" class="dateInputFormat form-control input-md  rotation_date" data-parsley-errors-container="#error-txtDate" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>

                    <div class="col-md-5  margin_bottom_ten pl-0">
                        <div class="form-group">
                            <label class="col-md-4 control-label" for="toDate" style="margin-top:10px;text-align:end">To Date</label>
                            <div class='input-group date col-md-7' id='toDate'>
                                <input type='text' name="toDate" id="toDate" class="dateInputFormat form-control input-md  rotation_date" value="<?php echo ($display_to_date); ?>" data-parsley-errors-container="#error-txtDate" />
                                <span class="input-group-addon calender-icon">
                                    <span class="glyphicon glyphicon-calendar"></span>
                                </span>
                            </div>
                            <div id="error-txtDate"></div>
                        </div>
                    </div>
                    <div class="col-md-1  margin_bottom_ten">
                        <div class="form-group">
                            <div class="input-group">
                            <button id="btnSearch" name="btnSearch" class="btn btn-success">Search</button>
                            
                            </div>
                        </div>
                    </div>
                    <div class="col-md-1  margin_bottom_ten">
                        <?php if($display_from_date != '' && $display_to_date != '') {?>
                        <a class="btn btn-link form-control" href="pouch.html">Clear</a>
                        <?php }?>
                    </div>
                </form>
                </div>
            </div>
        <div class="row">
            <div class="col-md-3 pull-right">
                <div class="form-group">
                    <label class="col-md-4 control-label mt-1" for="cborank">Rank</label>
                    <div class="col-md-8 pr-0">
                        <select id="cborank" name="cborank" class="form-control select2_single">
                            <option value="" selected>Select All</option>

                            <?php
                            if ($ranks != "") {
                                while ($row = mysqli_fetch_assoc($ranks)) {

                                    $selrankId  = $row['rankId'];
                                    $name  = stripslashes($row['title']);

                            ?>
                                    <option value="<?php echo EncodeQueryData($selrankId); ?>" <?php if ($rankId == $selrankId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
                            <?php

                                }
                            }
                            ?>
                        </select>
                    </div>
                </div>
            </div>
        </div> <br>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th style="vertical-align: middle; width: 64px;" class="sorting_disabled" rowspan="1" colspan="1" aria-label="Select All  "><input class="selectall" type="checkbox" id="selectall" name="selectall[0]" value="0"> &nbsp;Select All </th>
                    <th>First Name</th>
                    <th>Last Name</th>
                    <th>Rank</th>
                    <th>Contact Info</th>
                    <th>Account Created</th>
                    <!-- <th style="text-align: center">Action</th> -->
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalSchoolStudents > 0) {
                    while ($row = mysqli_fetch_array($rowsSchoolStudents)) {
                        $studentId = '';
                        $studentId = $row[0];
                        $firstName = stripslashes($row['firstName']);
                        $lastName = stripslashes($row['lastName']);
                        $fullName = $firstName . ' ' . $lastName;
                        $email = stripslashes($row['email']);
                        $phone = stripslashes($row['phone']);
                        $cellPhone = stripslashes($row['cellPhone']);
                        $IsPublished = $row['isActive'];
                        $isBlocked = $row['isBlocked'];
                        $rank = stripslashes($row['rank']);
                        $createdDate = ($row['createdDate']);
                        $createdDate = date("m/d/Y", strtotime($createdDate));
                        $location = ($row['location']);
                        $updateBlockStatus = "0";
                        $buttoncss = "btn-primary";

                        if ($IsPublished == "1") {
                            $displayStatus = "Active";
                            $updateStatus = "0";
                            $buttoncss = "text-primary";
                            $isShowemail = 1;
                        } else {
                            $displayStatus = "Inactive";
                            $updateStatus = "1";

                            $buttoncss = "text-warning";
                            $isShowemail = 0;
                        }


                        if ($isBlocked == "0") {
                            $displayBlockStatus = "Unlocked";
                            $updateBlockStatus = "1";
                            $buttoncss = "text-primary";
                        } else {
                            $displayBlockStatus = "Padlock";
                            $updateBlockStatus = "0";
                            $buttoncss = "text-warning";
                        }
                        $TotaldocumentCount = $objStudents->getDocumentsCount($studentId, '', 0, 1);
                        $countDocument = $TotaldocumentCount['countDocument'];


                ?>
                        <tr>
                            <td style="text-align: center">
                                <input type="checkbox" id="students" name="studentIds[]"   value="<?php echo ($studentId); ?>" >
                            </td>
                            <td> <?php echo ($firstName); ?></td>
                            <td><?php echo ($lastName); ?></td>
                            <td><?php echo ($rank); ?></td>
                            <td>
                                Email: <a href="mailto:<?php echo ($email); ?>">
                                    <?php echo ($email); ?>
                                </a> <br>
                                Phone: <a href="tel:<?php echo ($phone); ?>">
                                    <?php echo ($phone); ?>
                                </a>
                                <?php if ($cellPhone) { ?>
                                    <br>
                                    Cell Phone: <a href="tel:<?php echo ($cellPhone); ?>">
                                        <?php echo ($cellPhone); ?>
                                    </a>
                                <?php } ?>
                            </td>
                            <?php if ($isActiveCheckoff == 2) { ?> <td><?php echo $location; ?></td> <?php } ?>
                            <td><?php echo ($createdDate); ?>
                                <!-- <br>       
                                Status: <?php //echo($displayBlockStatus);
                                        ?> -->
                            </td>
                            <!-- <td style="text-align: center">			
                                    <a href="javascript:void(0);" class="deleteAjaxRow"
                                                studentId="<?php echo EncodeQueryData($studentId); ?>" schoolStudentFullName="<?php echo ($fullName); ?>">Delete</a>
                                    

                                </td> -->
                        </tr>
                <?php


                    }
                }
                unset($objStudents);
                ?>



            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
    <script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";

        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_tags").select2({
                'placeholder': 'Select'
            }); //for multiple selection
            $(".select2_single").select2();
            var isDeleted = '<?php echo $isDeleted ?>';
            if (isDeleted == 'Success')
                alertify.success('Deleted');

            var newUrl = 'schoolstudents.html';
            // history.pushState({}, null, newUrl);

            $(function() {
                $("#fromDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });

            $(function() {
                $("#toDate").datetimepicker({
                    format: "MM/DD/YYYY",
                    useCurrent: false
                });
            });
        });

        // $('.impostStudentList').magnificPopup({'type': 'ajax',});	

        $('.impostStudentList').magnificPopup({
            type: 'ajax',
            'closeOnBgClick': false
        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "aoColumns": [{
                    "sWidth": "10%",
                    "bSortable": false
                }, {
                    "sWidth": "20%"
                },
                <?php if ($isActiveCheckoff == 2) { ?> {
                        "sWidth": "20%"
                    },
                <?php } ?> {
                    "sWidth": "20%"
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter",
                }, {
                    "sWidth": "20%"
                },
                {
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                }
            ]
        });

        function ShowDeleteMessage() {
            alertify.alert('Warning', 'This is the Primary User. You can\'t delete this.');
        }


        $(document).on('click', '.loginAsSchoolUser', function() {

            var schoolId = $(this).attr('schoolId');
            var studentId = $(this).attr('studentId');
            var FullName = $(this).attr('schoolStudentFullName');

            alertify.confirm('Login Confirmation', 'Continue with login as ' + FullName + '?', function() {
                window.location.href = 'loginasschoolstudent.html?schoolId=' + schoolId + '&userId=' + studentId;
            }, function() {});

        });

        $(document).on('click', '.btnStudentImport', function() {

            var schoolId = $(this).attr('schoolId');
            var studentId = $(this).attr('studentId');
            var FullName = $(this).attr('schoolStudentFullName');

            alertify.confirm('Login Confirmation', 'Continue with login as ' + FullName + '?', function() {
                window.location.href = 'loginasschoolstudent.html?schoolId=' + schoolId + '&userId=' + studentId;
            }, function() {});

        });

        $(document).on('click', '.loginEmailAjaxRow', function() {

            var studentId = $(this).attr('studentId');
            var schoolStudentFullName = $(this).attr('schoolStudentFullName');
            var currentSchoolId = '<?php echo $currentSchoolId; ?>';


            alertify.confirm('Student Name: ' + schoolStudentFullName, 'Continue with send login detail\'s email?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_send_login_email_to_school_user.html",
                    data: {
                        id: studentId,
                        schoolId: currentSchoolId,
                        type: 'student'
                    },
                    success: function() {
                        alertify.success('Sent');
                    }
                });
            }, function() {});

        });

        $("#cborank").change(function() {
            var rankId = $(this).val();

            if (rankId) {
                window.location.href = "pouch.html?rankId=" + rankId;
            } else {
                window.location.href = "pouch.html";
            }
        });

        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var studentId = $(this).attr('studentId');
            var schoolStudentFullName = $(this).attr('schoolStudentFullName');
            var SchoolId = '<?php echo ($currentSchoolId); ?>';
            alertify.confirm('Student Name: ' + schoolStudentFullName, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: studentId,
                        SchoolId: SchoolId,
                        type: 'student'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        window.location.href = 'schoolstudents.html?isDeleted=Success';
                        // alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $(document).on('click', '.isBlock', function() {

            var studentId = $(this).attr('studentId');
            var updateblockstatus = $('#block_' + studentId).attr('blockstatus');
            var thisBlock = $(this);
            $.ajax({
                type: "POST",
                url: "schoolstudentstranssubmit.html",
                data: {
                    id: studentId,
                    newblockstatus: updateblockstatus,
                    type: 'block'
                },
                success: function(responseData) {
                    $('#block_' + studentId).attr('blockstatus', responseData);
                    if (responseData == 1) {
                        thisBlock.text('Unlocked');
                    } else {
                        thisBlock.text('Padlock');
                    }
                }
            });

        });

        $(document).on('click', '.isActive', function() {

            var studentId = $(this).attr('studentId');
            var updateblockstatus = $('#active_' + studentId).attr('activestatus');
            var thisBlock = $(this);
            $.ajax({
                type: "POST",
                url: "schoolstudentstranssubmit.html",
                data: {
                    id: studentId,
                    newStatus: updateblockstatus,
                    type: 'status'
                },
                success: function(responseData) {
                    $('#active_' + studentId).attr('activestatus', responseData);
                    if (responseData == 1) {
                        thisBlock.text('Inactive');
                    } else {
                        thisBlock.text('Active');
                    }
                }
            });

        });

        $('.selectall').click(function() {

            if ($(this).is(':checked')) {
                $('#btnsendrequest').attr('disabled', false);
                $('input:checkbox:not(:disabled)').prop('checked', true);
            } else {
                $('#btnsendrequest').attr('disabled', true);
                $('input:checkbox:not(:disabled)').prop('checked', false);
            }
        });
        $('#addPouch').click(function() {
            var studentsArray = $.map($('input[type="checkbox"]:checked'), function(c){return c.value; })
            console.log(studentsArray)
            $('#hideStudentIds').val(studentsArray)
            if(studentsArray != '')
            {
                if($('#hideStudentIds').val() != '')
                    $('#pouchForm').submit();
            }
            else{
                alertify.error('Please Select Student');
            }
        });

    </script>
</body>

</html>