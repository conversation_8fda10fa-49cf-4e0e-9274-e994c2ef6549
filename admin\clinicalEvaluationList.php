<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsCIevaluation.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsPerformance.php');
include('../class/clsClinicalEval.php');

$clinicalRotationId = 0;
$schoolId = 0;
$schoolId = $currentSchoolId;
$transchooldisplayName = '';
$isDefaultCiEval = '';
// $loggedUserId = $_SESSION["loggedUserId"];   
$clinicianId = isset($_SESSION['loggedClinicianId']) ? $_SESSION['loggedClinicianId'] : 0;
$loggedClinicianType = isset($_SESSION['loggedClinicianType']) ? $_SESSION['loggedClinicianType'] : '';
$studentId = isset($_GET['studentId']) ? DecodeQueryData($_GET['studentId']) : 0;
$isActiveCheckoff = '';
$isActiveCheckoff = $_SESSION["isActiveCheckoff"];
$isDefaultCiEval = $_SESSION["isDefaultCiEval"];
if (isset($_GET['clinicalRotationId'])) //Edit Mode
{
    $clinicalRotationId = $_GET['clinicalRotationId'];
    $clinicalRotationId = DecodeQueryData($clinicalRotationId);
} else {
    $transchooldisplayName = $currenschoolDisplayname;
}

$title = "Clinical Evaluation| " . $transchooldisplayName;

//For CI Evalution
$objPerformance = new clsPerformance();
$objClinicalEval = new clsClinicalEval();
$getEvaluationdetails = $objClinicalEval->GetAllClinicalEvaluation($schoolId, $clinicalRotationId, $studentId);

$totalEvaluationCount = 0;
if ($getEvaluationdetails != '') {
    $totalEvaluationCount = mysqli_num_rows($getEvaluationdetails);
}

//For Rotation Name 
$objRotation = new clsRotation();
$RotationName = $objRotation->GetrotationDetails($clinicalRotationId, $schoolId);
$rotationtitle = isset($RotationName['title']) ? $RotationName['title'] : '';

$objDB = new clsDB();
$getTimezoneId = $objDB->GetSingleColumnValueFromTable('schools', 'timeZoneId', 'schoolId', $currentSchoolId);
$TimeZone = $objDB->GetSingleColumnValueFromTable('timezonemaster', 'timezone', 'timeZoneId', $getTimezoneId);
unset($objDB);

$objStudent = new clsStudent();
$Rowstudent = $objStudent->GetSingleStudent($schoolId, $studentId);
$studentfullname = $Rowstudent ? ($Rowstudent['firstName'] . ' ' . $Rowstudent['lastName']) : '';
unset($objStudent);
?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />

    <style>
        .form-control {
            height: 45px;
        }

        div.dataTables_wrapper div.dataTables_length select {
            height: 45px;
        }
    </style>
</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <?php if (isset($_GET['studentId'])) { ?> <li><a href="clinical.html">Clinical</a></li>
                        <li class="active"><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>

                    <?php } else { ?>
                        <li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
                    <?php } ?>
                    <li class="active">Clinical Evaluation</li>

                </ol>
            </div>
            <?php //if ( $rotationStatus == 0) { 
            ?>
            <!-- <div class="pull-right">
                <a class="btn btn-link" href="clinicalEval.html?clinicalRotationId=<?php echo EncodeQueryData($clinicalRotationId); ?>">Add</a>
            </div> -->
            <?php //} 
            ?>

        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Clinical Evaluation added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Clinical Evaluation updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Clinical Evaluation deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>

                    <th>Evaluation <br> Date</th>
                    <th>Rotation</th>
                    <th>Student</th>
                    <th>Projection <br> A Score</th>
                    <th>Projection <br> B Score</th>
                    <th>Projection <br> C Score</th>
                    <th>Total <br> Score</th>
                    <th>Result</th>
                    <th>Evaluator <br> Signature <br> Date</th>
                    <th>Student <br> Signature <br> Date</th>
                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                // echo '<pre>';
                if ($totalEvaluationCount > 0) {
                    while ($row = mysqli_fetch_array($getEvaluationdetails)) {
                        // print_r($row);
                        $clinicalEvaluationMasterId = $row['clinicalEvaluationMasterId'];
                        $clinicianName = $row['clinicianName'];
                        $studentName = $row['studentName'];
                        $rotationId = $row['rotationId'];
                        $rotationName = $row['rotationName'];
                        $scoreA = $row['scoreA'];
                        $scoreB = $row['scoreB'];
                        $scoreC = $row['scoreC'];
                        $totalScore = $row['totalScore'];
                        // $totalScore = $row['totalScore'];
                        $result = $row['result'];

                        $evaluationDate = isset($row['evaluationDate']) ? stripslashes($row['evaluationDate']) : '';
                        if ($evaluationDate != '' && $evaluationDate != '0000-00-00 00:00:00') {
                            $evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
                            $evaluationDate = date("m/d/Y", strtotime($evaluationDate));
                        } else
                            $evaluationDate = '-';

                        $evaluatorSignatureDate = isset($row['evaluatorSignatureDate']) ? stripslashes($row['evaluatorSignatureDate']) : '';
                        if ($evaluatorSignatureDate != '' && $evaluatorSignatureDate != '0000-00-00 00:00:00') {
                            $evaluatorSignatureDate = converFromServerTimeZone($evaluatorSignatureDate, $TimeZone);
                            $evaluatorSignatureDate = date("m/d/Y", strtotime($evaluatorSignatureDate));
                        } else
                            $evaluatorSignatureDate = '-';

                        $studentSignatureDate = isset($row['studentSignatureDate']) ? stripslashes($row['studentSignatureDate']) : '';
                        if ($studentSignatureDate != '' && $studentSignatureDate != '0000-00-00 00:00:00') {
                            $studentSignatureDate = converFromServerTimeZone($studentSignatureDate, $TimeZone);
                            $studentSignatureDate = date("m/d/Y", strtotime($studentSignatureDate));
                        } else
                            $studentSignatureDate = '-';

                        // $viewParam = '';
                        // $linkText = 'Edit';
                        // if ($studentSignatureDate != '-') {
                        $viewParam = '&view=V';
                        $linkText = 'View';
                        // }

                        $resultText = ($result) ? 'Fail' : 'Pass';


                ?>
                        <tr>

                            <td><?php echo ($evaluationDate); ?></td>
                            <td><?php echo ($rotationName); ?></td>
                            <td><?php echo ($studentName); ?></td>
                            <td style="text-align: center"><?php echo ($scoreA); ?></td>
                            <td style="text-align: center"><?php echo ($scoreB); ?></td>
                            <td style="text-align: center"><?php echo ($scoreC); ?></td>
                            <td style="text-align: center"><?php echo ($totalScore); ?></td>
                            <td style="text-align: center"><?php echo ($resultText); ?></td>
                            <td><?php echo ($evaluatorSignatureDate); ?></td>
                            <td><?php echo ($studentSignatureDate); ?></td>

                            <td style="text-align: center">
                                <a href="clinicalEval.html?clinicalEvaluationMasterId=<?php echo (EncodeQueryData($clinicalEvaluationMasterId)); ?>
									&clinicalRotationId=<?php echo (EncodeQueryData($clinicalRotationId)); ?>&studentId=<?php echo EncodeQueryData($studentId); ?><?php echo $viewParam; ?>"><?php echo $linkText; ?></a>

                            </td>
                        </tr>
                <?php
                    }
                }
                unset($objRotation);
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>

    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');

        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "ordering": true,
            "order": [
                [0, "desc"]
            ],


            "aoColumns": [{
                "sWidth": "10%",
                "sClass": "alignCenter"

            }, {
                "sWidth": "20%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "20%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter",
                "bSortable": false
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter",
                "bSortable": false
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter",
                "bSortable": false
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter",
                "bSortable": false
            }, {
                "sWidth": "10%",
                "sClass": "alignCenter",
                "bSortable": false
            }, -{
                "sWidth": "5%",
                "sClass": "alignCenter",
                "bSortable": false
            }, {
                "sWidth": "5%",
                "sClass": "alignCenter"
            }, {
                "sWidth": "10%",
                "sClass": "alignCenter",
                "bSortable": false
            }],

        });
    </script>


</body>

</html>