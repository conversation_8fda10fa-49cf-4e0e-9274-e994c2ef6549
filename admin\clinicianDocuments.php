<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php');
	include('../includes/commonfun.php');
    include('../class/clsDB.php');
	include('../setRequest.php');  
    include('../class/clsStudent.php');
    include('../class/clsClinician.php');
    include('../class/clsClinicianImmunization.php');
    
	  
    $totalclinicianDocument = 0;
    $clinicianId=0;
    $Type='';
    $clinicianIdImmunizationId=0;
    $loggedUserId = $_SESSION["loggedUserId"]; 
	$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
    $schoolId = $currentSchoolId;	

    $objclinician = new clsClinician(); 
 
	if(isset($_GET['clinicianId'])) 
	{
		$clinicianId= DecodeQueryData($_GET['clinicianId']);
          
    }

    if(isset($_GET['Type'])) 
	{
		$Type= ($_GET['Type']);
          
    }

    if($Type =='I')
    {
        $title ='Immulization Documents'; 
    }
    else
    {
        $title ='Evaluator Documents';
    }

    //Get Immulization Id
    if(isset($_GET['clinicianIdImmunizationId'])) 
	{
		$clinicianIdImmunizationId= DecodeQueryData($_GET['clinicianIdImmunizationId']);
         
    }
    
    //For immunization name 
    $objimmunization = new clsClinicianImmunization();
    $immunization = $objimmunization->GetSingleClinicianImmulizationDetails($clinicianIdImmunizationId); 
    $immunizationName= $immunization ? $immunization['shortName'] : '';

    //For Documents Details
    if($Type =='I')
    {
        $clinicianDocument=$objimmunization->clinicianImmulizationDocumentDetails($clinicianId,$Type,$clinicianIdImmunizationId);
    }
    else
    {
        $clinicianDocument=$objclinician->clinicianDocumentDetails($clinicianId,$Type);
    }
   
    if($clinicianDocument !='')
    {
        $totalclinicianDocument = mysqli_num_rows($clinicianDocument);
    }

    
    //For Clinician Details
    $clinicianDetail = $objclinician->GetClinicianDetails($clinicianId);
    if($clinicianDetail != '') {

        $firstName=$clinicianDetail['firstName'];
        $lastName=$clinicianDetail['lastName'];
        $fullName=$firstName.' '.$lastName;
    }

    

    $title ='Evaluator Documents';
    
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title> <?php echo $title; ?></title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">  
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">


        <style>
           .mt-1 {
                    margin-top: 10px;
                    padding-left: 55px;
                }
        </style>

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>
                        <li><a href="schoolclinicians.html">Evaluator </a></li>
                        <li><?php echo $fullName; ?></li>
                        <?php if($Type == 'I') 
                        { ?>
                        <li><a href="singleclinicianimmunization.html?clinicianId=<?php echo(EncodeQueryData($clinicianId)); ?>">Immunization</a></li>
                        <?php } ?>

                        <li class="active">Documents</li>
                    </ol>
                </div>
                <div class="pull-right">
                     <a class="btn btn-link" href="clinicianDocumentUpload.html?clinicianId=<?php echo(EncodeQueryData($clinicianId)); ?>&Type=<?php echo $Type; ?>&clinicianIdImmunizationId=<?php echo(EncodeQueryData($clinicianIdImmunizationId)); ?>">Upload</a>
                </div>
            </div>
        </div>

        <div class="container"> 

            <?php
		
				if (isset($_GET["status"]))
				{
					
					if($_GET["status"] =="added")
					{
						
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Student added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="Updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Student updated successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="StatusUpdated")
					{
				?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                        </button> Student status updated successfully.
                    </div>
                <?php
					}
                    else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}
					 else if($_GET["status"] =="datanotfound")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Certification log not available.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
                <?php if($clinicianIdImmunizationId > 0) 
	            { ?>
				<div class="formSubHeading"><?php echo $immunizationName; ?></div>
                <?php } ?>
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                            <th>Title</th>
                            <th>Uploaded Date</th>
                            <th style="text-align:center">Action</th>
							
                        </tr>
                    </thead>
                    <tbody>
                    <?php
                        if($totalclinicianDocument > 0)
                        {
                            while($row = mysqli_fetch_array($clinicianDocument))
                            {
                                $clinicianDocumentId=$row['clinicianDocumentId'];
                                $fileTitle = ($row['fileTitle']);
                                $uploadedDate = ($row['uploadedDate']);
                                $uploadedDate = converFromServerTimeZone($uploadedDate,$TimeZone);
                                $documentDetails =$objclinician->clinicianSingleDocument($clinicianDocumentId);
                                $fileTitle=$documentDetails['fileTitle'];
                                
                                $documentPath = GetClinicianDocumentPath($schoolId,$clinicianId,$clinicianDocumentId,$fileTitle);
                             
                     ?>
                            <tr>
                                
                                <td>
                                
                                    <?php echo($fileTitle); ?>
                                </td>                                
                                    <td>
                                    <?php echo ($uploadedDate) ;?>
                                </td>
                                <td style="text-align:center">
                                   <!--<a href="downloadDocument.html?studentId=<?php //echo(EncodeQueryData($studentId)); ?>&studentDocumentId=<?php //echo(EncodeQueryData($studentDocumentId)); ?>" >Download</a>|-->
                                   <a href="<?php echo $documentPath;?>" target="_blank">View </a>|
                                   <a href="javascript:void(0);" class="deleteAjaxRow"  clinicianDocumentId="<?php echo EncodeQueryData($clinicianDocumentId); ?>" fileTitle="<?php echo($fileTitle); ?>">Delete</a>
                                </td>
                            </tr>
                            <?php


                            }
                        }
                    ?>



                    </tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
         <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>    
        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

             $(window).load(function(){
                $("#divTopLoading").addClass('hide');
            	$(".select2_tags").select2({'placeholder':'Select'}); //for multiple selection
				$(".select2_single").select2();
			});

            var current_datatable = $("#datatable-responsive").DataTable({
                "aoColumns": [{
                    "sWidth": "40%"
                }, {
                    "sWidth": "30%"
                }, {
                    "sWidth": "30%",
                    "sClass": "alignCenter",
                    "bSortable": false
                } ]
            });

            $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var clinicianDocumentId = $(this).attr('clinicianDocumentId');
            var fileTitle = $(this).attr('fileTitle');
            var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>';
            var isUser = 1; //for Admin


            alertify.confirm('Document: '+fileTitle, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: clinicianDocumentId,
                        userId: userId,
                        isUser: isUser,
                        type: 'ClinicianDocument'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

            });
          

        </script>
    </body>
    </html>