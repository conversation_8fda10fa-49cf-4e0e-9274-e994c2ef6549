<?php
	include('includes/validateUserLogin.php'); 	
    include('../includes/config.php'); 
	include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsStudent.php'); 
    include('../class/clsSchool.php'); 
	//include('../class/clsSystemUserRoleMaster.php');
	include('../setRequest.php');

	if( isset($_GET['userId']))
	{
		
		$studentId=DecodeQueryData($_GET['userId']);
		//----------------------------------
		
			$_SESSION["loggedAsStudentBackUserId"]=$_SESSION["loggedUserId"];
		
		//--------------------------------------------

		
		//Get User Details
		$objStudent = new clsStudent();
		$rowStudent =  $objStudent->GetStudentDetails($studentId);
		

		if($rowStudent !="")
		{
			$schoolId=$rowStudent['schoolId'];
			//------------------------------------------------------------------------------------------
			// Get school Details
			//------------------------------------------------------------------------------------------
			$objSchool = new clsSchool();
			$schoolDetails = $objSchool->GetschoolDetails($schoolId);
			$schoolSlug = stripslashes($schoolDetails['slug']);
			unset($objSchool);

			$dynamicLoginURL = BASE_PATH.'/school/'.$schoolSlug.'/student/dashboard.html';
			
			//------------------------------------------------------------------------------------------
			// check isSystemUserRole Primary


			$studentId = $rowStudent['studentId'];

			//Get System User profile Image
			$profileImageName = stripslashes($rowStudent['smallProfilePic']);
			$defaultProfileImagePath = GetStudentImagePath($studentId,$currentSchoolId,$profileImageName);

			$profileLargeImageName = stripslashes($rowStudent['profilePic']);
			$defaultProfileLargeImagePath = GetStudentImagePath($studentId,$currentSchoolId,$profileLargeImageName);

			$studentTimeZone = $objStudent->GetStudentTimeZoneByStudentId($studentId);
			if($studentTimeZone == '')
				$timezone = $rowStudent['timezone'];
			else
				$timezone = $studentTimeZone;
			
			
			//Start Session
			//-----------------------------------
			@session_start();
			$_SESSION["loggedStudentId"] = $studentId;
			$_SESSION["loggedStudentName"] = stripslashes($rowStudent['username']);
			$_SESSION["loggedStudentFirstName"] =  stripslashes($rowStudent['firstName']);
			$_SESSION["loggedStudentLastName"] = stripslashes($rowStudent['lastName']);
			$_SESSION["loggedStudentSchoolId"] = stripslashes($rowStudent['schoolId']);
			$_SESSION["loggedStudentSchoolTimeZone"] = $timezone;
			$_SESSION["loggedStudentProfileImagePath"] = $defaultProfileImagePath;
			$_SESSION["loggedStudentProfileLargeImagePath"] = $defaultProfileLargeImagePath;
			$_SESSION["isActiveCheckoff"] = stripslashes($rowStudent['isActiveCheckoff']);
			$_SESSION["loggedStudentLocation"] = stripslashes($rowStudent['locationId']);

		

			//------------------------------------------------------------------------------------------
			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = $objLog::LOGINAS;
			$userType = isset($_SESSION['loggedAsBackUserId']) ? $objLog::SUPERADMIN : $objLog::ADMIN; // User type is set to ADMIN
			$uId = isset($_SESSION['loggedAsBackUserId']) ?  $_SESSION['loggedAsBackUserId'] : $_SESSION['loggedAsStudentBackUserId'];
			$IsMobile = 0;

			$objStudent = new clsStudent();
			$objStudent->saveStudentAuditLog($studentId,$uId, $userType, $action, $IsMobile);
			unset($objStudent);

			unset($objLog);
			//Audit Log End

			header("location:".$dynamicLoginURL);
			exit();
		}
		else
		{
			header('location:schoolusers.html?Error=7');
		}
		unset($objStudent);
	}
	else
	{
		header('location:schoolusers.html?Error=2');
	}
	exit();
?>