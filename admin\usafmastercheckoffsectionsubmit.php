<?php 
	include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../includes/commonfun.php'); 
    include('../class/clsDB.php'); 
    include('../class/clsSchool.php');      	
	include('../class/clsUsafMasterCheckoffSection.php');
	include('../class/clsusafEquiment.php');
	include('../setRequest.php'); 
	
		//print_r($_POST);
	if($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit']))
	{
		
		$checkoffSectionIdId = isset($_GET['editid']) ? DecodeQueryData($_GET['editid']) : 0;
		
		$status = ($checkoffSectionIdId > 0) ? 'updated' : 'added';
		
		if(isset($_GET['topicid']))
		{
			$topicid=DecodeQueryData($_GET['topicid']);
		}

		
		$title = $_POST['txtcheckoffsection'];
		$sortOrder = $_POST['txtsortorder'];
		
		$description = $_POST['txtdescription'];
		$equipmentList  = $_POST['equipmentList'];
		//Save data
		$objCheckoffSection = new clsUsafMasterCheckoffSection();
		$objCheckoffSection->title = $title;		
		$objCheckoffSection->sortOrder = $sortOrder;		
		$objCheckoffSection->description = $description;				
		$retcheckoffsectionmasterId = $objCheckoffSection->SaveUsafCheckoffSection($checkoffSectionIdId);
		
		if($topicid > 0)
		{
			if(!isset($_GET['editid']))
			{
				$objCheckoffSection->defaultTopicId=$topicid;
				$objCheckoffSection->sectionId=$retcheckoffsectionmasterId;
				$objCheckoffSection->SaveUsafCheckoffSectionInTopicDetails($retcheckoffsectionmasterId);
			}
		}
		//Add Equipment List 
		$objusafEquiment = new clsusafEquiment();
		//Delete all clinician hospitalsites
		$objusafEquiment->DeleteAllSectionEquipmentList($checkoffSectionIdId);
		if(count($equipmentList))
		{
			//Now save new Equipment
			foreach($equipmentList as $equipmentId)
			{
				$studentEquipmentId = $objusafEquiment->SaveSectionusafequipmentlist($retcheckoffsectionmasterId,$equipmentId);
			}	
						
		}
		unset($objusafEquiment);

		unset($objCheckoffSection);
		if($retcheckoffsectionmasterId > 0)
		{	
			if($topicid > 0)
				header('location:usafmastercheckoffsection.html?status='.$status.'&topicid='.EncodeQueryData($topicid));
			else
				header('location:usafmasterviewsection.html?status='.$status);
		}
		else
		{
			header('location:usafmasteraddsection.html?status=error');
		}
	}
	else
	{
		header('location:usafmastercheckoffsection.html?status='.$status.'&topicid='.EncodeQueryData($topicid));
		
		exit();
	}
?>