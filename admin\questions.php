<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php'); 
    include('../class/clsCheckoffQuestionMaster.php');    
    include('../class/clsCheckoffSectionMaster.php');    
    include('../class/clsCheckoffTopicMaster.php');    
    include('../setRequest.php'); 	
	
	$schoolTopicId=0;
	$encodedSchoolTopicId=0;
	$encodedSchoolSectionId=0;
	$currentSchoolId;

	$objDB = new clsDB();
	if(isset($_GET['topicid']))
	{
		$encodedSchoolTopicId=$_GET['topicid'];
		$schoolTopicId = DecodeQueryData($_GET['topicid']);
	}
	if(isset($_GET['sectionId']))
	{
		$encodedSchoolSectionId=$_GET['sectionId'];
		$schoolSectionId=DecodeQueryData($_GET['sectionId']);
	}

	//For All Checkoff Questions
	$objQuestionMaster = new clsCheckoffQuestionMaster();
	$rowsQuestionData=$objQuestionMaster->GetAllCheckoffQuestions($currentSchoolId,$schoolSectionId,$schoolTopicId);
	$totalCount = 0;
	if($rowsQuestionData !='')
	{
		$totalCount = mysqli_num_rows($rowsQuestionData);
	}	
	unset($objQuestionMaster);
	
	//For Checkoff Section Title
	$objCheckoffSectionMaster=new clsCheckoffSectionMaster();
    $GetSingleSection=$objCheckoffSectionMaster->GetCheckoffSctionByCheckoffSectionId($currentSchoolId,$schoolSectionId);
	$SingleSection= isset($GetSingleSection['schoolSectionTitle']) ? $GetSingleSection['schoolSectionTitle'] : '';
	unset($objCheckoffSectionMaster);
	
	//For Checkoff Section Title 
	$objCheckoffTopicMaster=new clsCheckoffTopicMaster();
    $GetSingleTopicId=$objCheckoffTopicMaster->GetSingleCheckoffTopicId($currentSchoolId,$schoolTopicId,$schoolSectionId);
	$TopicTitle= isset($GetSingleTopicId['schooltitle']) ? $GetSingleTopicId['schooltitle'] : '';
	unset($objCheckoffTopicMaster);
	
	//For Super Admin Login
	$disabled='';
	if(isset($_SESSION["loggedAsBackUserId"]))
	{
		$disabled="";
	}
	else
	{
		$disabled="disabled";
	}
		
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Comps Steps</title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
           <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
		   <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

   </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>
						<li><a href="settings.html">Setting	</a></li>
                        <li><a href="checkofftopics.html">Comps Steps</a></li>	
                        <li><a title="<?php echo ($TopicTitle); ?>" href="checkoffsection.html?topicid=<?php echo EncodeQueryData($schoolTopicId); ?>&schoolSectionId=<?php echo EncodeQueryData($schoolSectionId); ?>"><?php echo substr($TopicTitle,0,21).(' - ' . $SingleSection); ?>-Comps Section</a></li>
                        <li class="active">Steps</li>
                    </ol>
                </div>
                <?php if(isset($_SESSION["loggedAsBackUserId"])) {?>   
					<div class="pull-right"> 
						<ol class="breadcrumb">
							<a href="addquestion.html?sectionId=<?php echo EncodeQueryData($schoolSectionId); ?>">Add</a> 
							
						</ol>  
					</div>
                <?php } ?>    
            </div>
        </div>

        <div class="container">

					<?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Question added successfully.
                </div>
                <?php 
					}                 
                    else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					} else if($_GET["status"] =="Imported")
					{
						?>
                    <div class="alert alert-success alert-dismissible fade in" role="alert">
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                                </button> Comps Steps added successfully.
                    </div>
                <?php 
					}
					
				}?>
                <div id="divTopLoading" >Loading...</div>
				
					<form name="checkoffquestion" id="checkoffquestion" data-parsley-validate method="POST" action="questionsubmit.html">
						<div class="row">
						<div class="col-md-10  margin_bottom_ten"></div>
						<div class="col-md-2  margin_bottom_ten">
						
								<div class="form-group">

								</div>
						</div>
					
					</div>	
                <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr >
							
                           <th></th>
                           <th>Sort Order</th>                                                   
                           <th>Comps Steps Title</th> 
							<th>Type</th>
                           <th style="text-align: center">Action</th>                                                   
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalCount > 0)
                        {
                            while($row = mysqli_fetch_array($rowsQuestionData))
                            {
								
                                $schoolQuestionId = ($row[0]);					
                                $schoolQuestionFullTitle = stripslashes($row['schoolQuestionTitle']);
                                $CheckdQueId =($row['CheckdQueId']);
                                $QueId =($row['QueId']);
                                $sortOrder =($row['sortOrder']);
								$questiontype =($row['questiontype']);
								if($CheckdQueId > 0)
								{
									$actiontype="false";
								}
								else
								{
									$actiontype="true";
								}
								
								$shortTitlelen=strlen($schoolQuestionFullTitle);
								
								if($shortTitlelen > 80)
								{
								   
								    $schoolQuestionTitle=substr($schoolQuestionFullTitle,0,80);
									$schoolQuestionTitle .= '...';
							      
								}else{
								    $schoolQuestionTitle=$schoolQuestionFullTitle;
								}
								
								// $isAlredyExist= $objDB->GetSingleColumnValueFromTable('schooltopicdetails', 'schoolQuestionId','schoolSectionId',$schoolSectionId,'schoolQuestionId',$schoolQuestionId);
                               ?>
                            <tr>
								
								<td style="text-align: center"> 
								<input <?php echo ($disabled); ?> schoolQuestionId="<?php echo EncodeQueryData($schoolQuestionId);?>" SchoolId="<?php echo EncodeQueryData($currentSchoolId);?>"   type="checkbox"  id="checkoffquestion" name="checkoffquestion[]" 
								value=""  actiontype="<?php echo ($actiontype); ?>" schoolTopicId="<?php echo EncodeQueryData($schoolTopicId);?>" schoolSectionId="<?php echo EncodeQueryData($schoolSectionId);?>" <?php if($CheckdQueId > 0)
								{?> checked  class="checkoffquestion sendrequest chkque"<?php }else { ?> class="sendrequest chkque" <?php } ?>>
								</td>
								<td style="text-align: center"><?php echo ($sortOrder); ?></td>
                                <td title="<?php echo ($schoolQuestionFullTitle); ?>">
                                    <?php echo($schoolQuestionTitle); 				
									?>
                                </td>
								<td><?php echo ($questiontype); ?></td>
								<td style="text-align: center"><a class="addCommentpopup " title="Preview Question Details" href="questiondetails.html?questionId=<?php echo $schoolQuestionId;  ?>">Preview</a>				
								</td>
								
								

                            </tr>
                            <?php
							}
                        }
                    ?>
					</tbody>
                </table>
				</form>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
		<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>

        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

            $(window).load(function(){
				
				$('.impostStudentList').magnificPopup({
                    type:'ajax',
                    'closeOnBgClick': false
                });
					var TotalCheckboxCount = $('input[name="checkoffquestion[]"]').length;                       
					var CheckedCheckboxCount = $('input[name="checkoffquestion[]"]:checked').length;
					if(TotalCheckboxCount==CheckedCheckboxCount)
					{
						$('.selectall').prop('checked', true);
						
					}else{
						$('.selectall').prop('checked', false);
						
					}		
				
                $("#divTopLoading").addClass('hide');
				
					var current_datatable = $("#datatable-responsive").DataTable({
					 "aoColumns": [{
                    "sWidth": "1%",
					
					"bSortable": false
                },{
                    "sWidth": "1%"
                },{
                    "sWidth": "83%"
				  },
				  {
                    "sWidth": "1%"
                },{
                    "sWidth": "15%",
					"bSortable": false					
                }],
				
				// "aLengthMenu": [[100, 200, 300, 400,500, -1], [100, 200, 300, 400,500, "All"]],
                    "iDisplayLength": 250,
				 }); 
            });
			
			
			$('.chkque').click(function() {         
					var TotalCheckboxCount = $('input[name="checkoffquestion[]"]').length;                       
					var CheckedCheckboxCount = $('input[name="checkoffquestion[]"]:checked').length;
					if(TotalCheckboxCount==CheckedCheckboxCount)
					{
						$('.selectall').prop('checked', true);
						
					}else{
						$('.selectall').prop('checked', false);
						
					}		
				
					
			});
			
			$('.addCommentpopup').magnificPopup({'type': 'ajax',});
	
				$('#selectall').click(function() {				
				if ($(this).is(':checked')) {					
					$('input:checkbox').prop('checked', true);
					var ischeckall=1;
				} else {					
					//$('input:checkbox').prop('checked', false);
					$('input').filter(':checkbox').removeAttr('checked');
					var ischeckall=0;
				}
				
				
				
					var schoolTopicId = '<?php echo ($encodedSchoolTopicId); ?>';
					var schoolSectionId = '<?php echo ($encodedSchoolSectionId); ?>';
					var SchoolId = '<?php echo ($currentSchoolId); ?>';
				
				$.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/assign_questions.html",
                        data: {	
							  schoolTopicId: schoolTopicId,
							  schoolSectionId: schoolSectionId,
							  SchoolId: SchoolId,
							  ischeckall: ischeckall,
							  type: 'assign_all_questions'							  							  
                        },
						 success: function() {                           
                            if(ischeckall == 1){ //Assigned 
                            	alertify.success('Assigned');
							 }
								
							else if(ischeckall == 0) {//Removed
                            	alertify.error('Removed');
							 }
                        }
                    });
				});
				
			
			
         
								
				
			//FOR SEND REQUEST 1 by 1
			$("#datatable-responsive").on("click", ".sendrequest", function() {
		
			
					var action;
					var thischeck= $(this);
					var schoolQuestionId = $(this).attr('schoolQuestionId');
					var SchoolId = $(this).attr('SchoolId');
					var actiontype = $(this).attr('actiontype');
					var schoolTopicId = $(this).attr('schoolTopicId');                 
			      	var schoolSectionId = $(this).attr('schoolSectionId'); 
					var userId = '<?php echo EncodeQueryData($_SESSION['loggedUserId']) ?>';
			        
					$.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/assign_questions.html",
                        data: {							
						      id: schoolQuestionId,
							  action : actiontype,
							  SchoolId: SchoolId,
							  schoolTopicId: schoolTopicId,
							  schoolSectionId: schoolSectionId,
							  type: 'assign_questions',
							  userId: userId									  
                        },
						 success: function() {                           
                            if(actiontype == 'true'){ //Assigned 
                            	alertify.success('Assigned');
							thischeck.attr('actiontype','false'); }
								
							else if(actiontype == 'false') {//Removed
                            	alertify.error('Removed');
							thischeck.attr('actiontype','true'); }
                        }
                    });
			        
			});
				

        </script>
    </body>
    </html>