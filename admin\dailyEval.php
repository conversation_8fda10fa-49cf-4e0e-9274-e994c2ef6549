<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsRotation.php');
include('../class/clsLocations.php');
include('../class/clsStudent.php');
include('../class/clsMidterm.php');
include('../class/clsDaily.php');
include('../class/clsQuestionOption.php');
include('../class/clsSectionStudentName.php');
include('../class/clsClinician.php');
include('../class/clsExternalPreceptors.php');


$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
$clinicianId = 0;

$rotationId = 0;
$studentId = 0;
$Type = '';
$currentstudentId = 0;
$studentDailyMasterId = 0;
$midtermrotationid = 0;
$dateOfStudentSignature = '';
$evaluationDate = '';
$schoolId = $currentSchoolId;
$dateOfInstructorSignature = '';
$studentSignature = '';
$hospitalSiteId = 0;
$readonly = '';
$view = '';
$bedCrumTitle = 'Add';
$page_title = "Add Daily/Weekly Evalution";
$currentDate = date('m/d/Y');
$preceptorId = 0;
//For Rotation  



if (isset($_GET['Type'])) {
	$Type = ($_GET['Type']);
}

if (isset($_GET['view'])) {
	$view = ($_GET['view']);
}


//For Edit Daily
if (isset($_GET['studentDailyMasterId'])) {
	$schoolId = $currentSchoolId;
	$page_title = "Edit Daily/Weekly ";
	$bedCrumTitle = 'Edit';

	//For Daily Details
	$objDailyEval = new clsDaily();
	$objRotation = new clsRotation();
	$studentDailyMasterId = DecodeQueryData($_GET['studentDailyMasterId']);
	$rowDailyEval = $objDailyEval->GetStudentDailyDetails($studentDailyMasterId);
	// echo "<pre>"; print_r($rowDailyEval);exit;
	unset($objDailyEval);
	if ($rowDailyEval == '') {
		header('location:dailyEval.html');
		exit;
	}
	$rotationId = ($rowDailyEval['rotationId']);
	$clinicianId = ($rowDailyEval['clinicianId']);
	$preceptorId = ($rowDailyEval['preceptorId']);
	$evaluationDate = ($rowDailyEval['evaluationDate']);
	$parentRotationId = $rowDailyEval['parentRotationId'];
	$hospitalSiteId = $rowDailyEval['hospitalSiteId'];
	$courselocationId = $rowDailyEval['locationId'];
	$rotationLocationId = stripslashes($rowDailyEval['rotationLocationId']);

	$locationId = 0;
	if ($rotationLocationId != $courselocationId && $parentRotationId > 0) {
		if ($parentRotationId > 0) {
			if (!$rotationLocationId)
				$locationId = $objRotation->GetLocationByRotation($rotationId);
			else
				$locationId  = $rotationLocationId;
		}
	} else {
		$locationId  = $courselocationId;
	}

	//Get Time Zone By Rotation 
	$objLocation = new clsLocations();
	$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
	unset($objLocation);
	if ($TimeZone == '')
		$TimeZone = $_SESSION["loggedUserSchoolTimeZone"];
	$evaluationDate = converFromServerTimeZone($evaluationDate, $TimeZone);
	$evaluationDate = (date('m/d/Y', strtotime($evaluationDate)));
	$studentId = ($rowDailyEval['studentId']);

	$dateOfStudentSignature = ($rowDailyEval['dateOfStudentSignature']);
	$dateOfStudentSignature = converFromServerTimeZone($dateOfStudentSignature, $TimeZone);
	$dateOfStudentSignature = (date('m/d/Y', strtotime($dateOfStudentSignature)));
	$dateOfInstructorSignature = ($rowDailyEval['dateOfInstructorSignature']);
	$dateOfInstructorSignature = converFromServerTimeZone($dateOfInstructorSignature, $TimeZone);
	$dateOfInstructorSignature = (date('m/d/Y', strtotime($dateOfInstructorSignature)));

	if ($dateOfInstructorSignature != '' && $dateOfInstructorSignature != '0000-00-00 00:00:00' && $dateOfInstructorSignature != '01/01/1970' && $dateOfInstructorSignature != '0000-00-00' && $dateOfInstructorSignature != '11/30/-0001 12:00 AM') {
		$dateOfInstructorSignature;
	}
}
unset($objRotation);
if (isset($_GET['rotationId'])) {
	$rotationId = DecodeQueryData($_GET['rotationId']);
}

if (isset($_GET['studentId'])) {
	$studentId = DecodeQueryData($_GET['studentId']);
}

//For Clinician Name
$objClinician = new clsClinician();
$Clinician = $objClinician->GetClinicianByRotation($currentSchoolId, $rotationId);
$clinicianDetails = $objClinician->GetClinicianDetails($clinicianId);
$firstName = isset($clinicianDetails['firstName']) ? $clinicianDetails['firstName'] : '';
$lastName = isset($clinicianDetails['lastName']) ? $clinicianDetails['lastName'] : '';
$fullName = $firstName . ' ' . $lastName;
unset($objClinician);

//For Student Name
$objStudent = new clsStudent();
$Student = $objStudent->GetStudentsByRotation($currentSchoolId, $rotationId);

//For Student Name
$rowsStudents = $objStudent->GetSingleStudent($currentSchoolId, $studentId);
$studentfullname = ($rowsStudents['firstName'] . ' ' . $rowsStudents['lastName']);

unset($objStudent);


//For Rotation Name
$objRotation = new clsRotation();
$RotationName = $objRotation->GetrotationDetails($rotationId, $schoolId);
$rotationtitle = $RotationName['title'];
unset($objRotation);


//rotation
$objrotation = new clsRotation();
if (isset($_GET['studentDailyMasterId'])) {
	$rotation = $objrotation->GetRotationBySchool($schoolId);
} else {
	$rotation = $objrotation->GetCurrentRotationBySchool($schoolId);
}
unset($objrotation);

//For Daily/Weekly Section 
$objDailyEval = new clsDaily();
$totalSection = 0;
$dailyEvalSection = $objDailyEval->GetSections($schoolId);
if ($dailyEvalSection != '') {
	$totalSection = mysqli_num_rows($dailyEvalSection);
}
$isPreceptor = isset($_GET['isPreceptor']) ? DecodeQueryData($_GET['isPreceptor']) : '';
$preceptorFullName = '';
if ($isPreceptor > 0) {
	$preceptorId = $isPreceptor;
	$objExternalPreceptors = new clsExternalPreceptors();
	$externalPreceptorDetail = $objExternalPreceptors->GetExternalPreceptorDetail($preceptorId);
	$preceptorFirstName = isset($externalPreceptorDetail['firstName']) ? $externalPreceptorDetail['firstName'] : '';
	$preceptorLastName = isset($externalPreceptorDetail['lastName']) ? $externalPreceptorDetail['lastName'] : '';
	$preceptorFullName = $preceptorFirstName . ' ' . $preceptorLastName;
}

?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo($page_title); ?></title>
    <?php include('includes/headercss.php');?>
	<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/datetimepicker/css/bootstrap-datetimepicker.min.css">
	<link rel="stylesheet" href="<?php echo($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
	<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />

	<style type="text/css">
		.some-class {
			float: left;
			clear: none;
		}
	</style>
</head>

<body>
	<?php include('includes/header.php'); ?>

	<div class="row margin_zero breadcrumb-bg">
		<div class="container">
			<div class="pull-left">
				<ol class="breadcrumb">
					<li><a href="dashboard.html">Home</a></li>

					<?php if (isset($_GET['rotationId']) && isset($_GET['Type'])) { ?>
						<li><a href="rotations.html">Rotations</a></li>
						<li><a href="rotations.html"><?php echo ($rotationtitle); ?></a></li>
						<li><a href="dailyEvalList.html?rotationId=<?php echo EncodeQueryData($rotationId) ?>&Type=<?php echo $Type; ?>">Daily/Weekly Evaluation</a></li>
					<?php } elseif (isset($_GET['studentId']) && isset($_GET['Type'])) { ?>
						<li><a href="clinical.html">Clinical</a></li>
						<li><a href="clinical.html"><?php echo ($studentfullname); ?></a></li>
						<li><a href="dailyEvalList.html?studentId=<?php echo EncodeQueryData($studentId) ?>&Type=<?php echo $Type; ?>">Daily/Weekly Evaluation</a></li>
					<?php } else { ?>
						<li><a href="dailyEvalList.html">Daily/Weekly Evaluation</a></li>
					<?php } ?>
					<li class="active"><?php echo ($bedCrumTitle); ?></li>
				</ol>
			</div>

		</div>
	</div>

	<div class="container">

		<form id="formdaily" data-parsley-validate class="form-horizontal" method="POST" <?php if (isset($_GET['rotationId']) && isset($_GET['Type'])) { ?> action="dailyEvalSubmit.html?rotationId=<?php echo (EncodeQueryData($rotationId)); ?>&studentDailyMasterId=<?php echo (EncodeQueryData($studentDailyMasterId)); ?>&Type=<?php echo $Type; ?>" <?php } elseif (isset($_GET['studentId']) && isset($_GET['Type'])) { ?> action="dailyEvalSubmit.html?studentId=<?php echo (EncodeQueryData($studentId)); ?>&studentDailyMasterId=<?php echo (EncodeQueryData($studentDailyMasterId)); ?>&Type=<?php echo $Type; ?>" <?php } else {  ?> action="dailyEvalSubmit.html?studentDailyMasterId=<?php echo (EncodeQueryData($studentDailyMasterId)); ?>&rotationId=<?php echo (EncodeQueryData($rotationId)); ?>" <?php } ?>>



			<div class="row">
				<div class="col-md-6">

					<div class="form-group">
						<!-- preceptor Id -->
						<input type="hidden" name="preceptorId" id="preceptorId" value="<?php echo $preceptorId; ?>">

						<label class="col-md-4 control-label" for="cborotation">Rotation</label>
						<div class="col-md-8">
							<select id="cborotation" name="cborotation" class="form-control input-md required-input select2_single" onChange="getHospital(this.value);" required>
								<option value="" selected>Select</option>
								<?php
								if ($rotation != "") {
									while ($row = mysqli_fetch_assoc($rotation)) {
										$selrotationId  = $row['rotationId'];
										$name  = stripslashes($row['title']);

								?>
										<option value="<?php echo EncodeQueryData($selrotationId); ?>" <?php if ($rotationId == $selrotationId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
								<?php

									}
								}
								?>
							</select>
						</div>
					</div>
					<!-- ROTATION DD END -->
				</div>

				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="cbohospitalsites">Hospital Sites</label>
						<div class="col-md-8">
							<select id="cbohospitalsites" name="cbohospitalsites" class="form-control input-md required-input select2_single">

								<option value=""> </option>
							</select>
							<p id="hospitalerror" style="color:#E74C3C; border: 1px solid #E8544;"></p>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="evaluationDate">Evaluation Date</label>
						<div class="col-md-8">
							<div class='input-group date' id='evaluationDate'>
								<input type='text' name="evaluationDate" id="evaluationDate" class="form-control input-md required-input rotation_date " value="<?php if ($evaluationDate != '01/01/1970' && $evaluationDate != '11/30/0001' && $evaluationDate != '12/31/1969') echo ($evaluationDate); ?>" data-parsley-errors-container="#error-txtDate" required />
								<span class="input-group-addon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<?php if ($isPreceptor || $preceptorId) { ?>
					<div class="col-md-6">
						<div class="form-group">
							<label class="col-md-4 control-label" for="cboclinician">Preceptor</label>
							<div class="col-md-8">
								<input type="text" name="" id="" value="<?php echo $preceptorFullName; ?>" class="form-control" disabled>
							</div>
						</div>
					</div>
				<?php } else { ?>
					<div class="col-md-6">

						<div class="form-group">
							<label class="col-md-4 control-label" for="cboclinician">Clinician</label>
							<div class="col-md-8">
								<select id="cboclinician" name="cboclinician" class="form-control input-md required-input select2_single" required data-parsley-errors-container="#error-txtClinician">
									<option value="" selected>Select</option>
									<?php
									if ($Clinician != "") {
										while ($row = mysqli_fetch_assoc($Clinician)) {
											$selClinicianId  = $row['clinicianId'];
											$name  = stripslashes($row['firstName']);
											$lastName  = stripslashes($row['lastName']);
											$fullName = $name . ' ' . $lastName;
									?>
											<option value="<?php echo ($selClinicianId); ?>" <?php if ($clinicianId == $selClinicianId) { ?> selected="true" <?php } ?>><?php echo ($fullName); ?></option>

									<?php

										}
									}
									?>
								</select>
								<div id="error-txtClinician"></div>
							</div>
						</div>
						<!-- ROTATION DD END -->
					</div>
				<?php } ?>

				<div class="col-md-6">
					<div class="form-group">
						<?php if ($isPreceptor || $preceptorId) { ?>
							<label class="col-md-4 control-label" for="InstructorDate">Date of Preceptor Signature</label>
						<?php } else { ?>
							<label class="col-md-4 control-label" for="InstructorDate">Date of Instructor Signature</label>
						<?php } ?>
						<div class="col-md-8">
							<div class='input-group date' id='InstructorDate'>

								<input type='text' name="InstructorDate" id="InstructorDate" class="form-control input-md required-input rotation_date" value="<?php if ($dateOfInstructorSignature != '01/01/1970' && $dateOfInstructorSignature != '11/30/0001' && $dateOfInstructorSignature != '12/31/1969') echo ($dateOfInstructorSignature); ?>" data-parsley-errors-container="#error-txtDate2" readonly />
								<span class="input-group-addon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate2"></div>
						</div>
					</div>
				</div>
			</div>

			<div class="row">
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="studentsigniture">Student </label>
						<div class="col-md-8">
							<select id="cbostudent" name="cbostudent" class="form-control input-md required-input select2_single" required>
								<option value="" selected>Select</option>
								<?php
								if ($Student != "") {
									while ($row = mysqli_fetch_assoc($Student)) {
										$selstudentId  = $row['studentId'];
										$firstName  = stripslashes($row['firstName']);
										$lastName  = stripslashes($row['lastName']);
										$name =	$firstName . ' ' . $lastName;
										if ($studentId > 0) { ?>
											<option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
										<?php } else { ?>
											<option value="<?php echo ($selstudentId); ?>" <?php if ($studentId == $selstudentId) { ?> selected="true" <?php } ?>><?php echo ($name); ?></option>
										<?php }  ?>
								<?php

									}
								}
								?>
							</select>
						</div>
					</div>

				</div>
				<div class="col-md-6">
					<div class="form-group">
						<label class="col-md-4 control-label" for="studentsignitureDate">Date Of Student Signature</label>
						<div class="col-md-8 col-sm-4 col-xs-12">
							<div class='input-group date' id='studentsignitureDate'>

								<input type='text' name="studentsignitureDate" readonly id="studentsignitureDate" class="form-control input-md required-input rotation_date" value="<?php if ($dateOfStudentSignature != '' && $dateOfStudentSignature != '01/01/1970' && $dateOfStudentSignature != '12/31/1969') {
																																														echo ($dateOfStudentSignature);
																																													} ?>" data-parsley-errors-container="#error-txtDate3" />
								<span class="input-group-addon">
									<span class="glyphicon glyphicon-calendar"></span>
								</span>
							</div>
							<div id="error-txtDate3"></div>
						</div>
					</div>
				</div>
			</div>
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="instructions:"></label>
						<div class="col-md-10 col-sm-6 col-xs-12">
							<div class="panel panel-default">
								<div class="panel-body">
									<p>Please rate the students in the following categories on a scale 1-5<br>
										<b>1-Critical Error, 2-Minor Error, 3-Satisfactory/Average, 4-Above Average, 5-Exceeds Expectations.</b>
									</p>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!-- 1st SECTION div start -->
			<div class="row">
				<div class="col-md-12">
					<div class="form-group">
						<label class="col-md-2 control-label" for="instructions:"></label>
						<div class="col-md-10 col-sm-6 col-xs-12">

							<div class="panel-group" id="posts">
								<?php
								while ($row = mysqli_fetch_array($dailyEvalSection)) {
									$sectionMasterId = $row['sectionMasterId'];
									$title = $row['title'];
									$sortOrder = $row['sortOrder'];
								?>

									<div class="panel panel-default">
										<div class="panel-heading">
											<h4 class="panel-title">
												<a href="#<?php echo $sectionMasterId; ?>" data-toggle="collapse" data-parent="#posts"><?php echo  $title; ?></a>
											</h4>
										</div>

										<div id="<?php echo $sectionMasterId; ?>" class="panel-collapse collapse">
											<?php
											// for question
											$totalDailyQuestions = 0;
											$dailyQuestion = $objDailyEval->GetAllDailyQuestionMaster($schoolId, $sectionMasterId);


											if ($dailyQuestion) {
												while ($row = mysqli_fetch_array($dailyQuestion)) {
													if (isset($_GET['studentDailyMasterId'])) {
														$studentDailyMasterId = DecodeQueryData($_GET['studentDailyMasterId']);
													} else {
														$studentDailyMasterId = 0;
													}

													$dailyQuestionId = $row['dailyQuestionId'];
													$schoolDailyQuestionTitle = $row['optionText'];
													$dailyQuestionType = $row['dailyQuestionType'];
													$qhtml = GetDailyQuestionHtml($dailyQuestionId, $dailyQuestionType, $studentDailyMasterId, $currentSchoolId);


													if ($sortOrder == 1) { ?>

														<div class="panel-body allRadioForFirst">
															<b><?php echo $schoolDailyQuestionTitle; ?></b><br /><br />
															<?php echo $qhtml; ?>
														</div>

													<?php  } elseif ($sortOrder == 2) { 	?>
														<div class="panel-body allRadioForSecond">
															<b><?php echo $schoolDailyQuestionTitle; ?></b><br /><br />
															<?php echo $qhtml; ?>
														</div>
													<?php } elseif ($sortOrder == 3) { ?>

														<div class="panel-body allRadioForThird">
															<b><?php echo $schoolDailyQuestionTitle; ?></b><br /><br />
															<?php echo $qhtml; ?>
														</div>

													<?php  } elseif ($sortOrder == 4) { ?>

														<div class="panel-body allRadioForFourth">
															<b><?php echo $schoolDailyQuestionTitle; ?></b><br /><br />
															<?php echo $qhtml; ?>
														</div>

													<?php  } elseif ($sortOrder == 5) { ?>

														<div class="panel-body allRadioForFifth">
															<b><?php echo $schoolDailyQuestionTitle; ?></b><br /><br />
															<?php echo $qhtml; ?>
														</div>

													<?php  } elseif ($sortOrder == 6) { ?>

														<div class="panel-body allRadioForSix">
															<b><?php echo $schoolDailyQuestionTitle; ?></b><br /><br />
															<?php echo $qhtml; ?>
														</div>

													<?php  } else { ?>

														<div class="panel-body">
															<b><?php echo $schoolDailyQuestionTitle; ?></b><br /><br />
															<?php echo $qhtml; ?>
														</div>
											<?php			 }
												}
											}
											?>


										</div>
									</div>
								<?php
								}

								?>
							</div>

						</div>
					</div>
				</div>

				<div class="row">
					<label class="col-md-2 control-label" for="instructions:"></label>
					<div class="col-md-10">
						<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">

							<thead>
								<tr>
									<th style="text-align:center">Avg Attendance</th>
									<th style="text-align:center">Avg Student Preparation</th>
									<th style="text-align:center">Avg Professionalism</th>
									<th style="text-align:center">Avg Knowledge</th>
									<th style="text-align:center">Avg Psychomotor</th>
									<th style="text-align:center">Avg Organization</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td align='center'><input readonly class="form-control input-md" type="text" id="firstSectionAvg" name="firstSectionAvg"></td>
									<td align='center'><input readonly class="form-control input-md" type="text" id="secondSectionAvg" name="secondSectionAvg"></td>
									<td align='center'><input readonly class="form-control input-md" type="text" id="thirdSectionAvg" name="thirdSectionAvg"></td>
									<td align='center'><input readonly class="form-control input-md" type="text" id="fourthSectionAvg" name="fourthSectionAvg"></td>
									<td align='center'><input readonly class="form-control input-md" type="text" id="fiveSectionAvg" name="fiveSectionAvg"></td>
									<td align='center'><input readonly class="form-control input-md" type="text" id="sixSectionAvg" name="sixSectionAvg"></td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<label class="col-md-2 control-label" for="instructions:"></label>
					<div class="col-md-2">
						<table id="" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
							<thead>
								<tr>
									<th style="text-align:center">Total Average</th>
								</tr>
							</thead>
							<tbody>
								<tr>
									<td align='center'><input readonly class="form-control input-md" type="text" id="totalAvg" name="totalAvg"></td>
								</tr>
							</tbody>
						</table>
					</div>
				</div>

				<div class="row">
					<div class="form-group">
						<label class="col-md-2 control-label"></label>
						<div class="col-md-10">
							<?php if ($view != 'V') { ?>
								<button style="margin: 0 0 0 11px;" id="btnSubmit" name="btnSubmit" class="btn btn-success">Save</button>
							<?php } ?>
							<?php if (isset($_GET['rotationId']) && isset($_GET['Type'])) { ?>
								<a type="button" href="dailyEvalList.html?rotationId=<?php echo EncodeQueryData($rotationId); ?>&Type=<?php echo $Type; ?>" class="btn btn-default">Cancel</a>
							<?php } else if (isset($_GET['studentId']) && isset($_GET['Type'])) { ?>
								<a type="button" href="dailyEvalList.html?studentId=<?php echo EncodeQueryData($studentId); ?>&Type=<?php echo $Type; ?>" class="btn btn-default">Cancel</a>
							<?php } else { ?>
								<a type="button" href="dailyEvalList.html" class="btn btn-default">Cancel</a>
							<?php } ?>
						</div>

					</div>
				</div>
		</form>


	</div>

	<?php include('includes/footer.php'); ?>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/parsleyjs/parsley.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/moment/moment.min.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/datetimepicker/bootstrap-datetimepicker.min.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/custom.js"></script>
	<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
	<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>

	<script type="text/javascript">
		$(window).load(function() {

			$('#mydiv *').prop('disabled', true);

			$('#formdaily').parsley().on('field:validated', function() {
					var ok = $('.parsley-error').length === 0;
				})
				.on('form:submit', function() {

					ShowProgressAnimation();
					return true; // Don't submit form for this demo
				});

			$('#evaluationDate').datetimepicker({
				format: 'MM/DD/YYYY',
				// useCurrent: false,
				maxDate: moment()
			});
			var evaluationDate = '<?php echo $evaluationDate; ?>';
			$('#evaluationDate').val(evaluationDate);

			$('#studentsignitureDate').datetimepicker({
				format: 'MM/DD/YYYY'
			});
			$('#InstructorDate').datetimepicker({
				format: 'MM/DD/YYYY'
			});



			//for searching dropdown
			$(".select2_single").select2();
			$('#select2-cborotation-container').addClass('required-select2');
			$('#select2-cboclinician-container').addClass('required-select2');
			$('#select2-cbostudent-container').addClass('required-select2');

			<?php
			if (isset($_GET['rotationId'])) {
				echo "getHospital({$rotationId}, {$hospitalSiteId});";
			} else {
				echo "getHospital({$rotationId}, {$hospitalSiteId});";
			}
			?>

		});


		function getHospital(val, selectedval) {
			selectedval = selectedval == undefined ? 0 : selectedval;
			$.ajax({
				type: "POST",
				url: "<?php echo ($dynamicOrgUrl); ?>/admin/getAllhospitalForDailyEval.html",

				data: 'rotationId=' + val + "&HospitalSite_Id=" + selectedval,
				success: function(data) {
					$("#cbohospitalsites").html(data);
				}
			});
		}

		$("#cborotation").change(function() {

			<?php if (isset($_GET['studentDailyMasterId'])) { ?>
				var studentDailyMasterId = '<?php echo EncodeQueryData($studentDailyMasterId); ?>';
			<?php } else { ?>
				var studentDailyMasterId = 0;
			<?php } ?>
			var rotationId = $(this).val();
			var Type = '<?php echo $Type; ?>';

			if (studentDailyMasterId != 0 && rotationId && Type != 'A') {
				window.location.href = "dailyEval.html?studentDailyMasterId=" + studentDailyMasterId + "&rotationId=" + rotationId;
			} else if (rotationId && studentDailyMasterId == 0 && Type != 'A') {
				window.location.href = "dailyEval.html?rotationId=" + rotationId;
			} else if (Type == 'A') {
				window.location.href = "dailyEval.html?studentDailyMasterId=" + studentDailyMasterId + "&rotationId=" + rotationId + "&Type=" + Type;
			} else {
				window.location.href = "dailyEval.html";
			}

		});

		//For First Section
		$(document).ready(function() {
			$(".allRadioForFirst").click(function() {
				var sumCheckedButton = 0;
				var checkedNACount = 0;
				var avgAttendance = 0;
				$(".allRadioForFirst input[type=radio]:checked").each(function() {
					var checkedRadio = ($(this).parent().text());

					if (checkedRadio != 3 && checkedRadio != 1 && checkedRadio != 2 && checkedRadio != 4 && checkedRadio != 5) {
						checkedRadio = 0;
						checkedNACount++;
					}

					sumCheckedButton += parseInt(($.trim(checkedRadio)));
				});

				var allradioButtons = $(".allRadioForFirst input[type='radio']:checked").length;
				allcheckedCount = allradioButtons - checkedNACount;
				if (allcheckedCount > 0)
					avgAttendance = sumCheckedButton / allcheckedCount;

				$("#firstSectionAvg").val(avgAttendance.toFixed(2));
			});

		});
		$(window).ready(function() {
			$(".allRadioForFirst").trigger('click');
		});

		//For Second Section
		$(document).ready(function() {
			$(".allRadioForSecond").click(function() {
				var sumCheckedButton = 0;
				var checkedNACount = 0;
				var avgAttendance = 0;
				$(".allRadioForSecond input[type=radio]:checked").each(function() {
					var checkedRadio = ($(this).parent().text());
					if (checkedRadio != 3 && checkedRadio != 1 && checkedRadio != 2 && checkedRadio != 4 && checkedRadio != 5) {
						checkedRadio = 0;
						checkedNACount++;
					}
					sumCheckedButton += parseInt(($.trim(checkedRadio)));
				});
				var allradioButtons = $(".allRadioForSecond input[type='radio']:checked").length;
				allcheckedCount = allradioButtons - checkedNACount;
				if (allcheckedCount > 0)
					avgAttendance = sumCheckedButton / allcheckedCount;

				$("#secondSectionAvg").val(avgAttendance.toFixed(2));
			});

		});

		$(window).ready(function() {
			$(".allRadioForSecond").trigger('click');
		});

		//For Third Section
		$(document).ready(function() {
			$(".allRadioForThird").click(function() {
				var sumCheckedButton = 0;
				var checkedNACount = 0;
				var avgAttendance = 0;

				$(".allRadioForThird input[type=radio]:checked").each(function() {
					var checkedRadio = ($(this).parent().text());
					if (checkedRadio != 3 && checkedRadio != 1 && checkedRadio != 2 && checkedRadio != 4 && checkedRadio != 5) {
						checkedRadio = 0;
						checkedNACount++;
					}

					sumCheckedButton += parseInt(($.trim(checkedRadio)));
				});
				var allradioButtons = $(".allRadioForThird input[type='radio']:checked").length;
				allcheckedCount = allradioButtons - checkedNACount;
				if (allcheckedCount > 0)
					avgAttendance = sumCheckedButton / allcheckedCount;
				$("#thirdSectionAvg").val(avgAttendance.toFixed(2));
			});

		});

		$(window).ready(function() {
			$(".allRadioForThird").trigger('click');
		});

		//For Fourth Section
		$(document).ready(function() {
			$(".allRadioForFourth").click(function() {
				var sumCheckedButton = 0;
				var checkedNACount = 0;
				var avgAttendance = 0;
				$(".allRadioForFourth input[type=radio]:checked").each(function() {
					var checkedRadio = ($(this).parent().text());
					if (checkedRadio != 3 && checkedRadio != 1 && checkedRadio != 2 && checkedRadio != 4 && checkedRadio != 5) {
						checkedRadio = 0;
						checkedNACount++;
					}

					sumCheckedButton += parseInt(($.trim(checkedRadio)));
				});
				var allradioButtons = $(".allRadioForFourth input[type='radio']:checked").length;
				allcheckedCount = allradioButtons - checkedNACount;
				if (allcheckedCount > 0)
					avgAttendance = sumCheckedButton / allcheckedCount;
				$("#fourthSectionAvg").val(avgAttendance.toFixed(2));
			});

		});

		$(window).ready(function() {
			$(".allRadioForFourth").trigger('click');
		});

		//For Five Section
		$(document).ready(function() {
			$(".allRadioForFifth").click(function() {
				var sumCheckedButton = 0;
				var checkedNACount = 0;
				var avgAttendance = 0;

				$(".allRadioForFifth input[type=radio]:checked").each(function() {
					var checkedRadio = ($(this).parent().text());
					if (checkedRadio != 3 && checkedRadio != 1 && checkedRadio != 2 && checkedRadio != 4 && checkedRadio != 5) {
						checkedRadio = 0;
						checkedNACount++;
					}
					sumCheckedButton += parseInt(($.trim(checkedRadio)));
				});
				var allradioButtons = $(".allRadioForFifth input[type='radio']:checked").length;
				allcheckedCount = allradioButtons - checkedNACount;
				if (allcheckedCount > 0)
					avgAttendance = sumCheckedButton / allcheckedCount;
				$("#fiveSectionAvg").val(avgAttendance.toFixed(2));
			});

		});

		$(window).ready(function() {
			$(".allRadioForFifth").trigger('click');
		});

		//For Six Section
		$(document).ready(function() {
			$(".allRadioForSix").click(function() {
				var sumCheckedButton = 0;
				var checkedNACount = 0;
				var avgAttendance = 0;
				$(".allRadioForSix input[type=radio]:checked").each(function() {
					var checkedRadio = ($(this).parent().text());
					if (checkedRadio != 3 && checkedRadio != 1 && checkedRadio != 2 && checkedRadio != 4 && checkedRadio != 5) {
						checkedRadio = 0;
						checkedNACount++;
					}

					sumCheckedButton += parseInt(($.trim(checkedRadio)));
				});
				var allradioButtons = $(".allRadioForSix input[type='radio']:checked").length;
				allcheckedCount = allradioButtons - checkedNACount;
				if (allcheckedCount > 0)
					avgAttendance = sumCheckedButton / allcheckedCount;
				$("#sixSectionAvg").val(avgAttendance.toFixed(2));
			});

		});

		$(window).ready(function() {
			$(".allRadioForSix").trigger('click');
		});

		//For All Average
		$(document).ready(function() {
			$("#formdaily").click(function() {
				var sumCheckedButton = 0;
				var checkedNACount = 0;
				var avgAttendance = 0;
				$("#formdaily input[type=radio]:checked").each(function() {
					var checkedRadio = ($(this).parent().text());
					if (checkedRadio != 3 && checkedRadio != 1 && checkedRadio != 2 && checkedRadio != 4 && checkedRadio != 5) {
						checkedRadio = 0;
						checkedNACount++;
					}
					sumCheckedButton += parseInt(($.trim(checkedRadio)));
				});
				var allradioButtons = $("#formdaily input[type='radio']:checked").length;
				allcheckedCount = allradioButtons - checkedNACount;
				if (allcheckedCount > 0)
					avgAttendance = sumCheckedButton / allcheckedCount;
				$("#totalAvg").val(avgAttendance.toFixed(2));

			});

		});

		$(window).ready(function() {
			$("#formdaily").trigger('click');
		});

		$('#btnSubmit').click(function() {

			var avgAttendance = $('#totalAvg').val();
			if (avgAttendance < '3.00') {
				$('#textarea').prop('required', true);
				alertify.error('Please add Instructor feedback Comment');
			}
		});
	</script>
</body>

</html>