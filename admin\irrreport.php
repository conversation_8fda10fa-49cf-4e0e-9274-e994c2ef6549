<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');       
    include('../class/clsIrr.php');       
    include('../class/clsRotation.php');       
    include('../class/clsLocations.php');       
    include('../setRequest.php'); 	
	
	$currentSchoolId;
    $irrMasterId =0;    
	$TimeZone=$_SESSION["loggedUserSchoolTimeZone"];
  
    
	if(isset($_GET['irrMasterId']))
	{
		$irrMasterId=DecodeQueryData($_GET['irrMasterId']);
	}
	
	//For IRR
	$objIrr = new clsIrr();	
    $objrotation = new clsRotation();	
	$rowsIrrData = $objIrr->GetIRRAssignmentsDetailsByClinician($currentSchoolId,0);
	
	
	$totalIrrCount = 0;
	if($rowsIrrData !='')
	{
		$totalIrrCount = mysqli_num_rows($rowsIrrData);
	}
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>IRR Report</title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
		<link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
                        <li><a href="dashboard.html">Home</a></li>						
                        <li><a href="settings.html">Settings</a></li>						
                        <li class="active">IRR</li>						
						<li class="active">IRR Report</li>						
                    </ol>
                </div>			
               
            </div>
        </div>

        <div class="container">


            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>IRR added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> IRR updated successfully.
                </div>
                <?php 
					}                   
                    else if($_GET["status"] =="Error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
				<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
							
                           <th>IRR Title</th> 						   
                            <th>Checkoff Topic</th>
							 <th style="text-align:center">Score</th>   
                            <th>IRR Student</th>
                            <th>Clinicians</th>
                            <th>Due Date</th>                            
                            <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalIrrCount > 0)
                        {
                            while($row = mysqli_fetch_array($rowsIrrData))
                            {								
                                $irrMasterId = ($row['irrMasterId']);					
                                $schoolTopicId = ($row['schoolTopicId']);					
                                $Irrtitle = ($row['title']);                               
                                $irrStartDate = stripslashes($row['irrStartDate']);
								$rotationId = stripslashes($row['rotationId']);
								$courselocationId = $row['locationId'];
								$parentRotationId = stripslashes($row['parentRotationId']);
								$rotationLocationId = stripslashes($row['rotationLocationId']);
								
								$locationId = 0;
									if($rotationLocationId != $courselocationId && $parentRotationId > 0)
								{
									if($parentRotationId > 0)
									{
										if(!$rotationLocationId)
											$locationId = $objrotation->GetLocationByRotation($rotationId);
										else
											$locationId  = $rotationLocationId;
									}
								}
								else
								{	
										$locationId  = $courselocationId;
								}
									
								//Get Time Zone By Rotation 
								$objLocation = new clsLocations();
								$TimeZone = $objLocation->GetTimeZoneByLocation($locationId);
								unset($objLocation);
								if($TimeZone =='')
									$TimeZone= $_SESSION["loggedUserSchoolTimeZone"];
								$irrStartDate = converFromServerTimeZone($irrStartDate,$TimeZone);
								$irrStartDate=date("m/d/Y",strtotime($irrStartDate)); 
                                $irrEndDate = stripslashes($row['irrEndDate']);
								$irrEndDate = converFromServerTimeZone($irrEndDate,$TimeZone);                                                         
                               $irrEndDate=date("m/d/Y",strtotime($irrEndDate));
							   
								$clinicianCompletionDate = stripslashes($row['clinicianCompletionDate']);
								$clinicianCompletionDate = converFromServerTimeZone($clinicianCompletionDate,$TimeZone);
								
								
								$CheckoffTitle=stripslashes($row['CheckoffTitle']);
								$firstName=stripslashes($row['firstName']);
								
								$lastName=stripslashes($row['lastName']);
								$ClinicianName=$firstName . ' ' . $lastName;
								$cliniciancountrow=$objIrr->GetClinicianCount($irrMasterId);
								
                                $cliniciancount = stripslashes($cliniciancountrow['clinicianId']);                           
                                   $GetScoreIrr=$objIrr->GetIrrStudentScore($irrMasterId);
                                $Score=$GetScoreIrr['IrrScore'];                     
                               ?>
                            <tr>
								
								<td><?php echo ($Irrtitle); ?></td>
								<td style="white-space: normal;word-wrap: break-word;"><?php echo ($CheckoffTitle); ?></td>
								<td style="text-align:center"><?php echo (number_format((float)$Score, 2, '.', '')); ?></td>

								<td> <?php echo ('IRR Student'); ?> </td>
								<td style="text-align:center"><a title="View To Clinicians" href="clinicianlist.html?irrMasterId=<?php echo (EncodeQueryData($irrMasterId)); ?>" class="cliniciandetails"><?php echo ($cliniciancount); ?></a></td>
								<td><?php echo ($irrEndDate); ?></td>					
                               
                               
                                <td style="text-align: center">	
									
								<a  href="irrreportview.html?irrMasterId=<?php echo EncodeQueryData($irrMasterId); ?>&schoolTopicId=<?php echo EncodeQueryData($schoolTopicId); ?>">View</a>
								
								 </td>
                            </tr>
                            <?php
                            }
                        }
						unset($objrotation);
                    ?>
					</tbody>
                </table>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
		<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>

        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

			$('.cliniciandetails').magnificPopup({'type': 'ajax',closeOnBgClick: false});	
			
            $(window).load(function(){
                $("#divTopLoading").addClass('hide');							
				
				
					
				
            });
			
			
                 var current_datatable = $("#datatable-responsive").DataTable({

                     "aoColumns": [{
                    "sWidth": "3%"
                },{
                    "sWidth": "10%",
                    "sClass": "alignCenter"
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter"
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter"
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter",
                    
                },{
                    "sWidth": "1%",
                    "sClass": "alignCenter",
                   
                },
				{
                    "sWidth": "1%",
                    "sClass": "alignCenter",
                    "bSortable":false
                
                }]
					
				 });    

                $(document).on('click', '.resendSms', function() {
                    var irrdetailId = $(this).attr('irrdetailId');
                    $.ajax({
                        type: "POST",
                        url: "../ajax/send_standard_checkoff_sms_to_preceptor.html",
                        data: {							
                            irrdetailId: irrdetailId
                                                    
                        },
                        success: function(data) {
                            alertify.success('Sent');
                        }
                    });
                });
        </script>
    </body>
    </html>