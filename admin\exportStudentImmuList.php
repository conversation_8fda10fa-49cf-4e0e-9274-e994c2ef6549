<?php
require '../vendor/autoload.php'; // PhpSpreadsheet autoload

use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xls;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Style\Border;

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../class/clsDB.php');
include('../includes/commonfun.php');
include('../class/clsImmunization.php');
include('../class/clsStudent.php');
include('../setRequest.php');

$studentImmunizationId = 0;
$currentSchoolId = $_GET['currentSchoolId'];
$currentSchoolId = DecodeQueryData($currentSchoolId);

$studentId = $_GET['studentId'];
$studentId = DecodeQueryData($studentId);
$objimmunization = new clsImmunization();

$rowsimmunization = $objimmunization->GetAllSingleStudent($studentImmunizationId, $studentId);

$objStudent = new clsStudent();
$rowStudent = $objStudent->GetStudentDetails($studentId);
$firstName = $rowStudent['firstName'];
$lastName = $rowStudent['lastName'];
$fullName = $firstName . ' ' . $lastName;

$title = 'Student Immunization List';
date_default_timezone_set('Asia/Kolkata');
$today = date('m/d/Y, H:i A');

if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnStudentExport'])) {
    $reportType = $_POST['cboreporttype'];

    switch ($reportType) {
        case "StudentImmulizationReport":

            $spreadsheet = new Spreadsheet();

            // Set document properties
            $spreadsheet->getProperties()->setCreator('Schools')
                ->setLastModifiedBy('JCC')
                ->setTitle('Reports')
                ->setSubject('Student Immunization List')
                ->setDescription('All School Reports');

            // Active Sheet
            $sheet = $spreadsheet->getActiveSheet();
            $sheet->setTitle('Immunization Report');

            // Print Heading
            $headerStyleArray = [
                'font' => ['bold' => true, 'size' => 16],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E0E0E0']],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
            ];

            $sheet->mergeCells("B2:E2");
            $sheet->setCellValue('B2', $title);
            $sheet->getStyle('B2:E2')->applyFromArray($headerStyleArray);

            // Student Name
            $sheet->mergeCells("B4:E4");
            $sheet->setCellValue('B4', $fullName);
            $sheet->getStyle('B4:E4')->applyFromArray($headerStyleArray);

            // Table Heading
            $tableHeaderStyleArray = [
                'font' => ['bold' => true, 'size' => 12],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'fill' => ['fillType' => Fill::FILL_SOLID, 'startColor' => ['rgb' => 'E0E0E0']],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
            ];

            $sheet->setCellValue('B6', 'Immunization')->getStyle('B6')->applyFromArray($tableHeaderStyleArray);
            $sheet->setCellValue('C6', 'Date')->getStyle('C6')->applyFromArray($tableHeaderStyleArray);
            $sheet->setCellValue('D6', 'Notification Date')->getStyle('D6')->applyFromArray($tableHeaderStyleArray);
            $sheet->setCellValue('E6', 'Expiry Date')->getStyle('E6')->applyFromArray($tableHeaderStyleArray);

            // Populate Data
            $printStartRowCounter = 7;
            $rowStyleArray = [
                'font' => ['size' => 10],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_LEFT],
                'borders' => ['allBorders' => ['borderStyle' => Border::BORDER_THIN]],
            ];

            if ($rowsimmunization) {
                while ($row = mysqli_fetch_array($rowsimmunization)) {
                    $shortName = $row['shortName'];
                    $immunizationDate = $row['immunizationDate'] ? date("m/d/Y", strtotime($row['immunizationDate'])) : 'N/A';
                    $immunizationNotificationDate = $row['immunizationNotificationDate'] ? date("m/d/Y", strtotime($row['immunizationNotificationDate'])) : 'N/A';
                    $ExpiryDate = $row['expiryDate'] ? date("m/d/Y", strtotime($row['expiryDate'])) : 'N/A';

                    $sheet->setCellValue('B' . $printStartRowCounter, $shortName);
                    $sheet->setCellValue('C' . $printStartRowCounter, $immunizationDate);
                    $sheet->setCellValue('D' . $printStartRowCounter, $immunizationNotificationDate);
                    $sheet->setCellValue('E' . $printStartRowCounter, $ExpiryDate);

                    $sheet->getStyle('B' . $printStartRowCounter . ':E' . $printStartRowCounter)->applyFromArray($rowStyleArray);

                    $printStartRowCounter++;
                }
            }

            // Auto size columns
            foreach (range('B', 'E') as $columnID) {
                $sheet->getColumnDimension($columnID)->setAutoSize(true);
            }

            $sheet->setSelectedCell('A1'); // Set focus to A1 to avoid selected rows

            // Output File
            $reportName = 'StudentImmunizationListReport_';
            $currentDate = date('m_d_Y_h_i');
            $fileName = $reportName . $today . '.xls';

            header('Content-Type: application/vnd.ms-excel');
            header('Content-Disposition: attachment;filename="' . $fileName . '"');
            header('Cache-Control: max-age=0');

            $writer = new Xls($spreadsheet);
            $writer->save('php://output');
            break;

        default:
            echo "<b>Please Select Valid Type.</b>";
            break;
    }
}
?>
