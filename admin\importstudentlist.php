<?php

include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsStudent.php');
include('../class/clsStudentRankMaster.php');
include('../class/clsLocations.php');
include('../class/PasswordHash.php');
include('../class/clsSendEmails.php');
include('../class/clsSMTPSettings.php');
include('../class/clsNotification.php');


if (isset($_GET['studentId'])) {
	$currentstudentId = $_GET['studentId'];
	//echo $currentstudentId;exit;
	$currentstudentId = DecodeQueryData($currentstudentId);
}
if ($_SERVER['REQUEST_METHOD'] == "POST") {
	ini_set('upload_max_filesize', '50M');
	ini_set('post_max_size', '50M');
	ini_set('max_input_time', 300000);
	ini_set('max_execution_time', 300000);

	$notifyMessage = 'Imported';
	$row = 1;
	if (isset($_FILES['file'])) {
		$filename = $_FILES["file"]["tmp_name"];
		$ext = strtolower(pathinfo($_FILES['file']['name'], PATHINFO_EXTENSION));

		if ($ext != "csv") {
			$error = "Upload only  .csv file.";
			header('location:schoolstudents.html?status=Importerror');
			exit();
		}


		if ($_FILES["file"]["size"] > 0) {
			$file = fopen($filename, "r");
			$counter = 0;
			$rankName = '';
			$rankDetail = '';
			$rankTitle = '';
			$studentArray = array();
			$encounteredValues = array(); // Initialize the array to store encountered values
			$encounteredPhoneNumbers = array(); // Initialize the array to store encountered values
			$isRepeated = false; // Flag to track if the value is repeated
			$isRepeatedPhone = false; // Flag to track if the phone number is repeated

			while (($getData = fgetcsv($file, 10000, ",")) !== FALSE) {
				if ($row == 1) {
					$row++;
					continue;
				}
				$getData = array_filter($getData, function ($value) {
					return $value !== '';
				});

				if (!empty($getData)) {
					$studentArray[] = $getData;
					$recordIdNumber = isset($getData[0]) ? trim($getData[0]) : '';

					if ($recordIdNumber != '') {
						if (in_array($recordIdNumber, $encounteredValues)) {

							$isrow = $counter + 2;
							$messageText = 'Error';
							header('location:schoolstudents.html?status=' . $messageText . '&isRow=' . $isrow . '&type=0&value=' . $recordIdNumber);
							exit();
						}

						$encounteredValues[] = $recordIdNumber;
					}
					$FirstName = isset($getData[1]) ? trim($getData[1]) : '';
					$MiddleName = isset($getData[2]) ? trim($getData[2]) : '';
					$LastName = isset($getData[3]) ? trim($getData[3]) : '';
					$Email = isset($getData[4]) ? trim($getData[4]) : '';
					$Phone = isset($getData[5]) ? trim($getData[5]) : '';

					if (in_array($Phone, $encounteredPhoneNumbers)) {
						$isrow = $counter + 2;
						$messageText = 'Error';
						header('location:schoolstudents.html?status=' . $messageText . '&isRow=' . $isrow . '&type=1&value=' . $Phone);
						exit();
					}
					$encounteredPhoneNumbers[] = $Phone;

					$CellPhone = isset($getData[6]) ? trim($getData[6]) : '';
					$Address1 = isset($getData[7]) ? trim($getData[7]) : '';
					$Address2 = isset($getData[8]) ? trim($getData[8]) : '';
					$CountryName = isset($getData[9]) ? trim($getData[9]) : '';
					$City = isset($getData[10]) ? trim($getData[10]) : '';
					$StateName = isset($getData[11]) ? trim($getData[11]) : '';
					$Zip = isset($getData[12]) ? trim($getData[12]) : '';
					$Username = isset($getData[13]) ? trim($getData[13]) : '';
					$Password = isset($getData[14]) ? trim($getData[14]) : '';
					$yes = isset($getData[15]) ? trim($getData[15]) : '';
					$LocationName = isset($getData[16]) ? trim($getData[16]) : '';
					$RankName = isset($getData[17]) ? trim($getData[17]) : '';

					if ($FirstName && $LastName && $Email && $Phone && $Address1 && $LocationName && $City && $StateName  && $Zip && $Username && $Password && $RankName) {

						if ($recordIdNumber) {
							$objDB = new clsDB();
							$isRecordId = $objDB->GetSingleColumnValueFromTable('student', 'studentId', 'recordIdNumber', $recordIdNumber, 'schoolId', $currentSchoolId);
							unset($objDB);
							if ($isRecordId) {
								$isrow = $counter + 2;
								$messageText = 'Error';
								header('location:schoolstudents.html?status=' . $messageText . '&isRow=' . $isrow . '&type=0&value=' . $recordIdNumber);
								exit();
							}
						}

						if ($Phone != '') {
							$PhoneNo = str_replace("-", "", $Phone);
							$objStudent = new clsStudent();
							$isreturn = $objStudent->checkStudentPhoneExist($PhoneNo, $currentSchoolId, 0);
							unset($objStudent);

							if ($isreturn) {
								$isrow = $counter + 2;
								$messageText = 'Error';
								header('location:schoolstudents.html?status=' . $messageText . '&isRow=' . $isrow . '&type=1&value=' . $Phone);
								exit();
							}
						}
						$objStudentRankMaster = new clsStudentRankMaster();
						$objCountryStateMaster = new clsCountryStateMaster();
						$objLocation = new clsLocations();
						$rankDetail = $objStudentRankMaster->GetRankbyTitle($RankName, $currentSchoolId);
						$rankTitle = $rankDetail['rankId'];

						$countryName = $objCountryStateMaster->GetCountyName($CountryName);
						$stateName = $objCountryStateMaster->GetStateNameByCode($StateName);

						$locationTitle = $objLocation->GetLocationName($LocationName, $currentSchoolId);

						if ($rankTitle == '' || $rankTitle == 0) {
							$isrow = $counter + 2;
							$messageText = 'Error';
							header('location:schoolstudents.html?status=' . $messageText . '&isRow=' . $isrow . '&type=2&value=' . $RankName);
							exit();
						}

						if ($locationTitle == '' || $locationTitle == 0) {
							$isrow = $counter + 2;
							$messageText = 'Error';
							header('location:schoolstudents.html?status=' . $messageText . '&isRow=' . $isrow . '&type=3&value=' . $LocationName);
							exit();
						}

						unset($objStudentRankMaster);
						unset($objCountryStateMaster);
						$counter = $counter + 1;
						continue;
						if ($counter == '500') {
							$messageText = $retStudentId ? 'Imported' : 'Error';
							header('location:schoolstudents.html?status=' . $messageText);
							exit();
						}
					} else {

						$isrow = $counter + 2;
						$messageText = 'Error';
						header('location:schoolstudents.html?status=' . $messageText . '&isRow=' . $isrow);
						exit();
					}
				}
			}


			// echo '<pre>';
			// print_r($encounteredValues);
			// exit;
			$studentCnt = 0;
			foreach ($studentArray as $students) {


				$recordIdNumber  = isset($students[0])  ? trim($students[0])  : '';
				$FirstName       = isset($students[1])  ? trim($students[1])  : '';
				// Remove special characters only if FirstName is not empty
				$FirstName       = $FirstName ? iconv('UTF-8', 'ASCII//TRANSLIT//IGNORE', $FirstName) : '';
				$MiddleName      = isset($students[2])  ? trim($students[2])  : '';
				$LastName        = isset($students[3])  ? trim($students[3])  : '';
				$Email           = isset($students[4])  ? trim($students[4])  : '';
				$Phone           = isset($students[5])  ? trim($students[5])  : '';
				$CellPhone       = isset($students[6])  ? trim($students[6])  : '';
				$Address1        = isset($students[7])  ? trim($students[7])  : '';
				$Address2        = isset($students[8])  ? trim($students[8])  : '';
				$CountryName     = isset($students[9])  ? trim($students[9])  : '';
				$City            = isset($students[10]) ? trim($students[10]) : '';
				$StateName       = isset($students[11]) ? trim($students[11]) : '';
				$Zip             = isset($students[12]) ? trim($students[12]) : '';
				$Username        = isset($students[13]) ? trim($students[13]) : '';
				$tempPassword    = $Password = isset($students[14]) ? trim($students[14]) : '';
				$yes             = isset($students[15]) ? trim($students[15]) : '';
				$isEmailPassword = ($yes === 'Y') ? 1 : 0;
				$LocationName    = isset($students[16]) ? trim($students[16]) : '';
				$RankName        = isset($students[17]) ? trim($students[17]) : '';


				$Password = PasswordHash::hash($Password);

				$objStudentRankMaster = new clsStudentRankMaster();
				$objCountryStateMaster = new clsCountryStateMaster();
				$objLocation = new clsLocations();
				$rankDetail = $objStudentRankMaster->GetRankbyTitle($RankName, $currentSchoolId);
				$rankTitle = $rankDetail['rankId'];

				$countryName = $objCountryStateMaster->GetCountyName($CountryName);
				$stateName = $objCountryStateMaster->GetStateNameByCode($StateName);

				$locationName = $objLocation->GetLocationName($LocationName, $currentSchoolId);


				unset($objStudentRankMaster);
				unset($objCountryStateMaster);
				//Save Data							
				$objStudent = new clsStudent();

				$objStudent->firstName = isset($FirstName) ? $FirstName : '';
				$objStudent->middleName = isset($MiddleName) ? $MiddleName : '';
				$objStudent->lastName = isset($LastName) ? $LastName : '';
				$objStudent->email = isset($Email) ? $Email : '';
				$objStudent->phone = isset($Phone) ? $Phone : 0;
				$objStudent->recordIdNumber = isset($recordIdNumber) ? $recordIdNumber : 0;
				$objStudent->cellPhone = isset($CellPhone) ? $CellPhone : 0;
				$objStudent->address1 = isset($Address1) ? $Address1 : '';
				$objStudent->address2 = isset($Address2) ? $Address2 : '';
				$objStudent->locationId = isset($locationName) ? $locationName : 0;
				$objStudent->city = isset($City) ? $City : 0;
				$objStudent->stateId = isset($stateName) ? $stateName : 0;
				$objStudent->zip = isset($Zip) ? $Zip : 0;
				$objStudent->username = isset($Username) ? $Username : '';
				$objStudent->passwordHash = isset($Password) ? $Password : '';
				$objStudent->rankId = isset($rankTitle) ? $rankTitle : 0;
				$objStudent->isActive = 1;
				$objStudent->isEmailPassword = $isEmailPassword;
				$objStudent->schoolId = isset($currentSchoolId) ? $currentSchoolId : 0;
				$objStudent->createdBy = $_SESSION["loggedUserId"];

				$retStudentId = $objStudent->SaveStudent(0);
				if ($isEmailPassword) {
					//Send email
					//------------------------------------------------------
					$objSendEmails = new clsSendEmails($currentSchoolId);
					$objSendEmails->SendStudentLoginDetails($retStudentId, $tempPassword);
					unset($objSendEmails);
				}

				if ($retStudentId) {

					$studentCnt++;
					//save data
					$objNotification = new clsNotification();

					$notification = 'Student added.';
					$modul = 'Student';
					$userType = 'SA';
					$created_at = date("Y-m-d");
					$objNotification->schoolId = $currentSchoolId;
					$objNotification->notification = $notification;
					$objNotification->modul = $modul;
					$objNotification->userType = $userType;
					$objNotification->referenceId = $retStudentId;
					$objNotification->created_at = $created_at;
					$objNotification->beforeDays = 0;
					$objNotification->afterDays = 0;
					$objNotification->beforemessage = 0;
					$objNotification->aftermessage = 0;
					$objNotification->isActiveNotification = 0;
					$notificationId = $objNotification->SaveWebNotification();
				}
			}

			//Audit Log Start
			// Instantiate the Logger class
			$objLog = new clsLogger();

			// Determine the action type (EDIT or ADD) based on the presence of a journal ID
			$action = $objLog::ADD;
			$type = "Import";
			$userType = $objLog::ADMIN; // User type is set to ADMIN
			$IsMobile = 0;

			$objStudent = new clsStudent();
			$objStudent->saveStudentAuditLog($retStudentId, $_SESSION['loggedUserId'], $userType, $action, $IsMobile, 0, $type, $studentCnt);
			unset($objStudent);

			unset($objLog);
			//Audit Log Ends


			unset($objStudent);
			fclose($file);


			$messageText = $retStudentId ? 'Imported' : 'Error';
			header('location:schoolstudents.html?status=' . $messageText);
			exit();
		}
	}
}
