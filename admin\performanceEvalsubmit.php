<?php
$IsMobile  = isset($_POST['IsMobile']) ? $_POST['IsMobile'] : 0;

include('../includes/config.php');
include('includes/validateUserLogin.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../setRequest.php');
include('../class/clsCIevaluation.php');
include('../class/clsQuestionOption.php');
include('../class/clsPerformance.php');

// echo '<pre>';
// print_r($_POST);
// print_r($_GET);
// exit;

@session_start();
// print_r($_SESSION);
$createdBy = isset($_SESSION["loggedStudentId"]) ? $_SESSION["loggedStudentId"] : 0;
$performanceEvaluationMasterId = 0;
if ($_SERVER['REQUEST_METHOD'] == "POST" && isset($_POST['btnSubmit'])) {
	$rotationId = 0;


	if (isset($_GET['performanceEvaluationMasterId'])) {
		$performanceEvaluationMasterId = $_GET['performanceEvaluationMasterId'];
		$performanceEvaluationMasterId = DecodeQueryData($performanceEvaluationMasterId);
	}
	if (isset($_GET['performanceRotationId'])) {
		$performanceRotationId = $_GET['performanceRotationId'];
		$performanceRotationId = DecodeQueryData($performanceRotationId);
	}
	//$status =  'Added';	
	$performanceEvaluationMasterId = isset($_GET['performanceEvaluationMasterId']) ? DecodeQueryData($_GET['performanceEvaluationMasterId']) : 0;
	$status = ($performanceEvaluationMasterId > 0) ? 'updated' : 'added';

	//echo $status.$formativeId;   

	$cbostudent = $_POST['cboStudent'];
	$cboclinician  = ($_POST['cboclinician']);
	$weeks  = ($_POST['weeks']);
	

	$evaluationDate = GetDateStringInServerFormat($_POST['evaluationDate']);
	$evaluationDate = str_replace('00:00:00', '12:00 PM', $evaluationDate);
	$evaluationDate = date('Y-m-d H:i', strtotime($evaluationDate));

    $studentDate = GetDateStringInServerFormat($_POST['studentDate']);
	$studentDate = str_replace('00:00:00', '12:00 PM', $studentDate);
	$studentDate = date('Y-m-d H:i', strtotime($studentDate));

    $evaluatorDate = GetDateStringInServerFormat($_POST['evaluatorDate']);
	$evaluatorDate = str_replace('00:00:00', '12:00 PM', $evaluatorDate);
	$evaluatorDate = date('Y-m-d H:i', strtotime($evaluatorDate));

    $totalPoints  = ($_POST['totalPoints']);
	$totalAvg  = ($_POST['totalAvg']);

    // $objCIevaluation = new clsCIevaluation();
	// $objCIevaluation->rotationId = $performanceRotationId;
	// $objCIevaluation->clinicianId = $cboclinician;
	// $objCIevaluation->schoolClinicalSiteUnitId = $cbohospitalsites;
	// $objCIevaluation->studentId = $cbostudent;
	// $objCIevaluation->schoolId = $currentSchoolId;
	// $objCIevaluation->evaluationDate = $evaluationDate;
	// $objCIevaluation->patientCareAdultAreaId = $Adult;
	// $objCIevaluation->patientCarePediatricAreaId = $Pediatric;
	// $objCIevaluation->patientCareNeonatalaAreaId = $Neonatal;
	// $objCIevaluation->createdBy = $_SESSION["loggedStudentId"];
	// $retperformanceEvaluationId = $objCIevaluation->SaveCIevaluation($performanceEvaluationMasterId);

	// $objCIevaluation->DeleteCIevaluationDetails($retperformanceEvaluationId);


    $objPerformance = new clsPerformance();
	$objPerformance->rotationId = $performanceRotationId;
	$objPerformance->clinicianId = $cboclinician;
	$objPerformance->studentId = $cbostudent;
	$objPerformance->schoolId = $currentSchoolId;
	$objPerformance->weeks = $weeks;
	$objPerformance->evaluationDate = $evaluationDate;
	$objPerformance->evaluatorSignatureDate = $evaluatorDate;
	$objPerformance->studentSignatureDate = $studentDate;
	$objPerformance->totalPoints = $totalPoints;
	$objPerformance->percentage = $totalAvg;
	$objPerformance->createdBy = $createdBy;
	$retperformanceEvaluationId = $objPerformance->SavePerformanceEvaluation($performanceEvaluationMasterId);

	$objPerformance->DeletePerformanceDetails($retperformanceEvaluationId);

	//SAVE OPTIONS
	foreach ($_POST as $id => $value) {

		if (strpos($id, 'questionoptions_') === 0) {
			$arraIds = explode("_", $id);

			if (!isset($arraIds[1]))
				continue;

			$schoolEvaluationQuestionId = $arraIds[1];
			$objPerformance->evaluationMasterId = $retperformanceEvaluationId;
			$objPerformance->schoolEvaluationQuestionId = $schoolEvaluationQuestionId;
			$objPerformance->schoolEvaluationOptionValue = $value[0];
			$objPerformance->schoolEvaluationOptionAnswerText = '';
			$objPerformance->comment = isset($_POST['textarea_' . $schoolEvaluationQuestionId]) ? $_POST['textarea_' . $schoolEvaluationQuestionId] : '';
			$evaluationDetaild = $objPerformance->SavePerformanceEvaluationDetails($retperformanceEvaluationId);
		}
	}

	//SAVE OPTIONS
	foreach ($_POST as $id => $value) {
		if (strpos($id, 'questionoptionst_') === 0) {
			$arraIds = explode("_", $id);
			//Skip null
			if (!isset($arraIds[1]))
				continue;

			$schoolCIEvaluationQuestionId = $arraIds[1];

			$schoolEvaluationQuestionId = $arraIds[1];
			$objPerformance->evaluationMasterId = $retperformanceEvaluationId;
			$objPerformance->schoolEvaluationQuestionId = $schoolEvaluationQuestionId;
			$objPerformance->schoolEvaluationOptionValue = $value[0];
			$objPerformance->schoolEvaluationOptionAnswerText = '';
			$objPerformance->comment = '';
			$evaluationDetaild = $objPerformance->SavePerformanceEvaluationDetails($retperformanceEvaluationId);
		}
	}
	unset($objCIevaluation);

	if ($retperformanceEvaluationId > 0) {
		if ($IsMobile) {
			header('location:' . BASE_PATH . '/webRedirect.html?status=Success&type=performanceEvaluation');
			exit;
		} else {
			header('location:performanceEvaluationList.html?performanceEvaluationMasterId=' . EncodeQueryData($performanceEvaluationMasterId) . '&performanceRotationId=' . EncodeQueryData($performanceRotationId) . '&status=' . $status);
			exit();
		}
	} else {
		if ($IsMobile) {
			header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=performanceEvaluation');
			exit();
		} else {
			header('location:performanceEval.html?status=error');
		}
	}
} {
	if ($IsMobile) {
		header('location:' . BASE_PATH . '/webRedirect.html?status=Error&type=performanceEvaluation');
		exit();
	} else {
		header('location:performanceEvaluationList.html');
		exit();
	}
}
