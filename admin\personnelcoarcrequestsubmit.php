<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsPersonnelCoarcRequestMaster.php');
include('../class/clsQuestionOption.php');
include('../class/clsSendEmails.php');
include('../class/clsSMTPSettings.php');
include("../class/class.phpmailer.php");
// include("../class/class.smtp.php");
include('../class/PasswordHash.php');
include('../class/clsPersonnelCoarc.php');
include('../class/clsClinician.php');
include('../class/clsSchool.php');
include('../class/clsStudent.php');
include('../class/clsCoarc.php');
include('../setRequest.php');


$personnelCoarcId = 0;
//	$getClinicianCoarcId='';

//Get Country Code
$objDB = new clsDB();
$countryCode = $objDB->GetSingleColumnValueFromTable('schools', 'countryCode', 'schoolId', $currentSchoolId);
unset($objDB);

if (isset($_POST['id']) && isset($_POST['type'])) {
	$personnelCoarcId = isset($_GET['personnelCoarcId']) ? DecodeQueryData($_GET['personnelCoarcId']) : 0;

	$getClinicianCoarcId = DecodeQueryData($_POST['getClinicianCoarcId']);

	$clinicianId = isset($_POST['id']) ? DecodeQueryData($_POST['id']) : 0;
	$currentSchoolId = DecodeQueryData($_POST['SchoolId']);
	$type = $_POST['type'];

	$objPersonnel = new clsPersonnelCoarc();
	$objCoarc = new clsCoarc();
	$objSchool = new clsSchool();
	//$clinicianPersonnelCoarcId=$objPersonnel->GetClinicianPersonnelCoarcMasterId($clinicianId);
	$coarcSurveyMasterId = DecodeQueryData($_POST['coarcSurveyMasterId']);
	$isDelivery = $objCoarc->GetCoarcSurveyDelivery($coarcSurveyMasterId);
	$currenschoolURL = $objSchool->GetSlugName($currentSchoolId);

	unset($objPersonnel);

	//For Clinician Details
	$objclinician = new clsClinician();
	$studentInfo = $objclinician->GetClinicianDetails($clinicianId);
	$Firstname = $studentInfo['firstName'];
	$Latstname = $studentInfo['lastName'];
	$Fullname = $Firstname . ' ' . $Latstname;
	$emailId = $studentInfo['email'];
	$phone = $studentInfo['phone'];
	unset($objclinician);

	$statuschange = '0';
	$dynamicePassword = generate_password(6);
	$passwordHash = PasswordHash::hash($dynamicePassword);
	$personnelName = strtolower($Fullname);
	$username = strtolower(preg_replace("/[^a-zA-Z]/", "", $personnelName));

	//For Save Data
	$objPersonnelCoarc = new clsPersonnelCoarcRequestMaster();
	$objSendEmails = new clsSendEmails($currentSchoolId);

	$objPersonnelCoarc->clinicianId = $clinicianId;
	$objPersonnelCoarc->personnelName = $personnelName;
	$objPersonnelCoarc->phoneNo = $phone;
	$objPersonnelCoarc->emailID = $emailId;
	$objPersonnelCoarc->password = '';
	$objPersonnelCoarc->status = $statuschange;
	$objPersonnelCoarc->schoolId = $currentSchoolId;
	$objPersonnelCoarc->username = $username;
	$objPersonnelCoarc->passwordHash = $passwordHash;
	$objPersonnelCoarc->coarcSurveyMasterId = $coarcSurveyMasterId;
	$objPersonnelCoarc->createdBy = $_SESSION["loggedUserId"];

	$objCoarc = new clsCoarc();
	$surveyDetail = $objCoarc->GetPersonnelCoarcSurveyByStudent($clinicianId, $coarcSurveyMasterId);
	$personnelCoarcId = stripslashes($surveyDetail['personnelCoarcId']);
	$status = stripslashes($surveyDetail['status']);

	$objDB = new clsDB();
	$schoolName = $objDB->GetSingleColumnValueFromTable('schools', 'displayName', 'schoolId', $currentSchoolId);
	unset($objDB);

	$coarcSurveyTitle = $objCoarc->GetCoarcSurveyTitle($coarcSurveyMasterId);

	if ($personnelCoarcId) {

		if ($isDelivery == '0') {

			$objSendEmails->SendEmailToStudentForPersonnelCoarcSurvey($clinicianId, $getClinicianCoarcId, $emailId, $currenSchoolLogoImagePath, $currenschoolDisplayname, $coarcSurveyMasterId, $coarcSurveyTitle);
		} elseif ($isDelivery == '1') {

			$objDB = new clsDB();
			$sql = "Select * From clinician Where clinicianId=" . $clinicianId;
			// 	 echo $sql; exit;
			$row = $objDB->GetDataRow($sql);
			// 
			$FirstName = stripslashes($row['firstName']);
			$LastName = stripslashes($row['lastName']);
			$Username = stripslashes($row['username']);
			$Phone = stripslashes($row['phone']);


			$link = BASE_PATH . '/school/' . $currenschoolURL . '/clinician/login_CoARC_Survey.html';
			$URL = $link . '?clinicianId=' . EncodeQueryData($clinicianId) . '&personnelcoarcId=' . EncodeQueryData($getClinicianCoarcId) . '&coarcSurveyMasterId=' . EncodeQueryData($coarcSurveyMasterId) . '&userName=' . $Username . '&schoolId=' . EncodeQueryData($currentSchoolId);
			$randomUrl = getTinyUrl($URL);
			$redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);
			$mobileNum = '+' . $countryCode . $Phone;
			// echo $mobileNum;exit;

			$previewURL = "Hello {$FirstName}, You received a Personnel JRCERT Survey Message request from " . $schoolName . ". Please Click below to start and submit your Survey. " . $redirectUrl;
			//SMS Send
			$result = sendSMS($mobileNum, $previewURL);

		}

	} else {
		$retPersonnelCoarctId = $objPersonnelCoarc->SavePersonnelCoarcRequest($clinicianId);
		if ($isDelivery == '0') {
			$objSendEmails->SendEmailToStudentForPersonnelCoarcSurvey($clinicianId, $retPersonnelCoarctId, $emailId, $currenSchoolLogoImagePath, $currenschoolDisplayname, $coarcSurveyMasterId, $coarcSurveyTitle);

		} elseif ($isDelivery == '1') {
			$objDB = new clsDB();
			$sql = "Select * From clinician Where clinicianId=" . $clinicianId;

			$row = $objDB->GetDataRow($sql);
			// echo '<pre>'; echo '$isDelivery->2';print_r($row);
			$FirstName = stripslashes($row['firstName']);
			$LastName = stripslashes($row['lastName']);
			$Username = stripslashes($row['username']);
			$Phone = stripslashes($row['phone']);

			$link = BASE_PATH . '/school/' . $currenschoolURL . '/clinician/login_CoARC_Survey.html';

			$URL = $link . '?clinicianId=' . EncodeQueryData($clinicianId) . '&personnelcoarcId=' . EncodeQueryData($getClinicianCoarcId) . '&coarcSurveyMasterId=' . EncodeQueryData($coarcSurveyMasterId) . '&userName=' . $Username . '&schoolId=' . EncodeQueryData($currentSchoolId);
			$randomUrl = getTinyUrl($URL);
			$redirectUrl = BASE_PATH . '/redirect/' . EncodeQueryData($randomUrl);
			$mobileNum = '+' . $countryCode . $Phone;
			echo $mobileNum;exit;
			$previewURL = "Hello {$FirstName}, You received a Personnel JRCERT Survey Message request from " . $schoolName . ". Please Click below to start and submit your Survey. " . $redirectUrl;
			//SMS Send
			$result = sendSMS($mobileNum, $previewURL);


		}
	}
	unset($objPersonnelCoarc);
	unset($objSendEmails);
}



