<?php
include('includes/validateUserLogin.php');
include('../includes/config.php');
include('../includes/commonfun.php');
include('../class/clsDB.php');
include('../class/clsProcedureCategory.php');
include('../setRequest.php');

$transchooldisplayName = '';
$currentSchoolId;
$transchooldisplayName = $currenschoolDisplayname;
$procedureCategoryId = '';

if (isset($_GET['procedureCategoryId'])) {
    $procedureCategoryId = $_GET['procedureCategoryId'];
    $procedureCategoryId = DecodeQueryData($procedureCategoryId);
}

$title = "Procedure Count| " . $transchooldisplayName;

//CREATE OBJECT
$objProcedureCategory = new clsProcedureCategory();
$totalProcedureCount = 0;
$rowsProcedureCount = $objProcedureCategory->GetAllProcedureCountDetails($procedureCategoryId);

if ($rowsProcedureCount != '') {
    $totalProcedureCount = mysqli_num_rows($rowsProcedureCount);
}

$isActiveCheckoff = isset($isActiveCheckoff) ? $isActiveCheckoff : null;
// $Category=$objProcedureCategory->GetAllCategory();
if ($isActiveCheckoff == 1)
    $Category = $objProcedureCategory->GetAllCategory();
elseif ($isActiveCheckoff == 2)
    $Category = $objUsafProcedureCategory->GetAllUsafCategory($usafSchoolId);
else
    $Category = $objProcedureCategory->GetAllAdvanceCategory();

unset($objProcedureCategory);


?>
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
    <title><?php echo ($title); ?></title>
    <?php include('includes/headercss.php'); ?>
    <?php include("includes/datatablecss.php") ?>

    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/css/select2.min.css">
    <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

</head>

<body>
    <?php include('includes/header.php'); ?>

    <div class="row margin_zero breadcrumb-bg">
        <div class="container">
            <div class="pull-left">
                <ol class="breadcrumb">
                    <li><a href="dashboard.html">Home</a></li>
                    <li><a href="settings.html">Settings</a></li>
                    <li class="active">Procedure Counts</li>
                </ol>
            </div>

            <div class="pull-right">
                <a class="btn btn-link" href="addprocedurecounts.html">Add</a>
            </div>


        </div>
    </div>

    <div class="container">

        <?php
        if (isset($_GET["status"])) {
            if ($_GET["status"] == "Added") {
        ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Procedure Count added successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Updated") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Procedure Count updated successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Deleted") {
            ?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Procedure Count deleted successfully.
                </div>
            <?php
            } else if ($_GET["status"] == "Error") {
            ?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                    </button> Error occurred.
                </div>
        <?php
            }
        }
        ?>

        <div id="divTopLoading">Loading...</div>

        <div class="col-md-2 pull-right padding_right_zero"  style="margin-bottom: 10px;padding-right: 0px;">
                    <select id="cbocategory" name="cbocategory" class="form-control input-md select2_single input-sm">
                    <option value="" selected>Select</option>
                        <?php
                        if($Category!="")
                        {
                            while($row = mysqli_fetch_assoc($Category))
                            {
                                
                                $selprocedureCategoryId  = $row['procedureCategoryId'];
                                $categoryName  = stripslashes($row['categoryName']);

                                ?>
                                <option value="<?php echo EncodeQueryData($selprocedureCategoryId); ?>" <?php if($procedureCategoryId==$selprocedureCategoryId){ ?>  selected="true" <?php } ?>><?php echo($categoryName); ?></option>
                                <?php

                            }
                        }
                    ?>
                    </select>
                </div>
                <label class="control-label pull-right mt-10" for="cbocategory" style="margin-top:8px">Category</label>
                <br>  
                <br> 
        <table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
            <thead>
                <tr>
                    <th>Procedure ID</th>
                    <th>Procedure</th>
                    <th>Procedure Description</th>

                    <th style="text-align: center">Action</th>
                </tr>
            </thead>
            <tbody>
                <?php
                if ($totalProcedureCount > 0) {
                    while ($row = mysqli_fetch_array($rowsProcedureCount)) {

                        $proceduteCountId = $row['proceduteCountId'];
                        $title = stripslashes($row['proceduteCountName']);
                        $categoryName = stripslashes($row['categoryName']);
                        $procedureCountsCode = stripslashes($row['procedureCountsCode']);
                        $procedures = stripslashes($row['procedures']);

                        $totalUserCount = 0;
                ?>
                        <tr>

                            <td>
                                <?php echo ($procedureCountsCode); ?>
                            </td>
                            <td>
                                <?php echo ($procedures); ?>
                            </td>
                            <td>
                                <?php echo ($title); ?>
                            </td>

                            <td style="text-align: center">
                                <a href="addprocedurecounts.html?id=<?php echo (EncodeQueryData($proceduteCountId)); ?>">Edit</a>
                                | <a href="javascript:void(0);" class="deleteAjaxRow" proceduteCountId="<?php echo EncodeQueryData($proceduteCountId); ?>" proceduteCountName="<?php echo ($title); ?>">Delete</a>
                            </td>
                        </tr>
                <?php
                    }
                }
                ?>
            </tbody>
        </table>
    </div>

    <?php include('includes/footer.php'); ?>
    <?php include("includes/datatablejs.php") ?>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
    <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/select2/dist/js/select2.full.js"></script>


    <script type="text/javascript">
        alertify.defaults.transition = "slide";
        alertify.defaults.theme.ok = "btn btn-success";
        alertify.defaults.theme.cancel = "btn btn-danger";
        alertify.defaults.theme.input = "form-control";


        $(window).load(function() {
            $("#divTopLoading").addClass('hide');
            $(".select2_single").select2();
        });

        var current_datatable = $("#datatable-responsive").DataTable({
            "aoColumns": [{
                    "sWidth": "20%",
                },
                {
                    "sWidth": "20%",
                },
                {
                    "sWidth": "20%",
                }, {
                    "sWidth": "20%",
                    "sClass": "alignCenter",
                    "bSortable": false
                },
            ]
        });





        $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var proceduteCountId = $(this).attr('proceduteCountId');
            var title = $(this).attr('proceduteCountName');

            alertify.confirm('Procedure Count: ' + title, 'Continue with delete?', function() {
                $.ajax({
                    type: "GET",
                    url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                    data: {
                        id: proceduteCountId,
                        type: 'ProcedureCount'
                    },
                    success: function() {
                        current_datatable.row(current_datatable_row).remove().draw(false);
                        alertify.success('Deleted');
                    }
                });
            }, function() {});

        });

        $("#cbocategory").change(function() {
            var procedureCategoryId = $(this).val();

            if (procedureCategoryId) {
                window.location.href = "viewprocedurecounts.html?procedureCategoryId=" + procedureCategoryId;
            } else {
                window.location.href = "viewprocedurecounts.html";
            }
        });
    </script>


</body>

</html>