<?php
    include('includes/validateUserLogin.php'); 
    include('../includes/config.php'); 
    include('../class/clsDB.php');
	include('../includes/commonfun.php');       
    include('../class/clsMedicalTerminology.php');         
    include('../setRequest.php'); 	
	
	
	$currentSchoolId;

	
	$objMedicalTerminology = new clsMedicalTerminology();			
	$rowsMedicalTerminology = $objMedicalTerminology->GetAllMedicalTerminology();
	
	$totalCount = 0;
	if($rowsMedicalTerminology !='')
	{
		$totalCount = mysqli_num_rows($rowsMedicalTerminology);
	}
	
	
	
?>

    <!DOCTYPE html>
    <html lang="en">

    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <!-- <meta name="viewport" content="width=device-width, initial-scale=1"> -->
	<meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
        <!-- The above 3 meta tags *must* come first in the head; any other head content must come *after* these tags -->
        <title>Medical Terminology</title>
        <?php include('includes/headercss.php');?>
        <?php include("includes/datatablecss.php") ?>
		
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/alertify.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/css/themes/bootstrap.css" />
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/css/magnific-popup.css">
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/assets/css/audioPlayer.css">
        <link rel="stylesheet" href="<?php echo ($dynamicOrgUrl); ?>/student/assets/Student.css">

    </head>

    <body>
        <?php include('includes/header.php');?>

        <div class="row margin_zero breadcrumb-bg">
            <div class="container">
                <div class="pull-left">
                    <ol class="breadcrumb">
						<li><a href="dashboard.html">Home</a></li>
						<li><a href="settings.html">Setting	</a></li>                        					
						<li class="active">Medical Terminology</li>						
                    </ol>
                </div>
               <div class="pull-right">  
				<ol class="breadcrumb">
                   <li> <a href="addMedicalTerminology.html" >Add</a> </li>
                 </ol>   
               </div>
            </div>
        </div>

        <div class="container">


            <?php
				if (isset($_GET["status"]))
				{
					if($_GET["status"] =="added")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Medical Terminology added successfully.
                </div>
                <?php 
					}
                    else if($_GET["status"] =="updated")
					{
						?>
                <div class="alert alert-success alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button>Medical Terminology updated successfully.
                </div>
                <?php 
					}                   
                    else if($_GET["status"] =="error")
					{
						?>
                <div class="alert alert-danger alert-dismissible fade in" role="alert">
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close"><span aria-hidden="true">×</span>
                            </button> Error occurred.
                </div>
                <?php 
					}

				}
              ?>

                <div id="divTopLoading" >Loading...</div>
				<table id="datatable-responsive" class="table table-bordered dt-responsive nowrap table-hover" cellspacing="0" width="100%">
                    <thead>
                        <tr>
                           <th>Title</th>
                           <th>Description</th>	   
                           <th style="text-align: center">Status</th>		   
                           <th style="text-align: center">Action</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        if($totalCount > 0)
                        {
                            while($row = mysqli_fetch_array($rowsMedicalTerminology))
                            {								
                                $id = $row['id'];                               			
                                $title = $row['title']; 
                                $status = $row['status'];  
                                $description = $row['description'];
                                $audio_file = $row['audio_file'];
                                if($status =="1")
                                {
                                    $displayStatus="Active";
                                    $updateStatus = "0";
                                    $buttoncss = "text-primary";
                                }
                                else
                                {
                                    $displayStatus="Inactive";
                                    $updateStatus = "1";
                                    $buttoncss = "text-warning";
                                }

                                $shortdescriptionlen=strlen($description);

                                if($shortdescriptionlen > 50)
                                {
                                    
                                    $sortdescription=substr($description,0,50);
                                    $sortdescription .= '...';
                                    
                                }else{
                                    $sortdescription=$description;
                                }
                                    
                                                     
                               ?>
                            <tr>
                                <td><?php echo ($title); ?> <span style="font-size: 20px;"><a href="#test-popup" class="open-popup-link glyphicon glyphicon-volume-up pull-right " medicalId="<?php echo $id; ?>" audiotitle="<?php echo $title; ?>" audio_file="<?php echo $audio_file; ?>" ></a></span></td>
                                <td title="<?php echo $description; ?>"><?php echo ($sortdescription); ?></td>
								<td style="text-align: center">
                                    <!-- <label class="switch">
                                        <input type="checkbox" id="medical_checkbox" name="medical_checkbox" class="sendrequest" <?php if($status == 1) {?> checked  <?php } ?> actiontype="<?php echo ($actiontype); ?>" medicalId="<?php echo ($id); ?>">
                                        <span class="slider round"></span>
                                    </label> -->
                                    <a class="<?php echo($buttoncss); ?>" href="assign_medical_terminology_status.html?id=<?php echo(EncodeQueryData($id)); ?>&newStatus=<?php echo($updateStatus); ?>" >
                                    <?php echo($displayStatus); ?></a>
                                </td>


								<td style="text-align:center"> 
                                    <a href="addMedicalTerminology.html?id=<?php echo EncodeQueryData($id); ?>" >Edit</a>|
                                    <a href="javascript:void(0);" class="deleteAjaxRow"
									id="<?php echo EncodeQueryData($id); ?>" title="<?php echo($title); ?>" >Delete</a>
                                </td>   
							   
                            </tr>
                            <?php
                            }
                        }
                    ?>
					</tbody>
                </table>
        </div>
        <!-- Popup itself -->
        <div id="test-popup" class="white-popup mfp-hide">
            <audio src="" id="hidden-player"></audio>
            <div id="player">
                <img src="<?php echo ($dynamicOrgUrl); ?>/assets/images/logo_small.png" class="coverr" alt="" height="100" width="100" />
                <div class="player-song">
                    <div class="title"></div>
                    <div class="artist"></div>
                    <progress value="0" max="1" class="audiotime"></progress>
                    <div class="timestamps">
                        <div class="time-now">0:00:00</div>
                        <div class="time-finish">0:00:00</div>
                    </div>
                    <div class="actions">
                        <!-- <div class="prev">
                            <i class="material-icons">fast_rewind</i>
                        </div> -->
                        <div class="play">
                            <a class="play-button paused" href="#">
                                <div class="left"></div>
                                <div class="right"></div>
                                <div class="triangle-1"></div>
                                <div class="triangle-2"></div>
                            </a>
                        </div>
                        <!-- <div class="next">
                            <i class="material-icons">fast_forward</i>
                        </div> -->
                    </div>
                </div>
            </div>
        </div>

        <?php include('includes/footer.php');?>
        <?php include("includes/datatablejs.php") ?>
		
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/alertifyjs/alertify.js"></script>
        <script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/magnificpopup/js/jquery.magnific-popup.min.js"></script>
		
        <script type="text/javascript">

            alertify.defaults.transition = "slide";
            alertify.defaults.theme.ok = "btn btn-success";
            alertify.defaults.theme.cancel = "btn btn-danger";
            alertify.defaults.theme.input = "form-control";

			
            $(window).load(function(){

                $("#divTopLoading").addClass('hide');
            });
			
            $(document).ready(function() {
                var current_datatable = $("#datatable-responsive").DataTable({
                        
                        "aoColumns": [{
                        "sWidth": "30%"
                            
                        },{
                        "sWidth": "30%",
                        "bSortable": false
                        },{
                        "sWidth": "20%",
                        "bSortable": false
                        },{
                        "sWidth": "20%"	,
                        "bSortable": false			
                    }],
                    "fnDrawCallback": function () {
                        $('.open-popup-link').magnificPopup({
                            enableEscapeKey: false,
                            closeOnBgClick: true,
                            type:'inline',
                            midClick: true // allow opening popup on middle mouse click. Always set it to true if you don't provide alternative source.
                        });
                    }
                });

            });   

            $(document).on('click', '#warningAjax', function() {

                var current_datatable_row = current_datatable.row($(this).parents('tr'));
                
                alertify.confirm('Warning!', 'This section already assigned, you cant delete it!', function(){
                }, function() {});
            });
			
            $(document).on('click', '.deleteAjaxRow', function() {

            var current_datatable_row = current_datatable.row($(this).parents('tr'));
            var id = $(this).attr('id');
            var title = $(this).attr('title');
            
            alertify.confirm('Title: '+title, 'Continue with delete?', function() {
                    $.ajax({
                        type: "GET",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_delete.html",
                        data: {
                            id: id,
                            type: 'Medical_Terminology'
                        },
                        success: function() {
                            current_datatable.row(current_datatable_row).remove().draw(false);
                            alertify.success('Deleted');
                        }
                    });
                }, function() {});

            });

            $("#datatable-responsive").on("click", ".sendrequest", function() {		
					var action;	
					var thischeck= $(this);
                    var actiontype = $(this).attr('actiontype'); 
                    var medicalId = $(this).attr('medicalId'); 
			       
					$.ajax({
                        type: "POST",
                        url: "<?php echo ($dynamicOrgUrl); ?>/ajax/ajax_ciEval_approve.html",
                        data: {							
						      id: medicalId,
							  action : actiontype,										  
							  type: 'medical_terminology_status'								  
                        },
						 success: function() {   
							if(actiontype == 'true'){ //Assigned 
                            	alertify.success('Activated');
							thischeck.attr('actiontype','false'); }
								
							else if(actiontype == 'false') {//Removed
                            	alertify.error('Deactivated');
							thischeck.attr('actiontype','true'); }
                        }
                    });
			        
                });
                
                
               
                
                $("#datatable-responsive").on("click", ".open-popup-link", function() {	

                    //audio
                    var num = 0;
                    var hiddenPlayer = $('#hidden-player');
                    var player = $('#player');
                    var title = $('.title');
                    var artist = $('.artist');
                    var cover = $('.coverr');

                    function secondsTimeSpanToHMS(s) {
                        var h = Math.floor(s / 3600); //Get whole hours
                        s -= h * 3600;
                        var m = Math.floor(s / 60); //Get remaining minutes
                        s -= m * 60;
                        return h + ":" + (m < 10 ? '0' + m : m) + ":" + (s < 10 ? '0' + s : s); //zero padding on minutes and seconds
                    };

                    var id = $(this).attr('medicalId');
                    var audiotitle = $(this).attr('audiotitle');
                    var audio_file = $(this).attr('audio_file');
                    var filePath = "<?php echo ($dynamicOrgUrl); ?>/upload/schools/1/MedicalTerminology/"+id+"/" +audio_file;
                    
                    songs = [{
                        src: filePath,
                        title: audiotitle
                    }];

                    var initSongSrc = songs[0].src;
                    var initSongTitle = songs[0].title;
                    
                    hiddenPlayer.attr("src", initSongSrc);
                    title.html(initSongTitle);

                    // hiddenPlayer.attr('order', '0');
                    hiddenPlayer[0].onloadedmetadata = function() {
                        var dur = hiddenPlayer[0].duration;
                        var songLength = secondsTimeSpanToHMS(dur)
                        var songLengthParse = songLength.split(".")[0];
                        $('.time-finish').html(songLengthParse);
                    };

                    var items = songs.length - 1;
                    $(".play-button").click(function() {
                        $(this).toggleClass("paused");
                        if ($(this).hasClass("paused")) {
                            hiddenPlayer[0].pause();
                        } else {
                            hiddenPlayer[0].play();
                        }
                    });

                   
                    $(".mfp-close").click(function() {
                        location.reload(true);
                    });

                    hiddenPlayer.on('timeupdate', function() {

                        var songLength = secondsTimeSpanToHMS(this.duration)
                        var songLengthParse = songLength.split(".")[0];
                        $('.time-finish').html(songLengthParse);

                        var songCurrent = secondsTimeSpanToHMS(this.currentTime)
                        var songCurrentParse = songCurrent.split(".")[0];
                        $('.time-now').html(songCurrentParse);
                        $('progress').attr("value", this.currentTime / this.duration);

                        if (!hiddenPlayer[0].paused) {
                            $(".play-button").removeClass('paused');
                            $('progress').css('cursor', 'pointer');
                            
                            
                            $('progress').on('click', function(e) {
                                var parentOffset = $(this).parent().offset(); 
                                var relX = e.pageX - parentOffset.left;
                                var percPos = relX * 100 / 355;
                                var second = hiddenPlayer[0].duration * parseInt(percPos) / 100;
                                console.log(second);
                                hiddenPlayer[0].currentTime = second;
                            })
                        }  
                        
                    });
                });
        </script>
    </body>
    </html>